'use client'

import {
  Box,
  Container,
  Flex,
  Text,
  HStack,
  Link as ChakraLink
} from '@chakra-ui/react'
import Link from 'next/link'

export default function Footer() {
  return (
    <Box bg="gray.800" color="white" py={8} mt="auto">
      <Container maxW="container.xl">
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align="center">
          <Text fontSize="sm">
            © 2024 Social NFT Platform. All rights reserved.
          </Text>

          <HStack gap={6} mt={{ base: 4, md: 0 }}>
            <ChakraLink as={Link} href="/about" fontSize="sm">
              About
            </ChakraLink>
            <ChakraLink as={Link} href="/privacy" fontSize="sm">
              Privacy
            </ChakraLink>
            <ChakraLink as={Link} href="/terms" fontSize="sm">
              Terms
            </ChakraLink>
          </HStack>
        </Flex>
      </Container>
    </Box>
  )
}
