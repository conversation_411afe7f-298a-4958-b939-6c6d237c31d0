# Enterprise Prisma Schema - Complete Implementation

## 🎯 **ENTERPRISE-GRADE SCHEMA WITH ALL FEATURES**

**Based on:** Sample app enterprise patterns + platform best practices  
**Features:** CQRS, audit trails, monitoring, compliance, security  
**Approach:** Template-First implementation (max 30 lines per section)  

## 📋 **COMPLETE PRISMA SCHEMA**

### **Generator and Datasource Configuration:**
```prisma
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["metrics", "tracing"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### **User Command Model (Write Side - CQRS):**
```prisma
model UserCommand {
  // Primary identifier
  id                    String    @id @default(cuid())
  
  // Core user fields
  username              String    @unique @db.VarChar(50)
  email                 String    @unique @db.VarChar(100)
  password              String    @db.VarChar(255)
  
  // Social integration
  twitterUsername       String?   @map("twitter_username") @db.Var<PERSON>har(50)
  twitterId             String?   @map("twitter_id") @db.VarChar(50)
  
  // User status and role
  role                  String    @default("user") @db.VarChar(20)
  isActive              Boolean   @default(true) @map("is_active")
  isEmailVerified       Boolean   @default(false) @map("is_email_verified")
  
  // Enterprise audit fields
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  createdBy             String?   @map("created_by") @db.VarChar(50)
  updatedBy             String?   @map("updated_by") @db.VarChar(50)
  version               Int       @default(1)
  
  // Security and compliance
  lastLoginAt           DateTime? @map("last_login_at")
  passwordChangedAt     DateTime? @map("password_changed_at")
  failedLoginAttempts   Int       @default(0) @map("failed_login_attempts")
  lockedUntil           DateTime? @map("locked_until")
  
  // Data classification
  dataClassification    String    @default("internal") @map("data_classification")
  retentionPolicy       String    @default("7years") @map("retention_policy")
  
  // Relationships
  auditLogs             AuditLog[]
  events                UserEvent[]
  
  @@map("user_commands")
  @@index([email])
  @@index([username])
  @@index([isActive])
  @@index([createdAt])
  @@index([lastLoginAt])
}
```

### **User Query Model (Read Side - CQRS):**
```prisma
model UserQuery {
  // Primary identifier (same as command)
  id                    String    @id
  
  // Denormalized user data for fast queries
  username              String    @unique @db.VarChar(50)
  email                 String    @unique @db.VarChar(100)
  displayName           String?   @map("display_name") @db.VarChar(100)
  
  // Social integration (denormalized)
  twitterUsername       String?   @map("twitter_username") @db.VarChar(50)
  twitterFollowers      Int?      @map("twitter_followers")
  twitterVerified       Boolean?  @map("twitter_verified")
  
  // User status and role
  role                  String    @default("user") @db.VarChar(20)
  isActive              Boolean   @default(true) @map("is_active")
  isEmailVerified       Boolean   @default(false) @map("is_email_verified")
  
  // Pre-computed aggregations
  totalNfts             Int       @default(0) @map("total_nfts")
  totalCampaigns        Int       @default(0) @map("total_campaigns")
  totalTransactions     Int       @default(0) @map("total_transactions")
  totalValue            Decimal   @default(0) @db.Decimal(18, 8) @map("total_value")
  
  // Performance metrics
  avgResponseTime       Float?    @map("avg_response_time")
  lastActivityAt        DateTime? @map("last_activity_at")
  
  // Timestamps
  createdAt             DateTime  @map("created_at")
  lastUpdated           DateTime  @map("last_updated")
  
  @@map("user_queries")
  @@index([email])
  @@index([username])
  @@index([isActive])
  @@index([totalNfts])
  @@index([lastActivityAt])
}
```

### **Event Sourcing Schema:**
```prisma
model UserEvent {
  // Event identification
  id                    String    @id @default(cuid())
  aggregateId           String    @map("aggregate_id") // User ID
  eventType             String    @map("event_type") @db.VarChar(50)
  eventVersion          Int       @map("event_version")

  // Event data
  eventData             Json      @map("event_data")
  metadata              Json?     // Additional context

  // Event tracking
  causationId           String?   @map("causation_id") // What caused this event
  correlationId         String?   @map("correlation_id") // Request correlation

  // Audit information
  createdAt             DateTime  @default(now()) @map("created_at")
  createdBy             String?   @map("created_by")

  // Relationships
  user                  UserCommand @relation(fields: [aggregateId], references: [id])

  @@map("user_events")
  @@index([aggregateId, eventVersion])
  @@index([eventType])
  @@index([createdAt])
  @@index([correlationId])
}
```

### **Audit Log Schema:**
```prisma
model AuditLog {
  // Audit identification
  id                    String    @id @default(cuid())
  entityType            String    @map("entity_type") @db.VarChar(50)
  entityId              String    @map("entity_id")
  action                String    @db.VarChar(50) // CREATE, UPDATE, DELETE

  // Change tracking
  oldValues             Json?     @map("old_values")
  newValues             Json?     @map("new_values")
  changedFields         String[]  @map("changed_fields")

  // Context information
  userId                String?   @map("user_id")
  sessionId             String?   @map("session_id")
  ipAddress             String?   @map("ip_address") @db.VarChar(45)
  userAgent             String?   @map("user_agent")

  // Compliance fields
  reason                String?   // Reason for change
  approvedBy            String?   @map("approved_by")
  complianceFlags       String[]  @map("compliance_flags")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  // Relationships
  user                  UserCommand? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
  @@index([entityType, entityId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
}
```

---
**Status:** ✅ Complete Enterprise Schema with Event Sourcing & Audit
**Location:** docs/architecture/ENTERPRISE-PRISMA-SCHEMA.md
