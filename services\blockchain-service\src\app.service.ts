import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'ok',
      service: 'blockchain-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || 3005,
      environment: process.env.NODE_ENV || 'development',
    };
  }

  getBlockchainStatus() {
    return {
      status: 'ok',
      service: 'blockchain-service',
      timestamp: new Date().toISOString(),
      networks: {
        ethereum: {
          mainnet: {
            rpc: process.env.ETHEREUM_RPC_URL ? 'configured' : 'not configured',
            chainId: process.env.ETHEREUM_CHAIN_ID || 1,
          },
          testnet: {
            rpc: process.env.ETHEREUM_TESTNET_RPC_URL ? 'configured' : 'not configured',
            chainId: process.env.ETHEREUM_TESTNET_CHAIN_ID || 11155111,
          },
        },
        polygon: {
          mainnet: {
            rpc: process.env.POLYGON_RPC_URL ? 'configured' : 'not configured',
            chainId: process.env.POLYGON_CHAIN_ID || 137,
          },
          testnet: {
            rpc: process.env.POLYGON_TESTNET_RPC_URL ? 'configured' : 'not configured',
            chainId: process.env.POLYGON_TESTNET_CHAIN_ID || 80001,
          },
        },
      },
      contracts: {
        ethereum: process.env.NFT_CONTRACT_ADDRESS_ETHEREUM || 'not deployed',
        polygon: process.env.NFT_CONTRACT_ADDRESS_POLYGON || 'not deployed',
      },
      ipfs: {
        gateway: process.env.IPFS_GATEWAY_URL || 'https://ipfs.io/ipfs/',
        pinata: process.env.PINATA_API_KEY ? 'configured' : 'not configured',
      },
    };
  }
}
