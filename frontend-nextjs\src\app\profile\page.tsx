'use client'

import {
  <PERSON>,
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON>ck,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  SimpleGrid,
  Input,
  InputGroup
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { ProfileLayout } from '@/components/PageLayout'
import ProfileForm from '@/components/ProfileForm'
import AvatarUpload from '@/components/AvatarUpload'
import SocialConnections from '@/components/SocialConnections'

export default function ProfilePage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'profile' | 'avatar' | 'social'>('profile')
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (isLoading) return
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }
  }, [isAuthenticated, isLoading, router])

  const handleProfileSave = async (data: any) => {
    setIsSaving(true)
    try {
      // TODO: Implement actual profile update API call
      console.log('💾 Saving profile data:', data)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      alert('Profile updated successfully!')
    } catch (error) {
      console.error('Profile update error:', error)
      alert('Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  const handleProfileCancel = () => {
    setActiveTab('profile')
  }

  const handleAvatarChange = (avatarUrl: string) => {
    console.log('🖼️ Avatar changed:', avatarUrl)
    // TODO: Update user avatar in context/backend
  }

  const handleConnectionChange = (platform: string, connected: boolean) => {
    console.log(`🔗 ${platform} ${connected ? 'connected' : 'disconnected'}`)
    // TODO: Update social connections in backend
  }

  // Show loading during AuthContext initialization
  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Initializing authentication...</Text>
        </VStack>
      </Container>
    )
  }

  return (
    <ProfileLayout>
      <VStack gap={8} align="stretch">
        {/* Tab Navigation */}
        <HStack gap={4} bg="white" p={4} borderRadius="lg" boxShadow="sm">
          <Button
            variant={activeTab === 'profile' ? 'solid' : 'ghost'}
            colorScheme="blue"
            onClick={() => setActiveTab('profile')}
          >
            📝 Profile
          </Button>
          <Button
            variant={activeTab === 'avatar' ? 'solid' : 'ghost'}
            colorScheme="blue"
            onClick={() => setActiveTab('avatar')}
          >
            📸 Avatar
          </Button>
          <Button
            variant={activeTab === 'social' ? 'solid' : 'ghost'}
            colorScheme="blue"
            onClick={() => setActiveTab('social')}
          >
            🔗 Social
          </Button>
        </HStack>

        {/* Tab Content */}
        <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
          {activeTab === 'profile' && (
            <ProfileForm
              onSave={handleProfileSave}
              onCancel={handleProfileCancel}
              isLoading={isSaving}
            />
          )}

          {activeTab === 'avatar' && (
            <AvatarUpload
              onAvatarChange={handleAvatarChange}
              currentAvatar={user?.avatar}
            />
          )}

          {activeTab === 'social' && (
            <SocialConnections
              onConnectionChange={handleConnectionChange}
            />
          )}
        </Box>
      </VStack>
    </ProfileLayout>
  )
}
