const { PrismaClient } = require('@prisma/client');

async function testEnterpriseSchema() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🚀 Testing Enterprise Schema...');
    
    // Test UserCommand table
    const userCommandCount = await prisma.userCommand.count();
    console.log('✅ UserCommand table accessible, count:', userCommandCount);
    
    // Test UserQuery table
    const userQueryCount = await prisma.userQuery.count();
    console.log('✅ UserQuery table accessible, count:', userQueryCount);
    
    // Test UserEvent table
    const userEventCount = await prisma.userEvent.count();
    console.log('✅ UserEvent table accessible, count:', userEventCount);
    
    // Test AuditLog table
    const auditLogCount = await prisma.auditLog.count();
    console.log('✅ AuditLog table accessible, count:', auditLogCount);
    
    console.log('🎉 All enterprise tables are working perfectly!');
    
  } catch (error) {
    console.error('❌ Error testing schema:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testEnterpriseSchema();
