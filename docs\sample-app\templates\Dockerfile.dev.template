# Smart Docker Caching Template for Social Commerce Platform
# Replace placeholders with actual service values:
# - SERVICE_NAME: Name of the service (e.g., user-service, product-service)
# - PORT_NUMBER: Service port (e.g., 3001, 3004)

# Dependencies Stage - CACHED (rarely changes)
FROM node:18-alpine AS dependencies

WORKDIR /app

# Copy only package files for dependency caching
COPY services/SERVICE_NAME/package*.json ./

# Copy shared libraries (also rarely change)
COPY libs/common ./libs/common

# Install dependencies - THIS LAYER WILL BE CACHED
RUN npm install --legacy-peer-deps

# Development Stage - NEVER CACHED (changes frequently)
FROM node:18-alpine AS development

WORKDIR /app

# Copy cached dependencies from previous stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/libs ./libs
COPY --from=dependencies /app/package*.json ./

# Copy source code - THIS LAYER IS NEVER CACHED
COPY services/SERVICE_NAME/src ./src
COPY services/SERVICE_NAME/tsconfig.json ./
COPY services/SERVICE_NAME/nest-cli.json ./

# Build the application - REBUILDS ONLY WHEN SOURCE CHANGES
RUN npm run build

# Set environment variables
ENV NODE_ENV=development

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
USER nestjs

# Expose port
EXPOSE PORT_NUMBER

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:PORT_NUMBER/api/health || exit 1

# Start the application
CMD ["node", "dist/main.js"]

# Usage Instructions:
# 1. Copy this template: cp docs/templates/Dockerfile.dev.template services/YOUR_SERVICE/Dockerfile.dev
# 2. Replace SERVICE_NAME with your service name
# 3. Replace PORT_NUMBER with your service port
# 4. Add service to docker-compose.override.yml
# 5. Test with: ./scripts/dev-restart-service.sh YOUR_SERVICE
