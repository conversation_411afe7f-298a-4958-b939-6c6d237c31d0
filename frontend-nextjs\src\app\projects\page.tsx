'use client'

import {
  Container,
  Heading,
  Text,
  VStack,
  Box
} from '@chakra-ui/react'
import { useEffect, useState } from 'react'
import Layout from '@/components/layout/Layout'
import ProjectList from '@/components/projects/ProjectList'
import ProjectFilters from '@/components/projects/ProjectFilters'
import { projectService, Project } from '@/services/projectService'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      setLoading(true)
      setError('')
      const data = await projectService.getProjects()
      setProjects(data)
    } catch (err: any) {
      setError('Failed to load projects')
      console.error('Projects error:', err)
    } finally {
      setLoading(false)
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || project.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleClearFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
  }

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack gap={8} align="stretch">
          {/* Page Header */}
          <Box textAlign="center">
            <Heading as="h1" size="2xl" mb={4} color="blue.600">
              🚀 Discover Projects
            </Heading>
            <Text fontSize="lg" color="gray.600">
              Explore exciting NFT projects and join campaigns to mint unique digital assets
            </Text>
          </Box>

          {/* Project Filters */}
          <ProjectFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusChange={setStatusFilter}
            onClearFilters={handleClearFilters}
            resultCount={filteredProjects.length}
            totalCount={projects.length}
          />

          {/* Projects List */}
          <ProjectList
            projects={filteredProjects}
            loading={loading}
            error={error}
          />
        </VStack>
      </Container>
    </Layout>
  )
}
