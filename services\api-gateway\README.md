# API Gateway

Unified API Gateway for the Social NFT Platform - Single entry point for all microservices.

## Features

- **Request Routing** - Routes requests to appropriate microservices
- **Authentication** - JWT-based authentication across all services
- **Rate Limiting** - Protects services from abuse
- **Health Monitoring** - Monitors all service health
- **Request Logging** - Comprehensive request/response logging
- **Error Handling** - Graceful error handling and forwarding
- **CORS Support** - Cross-origin resource sharing
- **Security Headers** - Helmet.js security middleware

## Quick Start

### Prerequisites
- Node.js 18+
- All microservices running (user-service, profile-analysis-service)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Start the API Gateway:
```bash
# Development
npm run start:dev

# Production
npm run build
npm run start:prod
```

### API Documentation

Once running, visit: http://localhost:3000/api/docs

## API Routes

### Gateway Health
- `GET /api/` - Gateway status
- `GET /api/health` - Health check for all services
- `GET /api/services` - Status of all microservices

### User Management (Proxied to user-service:3001)
- `POST /api/users/register` - Register new user
- `POST /api/users/login` - Login user
- `GET /api/users/profile` - Get user profile (requires auth)

### Profile Analysis (Proxied to profile-analysis-service:3002)
- `POST /api/analysis/twitter-profile` - Analyze Twitter profile (requires auth)
- `GET /api/analysis/results/:id` - Get analysis results (requires auth)
- `GET /api/analysis/history` - Get analysis history (requires auth)

## Service Integration

### Registered Services
- **user-service** - http://localhost:3001
- **profile-analysis-service** - http://localhost:3002

### Adding New Services
1. Add service URL to `app.service.ts` services array
2. Add service URL to `proxy.service.ts` serviceUrls
3. Create new controller in `routing/controllers/`
4. Add controller to `routing.module.ts`

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| NODE_ENV | Environment | development |
| PORT | Gateway port | 3000 |
| CORS_ORIGIN | CORS origin | * |
| JWT_SECRET | JWT secret key | your-secret-key |
| THROTTLE_TTL | Rate limit window | 60000 |
| THROTTLE_LIMIT | Rate limit count | 100 |

## Architecture

```
Client Request
     ↓
API Gateway (Port 3000)
     ↓
┌─────────────────┬─────────────────┐
│  User Service   │ Profile Service │
│   (Port 3001)   │   (Port 3002)   │
└─────────────────┴─────────────────┘
```

## Error Handling

The gateway handles errors gracefully:
- **503 Service Unavailable** - When target service is down
- **504 Gateway Timeout** - When target service doesn't respond
- **502 Bad Gateway** - When target service returns an error
- **Original Error** - Forwards original error from services

## Security Features

- **Helmet.js** - Security headers
- **Rate Limiting** - Prevents abuse
- **CORS** - Cross-origin protection
- **Request Sanitization** - Removes sensitive data from logs
- **JWT Forwarding** - Passes authentication to services
