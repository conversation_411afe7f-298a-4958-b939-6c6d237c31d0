"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/basic-analytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/basic-analytics.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BasicAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BasicAnalytics() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for different data sections\n    const [topGainers, setTopGainers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topLosers, setTopLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collectionGainers, setCollectionGainers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collectionLosers, setCollectionLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topUsers, setTopUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentTransactions, setRecentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch data from backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BasicAnalytics.useEffect\": ()=>{\n            const fetchAnalyticsData = {\n                \"BasicAnalytics.useEffect.fetchAnalyticsData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        // Fetch all analytics data in parallel\n                        const [gainersLosersResponse, collectionMarketResponse, platformOverviewResponse, recentTransactionsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getTopGainersLosers('7d', 10),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getCollectionMarketValue(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getPlatformOverview(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getRecentTransactions(10)\n                        ]);\n                        // Process gainers/losers data\n                        if (gainersLosersResponse.success && gainersLosersResponse.data) {\n                            const { gainers, losers } = gainersLosersResponse.data;\n                            setTopGainers(gainers || []);\n                            setTopLosers(losers || []);\n                        }\n                        // Process collection market data\n                        if (collectionMarketResponse.success && collectionMarketResponse.data) {\n                            const { gainers, losers } = collectionMarketResponse.data;\n                            setCollectionGainers(gainers || []);\n                            setCollectionLosers(losers || []);\n                        }\n                        // Process platform overview for top users\n                        if (platformOverviewResponse.success && platformOverviewResponse.data) {\n                            const { topUsers: users } = platformOverviewResponse.data;\n                            setTopUsers(users || []);\n                        }\n                        // Process recent transactions\n                        if (recentTransactionsResponse.success && recentTransactionsResponse.data) {\n                            setRecentTransactions(recentTransactionsResponse.data.transactions || []);\n                        }\n                    } catch (err) {\n                        console.error('Error fetching analytics data:', err);\n                        setError('Failed to load analytics data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"BasicAnalytics.useEffect.fetchAnalyticsData\"];\n            fetchAnalyticsData();\n        }\n    }[\"BasicAnalytics.useEffect\"], []);\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error Loading Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Analytics Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Platform insights and performance tracking\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Users Score Board\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700\",\n                                children: \"See Top 100 ↗\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Gainer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Absolute (5m)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Relative (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ30D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ3M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    topGainers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold\",\n                                                                                children: user.name.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900\",\n                                                                    children: user.current\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d7\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d30\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d3m\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Loser\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Absolute (5m)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Relative (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ30D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ3M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    topLosers.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold\",\n                                                                                children: user.name.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900\",\n                                                                    children: user.current\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d7\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d30\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d3m\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Top20\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"7D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-medium\",\n                                                        children: \"30D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"3M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"6M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"12M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '5%',\n                                                    width: '22%',\n                                                    height: '35%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"\\uD83E\\uDDCA IcoBe...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-yellow-200 text-xs\",\n                                                                    children: \"\\uD83D\\uDC51\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"0.73%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-yellow-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-yellow-300 rounded\",\n                                                                        style: {\n                                                                            width: '60%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-red-600 to-red-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '26%',\n                                                    top: '5%',\n                                                    width: '20%',\n                                                    height: '32%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"\\uD83C\\uDF5E BREAD | Σ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"0.56%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-red-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-red-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '48%',\n                                                    top: '5%',\n                                                    width: '18%',\n                                                    height: '30%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDFE2 mert | heliu...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.55%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-green-300 rounded\",\n                                                                        style: {\n                                                                            width: '55%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '68%',\n                                                    top: '5%',\n                                                    width: '16%',\n                                                    height: '28%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDF19 Yueya.eth...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.51%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-blue-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-blue-300 rounded\",\n                                                                        style: {\n                                                                            width: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '42%',\n                                                    width: '16%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC0B wale.m...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.45%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-teal-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '20%',\n                                                    top: '42%',\n                                                    width: '15%',\n                                                    height: '23%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC64 david p...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.40%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-purple-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-purple-300 rounded\",\n                                                                        style: {\n                                                                            width: '40%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '37%',\n                                                    top: '42%',\n                                                    width: '13%',\n                                                    height: '20%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDFAF nairolf\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"0.38%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '52%',\n                                                    top: '42%',\n                                                    width: '12%',\n                                                    height: '18%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"⚡ Tomas...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"0.38%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Collection Market Value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700\",\n                                children: \"See All Collections ↗\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Gainer Collections\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Market Cap (24h)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Volume (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Floor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ24h\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'CryptoArt Pro',\n                                                            symbol: 'CAP',\n                                                            floor: '2.5 ETH',\n                                                            d24h: '+15.2%',\n                                                            d7d: '+45.8%',\n                                                            volume: '125 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Digital Punks',\n                                                            symbol: 'DP',\n                                                            floor: '1.8 ETH',\n                                                            d24h: '+12.7%',\n                                                            d7d: '+38.4%',\n                                                            volume: '98 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Meta Worlds',\n                                                            symbol: 'MW',\n                                                            floor: '3.2 ETH',\n                                                            d24h: '+9.8%',\n                                                            d7d: '+28.9%',\n                                                            volume: '156 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Pixel Heroes',\n                                                            symbol: 'PH',\n                                                            floor: '0.9 ETH',\n                                                            d24h: '+8.5%',\n                                                            d7d: '+22.1%',\n                                                            volume: '67 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Cyber Cats',\n                                                            symbol: 'CC',\n                                                            floor: '1.4 ETH',\n                                                            d24h: '+7.2%',\n                                                            d7d: '+19.6%',\n                                                            volume: '89 ETH',\n                                                            trend: 'up'\n                                                        }\n                                                    ].map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-white\",\n                                                                                children: collection.symbol.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: collection.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900 font-medium\",\n                                                                    children: collection.floor\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: collection.d24h\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: collection.d7d\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-700 font-medium\",\n                                                                    children: collection.volume\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Loser Collections\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Market Cap (24h)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Volume (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Floor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ24h\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'Old School NFT',\n                                                            symbol: 'OSN',\n                                                            floor: '0.3 ETH',\n                                                            d24h: '-18.5%',\n                                                            d7d: '-42.3%',\n                                                            volume: '12 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Retro Pixels',\n                                                            symbol: 'RP',\n                                                            floor: '0.5 ETH',\n                                                            d24h: '-15.2%',\n                                                            d7d: '-35.7%',\n                                                            volume: '23 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Classic Art',\n                                                            symbol: 'CA',\n                                                            floor: '0.8 ETH',\n                                                            d24h: '-12.8%',\n                                                            d7d: '-28.4%',\n                                                            volume: '34 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Vintage Cards',\n                                                            symbol: 'VC',\n                                                            floor: '0.2 ETH',\n                                                            d24h: '-10.9%',\n                                                            d7d: '-25.1%',\n                                                            volume: '18 ETH',\n                                                            trend: 'down'\n                                                        }\n                                                    ].map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gradient-to-r from-red-400 to-orange-500 rounded flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-white\",\n                                                                                children: collection.symbol.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: collection.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900 font-medium\",\n                                                                    children: collection.floor\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: collection.d24h\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: collection.d7d\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-700 font-medium\",\n                                                                    children: collection.volume\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Top20 Collections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 font-medium\",\n                                                        children: \"24h\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"7D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"30D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"3M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '5%',\n                                                    width: '22%',\n                                                    height: '35%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"\\uD83C\\uDFA8 CryptoArt Pro\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"\\uD83D\\uDC51\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"2.5 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"+15.2%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-blue-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-blue-300 rounded\",\n                                                                        style: {\n                                                                            width: '75%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '26%',\n                                                    top: '5%',\n                                                    width: '20%',\n                                                    height: '32%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"\\uD83D\\uDC7E Digital Punks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"1.8 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-purple-200 text-xs\",\n                                                                    children: \"+12.7%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-purple-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-purple-300 rounded\",\n                                                                        style: {\n                                                                            width: '65%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '48%',\n                                                    top: '5%',\n                                                    width: '18%',\n                                                    height: '30%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDF0D Meta Worlds\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"3.2 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-200 text-xs\",\n                                                                    children: \"+9.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-green-300 rounded\",\n                                                                        style: {\n                                                                            width: '60%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-yellow-600 to-yellow-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '68%',\n                                                    top: '5%',\n                                                    width: '16%',\n                                                    height: '28%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83E\\uDDB8 Pixel Heroes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.9 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-200 text-xs\",\n                                                                    children: \"+8.5%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-yellow-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-yellow-300 rounded\",\n                                                                        style: {\n                                                                            width: '55%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '42%',\n                                                    width: '16%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC31 Cyber Cats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"1.4 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-teal-200 text-xs\",\n                                                                    children: \"+7.2%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-teal-300 rounded\",\n                                                                        style: {\n                                                                            width: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '20%',\n                                                    top: '42%',\n                                                    width: '15%',\n                                                    height: '23%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDE80 Space NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"2.1 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-indigo-200 text-xs\",\n                                                                    children: \"+6.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-indigo-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-indigo-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-pink-600 to-pink-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '37%',\n                                                    top: '42%',\n                                                    width: '13%',\n                                                    height: '20%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC8E Gems\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-xs font-bold\",\n                                                                    children: \"1.7 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-pink-200 text-xs\",\n                                                                    children: \"+5.4%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '52%',\n                                                    top: '42%',\n                                                    width: '12%',\n                                                    height: '18%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDFAD Art Club\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-xs font-bold\",\n                                                                    children: \"0.8 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-orange-200 text-xs\",\n                                                                    children: \"+4.9%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Top 100 Platform Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"7d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-purple-600 text-white rounded\",\n                                            children: \"30d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"90d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"1y\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-4 mb-4 text-xs text-gray-500 font-medium border-b border-gray-200 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1\",\n                                children: \"Rank\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-3\",\n                                children: \"User\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"NFTs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Growth\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: [\n                            {\n                                rank: 1,\n                                name: '@alice_crypto',\n                                avatar: 'AC',\n                                score: 2547,\n                                nfts: 89,\n                                volume: 45.2,\n                                growth: 12.5,\n                                scoreBar: 95,\n                                nftBar: 78,\n                                volumeBar: 85,\n                                growthBar: 92\n                            },\n                            {\n                                rank: 2,\n                                name: '@bob_nft_master',\n                                avatar: 'BN',\n                                score: 2341,\n                                nfts: 76,\n                                volume: 38.7,\n                                growth: 8.2,\n                                scoreBar: 88,\n                                nftBar: 65,\n                                volumeBar: 72,\n                                growthBar: 78\n                            },\n                            {\n                                rank: 3,\n                                name: '@charlie_artist',\n                                avatar: 'CA',\n                                score: 2156,\n                                nfts: 92,\n                                volume: 52.1,\n                                growth: 5.7,\n                                scoreBar: 82,\n                                nftBar: 85,\n                                volumeBar: 95,\n                                growthBar: 65\n                            },\n                            {\n                                rank: 4,\n                                name: '@diana_collector',\n                                avatar: 'DC',\n                                score: 1987,\n                                nfts: 45,\n                                volume: 28.9,\n                                growth: -2.1,\n                                scoreBar: 75,\n                                nftBar: 42,\n                                volumeBar: 55,\n                                growthBar: 25\n                            },\n                            {\n                                rank: 5,\n                                name: '@eve_crypto_queen',\n                                avatar: 'EC',\n                                score: 1834,\n                                nfts: 67,\n                                volume: 34.5,\n                                growth: 3.4,\n                                scoreBar: 70,\n                                nftBar: 58,\n                                volumeBar: 65,\n                                growthBar: 58\n                            },\n                            {\n                                rank: 6,\n                                name: '@frank_nft_lord',\n                                avatar: 'FN',\n                                score: 1723,\n                                nfts: 54,\n                                volume: 29.8,\n                                growth: 1.2,\n                                scoreBar: 65,\n                                nftBar: 48,\n                                volumeBar: 58,\n                                growthBar: 52\n                            },\n                            {\n                                rank: 7,\n                                name: '@grace_digital',\n                                avatar: 'GD',\n                                score: 1645,\n                                nfts: 71,\n                                volume: 31.2,\n                                growth: 4.8,\n                                scoreBar: 62,\n                                nftBar: 62,\n                                volumeBar: 60,\n                                growthBar: 62\n                            },\n                            {\n                                rank: 8,\n                                name: '@henry_meta',\n                                avatar: 'HM',\n                                score: 1567,\n                                nfts: 38,\n                                volume: 22.4,\n                                growth: -1.5,\n                                scoreBar: 58,\n                                nftBar: 35,\n                                volumeBar: 42,\n                                growthBar: 35\n                            }\n                        ].map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-gray-700 text-sm font-medium\",\n                                        children: user.rank\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs font-bold\",\n                                                    children: user.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-900 text-sm font-medium\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500 text-xs\",\n                                                        children: \"Verified Creator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: user.score.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.scoreBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: user.nfts\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.nftBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: [\n                                                    user.volume,\n                                                    \" ETH\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.volumeBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold mb-1 \".concat(user.growth >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                children: [\n                                                    user.growth >= 0 ? '+' : '',\n                                                    user.growth,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 rounded-full \".concat(user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'),\n                                                    style: {\n                                                        width: \"\".concat(user.growthBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, user.rank, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Transactions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-blue-800\",\n                                                    children: \"GEN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Digital Art #123\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"User: @alice_crypto • generation • 5m ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: \"0.5 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-green-800\",\n                                                    children: \"SHR\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Punk Avatar #456\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"User: @bob_nft • share • 12m ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: \"0.3 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(BasicAnalytics, \"j5oenyuohU+ku7NNaS0gpL/DV3w=\");\n_c = BasicAnalytics;\nvar _c;\n$RefreshReg$(_c, \"BasicAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/basic-analytics.tsx\n"));

/***/ })

});