# Campaign Data E2E Test
Write-Host "=== CAMPAIGN DATA E2E TEST ===" -ForegroundColor Cyan
Write-Host "Testing campaign data retrieval for frontend" -ForegroundColor Green

# Test Configuration
$projectServiceUrl = "http://localhost:3005"

Write-Host "`nTesting campaign data API..." -ForegroundColor Yellow

# Test 1: Get Campaigns
try {
    $campaigns = Invoke-RestMethod -Uri "$projectServiceUrl/api/campaigns" -Method Get -TimeoutSec 10
    Write-Host "✅ Campaigns retrieved successfully!" -ForegroundColor Green
    Write-Host "Campaign count: $($campaigns.Count)" -ForegroundColor Green
    
    if ($campaigns.Count -gt 0) {
        Write-Host "First campaign: $($campaigns[0].name)" -ForegroundColor Green
        Write-Host "Campaign status: $($campaigns[0].status)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Campaign retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 Campaign Data E2E Test PASSED!" -ForegroundColor Green
