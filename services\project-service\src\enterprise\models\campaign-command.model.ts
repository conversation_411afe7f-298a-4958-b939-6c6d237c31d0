// Enterprise Campaign Command Model (Write Side) - Requirements-Driven Implementation
import { IsString, IsOptional, IsBoolean, IsInt, IsEnum, IsJSON, IsUUID, IsDateString, IsArray, IsObject, ValidateNested, IsNumber, IsUrl, Min, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum CampaignStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum CampaignType {
  USER_ACQUISITION = 'user_acquisition',
  ENGAGEMENT = 'engagement',
  RETENTION = 'retention'
}

// Target Audience Configuration (Requirements-driven)
export class TargetAudienceConfig {
  @ApiProperty({ description: 'Minimum follower count' })
  @IsNumber()
  @Min(0)
  minFollowers: number;

  @ApiPropertyOptional({ description: 'Maximum follower count' })
  @IsOptional()
  @IsNumber()
  maxFollowers?: number;

  @ApiPropertyOptional({ description: 'Required account age in days' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minAccountAge?: number;

  @ApiPropertyOptional({ description: 'Required engagement rate' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  minEngagementRate?: number;

  @ApiPropertyOptional({ description: 'Geographic restrictions', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedCountries?: string[];

  @ApiPropertyOptional({ description: 'Language requirements', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  languages?: string[];
}

// Social Platform Configuration (Requirements-driven)
export class SocialPlatformConfig {
  @ApiProperty({ description: 'Platform name' })
  @IsString()
  platform: string;

  @ApiProperty({ description: 'Is platform enabled' })
  @IsBoolean()
  enabled: boolean;

  @ApiPropertyOptional({ description: 'Required hashtags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredHashtags?: string[];

  @ApiPropertyOptional({ description: 'Required mentions', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredMentions?: string[];

  @ApiPropertyOptional({ description: 'Minimum interactions per day' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minDailyInteractions?: number;
}

// NFT Generation Rules (Requirements-driven)
export class NFTGenerationRules {
  @ApiProperty({ description: 'Auto-mint on campaign join' })
  @IsBoolean()
  autoMintOnJoin: boolean;

  @ApiProperty({ description: 'Evolution frequency in hours' })
  @IsNumber()
  @Min(1)
  @Max(168)
  evolutionFrequencyHours: number;

  @ApiPropertyOptional({ description: 'Custom score multipliers' })
  @IsOptional()
  @IsObject()
  scoreMultipliers?: Record<string, number>;

  @ApiPropertyOptional({ description: 'Special evolution triggers' })
  @IsOptional()
  @IsArray()
  specialTriggers?: string[];
}

// Campaign Metrics Configuration (Requirements-driven)
export class CampaignMetrics {
  @ApiPropertyOptional({ description: 'Target participant count' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetParticipants?: number;

  @ApiPropertyOptional({ description: 'Target engagement rate' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  targetEngagementRate?: number;

  @ApiPropertyOptional({ description: 'Target NFT mint count' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetNftMints?: number;

  @ApiPropertyOptional({ description: 'Target social media reach' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetReach?: number;

  @ApiPropertyOptional({ description: 'Success criteria' })
  @IsOptional()
  @IsObject()
  successCriteria?: Record<string, any>;
}

// Reward Structure (Requirements-driven)
export class RewardStructure {
  @ApiProperty({ description: 'NFT rewards configuration' })
  @IsObject()
  nftRewards: {
    common: { probability: number; bonusPoints?: number };
    rare: { probability: number; bonusPoints?: number };
    legendary: { probability: number; bonusPoints?: number };
  };

  @ApiPropertyOptional({ description: 'Token rewards' })
  @IsOptional()
  @IsObject()
  tokenRewards?: {
    amount: number;
    tokenSymbol: string;
    distributionMethod: string;
  };

  @ApiPropertyOptional({ description: 'Special rewards for milestones' })
  @IsOptional()
  @IsArray()
  milestoneRewards?: Array<{
    milestone: string;
    reward: string;
    criteria: string;
  }>;
}

// Main Campaign DTOs (Requirements-driven)
export class CreateCampaignCommandDto {
  @ApiProperty({ description: 'Project ID this campaign belongs to' })
  @IsString()
  projectId: string;

  @ApiProperty({ description: 'Campaign name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Campaign description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Campaign type' })
  @IsEnum(CampaignType)
  campaignType: CampaignType;

  @ApiProperty({ description: 'Campaign start date' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: 'Campaign end date' })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({ description: 'Timezone', default: 'UTC' })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({ description: 'Target audience configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => TargetAudienceConfig)
  targetAudience?: TargetAudienceConfig;

  @ApiPropertyOptional({ description: 'Participation requirements', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requirements?: string[];

  @ApiProperty({ description: 'Reward structure' })
  @ValidateNested()
  @Type(() => RewardStructure)
  rewards: RewardStructure;

  @ApiPropertyOptional({ description: 'Social platforms configuration', type: [SocialPlatformConfig] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SocialPlatformConfig)
  socialPlatforms?: SocialPlatformConfig[];

  @ApiPropertyOptional({ description: 'NFT generation rules' })
  @IsOptional()
  @ValidateNested()
  @Type(() => NFTGenerationRules)
  nftGenerationRules?: NFTGenerationRules;

  @ApiPropertyOptional({ description: 'Target metrics' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CampaignMetrics)
  targetMetrics?: CampaignMetrics;

  @ApiPropertyOptional({ description: 'Maximum participants' })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxParticipants?: number;

  @ApiPropertyOptional({ description: 'Minimum participants', default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  minParticipants?: number;

  @ApiPropertyOptional({ description: 'Launch approval required', default: false })
  @IsOptional()
  @IsBoolean()
  launchApproval?: boolean;
}

export class UpdateCampaignCommandDto {
  @ApiPropertyOptional({ description: 'Campaign name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Campaign description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Campaign type' })
  @IsOptional()
  @IsEnum(CampaignType)
  campaignType?: CampaignType;

  @ApiPropertyOptional({ description: 'Campaign start date' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'Campaign end date' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Timezone' })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({ description: 'Target audience configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => TargetAudienceConfig)
  targetAudience?: TargetAudienceConfig;

  @ApiPropertyOptional({ description: 'Participation requirements', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requirements?: string[];

  @ApiPropertyOptional({ description: 'Reward structure' })
  @IsOptional()
  @ValidateNested()
  @Type(() => RewardStructure)
  rewards?: RewardStructure;

  @ApiPropertyOptional({ description: 'Social platforms configuration', type: [SocialPlatformConfig] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SocialPlatformConfig)
  socialPlatforms?: SocialPlatformConfig[];

  @ApiPropertyOptional({ description: 'NFT generation rules' })
  @IsOptional()
  @ValidateNested()
  @Type(() => NFTGenerationRules)
  nftGenerationRules?: NFTGenerationRules;

  @ApiPropertyOptional({ description: 'Target metrics' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CampaignMetrics)
  targetMetrics?: CampaignMetrics;

  @ApiPropertyOptional({ description: 'Maximum participants' })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxParticipants?: number;

  @ApiPropertyOptional({ description: 'Minimum participants' })
  @IsOptional()
  @IsInt()
  @Min(1)
  minParticipants?: number;

  @ApiPropertyOptional({ description: 'Campaign status' })
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;

  @ApiPropertyOptional({ description: 'Launch approval status' })
  @IsOptional()
  @IsBoolean()
  launchApproval?: boolean;
}
