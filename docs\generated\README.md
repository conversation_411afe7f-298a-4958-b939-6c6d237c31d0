# Generated Documentation

This directory contains auto-generated documentation files created from the master reference.

## Files

- `API_ENDPOINTS.md` - Complete API endpoints reference
- `SERVICE_CONFIG.md` - Service configuration and ports
- `ENVIRONMENT_CONFIG.md` - Environment variables and settings

## Important Notes

⚠️ **DO NOT EDIT THESE FILES MANUALLY**

These files are automatically generated from `docs/MASTER_REFERENCE.md`.
To make changes:

1. Edit `docs/MASTER_REFERENCE.md` (the single source of truth)
2. Run `./tools/scripts/generate-docs.sh` to regenerate all files

## Generation

To regenerate all documentation:

```bash
./tools/scripts/generate-docs.sh
```
