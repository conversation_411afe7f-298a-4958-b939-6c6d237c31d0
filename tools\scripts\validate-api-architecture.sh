#!/bin/bash

# =============================================================================
# API ARCHITECTURE VALIDATION SCRIPT
# =============================================================================
# Validates that all services follow the standardized API routing architecture
# as defined in docs/guidelines/api-routing-architecture.md
#
# USAGE: ./tools/scripts/validate-api-architecture.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_SERVICES=0
COMPLIANT_SERVICES=0
ISSUES_FOUND=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
    ((ISSUES_FOUND++))
}

# Check if we're in the right directory
if [ ! -d "services" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

echo ""
log_info "🔍 API ARCHITECTURE VALIDATION"
echo "==============================="
echo ""

# Get all service directories (Windows-compatible)
SERVICES=(
    "services/marketplace-service"
    "services/project-service"
    "services/nft-generation-service"
    "services/profile-analysis-service"
    "services/user-service"
    "services/notification-service"
    "services/analytics-service"
    "services/api-gateway"
)

# Filter to only existing services
EXISTING_SERVICES=()
for service in "${SERVICES[@]}"; do
    if [ -d "$service" ]; then
        EXISTING_SERVICES+=("$service")
    fi
done

if [ ${#EXISTING_SERVICES[@]} -eq 0 ]; then
    log_error "No services found in services/ directory"
    exit 1
fi

SERVICES=("${EXISTING_SERVICES[@]}")

log_info "Found ${#SERVICES[@]} services to validate"
echo ""

# Validation functions
validate_global_prefix() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    local main_file="$service_path/src/main.ts"
    
    if [ ! -f "$main_file" ]; then
        log_error "$service_name: main.ts not found"
        return 1
    fi
    
    if grep -q "setGlobalPrefix.*api" "$main_file"; then
        log_success "$service_name: Global prefix 'api' configured"
        return 0
    else
        log_error "$service_name: Missing global prefix 'api' in main.ts"
        return 1
    fi
}

validate_controller_patterns() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    
    # Find all controller files (Windows-compatible)
    local controller_files=""
    if [ -d "$service_path/src" ]; then
        controller_files=$(ls "$service_path/src"/**/*.controller.ts 2>/dev/null || echo "")
        if [ -z "$controller_files" ]; then
            # Fallback: check common controller locations
            for dir in "$service_path/src" "$service_path/src"/*/ "$service_path/src"/*/*/; do
                if [ -d "$dir" ]; then
                    controller_files="$controller_files $(ls "$dir"*.controller.ts 2>/dev/null || echo "")"
                fi
            done
        fi
    fi
    
    if [ -z "$controller_files" ]; then
        log_warning "$service_name: No controller files found"
        return 0
    fi
    
    local has_issues=false
    local controller_count=0
    
    # Check each controller
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            ((controller_count++))
            local filename=$(basename "$file")
            
            # Check for double API prefix
            if grep -q "@Controller('api/" "$file"; then
                log_error "$service_name: Double API prefix in $filename"
                has_issues=true
            fi
        fi
    done <<< "$controller_files"
    
    if [ "$has_issues" = false ] && [ $controller_count -gt 0 ]; then
        log_success "$service_name: All $controller_count controllers follow correct pattern"
        return 0
    elif [ $controller_count -eq 0 ]; then
        log_warning "$service_name: No controllers to validate"
        return 0
    else
        return 1
    fi
}

validate_health_endpoint() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    
    # Check if app.controller.ts exists and has health endpoint
    local app_controller="$service_path/src/app.controller.ts"
    
    if [ -f "$app_controller" ]; then
        if grep -q "@Get('health')" "$app_controller" || grep -q "getHealth" "$app_controller"; then
            log_success "$service_name: Health endpoint configured"
            return 0
        else
            log_error "$service_name: Health endpoint not found in app.controller.ts"
            return 1
        fi
    else
        log_error "$service_name: app.controller.ts not found"
        return 1
    fi
}

validate_swagger_setup() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    local main_file="$service_path/src/main.ts"
    
    if grep -q "SwaggerModule.setup.*api/docs" "$main_file"; then
        log_success "$service_name: Swagger configured at /api/docs"
        return 0
    else
        log_warning "$service_name: Swagger not configured or incorrect path"
        return 1
    fi
}

# Main validation loop
for service_path in "${SERVICES[@]}"; do
    service_name=$(basename "$service_path")
    ((TOTAL_SERVICES++))
    
    echo ""
    log_info "🔍 Validating $service_name"
    echo "$(printf '%.0s-' {1..50})"
    
    local service_compliant=true
    
    # Run all validations
    validate_global_prefix "$service_path" || service_compliant=false
    validate_controller_patterns "$service_path" || service_compliant=false
    validate_health_endpoint "$service_path" || service_compliant=false
    validate_swagger_setup "$service_path" || true  # Warning only
    
    if [ "$service_compliant" = true ]; then
        ((COMPLIANT_SERVICES++))
        log_success "$service_name: ✅ FULLY COMPLIANT"
    else
        log_error "$service_name: ❌ NON-COMPLIANT"
    fi
done

# Summary report
echo ""
echo ""
log_info "📊 VALIDATION SUMMARY"
echo "====================="
echo "Total Services: $TOTAL_SERVICES"
echo "Compliant Services: $COMPLIANT_SERVICES"
echo "Non-Compliant Services: $((TOTAL_SERVICES - COMPLIANT_SERVICES))"
echo "Total Issues Found: $ISSUES_FOUND"

if [ $ISSUES_FOUND -eq 0 ]; then
    echo ""
    log_success "🎉 ALL SERVICES PASS API ARCHITECTURE VALIDATION!"
    echo ""
    log_info "✅ Architecture Compliance: 100%"
    log_info "✅ No double API prefixes found"
    log_info "✅ All services follow standardized patterns"
    echo ""
    exit 0
else
    echo ""
    log_error "❌ VALIDATION FAILED - $ISSUES_FOUND issues found"
    echo ""
    log_info "📋 Next Steps:"
    log_info "1. Review failed validations above"
    log_info "2. Fix issues using docs/guidelines/api-routing-architecture.md"
    log_info "3. Run validation again: ./tools/scripts/validate-api-architecture.sh"
    echo ""
    exit 1
fi
