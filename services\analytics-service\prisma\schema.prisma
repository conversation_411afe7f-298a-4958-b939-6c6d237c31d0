// Enterprise Analytics Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model AnalyticsEventCommand {
  id                  String   @id @default(cuid())

  // Core Event Data
  eventType           String   // "user_action", "nft_interaction", "marketplace_activity"
  eventCategory       String   // "engagement", "transaction", "performance"
  eventName           String   // "nft_view", "listing_created", "purchase_completed"
  entityId            String   // ID of the entity being tracked
  entityType          String   // "user", "nft", "project", "listing"

  // Event Context
  userId              String?
  sessionId           String?
  projectId           String?

  // Event Data
  eventData           Json?
  metadata            Json?

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  @@map("analytics_event_commands")
}

model MetricsCommand {
  id                  String   @id @default(cuid())

  // Core Metrics Data
  metricName          String   // "daily_active_users", "nft_views", "transaction_volume"
  metricType          String   // "counter", "gauge", "histogram"
  metricValue         Float
  metricUnit          String?  // "count", "percentage", "currency"

  // Time Context
  timeframe           String   // "hourly", "daily", "weekly", "monthly"
  periodStart         DateTime
  periodEnd           DateTime

  // Dimensions
  dimensions          Json?    // Additional grouping dimensions

  // Status
  status              String   @default("active") // active, archived

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  @@map("metrics_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model AnalyticsQuery {
  id                  String   @id

  // Optimized Display Data
  displayName         String
  displayValue        String
  displayUnit         String?
  metricType          String

  // Time Context
  timeframe           String
  periodStart         DateTime
  periodEnd           DateTime

  // Performance Metrics
  calculatedValue     Float
  trendDirection      String?  // "up", "down", "stable"
  percentageChange    Float?

  // Aggregation Data
  aggregationType     String   // "sum", "avg", "count", "max", "min"
  sampleSize          Int?

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("analytics_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "analytics_event", "metrics", "report"
  entityId        String
  action          String   // "create", "update", "delete", "calculate", "aggregate"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model AnalyticsEvent {
  id              String   @id @default(cuid())
  eventType       String   // "metric_calculated", "report_generated", "data_aggregated"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Analytics/Metrics ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("analytics_events")
}
