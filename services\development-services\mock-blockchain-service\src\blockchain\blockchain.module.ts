import { Modu<PERSON> } from '@nestjs/common';
import { BlockchainController } from './blockchain.controller';
import { BlockchainService } from './blockchain.service';
import { NFTController } from './nft.controller';
import { WalletController } from './wallet.controller';
import { TransactionsController } from './transactions.controller';

@Module({
  controllers: [
    BlockchainController,
    NFTController,
    WalletController,
    TransactionsController,
  ],
  providers: [BlockchainService],
  exports: [BlockchainService],
})
export class BlockchainModule {}
