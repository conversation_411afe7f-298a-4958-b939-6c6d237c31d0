// Enterprise Project Command Model (Write Side) - Requirements-Driven Implementation
import { IsString, IsOptional, IsBoolean, IsInt, IsEnum, IsJSON, IsUUID, IsDateString, IsArray, IsObject, ValidateNested, IsNumber, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum ProjectStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

export enum BlockchainNetwork {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  BASE = 'base'
}

// Analysis Configuration Types (Requirements-driven)
export class FixedParameterConfig {
  @ApiProperty({ description: 'Parameter weight (0-100)' })
  @IsNumber()
  weight: number;

  @ApiProperty({ description: 'Is parameter enabled' })
  @IsBoolean()
  enabled: boolean;
}

export class FollowerCategory {
  @ApiProperty({ description: 'Category name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Minimum followers' })
  @IsInt()
  min: number;

  @ApiProperty({ description: 'Maximum followers' })
  @IsInt()
  max: number;

  @ApiProperty({ description: 'Category weight' })
  @IsNumber()
  weight: number;
}

export class FixedParameters {
  @ApiProperty({ description: 'Has bio parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  hasBio: FixedParameterConfig;

  @ApiProperty({ description: 'Has avatar parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  hasAvatar: FixedParameterConfig;

  @ApiProperty({ description: 'Has banner parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  hasBanner: FixedParameterConfig;

  @ApiProperty({ description: 'Follower categories', type: [FollowerCategory] })
  @ValidateNested({ each: true })
  @Type(() => FollowerCategory)
  followerCategories: FollowerCategory[];

  @ApiProperty({ description: 'Engagement rate parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  engagementRate: FixedParameterConfig;

  @ApiProperty({ description: 'Account age parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  accountAge: FixedParameterConfig;
}

export class VariableParameters {
  @ApiProperty({ description: 'Activity level parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  activityLevel: FixedParameterConfig;

  @ApiProperty({ description: 'Content quality parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  contentQuality: FixedParameterConfig;

  @ApiProperty({ description: 'Project interactions parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  projectInteractions: FixedParameterConfig;

  @ApiProperty({ description: 'Referrals parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  referrals: FixedParameterConfig;

  @ApiProperty({ description: 'Campaign loyalty parameter' })
  @ValidateNested()
  @Type(() => FixedParameterConfig)
  campaignLoyalty: FixedParameterConfig;
}

export class AnalysisConfiguration {
  @ApiProperty({ description: 'Fixed parameters configuration' })
  @ValidateNested()
  @Type(() => FixedParameters)
  fixedParameters: FixedParameters;

  @ApiProperty({ description: 'Variable parameters configuration' })
  @ValidateNested()
  @Type(() => VariableParameters)
  variableParameters: VariableParameters;

  @ApiProperty({ description: 'Update frequency in hours' })
  @IsInt()
  updateFrequencyHours: number;
}

// NFT Configuration Types (Requirements-driven)
export class RarityThresholds {
  @ApiProperty({ description: 'Minimum score for Common NFT' })
  @IsNumber()
  common: number;

  @ApiProperty({ description: 'Minimum score for Rare NFT' })
  @IsNumber()
  rare: number;

  @ApiProperty({ description: 'Minimum score for Legendary NFT' })
  @IsNumber()
  legendary: number;
}

export class NFTDesignConfig {
  @ApiProperty({ description: 'NFT theme (e.g., character, animal, abstract)' })
  @IsString()
  theme: string;

  @ApiProperty({ description: 'NFT style (e.g., modern, retro, minimalist)' })
  @IsString()
  style: string;

  @ApiProperty({ description: 'Main color scheme' })
  @IsString()
  mainColor: string;

  @ApiProperty({ description: 'Fixed elements (logo, branding)', type: [String] })
  @IsArray()
  @IsString({ each: true })
  fixedElements: string[];
}

export class EvolutionTrigger {
  @ApiProperty({ description: 'Trigger type' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Trigger condition' })
  @IsString()
  condition: string;

  @ApiProperty({ description: 'Target rarity' })
  @IsString()
  targetRarity: string;
}

export class NFTConfiguration {
  @ApiProperty({ description: 'Rarity score thresholds' })
  @ValidateNested()
  @Type(() => RarityThresholds)
  scoreThresholds: RarityThresholds;

  @ApiProperty({ description: 'NFT design configuration' })
  @ValidateNested()
  @Type(() => NFTDesignConfig)
  design: NFTDesignConfig;

  @ApiProperty({ description: 'Evolution triggers', type: [EvolutionTrigger] })
  @ValidateNested({ each: true })
  @Type(() => EvolutionTrigger)
  evolutionTriggers: EvolutionTrigger[];

  @ApiProperty({ description: 'Blockchain update policy' })
  @IsEnum(['immediate', 'batched', 'manual'])
  blockchainUpdatePolicy: 'immediate' | 'batched' | 'manual';
}

// Social Media Links
export class SocialMediaLinks {
  @ApiPropertyOptional({ description: 'Twitter URL' })
  @IsOptional()
  @IsUrl()
  twitter?: string;

  @ApiPropertyOptional({ description: 'Discord URL' })
  @IsOptional()
  @IsUrl()
  discord?: string;

  @ApiPropertyOptional({ description: 'Telegram URL' })
  @IsOptional()
  @IsUrl()
  telegram?: string;

  @ApiPropertyOptional({ description: 'Website URL' })
  @IsOptional()
  @IsUrl()
  website?: string;
}

// Campaign Duration
export class CampaignDuration {
  @ApiProperty({ description: 'Campaign start date' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: 'Campaign end date' })
  @IsDateString()
  endDate: string;

  @ApiProperty({ description: 'Timezone' })
  @IsString()
  timezone: string;
}

// Main Project DTOs (Requirements-driven)
export class CreateProjectCommandDto {
  @ApiProperty({ description: 'Project name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Project description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Project owner ID' })
  @IsString()
  ownerId: string;

  @ApiPropertyOptional({ description: 'Project category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Project images', type: [String] })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  images?: string[];

  @ApiPropertyOptional({ description: 'Project website' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({ description: 'Social media links' })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaLinks)
  socialMediaLinks?: SocialMediaLinks;

  @ApiPropertyOptional({ description: 'Campaign duration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CampaignDuration)
  duration?: CampaignDuration;

  @ApiPropertyOptional({ description: 'Participation conditions', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  participationConditions?: string[];

  @ApiProperty({ description: 'Analysis configuration' })
  @ValidateNested()
  @Type(() => AnalysisConfiguration)
  analysisConfiguration: AnalysisConfiguration;

  @ApiProperty({ description: 'NFT configuration' })
  @ValidateNested()
  @Type(() => NFTConfiguration)
  nftConfiguration: NFTConfiguration;

  @ApiProperty({ description: 'Blockchain network' })
  @IsEnum(BlockchainNetwork)
  blockchainNetwork: BlockchainNetwork;

  @ApiPropertyOptional({ description: 'Contract address' })
  @IsOptional()
  @IsString()
  contractAddress?: string;

  @ApiPropertyOptional({ description: 'Is project public', default: true })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Allow participation', default: true })
  @IsOptional()
  @IsBoolean()
  allowParticipation?: boolean;

  @ApiPropertyOptional({ description: 'Maximum participants' })
  @IsOptional()
  @IsInt()
  maxParticipants?: number;
}

export class UpdateProjectCommandDto {
  @ApiPropertyOptional({ description: 'Project name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Project description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Project category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Project images', type: [String] })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  images?: string[];

  @ApiPropertyOptional({ description: 'Project website' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({ description: 'Social media links' })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaLinks)
  socialMediaLinks?: SocialMediaLinks;

  @ApiPropertyOptional({ description: 'Campaign duration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CampaignDuration)
  duration?: CampaignDuration;

  @ApiPropertyOptional({ description: 'Participation conditions', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  participationConditions?: string[];

  @ApiPropertyOptional({ description: 'Analysis configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => AnalysisConfiguration)
  analysisConfiguration?: AnalysisConfiguration;

  @ApiPropertyOptional({ description: 'NFT configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => NFTConfiguration)
  nftConfiguration?: NFTConfiguration;

  @ApiPropertyOptional({ description: 'Blockchain network' })
  @IsOptional()
  @IsEnum(BlockchainNetwork)
  blockchainNetwork?: BlockchainNetwork;

  @ApiPropertyOptional({ description: 'Contract address' })
  @IsOptional()
  @IsString()
  contractAddress?: string;

  @ApiPropertyOptional({ description: 'Is project public' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Allow participation' })
  @IsOptional()
  @IsBoolean()
  allowParticipation?: boolean;

  @ApiPropertyOptional({ description: 'Maximum participants' })
  @IsOptional()
  @IsInt()
  maxParticipants?: number;

  @ApiPropertyOptional({ description: 'Project status' })
  @IsOptional()
  @IsEnum(ProjectStatus)
  status?: ProjectStatus;
}
