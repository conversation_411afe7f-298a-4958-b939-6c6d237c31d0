import { IsString, IsOptional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, IsDateString, Val<PERSON><PERSON>Nested, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';

export enum CampaignType {
  SOCIAL_ENGAGEMENT = 'social_engagement',
  CONTENT_CREATION = 'content_creation',
  COMMUNITY_BUILDING = 'community_building',
  TRADING_ACTIVITY = 'trading_activity',
  REFERRAL_PROGRAM = 'referral_program',
  MILESTONE_ACHIEVEMENT = 'milestone_achievement',
  SEASONAL_EVENT = 'seasonal_event',
  BRAND_COLLABORATION = 'brand_collaboration',
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum RewardType {
  NFT_GENERATION = 'nft_generation',
  TOKEN_REWARD = 'token_reward',
  POINTS = 'points',
  BADGE = 'badge',
  MARKETPLACE_CREDIT = 'marketplace_credit',
  EXCLUSIVE_ACCESS = 'exclusive_access',
  PHYSICAL_ITEM = 'physical_item',
}

export enum ParticipationRequirement {
  TWITTER_FOLLOW = 'twitter_follow',
  TWITTER_RETWEET = 'twitter_retweet',
  TWITTER_LIKE = 'twitter_like',
  TWITTER_COMMENT = 'twitter_comment',
  CONTENT_CREATION = 'content_creation',
  NFT_OWNERSHIP = 'nft_ownership',
  TRADING_VOLUME = 'trading_volume',
  REFERRAL_COUNT = 'referral_count',
  COMMUNITY_ENGAGEMENT = 'community_engagement',
  PROFILE_COMPLETION = 'profile_completion',
}

export class CampaignRequirementDto {
  @ApiProperty({
    description: 'Type of requirement',
    enum: ParticipationRequirement,
    example: ParticipationRequirement.TWITTER_FOLLOW,
  })
  @IsEnum(ParticipationRequirement)
  type: ParticipationRequirement;

  @ApiPropertyOptional({
    description: 'Target value for the requirement',
    example: '@project_twitter',
  })
  @IsOptional()
  @IsString()
  target?: string;

  @ApiPropertyOptional({
    description: 'Minimum value required',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minValue?: number;

  @ApiPropertyOptional({
    description: 'Maximum value allowed',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxValue?: number;

  @ApiPropertyOptional({
    description: 'Whether this requirement is mandatory',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  mandatory?: boolean = true;

  @ApiPropertyOptional({
    description: 'Points awarded for completing this requirement',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  points?: number;
}

export class CampaignRewardDto {
  @ApiProperty({
    description: 'Type of reward',
    enum: RewardType,
    example: RewardType.NFT_GENERATION,
  })
  @IsEnum(RewardType)
  type: RewardType;

  @ApiProperty({
    description: 'Reward name',
    example: 'Exclusive Campaign NFT',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Reward description',
    example: 'A unique NFT commemorating your participation in this campaign',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Reward value (tokens, points, etc.)',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  value?: number;

  @ApiPropertyOptional({
    description: 'Reward rarity or tier',
    example: 'rare',
    enum: ['common', 'rare', 'epic', 'legendary'],
  })
  @IsOptional()
  @IsString()
  rarity?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of this reward available',
    example: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantity?: number;

  @ApiPropertyOptional({
    description: 'Minimum points required to earn this reward',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minPointsRequired?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata for the reward',
    example: { nftTemplate: 'campaign_special', attributes: { color: 'gold' } },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class CreateCampaignDto {
  @ApiProperty({
    description: 'Campaign name',
    example: 'Summer NFT Collection Launch',
    maxLength: 100,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Campaign description',
    example: 'Join our summer campaign to earn exclusive NFTs and rewards!',
    maxLength: 1000,
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Campaign type',
    enum: CampaignType,
    example: CampaignType.SOCIAL_ENGAGEMENT,
  })
  @IsEnum(CampaignType)
  type: CampaignType;

  @ApiProperty({
    description: 'Campaign start date',
    example: '2025-06-10T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Campaign end date',
    example: '2025-06-30T23:59:59Z',
  })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: 'Campaign banner image URL',
    example: 'https://example.com/campaign-banner.jpg',
  })
  @IsOptional()
  @IsString()
  bannerImage?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of participants allowed',
    example: 10000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxParticipants?: number;

  @ApiPropertyOptional({
    description: 'Minimum age requirement',
    example: 18,
  })
  @IsOptional()
  @IsNumber()
  @Min(13)
  @Max(100)
  minAge?: number;

  @ApiPropertyOptional({
    description: 'Geographic restrictions (country codes)',
    example: ['US', 'CA', 'UK'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  geoRestrictions?: string[];

  @ApiProperty({
    description: 'Campaign requirements',
    type: [CampaignRequirementDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CampaignRequirementDto)
  requirements: CampaignRequirementDto[];

  @ApiProperty({
    description: 'Campaign rewards',
    type: [CampaignRewardDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CampaignRewardDto)
  rewards: CampaignRewardDto[];

  @ApiPropertyOptional({
    description: 'Campaign tags for categorization',
    example: ['summer', 'nft', 'social'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Campaign priority level',
    example: 5,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number = 5;

  @ApiPropertyOptional({
    description: 'Whether campaign is featured',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  featured?: boolean = false;

  @ApiPropertyOptional({
    description: 'Additional campaign metadata',
    example: { 
      socialLinks: { twitter: '@campaign', discord: 'discord.gg/campaign' },
      sponsorInfo: { name: 'Brand Partner', logo: 'https://example.com/logo.png' }
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateCampaignDto {
  @ApiPropertyOptional({
    description: 'Campaign name',
    example: 'Updated Summer NFT Collection Launch',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Campaign description',
    example: 'Updated description for our summer campaign!',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Campaign start date',
    example: '2025-06-15T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Campaign end date',
    example: '2025-07-15T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Campaign status',
    enum: CampaignStatus,
    example: CampaignStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;

  @ApiPropertyOptional({
    description: 'Campaign banner image URL',
    example: 'https://example.com/new-campaign-banner.jpg',
  })
  @IsOptional()
  @IsString()
  bannerImage?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of participants allowed',
    example: 15000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxParticipants?: number;

  @ApiPropertyOptional({
    description: 'Campaign requirements',
    type: [CampaignRequirementDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CampaignRequirementDto)
  requirements?: CampaignRequirementDto[];

  @ApiPropertyOptional({
    description: 'Campaign rewards',
    type: [CampaignRewardDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CampaignRewardDto)
  rewards?: CampaignRewardDto[];

  @ApiPropertyOptional({
    description: 'Campaign tags for categorization',
    example: ['summer', 'nft', 'social', 'updated'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Campaign priority level',
    example: 7,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({
    description: 'Whether campaign is featured',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiPropertyOptional({
    description: 'Additional campaign metadata',
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
