"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClockIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = ClockIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClockIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ClockIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/analytics-dashboard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/charts/analytics-charts */ \"(app-pages-browser)/./src/components/charts/analytics-charts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [platformData, setPlatformData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gainersLosers, setGainersLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [marketValue, setMarketValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTransactions, setRecentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('7d');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('yaps-kaito');\n    // Mock chart data - in production this would come from the analytics API\n    const mockGrowthData = [\n        {\n            date: '2025-06-01',\n            users: 1200,\n            nfts: 3200,\n            volume: 220\n        },\n        {\n            date: '2025-06-02',\n            users: 1210,\n            nfts: 3350,\n            volume: 225\n        },\n        {\n            date: '2025-06-03',\n            users: 1225,\n            nfts: 3400,\n            volume: 230\n        },\n        {\n            date: '2025-06-04',\n            users: 1240,\n            nfts: 3420,\n            volume: 232\n        },\n        {\n            date: '2025-06-05',\n            users: 1245,\n            nfts: 3450,\n            volume: 234\n        },\n        {\n            date: '2025-06-06',\n            users: 1247,\n            nfts: 3456,\n            volume: 235\n        }\n    ];\n    const mockEngagementData = [\n        {\n            date: '2025-06-01',\n            active_users: 89,\n            nft_generations: 45,\n            shares: 23\n        },\n        {\n            date: '2025-06-02',\n            active_users: 94,\n            nft_generations: 52,\n            shares: 28\n        },\n        {\n            date: '2025-06-03',\n            active_users: 76,\n            nft_generations: 38,\n            shares: 19\n        },\n        {\n            date: '2025-06-04',\n            active_users: 112,\n            nft_generations: 67,\n            shares: 34\n        },\n        {\n            date: '2025-06-05',\n            active_users: 98,\n            nft_generations: 43,\n            shares: 25\n        },\n        {\n            date: '2025-06-06',\n            active_users: 105,\n            nft_generations: 58,\n            shares: 31\n        }\n    ];\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch platform overview using proper API client\n            const platformResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getPlatformOverview();\n            if (platformResult.success) {\n                setPlatformData(platformResult.data);\n            }\n            // Fetch top gainers/losers using proper API client\n            const gainersResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getTopGainersLosers(selectedPeriod, 10);\n            if (gainersResult.success) {\n                setGainersLosers(gainersResult.data);\n            }\n            // Fetch collection market value using proper API client\n            const marketResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getCollectionMarketValue();\n            if (marketResult.success) {\n                setMarketValue(marketResult.data);\n            }\n            // Fetch recent transactions using proper API client\n            const transactionsResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getRecentTransactions(15);\n            if (transactionsResult.success) {\n                setRecentTransactions(transactionsResult.data.transactions);\n            }\n        } catch (err) {\n            setError('Failed to fetch analytics data');\n            console.error('Analytics fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsDashboard.useEffect\"], [\n        selectedPeriod\n    ]);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n        return num.toString();\n    };\n    const formatEth = (eth)=>\"\".concat(eth.toFixed(2), \" ETH\");\n    const getRarityColor = (rarity)=>{\n        switch(rarity.toLowerCase()){\n            case 'legendary':\n                return 'bg-yellow-500';\n            case 'epic':\n                return 'bg-purple-500';\n            case 'rare':\n                return 'bg-blue-500';\n            case 'common':\n                return 'bg-gray-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading analytics data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchAnalyticsData,\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            \"Retry\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Comprehensive platform insights and performance metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.user_growth_rate,\n                                                \"% from last month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"NFTs Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_nfts_generated)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.nft_generation_growth_rate,\n                                                \"% growth rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatEth(platformData.overview.total_volume_eth)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.marketplace_volume_growth_rate,\n                                                \"% volume growth\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Active Campaigns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: platformData.overview.active_campaigns\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                platformData.growth_metrics.campaign_participation_rate,\n                                                \"% participation rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: 'yaps-kaito',\n                                    name: '🎯 Yaps Kaito Style'\n                                },\n                                {\n                                    id: 'overview',\n                                    name: 'Overview'\n                                },\n                                {\n                                    id: 'gainers-losers',\n                                    name: 'Top Gainers/Losers'\n                                },\n                                {\n                                    id: 'market-value',\n                                    name: 'Market Value'\n                                },\n                                {\n                                    id: 'performance',\n                                    name: 'Performance'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab.name\n                                }, tab.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'yaps-kaito' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            gainersLosers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Top Gainers & Losers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Users' final score status and performance tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Period:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    [\n                                                        '7d',\n                                                        '30d'\n                                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedPeriod(period),\n                                                            className: \"px-3 py-1 text-sm font-medium rounded-md \".concat(selectedPeriod === period ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'),\n                                                            children: period\n                                                        }, period, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon, {\n                                                                className: \"h-5 w-5 text-green-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Top Gainers (\",\n                                                            gainersLosers.top_gainers.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: gainersLosers.top_gainers.map((item)=>{\n                                                            var _item_name_match;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                    className: \"h-6 w-6 text-gray-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 382,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-gray-900\",\n                                                                                                children: ((_item_name_match = item.name.match(/@(\\w+)/)) === null || _item_name_match === void 0 ? void 0 : _item_name_match[0]) || '@user'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                                lineNumber: 387,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getRarityColor(item.rarity), \" text-white\"),\n                                                                                                children: item.rarity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                                lineNumber: 388,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 386,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: [\n                                                                                            \"Score: \",\n                                                                                            item.current_score,\n                                                                                            \" | Volume: \",\n                                                                                            item.volume_24h,\n                                                                                            \" ETH\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 392,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1 text-green-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon, {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 399,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-bold\",\n                                                                                        children: item.change_percentage\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 400,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    selectedPeriod,\n                                                                                    \" change\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 402,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Performance Bubble Map\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-full h-full\",\n                                                                children: gainersLosers.top_gainers.map((item, index)=>{\n                                                                    var _item_name_match, _item_name_match1;\n                                                                    const size = 40 + item.current_score / 100 * 80; // 40-120px\n                                                                    const x = index % 3 * 30 + 10;\n                                                                    const y = Math.floor(index / 3) * 25 + 10;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute cursor-pointer transition-all duration-300 hover:scale-110\",\n                                                                        style: {\n                                                                            left: \"\".concat(x, \"%\"),\n                                                                            top: \"\".concat(y, \"%\"),\n                                                                            width: \"\".concat(size, \"px\"),\n                                                                            height: \"\".concat(size, \"px\")\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500\",\n                                                                            title: \"\".concat(((_item_name_match = item.name.match(/@(\\w+)/)) === null || _item_name_match === void 0 ? void 0 : _item_name_match[0]) || '@user', \" - Score: \").concat(item.current_score, \" (\").concat(item.change_percentage, \")\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs\",\n                                                                                        children: (((_item_name_match1 = item.name.match(/@(\\w+)/)) === null || _item_name_match1 === void 0 ? void 0 : _item_name_match1[0]) || '@user').slice(0, 6)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 435,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs font-bold\",\n                                                                                        children: item.current_score\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 436,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, item.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 29\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 right-2 bg-white p-2 rounded shadow\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Gainers\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs mt-1\",\n                                                                            children: \"Size = Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this),\n                            marketValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Collection Market Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Project collections growth and decline tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: [\n                                                            marketValue.market_overview.total_market_value.toFixed(2),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(marketValue.market_overview.market_cap_change_24h.startsWith('+') ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            marketValue.market_overview.market_cap_change_24h,\n                                                            \" (24h)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: marketValue.bubble_map_data.map((collection, index)=>{\n                                                    const maxValue = Math.max(...marketValue.bubble_map_data.map((c)=>c.value));\n                                                    const size = 60 + collection.value / maxValue * 90; // 60-150px\n                                                    const x = index % 2 * 40 + 10;\n                                                    const y = Math.floor(index / 2) * 35 + 10;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl\",\n                                                        style: {\n                                                            left: \"\".concat(x, \"%\"),\n                                                            top: \"\".concat(y, \"%\"),\n                                                            width: \"\".concat(size, \"px\"),\n                                                            height: \"\".concat(size, \"px\")\n                                                        },\n                                                        onClick: ()=>console.log('Navigate to collection:', collection.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg border-2 border-white\",\n                                                            style: {\n                                                                backgroundColor: collection.color\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs mb-1\",\n                                                                        children: collection.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            collection.value,\n                                                                            \" ETH\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            collection.size,\n                                                                            \" NFTs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-bold mt-1 \".concat(collection.change_24h.startsWith('+') ? 'text-green-200' : 'text-red-200'),\n                                                                        children: collection.change_24h\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, collection.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-4 right-4 bg-white p-3 rounded shadow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Collection Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: marketValue.bubble_map_data.map((collection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded\",\n                                                                            style: {\n                                                                                backgroundColor: collection.color\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: collection.rarity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, collection.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-2\",\n                                                            children: \"Click to view collection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this),\n                            recentTransactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Recent Transactions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Latest NFT activities, purchases and interactions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Real-time updates\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTransactions.slice(0, 10).map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(transaction.type === 'generation' ? 'bg-blue-100 text-blue-800' : transaction.type === 'share' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: transaction.type === 'generation' ? '🎨' : transaction.type === 'share' ? '📤' : '❤️'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: transaction.nft_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getRarityColor(transaction.rarity), \" text-white\"),\n                                                                                children: transaction.rarity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"User: @\",\n                                                                                    transaction.username\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"capitalize\",\n                                                                                children: transaction.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    Math.floor((new Date().getTime() - new Date(transaction.timestamp).getTime()) / (1000 * 60)),\n                                                                                    \"m ago\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-900\",\n                                                                        children: [\n                                                                            transaction.value_eth.toFixed(3),\n                                                                            \" ETH\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    (transaction.value_eth * 2000).toFixed(0),\n                                                                    \" USD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'overview' && platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.PlatformGrowthChart, {\n                                        data: mockGrowthData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.RarityDistributionChart, {\n                                        data: platformData.top_performing.nft_rarities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Top Performing Campaigns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Campaigns with highest engagement\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: platformData.top_performing.campaigns.map((campaign, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: campaign.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                formatNumber(campaign.participants),\n                                                                                \" participants\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        formatNumber(campaign.nfts_generated),\n                                                                        \" NFTs\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, campaign.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.EngagementMetricsChart, {\n                                        data: mockEngagementData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'gainers-losers' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Period:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    [\n                                        '24h',\n                                        '7d',\n                                        '30d'\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPeriod(period),\n                                            className: \"px-3 py-1 text-sm font-medium rounded-md \".concat(selectedPeriod === period ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'),\n                                            children: period\n                                        }, period, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this),\n                            gainersLosers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.GainersLosersChart, {\n                                        gainers: gainersLosers.top_gainers,\n                                        losers: gainersLosers.top_losers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Market Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Overall market performance for \",\n                                                            selectedPeriod\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: formatEth(gainersLosers.market_summary.total_volume_24h)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Total Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 \".concat(gainersLosers.market_summary.volume_change_24h.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: gainersLosers.market_summary.volume_change_24h\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: gainersLosers.market_summary.average_score_change\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Avg Score Change\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: gainersLosers.market_summary.most_active_rarity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Most Active Rarity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap justify-center gap-1\",\n                                                                children: gainersLosers.market_summary.trending_themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: theme\n                                                                    }, theme, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: \"Trending Themes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-8 w-8 mx-auto text-gray-400 animate-spin mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading gainers and losers data...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'market-value' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: marketValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.MarketValueChart, {\n                                    data: marketValue.bubble_map_data\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Market Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Collection market value and statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatEth(marketValue.market_overview.total_market_value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Total Market Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 \".concat(marketValue.market_overview.market_cap_change_24h.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: marketValue.market_overview.market_cap_change_24h\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: marketValue.market_overview.total_collections\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Collections\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatNumber(marketValue.market_overview.total_nfts)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Total NFTs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatEth(marketValue.market_overview.average_nft_value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Avg NFT Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-400 animate-spin mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Loading market value data...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'performance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Advanced Performance Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Coming soon: Real-time performance tracking, predictive analytics, and advanced insights.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 776,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"iXdvtH/Ekfjub2RYzHmwB92fHMs=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowPathIcon: () => (/* reexport safe */ _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   PresentationChartLineIcon: () => (/* reexport safe */ _PresentationChartLineIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowPathIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ClockIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _PresentationChartLineIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PresentationChartLineIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UsersIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFycm93UGF0aEljb24sQ2FsZW5kYXJJY29uLENoYXJ0QmFySWNvbixDbG9ja0ljb24sQ3VycmVuY3lEb2xsYXJJY29uLFByZXNlbnRhdGlvbkNoYXJ0TGluZUljb24sVHJlbmRpbmdVcEljb24sVXNlcnNJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNGO0FBQ0E7QUFDTjtBQUNrQjtBQUNjO0FBQ2hDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJ6XFxEb2N1bWVudHNcXEF1Z21lbnRcXHNvY2lhbC1uZnQtcGxhdGZvcm0tdjJcXGZyb250ZW5kLWhlYWRsZXNzXFxub2RlX21vZHVsZXNcXEBoZXJvaWNvbnNcXHJlYWN0XFwyNFxcb3V0bGluZVxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dQYXRoSWNvbiB9IGZyb20gXCIuL0Fycm93UGF0aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxlbmRhckljb24gfSBmcm9tIFwiLi9DYWxlbmRhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdXJyZW5jeURvbGxhckljb24gfSBmcm9tIFwiLi9DdXJyZW5jeURvbGxhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQcmVzZW50YXRpb25DaGFydExpbmVJY29uIH0gZnJvbSBcIi4vUHJlc2VudGF0aW9uQ2hhcnRMaW5lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJBcnJvd1BhdGhJY29uIiwiQ2FsZW5kYXJJY29uIiwiQ2hhcnRCYXJJY29uIiwiQ2xvY2tJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiUHJlc2VudGF0aW9uQ2hhcnRMaW5lSWNvbiIsIlVzZXJzSWNvbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ })

});