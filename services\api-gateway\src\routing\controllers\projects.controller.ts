import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, Headers, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('Projects & Campaigns')
@Controller()
export class ProjectsController {
  constructor(private readonly proxyService: ProxyService) {}

  // ===== PROJECT ENDPOINTS =====

  @Get('projects')
  @ApiOperation({ summary: 'Get all projects with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'List of projects with pagination' })
  async getProjects(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/projects',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('projects/search')
  @ApiOperation({ summary: 'Search projects with advanced filtering' })
  @ApiResponse({ status: 200, description: 'Search results' })
  async searchProjects(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/projects/search',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('projects/stats')
  @ApiOperation({ summary: 'Get project statistics and analytics' })
  @ApiResponse({ status: 200, description: 'Project statistics' })
  async getProjectStats(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/projects/stats',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Post('projects')
  @ApiOperation({ summary: 'Create a new project with complete configuration' })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data or configuration' })
  @ApiResponse({ status: 409, description: 'Project with this name already exists' })
  async createProject(@Body() createProjectDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/projects',
        'POST',
        createProjectDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('projects/owner/:ownerId')
  @ApiOperation({ summary: 'Get projects by owner' })
  @ApiResponse({ status: 200, description: 'Owner projects retrieved successfully' })
  async getProjectsByOwner(@Param('ownerId') ownerId: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/projects/owner/${ownerId}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('projects/:id')
  @ApiOperation({ summary: 'Get project by ID with complete details' })
  @ApiResponse({ status: 200, description: 'Project details' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async getProjectById(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/projects/${id}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Put('projects/:id')
  @ApiOperation({ summary: 'Update project configuration' })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateProject(@Param('id') id: string, @Body() updateProjectDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/projects/${id}`,
        'PUT',
        updateProjectDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Delete('projects/:id')
  @ApiOperation({ summary: 'Delete project (soft delete)' })
  @ApiResponse({ status: 200, description: 'Project deleted successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async deleteProject(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/projects/${id}`,
        'DELETE',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('projects/:id/campaigns')
  @ApiOperation({ summary: 'Get campaigns by project' })
  @ApiResponse({ status: 200, description: 'List of project campaigns' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async getProjectCampaigns(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/campaigns/project/${id}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  // ===== CAMPAIGN ENDPOINTS =====

  @Get('campaigns/search')
  @ApiOperation({ summary: 'Search campaigns with advanced filtering' })
  @ApiResponse({ status: 200, description: 'Campaign search results' })
  async searchCampaigns(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/campaigns/search',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('campaigns/stats')
  @ApiOperation({ summary: 'Get campaign statistics and analytics' })
  @ApiResponse({ status: 200, description: 'Campaign statistics' })
  async getCampaignStats(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/campaigns/stats',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('campaigns')
  @ApiOperation({ summary: 'Get campaigns with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'List of campaigns with pagination' })
  async getCampaigns(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/campaigns',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Post('campaigns')
  @ApiOperation({ summary: 'Create a new campaign with complete configuration' })
  @ApiResponse({ status: 201, description: 'Campaign created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data or configuration' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 409, description: 'Campaign with this name already exists' })
  async createCampaign(@Body() createCampaignDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        '/enterprise/campaigns',
        'POST',
        createCampaignDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Get('campaigns/:id')
  @ApiOperation({ summary: 'Get campaign by ID with complete details' })
  @ApiResponse({ status: 200, description: 'Campaign details' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  async getCampaignById(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/campaigns/${id}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Put('campaigns/:id')
  @ApiOperation({ summary: 'Update campaign configuration' })
  @ApiResponse({ status: 200, description: 'Campaign updated successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data or campaign cannot be updated' })
  async updateCampaign(@Param('id') id: string, @Body() updateCampaignDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/campaigns/${id}`,
        'PUT',
        updateCampaignDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Delete('campaigns/:id')
  @ApiOperation({ summary: 'Delete campaign (soft delete)' })
  @ApiResponse({ status: 200, description: 'Campaign deleted successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 400, description: 'Campaign cannot be deleted' })
  async deleteCampaign(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/campaigns/${id}`,
        'DELETE',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }

  @Post('campaigns/:id/approve')
  @ApiOperation({ summary: 'Approve campaign for launch' })
  @ApiResponse({ status: 200, description: 'Campaign approved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 409, description: 'Campaign already approved' })
  async approveCampaign(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'project-service',
        `/enterprise/campaigns/${id}/approve`,
        'POST',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'project-service'
      });
    }
  }
}
