# Social NFT Platform - Architecture Overview

## 🏗️ **Platform Architecture**

### **System Overview**
The Social NFT Platform follows a microservices architecture pattern with clear separation of concerns, enabling scalability, maintainability, and independent service deployment.

### **Architecture Principles**
- **Microservices Pattern:** Independent, loosely coupled services
- **Database-per-Service:** Each service owns its data
- **API Gateway Pattern:** Centralized routing and authentication
- **Event-Driven Communication:** Asynchronous service communication
- **Containerization Ready:** Docker-ready service architecture

## 🔧 **Service Architecture**

### **Core Services**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   User Service  │
│   (Next.js)     │◄──►│   (Port 3010)   │◄──►│   (Port 3001)   │
│   (Port 3000)   │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
        ┌─────────────────┬─────────────────┬─────────────────┐
        │                 │                 │                 │
        ▼                 ▼                 ▼                 ▼
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│ Profile     │  │ NFT Gen     │  │ Blockchain  │  │ Project     │
│ Analysis    │  │ Service     │  │ Service     │  │ Service     │
│ (Port 3002) │  │ (Port 3003) │  │ (Port 3005) │  │ (Port 3006) │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘

        ┌─────────────────┬─────────────────┬─────────────────┐
        │                 │                 │                 │
        ▼                 ▼                 ▼                 ▼
┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│ Marketplace │  │ Notification│  │ Analytics   │  │ Campaign    │
│ Service     │  │ Service     │  │ Service     │  │ Service     │
│ (Port 3007) │  │ (Port 3008) │  │ (Port 3009) │  │ (Port 3004) │
└─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘
```

### **Database Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ social_nft_users│    │profile_analysis │    │nft_generation_db│
│                 │    │_db              │    │                 │
│ - users         │    │ - profiles      │    │ - nfts          │
│ - auth_tokens   │    │ - analysis      │    │ - generations   │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│blockchain_service│    │project_service  │    │marketplace_     │
│_db              │    │_db              │    │service_db       │
│ - transactions  │    │ - projects      │    │ - listings      │
│ - contracts     │    │ - campaigns     │    │ - transactions  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔄 **Data Flow Architecture**

### **User Authentication Flow**
1. **Frontend** → API Gateway → User Service
2. **JWT Token** ← API Gateway ← User Service
3. **Authenticated Requests** → API Gateway (with JWT) → Services

### **NFT Generation Flow**
1. **User Request** → Frontend → API Gateway
2. **Profile Analysis** → Profile Analysis Service
3. **NFT Generation** → NFT Generation Service
4. **Blockchain Recording** → Blockchain Service
5. **Campaign Update** → Campaign Service

### **Campaign Participation Flow**
1. **Campaign Browse** → Project Service
2. **User Join** → Campaign Service
3. **Profile Analysis** → Profile Analysis Service
4. **NFT Generation** → NFT Generation Service
5. **Collection Update** → User Service

## 🛡️ **Security Architecture**

### **Authentication & Authorization**
- **JWT-based Authentication:** Stateless token-based auth
- **API Gateway Security:** Centralized authentication check
- **Service-to-Service:** Internal API keys
- **Database Security:** Connection pooling and encryption

### **Data Protection**
- **Input Validation:** All service endpoints
- **SQL Injection Prevention:** Parameterized queries
- **CORS Configuration:** Proper cross-origin setup
- **Rate Limiting:** API Gateway level protection
