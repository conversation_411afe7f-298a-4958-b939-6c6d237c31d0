#!/usr/bin/env node

/**
 * Test Script for Real Twitter API Integration
 * 
 * This script tests the enhanced Profile Analysis Service with real Twitter API v2 integration.
 * It demonstrates the difference between mock data and real Twitter data.
 */

const axios = require('axios');

const ANALYSIS_SERVICE_URL = 'http://localhost:3002';

async function testRealTwitterAPI() {
  console.log('🐦 Testing Real Twitter API Integration in Profile Analysis Service\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${ANALYSIS_SERVICE_URL}/api/analysis/health`);
    console.log('✅ Health Check:', {
      status: healthResponse.data.success,
      service: healthResponse.data.data?.service,
      uptime: Math.round(healthResponse.data.data?.uptime || 0)
    });
    console.log('');

    // Test 2: Analyze with Real Twitter API (if configured)
    console.log('2. Testing Real Twitter API Integration...');
    
    // Test with a well-known Twitter account
    const testProfiles = [
      { handle: 'elonmusk', description: 'High-profile verified account' },
      { handle: 'tim_cook', description: 'CEO verified account' },
      { handle: 'github', description: 'Corporate verified account' }
    ];

    for (const profile of testProfiles) {
      try {
        console.log(`\n📊 Analyzing @${profile.handle} (${profile.description})...`);
        
        const analysisRequest = {
          twitterHandle: profile.handle,
          userId: `test_user_${Date.now()}`
        };

        const startTime = Date.now();
        const analysisResponse = await axios.post(
          `${ANALYSIS_SERVICE_URL}/api/analysis/twitter-profile`,
          analysisRequest,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-correlation-id': `test-real-api-${Date.now()}`
            },
            timeout: 30000 // 30 second timeout for real API calls
          }
        );

        const responseTime = Date.now() - startTime;

        if (analysisResponse.data.success) {
          const analysis = analysisResponse.data.data;
          const profileData = analysis.analysisData?.profile;
          const nftRec = analysis.analysisData?.nftRecommendation;

          console.log('✅ Analysis Results:', {
            score: analysis.score,
            rarity: nftRec?.rarity,
            responseTime: `${responseTime}ms`
          });

          console.log('📈 Profile Metrics:', {
            followers: profileData?.followerCount?.toLocaleString() || 'N/A',
            following: profileData?.followingCount?.toLocaleString() || 'N/A',
            tweets: profileData?.tweetCount?.toLocaleString() || 'N/A',
            verified: profileData?.isVerified ? '✅ Verified' : '❌ Not Verified',
            engagement: `${profileData?.engagementRate || 0}%`
          });

          console.log('🎯 NFT Recommendation:', {
            rarity: nftRec?.rarity?.toUpperCase() || 'Unknown',
            reasoning: nftRec?.reasoning?.slice(0, 2) || ['No reasoning available']
          });

          // Check if this looks like real data vs mock data
          const isLikelyRealData = 
            profileData?.followerCount > 10000 || 
            profileData?.isVerified || 
            !analysis.id.includes('mock');

          console.log('🔍 Data Source:', isLikelyRealData ? 
            '🌐 Likely REAL Twitter API data' : 
            '🎭 Likely MOCK/fallback data'
          );

        } else {
          console.log('❌ Analysis failed:', analysisResponse.data.error);
        }

      } catch (error) {
        console.log(`❌ Failed to analyze @${profile.handle}:`, error.response?.data?.message || error.message);
      }
    }

    // Test 3: Configuration Check
    console.log('\n3. Configuration Information...');
    console.log('📋 To enable real Twitter API:');
    console.log('   1. Get Bearer Token from https://developer.twitter.com');
    console.log('   2. Set USE_REAL_TWITTER_API=true in .env');
    console.log('   3. Set TWITTER_BEARER_TOKEN=your_token in .env');
    console.log('   4. Restart the Profile Analysis Service');
    console.log('');
    console.log('📋 Current fallback order:');
    console.log('   1. Real Twitter API v2 (if USE_REAL_TWITTER_API=true)');
    console.log('   2. External Twitter Service (Mock Service on port 3020)');
    console.log('   3. Generated mock data (development fallback)');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the Profile Analysis Service is running:');
      console.log('   cd services/profile-analysis-service');
      console.log('   npm run start:dev');
    }
  }
}

// Run the test
if (require.main === module) {
  testRealTwitterAPI().then(() => {
    console.log('\n🎉 Real Twitter API integration test completed!');
    console.log('📚 See TWITTER_API_SETUP.md for configuration details.');
  }).catch(console.error);
}

module.exports = { testRealTwitterAPI };
