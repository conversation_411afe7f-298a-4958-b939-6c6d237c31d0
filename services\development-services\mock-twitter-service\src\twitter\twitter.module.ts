import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TwitterController } from './twitter.controller';
import { TwitterService } from './twitter.service';
import { AuthController } from './auth.controller';
import { UsersController } from './users.controller';
import { AnalyticsController } from './analytics.controller';

@Module({
  controllers: [
    TwitterController,
    AuthController,
    UsersController,
    AnalyticsController,
  ],
  providers: [TwitterService],
  exports: [TwitterService],
})
export class TwitterModule {}
