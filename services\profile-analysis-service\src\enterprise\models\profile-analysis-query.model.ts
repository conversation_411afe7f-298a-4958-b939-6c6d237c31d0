// Enterprise Profile Analysis Query Model (Read Side) - Template
import { ApiProperty } from '@nestjs/swagger';

export class ProfileAnalysisQueryDto {
  @ApiProperty({ description: 'Analysis ID' })
  id: string;

  @ApiProperty({ description: 'Display user ID' })
  displayUserId: string;

  @ApiProperty({ description: 'Display platform' })
  displayPlatform: string;

  @ApiProperty({ description: 'Display handle' })
  displayHandle: string;

  @ApiProperty({ description: 'Analysis status' })
  status: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;
}
