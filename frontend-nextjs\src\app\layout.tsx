import type { Metada<PERSON> } from "next";
import { Providers } from '../providers'
import "./globals.css";

export const metadata: Metadata = {
  title: "Social NFT Platform",
  description: "Connect your Twitter, join campaigns, and mint evolving NFTs",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
