import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);

    // Set global prefix
    app.setGlobalPrefix('api');

    // Enable CORS
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3010'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'x-user-id', 'x-admin-key'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    // Swagger documentation
    const config = new DocumentBuilder()
      .setTitle('Analytics Service API')
      .setDescription('API for platform analytics, reporting, metrics, and business intelligence')
      .setVersion('1.0')
      .addTag('analytics')
      .addTag('metrics')
      .addTag('reports')
      .addTag('dashboards')
      .addTag('events')
      .addBearerAuth()
      .addApiKey({ type: 'apiKey', name: 'x-admin-key', in: 'header' }, 'admin-key')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);

    const port = process.env.PORT || 3007;
    await app.listen(port, '0.0.0.0');

    logger.log(`📊 Analytics Service running on port ${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    logger.log(`🏥 Health Check: http://localhost:${port}/api/health`);
    logger.log(`✅ Analytics features: Platform metrics, User insights, Performance tracking`);

  } catch (error) {
    logger.error('❌ Failed to start Analytics Service:', error);
    process.exit(1);
  }
}

bootstrap();
