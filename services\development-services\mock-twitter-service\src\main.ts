import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for development
  app.enableCors({
    origin: ['http://localhost:3000', 'http://localhost:3010'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Mock Twitter Service')
    .setDescription('Mock Twitter API for Social NFT Platform Development')
    .setVersion('1.0')
    .addTag('twitter')
    .addTag('auth')
    .addTag('users')
    .addTag('analytics')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Start server on port 3020
  const port = process.env.PORT || 3020;
  await app.listen(port);
  
  console.log(`🐦 Mock Twitter Service is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🔄 Environment: ${process.env.NODE_ENV || 'development'}`);
}

bootstrap();
