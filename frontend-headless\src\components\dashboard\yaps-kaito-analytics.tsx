'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  UsersIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  ArrowPathIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { analyticsApi } from '@/lib/api';

// Types
interface PlatformOverview {
  overview: {
    total_users: number;
    total_nfts_generated: number;
    total_volume_eth: number;
    active_campaigns: number;
  };
  growth_metrics: {
    user_growth_rate: number;
    nft_generation_growth_rate: number;
    marketplace_volume_growth_rate: number;
    campaign_participation_rate: number;
  };
  top_performing: {
    campaigns: Array<{
      id: string;
      name: string;
      participants: number;
      nfts_generated: number;
    }>;
    nft_rarities: Array<{
      rarity: string;
      count: number;
      percentage: number;
    }>;
  };
}

interface TopGainersLosers {
  top_gainers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
  }>;
  top_losers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
  }>;
  market_summary: {
    total_volume_24h: number;
    volume_change_24h: string;
    average_score_change: string;
    most_active_rarity: string;
    trending_themes: string[];
  };
}

interface CollectionMarketValue {
  market_overview: {
    total_market_value: number;
    market_cap_change_24h: string;
    total_collections: number;
    total_nfts: number;
    average_nft_value: number;
  };
  bubble_map_data: Array<{
    id: string;
    name: string;
    value: number;
    size: number;
    change_24h: string;
    color: string;
    rarity: string;
  }>;
}

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatEth = (eth: number): string => {
  return `${eth.toFixed(3)} ETH`;
};

const getRarityColor = (rarity: string): string => {
  switch (rarity.toLowerCase()) {
    case 'legendary': return 'bg-yellow-500';
    case 'epic': return 'bg-purple-500';
    case 'rare': return 'bg-blue-500';
    case 'common': return 'bg-gray-500';
    default: return 'bg-gray-400';
  }
};

export default function YapsKaitoAnalytics() {
  const [platformData, setPlatformData] = useState<PlatformOverview | null>(null);
  const [gainersLosers, setGainersLosers] = useState<TopGainersLosers | null>(null);
  const [marketValue, setMarketValue] = useState<CollectionMarketValue | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch platform overview
      const platformResult = await analyticsApi.getPlatformOverview();
      if (platformResult.success) {
        setPlatformData(platformResult.data);
      }

      // Fetch top gainers/losers
      const gainersResult = await analyticsApi.getTopGainersLosers(selectedPeriod, 10);
      if (gainersResult.success) {
        setGainersLosers(gainersResult.data);
      }

      // Fetch collection market value
      const marketResult = await analyticsApi.getCollectionMarketValue();
      if (marketResult.success) {
        setMarketValue(marketResult.data);
      }

      // Fetch recent transactions
      const transactionsResult = await analyticsApi.getRecentTransactions(15);
      if (transactionsResult.success) {
        setRecentTransactions(transactionsResult.data.transactions);
      }

    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <ArrowPathIcon className="h-6 w-6 animate-spin" />
          <span>Loading analytics data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button 
            onClick={fetchAnalyticsData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yaps Kaito Analytics Dashboard</h1>
          <p className="text-gray-600">
            Comprehensive platform insights with bubble maps and performance tracking
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={fetchAnalyticsData}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Platform Overview Cards */}
      {platformData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(platformData.overview.total_users)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.user_growth_rate}% from last month
                </p>
              </div>
              <UsersIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">NFTs Generated</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(platformData.overview.total_nfts_generated)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.nft_generation_growth_rate}% growth rate
                </p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Volume</p>
                <p className="text-2xl font-bold text-gray-900">{formatEth(platformData.overview.total_volume_eth)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.marketplace_volume_growth_rate}% volume growth
                </p>
              </div>
              <CurrencyDollarIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Campaigns</p>
                <p className="text-2xl font-bold text-gray-900">{platformData.overview.active_campaigns}</p>
                <p className="text-xs text-gray-500">
                  {platformData.growth_metrics.campaign_participation_rate}% participation rate
                </p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>
        </div>
      )}

      {/* Section 1: Top Gainers/Losers */}
      {gainersLosers && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Top Gainers & Losers</h2>
              <p className="text-sm text-gray-500">Users' final score status and performance tracking</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Period:</span>
              {['7d', '30d'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-3 py-1 text-sm font-medium rounded-md ${
                    selectedPeriod === period
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Side: Gainers List */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <TrendingUpIcon className="h-5 w-5 text-green-500 mr-2" />
                Top Gainers ({gainersLosers.top_gainers.length})
              </h3>
              <div className="space-y-3">
                {gainersLosers.top_gainers.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <UsersIcon className="h-6 w-6 text-gray-500" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{item.name.match(/@(\w+)/)?.[0] || '@user'}</span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRarityColor(item.rarity)} text-white`}>
                            {item.rarity}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          Score: {item.current_score} | Volume: {item.volume_24h} ETH
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1 text-green-600">
                        <TrendingUpIcon className="h-4 w-4" />
                        <span className="font-bold">{item.change_percentage}</span>
                      </div>
                      <div className="text-sm text-gray-500">{selectedPeriod} change</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Side: Bubble Map */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Bubble Map</h3>
              <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
                <div className="relative w-full h-full">
                  {gainersLosers.top_gainers.map((item, index) => {
                    const size = 40 + (item.current_score / 100) * 80; // 40-120px
                    const x = (index % 3) * 30 + 10;
                    const y = Math.floor(index / 3) * 25 + 10;

                    return (
                      <div
                        key={item.id}
                        className="absolute cursor-pointer transition-all duration-300 hover:scale-110"
                        style={{
                          left: `${x}%`,
                          top: `${y}%`,
                          width: `${size}px`,
                          height: `${size}px`,
                        }}
                      >
                        <div
                          className="w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500"
                          title={`${item.name.match(/@(\w+)/)?.[0] || '@user'} - Score: ${item.current_score} (${item.change_percentage})`}
                        >
                          <div className="text-center">
                            <div className="text-xs">{(item.name.match(/@(\w+)/)?.[0] || '@user').slice(0, 6)}</div>
                            <div className="text-xs font-bold">{item.current_score}</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className="absolute bottom-2 right-2 bg-white p-2 rounded shadow">
                  <div className="text-xs text-gray-600">
                    <div className="flex items-center space-x-1 mb-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span>Gainers</span>
                    </div>
                    <div className="text-xs mt-1">Size = Score</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Section 2: Collection Market Value */}
      {marketValue && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
              <p className="text-sm text-gray-500">Project collections growth and decline tracking</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">{marketValue.market_overview.total_market_value.toFixed(2)} ETH</div>
              <div className={`text-sm font-medium ${
                marketValue.market_overview.market_cap_change_24h.startsWith('+') ? 'text-green-600' : 'text-red-600'
              }`}>
                {marketValue.market_overview.market_cap_change_24h} (24h)
              </div>
            </div>
          </div>

          <div className="relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 overflow-hidden">
            <div className="relative w-full h-full">
              {marketValue.bubble_map_data.map((collection, index) => {
                const maxValue = Math.max(...marketValue.bubble_map_data.map(c => c.value));
                const size = 60 + (collection.value / maxValue) * 90; // 60-150px
                const x = (index % 2) * 40 + 10;
                const y = Math.floor(index / 2) * 35 + 10;

                return (
                  <div
                    key={collection.id}
                    className="absolute cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
                    style={{
                      left: `${x}%`,
                      top: `${y}%`,
                      width: `${size}px`,
                      height: `${size}px`,
                    }}
                    onClick={() => console.log('Navigate to collection:', collection.id)}
                  >
                    <div
                      className="w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg border-2 border-white"
                      style={{ backgroundColor: collection.color }}
                    >
                      <div className="text-center p-2">
                        <div className="text-xs mb-1">{collection.name}</div>
                        <div className="text-lg font-bold">{collection.value} ETH</div>
                        <div className="text-xs">{collection.size} NFTs</div>
                        <div className={`text-xs font-bold mt-1 ${
                          collection.change_24h.startsWith('+') ? 'text-green-200' : 'text-red-200'
                        }`}>
                          {collection.change_24h}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="absolute bottom-4 right-4 bg-white p-3 rounded shadow">
              <div className="text-xs text-gray-600">
                <div className="font-medium mb-2">Collection Value</div>
                <div className="space-y-1">
                  {marketValue.bubble_map_data.map((collection) => (
                    <div key={collection.id} className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded"
                        style={{ backgroundColor: collection.color }}
                      ></div>
                      <span>{collection.rarity}</span>
                    </div>
                  ))}
                </div>
                <div className="text-xs mt-2">Click to view collection</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Section 3: Recent Transactions */}
      {recentTransactions.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
              <p className="text-sm text-gray-500">Latest NFT activities, purchases and interactions</p>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <ClockIcon className="h-4 w-4" />
              <span>Real-time updates</span>
            </div>
          </div>

          <div className="space-y-3">
            {recentTransactions.slice(0, 10).map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    transaction.type === 'generation' ? 'bg-blue-100 text-blue-800' :
                    transaction.type === 'share' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    <span className="text-lg">
                      {transaction.type === 'generation' ? '🎨' :
                       transaction.type === 'share' ? '📤' : '❤️'}
                    </span>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{transaction.nft_name}</span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRarityColor(transaction.rarity)} text-white`}>
                        {transaction.rarity}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>User: @{transaction.username}</span>
                      <span>•</span>
                      <span className="capitalize">{transaction.type}</span>
                      <span>•</span>
                      <span>{Math.floor((new Date().getTime() - new Date(transaction.timestamp).getTime()) / (1000 * 60))}m ago</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                    <span className="font-bold text-gray-900">{transaction.value_eth.toFixed(3)} ETH</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    ${(transaction.value_eth * 2000).toFixed(0)} USD
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
