'use client';

import React, { useState, useEffect } from 'react';
import { ChartBarIcon, TrendingUpIcon, UsersIcon, CurrencyDollarIcon, ClockIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

export default function BasicAnalytics() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Platform insights and performance tracking</p>
        </div>
      </div>

      {/* Section 1: Top Gainers & Losers */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <TrendingUpIcon className="h-6 w-6 text-green-500" />
          <h2 className="text-2xl font-bold text-gray-900">Top Gainers & Losers</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Side: List */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Gainers (3)</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <UsersIcon className="h-6 w-6 text-gray-500" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">@alice_crypto</div>
                    <div className="text-sm text-gray-500">Score: 85</div>
                  </div>
                </div>
                <div className="text-green-600 font-bold">+12%</div>
              </div>
            </div>
          </div>

          {/* Right Side: Bubble Map */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Bubble Map</h3>
            <div className="relative h-64 bg-gray-50 rounded-lg p-4">
              <div className="absolute w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold" style={{left: '20%', top: '20%'}}>
                <div className="text-center">
                  <div>@alice</div>
                  <div>85</div>
                </div>
              </div>
              <div className="absolute w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold" style={{left: '60%', top: '40%'}}>
                <div className="text-center">
                  <div>@bob</div>
                  <div>78</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Collection Market Value */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <ChartBarIcon className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
        </div>

        <div className="relative h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6">
          <div className="absolute w-20 h-16 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs font-bold" style={{left: '15%', top: '20%'}}>
            <div className="text-center">
              <div>CryptoArt</div>
              <div>45.2 ETH</div>
            </div>
          </div>
          <div className="absolute w-16 h-14 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs font-bold" style={{left: '55%', top: '30%'}}>
            <div className="text-center">
              <div>Punks</div>
              <div>38.7 ETH</div>
            </div>
          </div>
          <div className="absolute w-24 h-16 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs font-bold" style={{left: '30%', top: '60%'}}>
            <div className="text-center">
              <div>MetaVerse</div>
              <div>52.1 ETH</div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 3: Recent Transactions */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <ClockIcon className="h-6 w-6 text-gray-500" />
          <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-xs font-bold text-blue-800">GEN</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">Digital Art #123</div>
                <div className="text-sm text-gray-500">User: @alice_crypto • generation • 5m ago</div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                <span className="font-bold text-gray-900">0.5 ETH</span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-xs font-bold text-green-800">SHR</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">Punk Avatar #456</div>
                <div className="text-sm text-gray-500">User: @bob_nft • share • 12m ago</div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                <span className="font-bold text-gray-900">0.3 ETH</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
