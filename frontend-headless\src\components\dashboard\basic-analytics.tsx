'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { ArrowTrendingUpIcon } from '@heroicons/react/24/outline';

export default function BasicAnalytics() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Platform insights and performance tracking</p>
        </div>
      </div>

      {/* Section 1: Users Score Board */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <ArrowTrendingUpIcon className="h-6 w-6 text-green-500" />
            <h2 className="text-2xl font-bold text-gray-900">Users Score Board</h2>
          </div>
          <button className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700">
            See Top 100 ↗
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Side: Top Gainer & Top Loser Lists */}
          <div className="space-y-6">
            {/* Top Gainer */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Gainer</span>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Δ Absolute (5m)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Relative (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Name</div>
                  <div className="col-span-2">Current</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Δ30D</div>
                  <div className="col-span-2">Δ3M</div>
                </div>

                {[
                  { name: 'Robert Le...', handle: '@robert', current: '0.48%', d7: '+43pps', d30: '+30pps', d3m: '+36pps', trend: 'up' },
                  { name: 'Laura S...', handle: '@laura', current: '0.58%', d7: '+35pps', d30: '+30pps', d3m: '+32pps', trend: 'up' },
                  { name: 'Yann...', handle: '@yann', current: '0.50%', d7: '+22pps', d30: '+25pps', d3m: '+26pps', trend: 'up' },
                  { name: 'Yu Hu...', handle: '@yuhu', current: '0.61%', d7: '+43pps', d30: '+25pps', d3m: '+36pps', trend: 'up' },
                  { name: 'alext...', handle: '@alext', current: '0.51%', d7: '+30pps', d30: '+20pps', d3m: '+28pps', trend: 'up' }
                ].map((user, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{user.name.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900">{user.current}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d7}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d30}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d3m}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Loser */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Loser</span>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Δ Absolute (5m)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Relative (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Name</div>
                  <div className="col-span-2">Current</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Δ30D</div>
                  <div className="col-span-2">Δ3M</div>
                </div>

                {[
                  { name: 'Tomass K...', handle: '@tomass', current: '0.06%', d7: '-7pps', d30: '-37pps', d3m: '-44pps', trend: 'down' },
                  { name: 'BREAD...', handle: '@bread', current: '0.27%', d7: '-57pps', d30: '-25pps', d3m: '-50pps', trend: 'down' },
                  { name: '_gabrielS...', handle: '@gabriel', current: '0.39%', d7: '-6pps', d30: '-30pps', d3m: '-44pps', trend: 'down' },
                  { name: 'alyson...', handle: '@alyson', current: '0.30%', d7: '-6pps', d30: '-24pps', d3m: '-37pps', trend: 'down' }
                ].map((user, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{user.name.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900">{user.current}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d7}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d30}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d3m}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side: Top20 Treemap */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Top20</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>7D</span>
                <span className="text-green-600 font-medium">30D</span>
                <span>3M</span>
                <span>6M</span>
                <span>12M</span>
              </div>
            </div>

            <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
              {/* Large blocks */}
              <div className="absolute bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg border border-white"
                   style={{left: '2%', top: '5%', width: '22%', height: '35%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🧊 IcoBe...</span>
                    <span className="text-yellow-200 text-xs">👑</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">0.73%</div>
                    <div className="w-full h-8 bg-yellow-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-yellow-300 rounded" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-red-600 to-red-700 rounded-lg border border-white"
                   style={{left: '26%', top: '5%', width: '20%', height: '32%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🍞 BREAD | Σ</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">0.56%</div>
                    <div className="w-full h-8 bg-red-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-red-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white"
                   style={{left: '48%', top: '5%', width: '18%', height: '30%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🟢 mert | heliu...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.55%</div>
                    <div className="w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-green-300 rounded" style={{width: '55%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg border border-white"
                   style={{left: '68%', top: '5%', width: '16%', height: '28%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🌙 Yueya.eth...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.51%</div>
                    <div className="w-full h-6 bg-blue-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-blue-300 rounded" style={{width: '50%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Medium blocks */}
              <div className="absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white"
                   style={{left: '2%', top: '42%', width: '16%', height: '25%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🐋 wale.m...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.45%</div>
                    <div className="w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-teal-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white"
                   style={{left: '20%', top: '42%', width: '15%', height: '23%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">👤 david p...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.40%</div>
                    <div className="w-full h-4 bg-purple-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-purple-300 rounded" style={{width: '40%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Smaller blocks */}
              <div className="absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white"
                   style={{left: '37%', top: '42%', width: '13%', height: '20%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🎯 nairolf</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.38%</div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white"
                   style={{left: '52%', top: '42%', width: '12%', height: '18%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">⚡ Tomas...</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.38%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Collection Market Value */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <ChartBarIcon className="h-6 w-6 text-blue-500" />
            <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700">
            See All Collections ↗
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Side: Top Gainer & Top Loser Collections */}
          <div className="space-y-6">
            {/* Top Gainer Collections */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Gainer Collections</span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Δ Market Cap (24h)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Volume (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Collection</div>
                  <div className="col-span-2">Floor</div>
                  <div className="col-span-2">Δ24h</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Volume</div>
                </div>

                {[
                  { name: 'CryptoArt Pro', symbol: 'CAP', floor: '2.5 ETH', d24h: '+15.2%', d7d: '+45.8%', volume: '125 ETH', trend: 'up' },
                  { name: 'Digital Punks', symbol: 'DP', floor: '1.8 ETH', d24h: '+12.7%', d7d: '+38.4%', volume: '98 ETH', trend: 'up' },
                  { name: 'Meta Worlds', symbol: 'MW', floor: '3.2 ETH', d24h: '+9.8%', d7d: '+28.9%', volume: '156 ETH', trend: 'up' },
                  { name: 'Pixel Heroes', symbol: 'PH', floor: '0.9 ETH', d24h: '+8.5%', d7d: '+22.1%', volume: '67 ETH', trend: 'up' },
                  { name: 'Cyber Cats', symbol: 'CC', floor: '1.4 ETH', d24h: '+7.2%', d7d: '+19.6%', volume: '89 ETH', trend: 'up' }
                ].map((collection, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{collection.symbol.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{collection.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900 font-medium">{collection.floor}</div>
                    <div className="col-span-2 text-green-600 font-medium">{collection.d24h}</div>
                    <div className="col-span-2 text-green-600 font-medium">{collection.d7d}</div>
                    <div className="col-span-2 text-gray-700 font-medium">{collection.volume}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Loser Collections */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Loser Collections</span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Δ Market Cap (24h)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Volume (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Collection</div>
                  <div className="col-span-2">Floor</div>
                  <div className="col-span-2">Δ24h</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Volume</div>
                </div>

                {[
                  { name: 'Old School NFT', symbol: 'OSN', floor: '0.3 ETH', d24h: '-18.5%', d7d: '-42.3%', volume: '12 ETH', trend: 'down' },
                  { name: 'Retro Pixels', symbol: 'RP', floor: '0.5 ETH', d24h: '-15.2%', d7d: '-35.7%', volume: '23 ETH', trend: 'down' },
                  { name: 'Classic Art', symbol: 'CA', floor: '0.8 ETH', d24h: '-12.8%', d7d: '-28.4%', volume: '34 ETH', trend: 'down' },
                  { name: 'Vintage Cards', symbol: 'VC', floor: '0.2 ETH', d24h: '-10.9%', d7d: '-25.1%', volume: '18 ETH', trend: 'down' }
                ].map((collection, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-red-400 to-orange-500 rounded flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{collection.symbol.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{collection.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900 font-medium">{collection.floor}</div>
                    <div className="col-span-2 text-red-600 font-medium">{collection.d24h}</div>
                    <div className="col-span-2 text-red-600 font-medium">{collection.d7d}</div>
                    <div className="col-span-2 text-gray-700 font-medium">{collection.volume}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side: Top20 Collections Treemap */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Top20 Collections</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="text-blue-600 font-medium">24h</span>
                <span>7D</span>
                <span>30D</span>
                <span>3M</span>
                <span>All</span>
              </div>
            </div>

            <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
              {/* Large collection blocks */}
              <div className="absolute bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg border border-white"
                   style={{left: '2%', top: '5%', width: '22%', height: '35%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🎨 CryptoArt Pro</span>
                    <span className="text-blue-200 text-xs">👑</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">2.5 ETH</div>
                    <div className="text-blue-200 text-xs">+15.2%</div>
                    <div className="w-full h-8 bg-blue-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-blue-300 rounded" style={{width: '75%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white"
                   style={{left: '26%', top: '5%', width: '20%', height: '32%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-sm">👾 Digital Punks</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">1.8 ETH</div>
                    <div className="text-purple-200 text-xs">+12.7%</div>
                    <div className="w-full h-8 bg-purple-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-purple-300 rounded" style={{width: '65%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white"
                   style={{left: '48%', top: '5%', width: '18%', height: '30%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🌍 Meta Worlds</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">3.2 ETH</div>
                    <div className="text-green-200 text-xs">+9.8%</div>
                    <div className="w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-green-300 rounded" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-yellow-600 to-yellow-700 rounded-lg border border-white"
                   style={{left: '68%', top: '5%', width: '16%', height: '28%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🦸 Pixel Heroes</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.9 ETH</div>
                    <div className="text-yellow-200 text-xs">+8.5%</div>
                    <div className="w-full h-6 bg-yellow-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-yellow-300 rounded" style={{width: '55%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Medium collection blocks */}
              <div className="absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white"
                   style={{left: '2%', top: '42%', width: '16%', height: '25%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🐱 Cyber Cats</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">1.4 ETH</div>
                    <div className="text-teal-200 text-xs">+7.2%</div>
                    <div className="w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-teal-300 rounded" style={{width: '50%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white"
                   style={{left: '20%', top: '42%', width: '15%', height: '23%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🚀 Space NFT</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">2.1 ETH</div>
                    <div className="text-indigo-200 text-xs">+6.8%</div>
                    <div className="w-full h-4 bg-indigo-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-indigo-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Smaller collection blocks */}
              <div className="absolute bg-gradient-to-br from-pink-600 to-pink-700 rounded-lg border border-white"
                   style={{left: '37%', top: '42%', width: '13%', height: '20%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">💎 Gems</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">1.7 ETH</div>
                    <div className="text-pink-200 text-xs">+5.4%</div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white"
                   style={{left: '52%', top: '42%', width: '12%', height: '18%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🎭 Art Club</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.8 ETH</div>
                    <div className="text-orange-200 text-xs">+4.9%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 3: Top 100 Platform Users */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <UsersIcon className="h-6 w-6 text-purple-500" />
            <h2 className="text-2xl font-bold text-gray-900">Top 100 Platform Users</h2>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">7d</button>
              <button className="px-3 py-1 bg-purple-600 text-white rounded">30d</button>
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">90d</button>
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">1y</button>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="grid grid-cols-12 gap-4 mb-4 text-xs text-gray-500 font-medium border-b border-gray-200 pb-2">
          <div className="col-span-1">Rank</div>
          <div className="col-span-3">User</div>
          <div className="col-span-2">Score</div>
          <div className="col-span-2">NFTs</div>
          <div className="col-span-2">Volume</div>
          <div className="col-span-2">Growth</div>
        </div>

        {/* User rows */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {[
            { rank: 1, name: '@alice_crypto', avatar: 'AC', score: 2547, nfts: 89, volume: 45.2, growth: 12.5, scoreBar: 95, nftBar: 78, volumeBar: 85, growthBar: 92 },
            { rank: 2, name: '@bob_nft_master', avatar: 'BN', score: 2341, nfts: 76, volume: 38.7, growth: 8.2, scoreBar: 88, nftBar: 65, volumeBar: 72, growthBar: 78 },
            { rank: 3, name: '@charlie_artist', avatar: 'CA', score: 2156, nfts: 92, volume: 52.1, growth: 5.7, scoreBar: 82, nftBar: 85, volumeBar: 95, growthBar: 65 },
            { rank: 4, name: '@diana_collector', avatar: 'DC', score: 1987, nfts: 45, volume: 28.9, growth: -2.1, scoreBar: 75, nftBar: 42, volumeBar: 55, growthBar: 25 },
            { rank: 5, name: '@eve_crypto_queen', avatar: 'EC', score: 1834, nfts: 67, volume: 34.5, growth: 3.4, scoreBar: 70, nftBar: 58, volumeBar: 65, growthBar: 58 },
            { rank: 6, name: '@frank_nft_lord', avatar: 'FN', score: 1723, nfts: 54, volume: 29.8, growth: 1.2, scoreBar: 65, nftBar: 48, volumeBar: 58, growthBar: 52 },
            { rank: 7, name: '@grace_digital', avatar: 'GD', score: 1645, nfts: 71, volume: 31.2, growth: 4.8, scoreBar: 62, nftBar: 62, volumeBar: 60, growthBar: 62 },
            { rank: 8, name: '@henry_meta', avatar: 'HM', score: 1567, nfts: 38, volume: 22.4, growth: -1.5, scoreBar: 58, nftBar: 35, volumeBar: 42, growthBar: 35 }
          ].map((user) => (
            <div key={user.rank} className="grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100">
              {/* Rank */}
              <div className="col-span-1 text-gray-700 text-sm font-medium">
                {user.rank}
              </div>

              {/* User */}
              <div className="col-span-3 flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{user.avatar}</span>
                </div>
                <div>
                  <div className="text-gray-900 text-sm font-medium">{user.name}</div>
                  <div className="text-gray-500 text-xs">Verified Creator</div>
                </div>
              </div>

              {/* Score with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.score.toLocaleString()}</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                       style={{width: `${user.scoreBar}%`}}></div>
                </div>
              </div>

              {/* NFTs with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.nfts}</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full"
                       style={{width: `${user.nftBar}%`}}></div>
                </div>
              </div>

              {/* Volume with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.volume} ETH</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full"
                       style={{width: `${user.volumeBar}%`}}></div>
                </div>
              </div>

              {/* Growth with bar */}
              <div className="col-span-2">
                <div className={`text-sm font-bold mb-1 ${user.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {user.growth >= 0 ? '+' : ''}{user.growth}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`h-2 rounded-full ${user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`}
                       style={{width: `${user.growthBar}%`}}></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Section 4: Recent Transactions */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <ClockIcon className="h-6 w-6 text-gray-500" />
          <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-xs font-bold text-blue-800">GEN</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">Digital Art #123</div>
                <div className="text-sm text-gray-500">User: @alice_crypto • generation • 5m ago</div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                <span className="font-bold text-gray-900">0.5 ETH</span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-xs font-bold text-green-800">SHR</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">Punk Avatar #456</div>
                <div className="text-sm text-gray-500">User: @bob_nft • share • 12m ago</div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                <span className="font-bold text-gray-900">0.3 ETH</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
