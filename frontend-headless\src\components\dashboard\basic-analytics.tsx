'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import { analyticsApi } from '@/lib/api';

// Types for our analytics data
interface UserGainerLoser {
  rank: number;
  name: string;
  handle: string;
  current: string;
  d7: string;
  d30: string;
  d3m: string;
  trend: 'up' | 'down';
}

interface CollectionData {
  name: string;
  symbol: string;
  floor: string;
  d24h: string;
  d7d: string;
  volume: string;
  trend: 'up' | 'down';
}

interface TopUser {
  rank: number;
  name: string;
  avatar: string;
  score: number;
  nfts: number;
  volume: number;
  growth: number;
  scoreBar: number;
  nftBar: number;
  volumeBar: number;
  growthBar: number;
}

interface Transaction {
  id: string;
  type: string;
  nftName: string;
  user: string;
  amount: string;
  timestamp: string;
}

export default function BasicAnalytics() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for different data sections
  const [topGainers, setTopGainers] = useState<UserGainerLoser[]>([]);
  const [topLosers, setTopLosers] = useState<UserGainerLoser[]>([]);
  const [collectionGainers, setCollectionGainers] = useState<CollectionData[]>([]);
  const [collectionLosers, setCollectionLosers] = useState<CollectionData[]>([]);
  const [topUsers, setTopUsers] = useState<TopUser[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);

  // Fetch data from backend
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all analytics data in parallel
        const [
          gainersLosersResponse,
          collectionMarketResponse,
          platformOverviewResponse,
          recentTransactionsResponse
        ] = await Promise.all([
          analyticsApi.getTopGainersLosers('7d', 10),
          analyticsApi.getCollectionMarketValue(),
          analyticsApi.getPlatformOverview(),
          analyticsApi.getRecentTransactions(10)
        ]);

        // Process gainers/losers data
        if (gainersLosersResponse.success && gainersLosersResponse.data) {
          const { gainers, losers } = gainersLosersResponse.data;
          setTopGainers(gainers || []);
          setTopLosers(losers || []);
        }

        // Process collection market data
        if (collectionMarketResponse.success && collectionMarketResponse.data) {
          const { gainers, losers } = collectionMarketResponse.data;
          setCollectionGainers(gainers || []);
          setCollectionLosers(losers || []);
        }

        // Process platform overview for top users
        if (platformOverviewResponse.success && platformOverviewResponse.data) {
          const { topUsers: users } = platformOverviewResponse.data;
          setTopUsers(users || []);
        }

        // Process recent transactions
        if (recentTransactionsResponse.success && recentTransactionsResponse.data) {
          setRecentTransactions(recentTransactionsResponse.data.transactions || []);
        }

      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="text-red-600">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Analytics</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Platform insights and performance tracking</p>
        </div>
      </div>

      {/* Section 1: Users Score Board */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <ArrowTrendingUpIcon className="h-6 w-6 text-green-500" />
            <h2 className="text-2xl font-bold text-gray-900">Users Score Board</h2>
          </div>
          <button className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700">
            See Top 100 ↗
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Side: Top Gainer & Top Loser Lists */}
          <div className="space-y-6">
            {/* Top Gainer */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Gainer</span>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Δ Absolute (5m)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Relative (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Name</div>
                  <div className="col-span-2">Current</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Δ30D</div>
                  <div className="col-span-2">Δ3M</div>
                </div>

                {topGainers.map((user, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{user.name.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900">{user.current}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d7}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d30}</div>
                    <div className="col-span-2 text-green-600 font-medium">{user.d3m}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Loser */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Loser</span>
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Δ Absolute (5m)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Relative (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Name</div>
                  <div className="col-span-2">Current</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Δ30D</div>
                  <div className="col-span-2">Δ3M</div>
                </div>

                {topLosers.map((user, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{user.name.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900">{user.current}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d7}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d30}</div>
                    <div className="col-span-2 text-red-600 font-medium">{user.d3m}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side: Top20 Treemap */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Top20</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>7D</span>
                <span className="text-green-600 font-medium">30D</span>
                <span>3M</span>
                <span>6M</span>
                <span>12M</span>
              </div>
            </div>

            <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
              {/* Large blocks */}
              <div className="absolute bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg border border-white"
                   style={{left: '2%', top: '5%', width: '22%', height: '35%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🧊 IcoBe...</span>
                    <span className="text-yellow-200 text-xs">👑</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">0.73%</div>
                    <div className="w-full h-8 bg-yellow-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-yellow-300 rounded" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-red-600 to-red-700 rounded-lg border border-white"
                   style={{left: '26%', top: '5%', width: '20%', height: '32%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🍞 BREAD | Σ</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">0.56%</div>
                    <div className="w-full h-8 bg-red-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-red-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white"
                   style={{left: '48%', top: '5%', width: '18%', height: '30%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🟢 mert | heliu...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.55%</div>
                    <div className="w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-green-300 rounded" style={{width: '55%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg border border-white"
                   style={{left: '68%', top: '5%', width: '16%', height: '28%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🌙 Yueya.eth...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.51%</div>
                    <div className="w-full h-6 bg-blue-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-blue-300 rounded" style={{width: '50%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Medium blocks */}
              <div className="absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white"
                   style={{left: '2%', top: '42%', width: '16%', height: '25%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🐋 wale.m...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.45%</div>
                    <div className="w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-teal-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white"
                   style={{left: '20%', top: '42%', width: '15%', height: '23%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">👤 david p...</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.40%</div>
                    <div className="w-full h-4 bg-purple-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-purple-300 rounded" style={{width: '40%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Smaller blocks */}
              <div className="absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white"
                   style={{left: '37%', top: '42%', width: '13%', height: '20%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🎯 nairolf</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.38%</div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white"
                   style={{left: '52%', top: '42%', width: '12%', height: '18%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">⚡ Tomas...</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.38%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Collection Market Value */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <ChartBarIcon className="h-6 w-6 text-blue-500" />
            <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700">
            See All Collections ↗
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Side: Top Gainer & Top Loser Collections */}
          <div className="space-y-6">
            {/* Top Gainer Collections */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Gainer Collections</span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Δ Market Cap (24h)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Volume (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Collection</div>
                  <div className="col-span-2">Floor</div>
                  <div className="col-span-2">Δ24h</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Volume</div>
                </div>

                {collectionGainers.map((collection, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{collection.symbol.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{collection.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900 font-medium">{collection.floor}</div>
                    <div className="col-span-2 text-green-600 font-medium">{collection.d24h}</div>
                    <div className="col-span-2 text-green-600 font-medium">{collection.d7d}</div>
                    <div className="col-span-2 text-gray-700 font-medium">{collection.volume}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Loser Collections */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm font-medium text-gray-700">Top Loser Collections</span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Δ Market Cap (24h)</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Δ Volume (%)</span>
              </div>

              <div className="space-y-2">
                <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium">
                  <div className="col-span-4">Collection</div>
                  <div className="col-span-2">Floor</div>
                  <div className="col-span-2">Δ24h</div>
                  <div className="col-span-2">Δ7D</div>
                  <div className="col-span-2">Volume</div>
                </div>

                {collectionLosers.map((collection, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm">
                    <div className="col-span-4 flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-red-400 to-orange-500 rounded flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{collection.symbol.slice(0, 2)}</span>
                      </div>
                      <span className="font-medium">{collection.name}</span>
                    </div>
                    <div className="col-span-2 text-gray-900 font-medium">{collection.floor}</div>
                    <div className="col-span-2 text-red-600 font-medium">{collection.d24h}</div>
                    <div className="col-span-2 text-red-600 font-medium">{collection.d7d}</div>
                    <div className="col-span-2 text-gray-700 font-medium">{collection.volume}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side: Top20 Collections Treemap */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Top20 Collections</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="text-blue-600 font-medium">24h</span>
                <span>7D</span>
                <span>30D</span>
                <span>3M</span>
                <span>All</span>
              </div>
            </div>

            <div className="relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden">
              {/* Large collection blocks */}
              <div className="absolute bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg border border-white"
                   style={{left: '2%', top: '5%', width: '22%', height: '35%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-white font-bold text-sm">🎨 CryptoArt Pro</span>
                    <span className="text-blue-200 text-xs">👑</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">2.5 ETH</div>
                    <div className="text-blue-200 text-xs">+15.2%</div>
                    <div className="w-full h-8 bg-blue-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-blue-300 rounded" style={{width: '75%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white"
                   style={{left: '26%', top: '5%', width: '20%', height: '32%'}}>
                <div className="p-3 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-sm">👾 Digital Punks</span>
                  </div>
                  <div>
                    <div className="text-white text-lg font-bold">1.8 ETH</div>
                    <div className="text-purple-200 text-xs">+12.7%</div>
                    <div className="w-full h-8 bg-purple-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-purple-300 rounded" style={{width: '65%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white"
                   style={{left: '48%', top: '5%', width: '18%', height: '30%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🌍 Meta Worlds</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">3.2 ETH</div>
                    <div className="text-green-200 text-xs">+9.8%</div>
                    <div className="w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-green-300 rounded" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-yellow-600 to-yellow-700 rounded-lg border border-white"
                   style={{left: '68%', top: '5%', width: '16%', height: '28%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🦸 Pixel Heroes</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">0.9 ETH</div>
                    <div className="text-yellow-200 text-xs">+8.5%</div>
                    <div className="w-full h-6 bg-yellow-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-yellow-300 rounded" style={{width: '55%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Medium collection blocks */}
              <div className="absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white"
                   style={{left: '2%', top: '42%', width: '16%', height: '25%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🐱 Cyber Cats</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">1.4 ETH</div>
                    <div className="text-teal-200 text-xs">+7.2%</div>
                    <div className="w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-teal-300 rounded" style={{width: '50%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white"
                   style={{left: '20%', top: '42%', width: '15%', height: '23%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🚀 Space NFT</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-bold">2.1 ETH</div>
                    <div className="text-indigo-200 text-xs">+6.8%</div>
                    <div className="w-full h-4 bg-indigo-400 bg-opacity-30 rounded mt-1">
                      <div className="h-full bg-indigo-300 rounded" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Smaller collection blocks */}
              <div className="absolute bg-gradient-to-br from-pink-600 to-pink-700 rounded-lg border border-white"
                   style={{left: '37%', top: '42%', width: '13%', height: '20%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">💎 Gems</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">1.7 ETH</div>
                    <div className="text-pink-200 text-xs">+5.4%</div>
                  </div>
                </div>
              </div>

              <div className="absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white"
                   style={{left: '52%', top: '42%', width: '12%', height: '18%'}}>
                <div className="p-2 h-full flex flex-col justify-between">
                  <div>
                    <span className="text-white font-bold text-xs">🎭 Art Club</span>
                  </div>
                  <div>
                    <div className="text-white text-xs font-bold">0.8 ETH</div>
                    <div className="text-orange-200 text-xs">+4.9%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 3: Top 100 Platform Users */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <UsersIcon className="h-6 w-6 text-purple-500" />
            <h2 className="text-2xl font-bold text-gray-900">Top 100 Platform Users</h2>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">7d</button>
              <button className="px-3 py-1 bg-purple-600 text-white rounded">30d</button>
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">90d</button>
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">1y</button>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="grid grid-cols-12 gap-4 mb-4 text-xs text-gray-500 font-medium border-b border-gray-200 pb-2">
          <div className="col-span-1">Rank</div>
          <div className="col-span-3">User</div>
          <div className="col-span-2">Score</div>
          <div className="col-span-2">NFTs</div>
          <div className="col-span-2">Volume</div>
          <div className="col-span-2">Growth</div>
        </div>

        {/* User rows */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {topUsers.map((user) => (
            <div key={user.rank} className="grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100">
              {/* Rank */}
              <div className="col-span-1 text-gray-700 text-sm font-medium">
                {user.rank}
              </div>

              {/* User */}
              <div className="col-span-3 flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{user.avatar}</span>
                </div>
                <div>
                  <div className="text-gray-900 text-sm font-medium">{user.name}</div>
                  <div className="text-gray-500 text-xs">Verified Creator</div>
                </div>
              </div>

              {/* Score with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.score.toLocaleString()}</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                       style={{width: `${user.scoreBar}%`}}></div>
                </div>
              </div>

              {/* NFTs with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.nfts}</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full"
                       style={{width: `${user.nftBar}%`}}></div>
                </div>
              </div>

              {/* Volume with bar */}
              <div className="col-span-2">
                <div className="text-gray-900 text-sm font-bold mb-1">{user.volume} ETH</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full"
                       style={{width: `${user.volumeBar}%`}}></div>
                </div>
              </div>

              {/* Growth with bar */}
              <div className="col-span-2">
                <div className={`text-sm font-bold mb-1 ${user.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {user.growth >= 0 ? '+' : ''}{user.growth}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`h-2 rounded-full ${user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`}
                       style={{width: `${user.growthBar}%`}}></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Section 4: Recent Transactions */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <ClockIcon className="h-6 w-6 text-gray-500" />
          <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
        </div>

        <div className="space-y-3">
          {recentTransactions.length > 0 ? (
            recentTransactions.map((transaction, index) => (
              <div key={transaction.id || index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    transaction.type === 'generation' ? 'bg-blue-100' :
                    transaction.type === 'share' ? 'bg-green-100' :
                    transaction.type === 'purchase' ? 'bg-purple-100' :
                    'bg-gray-100'
                  }`}>
                    <span className={`text-xs font-bold ${
                      transaction.type === 'generation' ? 'text-blue-800' :
                      transaction.type === 'share' ? 'text-green-800' :
                      transaction.type === 'purchase' ? 'text-purple-800' :
                      'text-gray-800'
                    }`}>
                      {transaction.type === 'generation' ? 'GEN' :
                       transaction.type === 'share' ? 'SHR' :
                       transaction.type === 'purchase' ? 'PUR' :
                       'TXN'}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{transaction.nftName}</div>
                    <div className="text-sm text-gray-500">
                      User: {transaction.user} • {transaction.type} • {transaction.timestamp}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                    <span className="font-bold text-gray-900">{transaction.amount}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <ClockIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No recent transactions available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
