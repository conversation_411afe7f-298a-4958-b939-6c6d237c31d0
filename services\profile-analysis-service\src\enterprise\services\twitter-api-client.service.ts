import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

// ✅ ENHANCED: Type definitions for Twitter API responses
interface TwitterUser {
  id: string;
  username: string;
  name: string;
  email: string;
  profile_image_url: string;
  verified: boolean;
  public_metrics: {
    followers_count: number;
    following_count: number;
    tweet_count: number;
    listed_count: number;
  };
  description: string;
  location: string;
  url?: string;
  created_at: string;
  protected: boolean;
}

interface TokenExchangeResponse {
  success: boolean;
  data?: {
    access_token: string;
    token_type: string;
    expires_in: number;
    refresh_token: string;
    scope: string;
    user: TwitterUser;
  };
  error?: string;
  error_code?: string;
}

interface UserProfileResponse {
  success: boolean;
  data?: TwitterUser;
  error?: string;
  error_code?: string;
}

@Injectable()
export class TwitterApiClientService {
  private readonly logger = new Logger(TwitterApiClientService.name);
  private readonly mockTwitterServiceUrl: string;
  private readonly useRealTwitterAPI: boolean;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    this.mockTwitterServiceUrl = this.configService.get('MOCK_TWITTER_SERVICE_URL', 'http://localhost:3020');
    this.useRealTwitterAPI = this.configService.get('USE_MOCK_SERVICES') !== 'true';
    
    this.logger.log(`🔧 Twitter API Client initialized`);
    this.logger.log(`📍 Mock Twitter Service URL: ${this.mockTwitterServiceUrl}`);
    this.logger.log(`🔄 Using Real Twitter API: ${this.useRealTwitterAPI}`);
  }

  async exchangeCodeForToken(code: string, state: string): Promise<any> {
    if (this.useRealTwitterAPI) {
      return this.exchangeWithRealTwitter(code, state);
    } else {
      return this.exchangeWithMockTwitter(code, state);
    }
  }

  private async exchangeWithMockTwitter(code: string, state: string): Promise<TokenExchangeResponse> {
    try {
      this.logger.log('🔧 Calling Enhanced Mock Twitter Service for token exchange');
      this.logger.log(`📡 Request URL: ${this.mockTwitterServiceUrl}/auth/twitter/exchange`);
      this.logger.log(`📝 Request data: { code: "${code}", state: "${state}" }`);

      const response = await firstValueFrom(
        this.httpService.post(`${this.mockTwitterServiceUrl}/auth/twitter/exchange`, {
          code,
          state
        }, {
          timeout: 10000, // Increased timeout for reliability
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Profile-Analysis-Service/2.0',
            'Accept': 'application/json'
          }
        })
      );

      this.logger.log('✅ Enhanced Mock Twitter Service response received');
      this.logger.log(`📊 Response status: ${response.status}`);

      if (response.data.success) {
        this.logger.log(`👤 User authenticated: ${response.data.data.user.username}`);
        this.logger.log(`🔑 Access token received: ${response.data.data.access_token.substring(0, 20)}...`);
        this.logger.log(`⏰ Token expires in: ${response.data.data.expires_in} seconds`);
      } else {
        this.logger.error(`❌ Token exchange failed: ${response.data.error}`);
      }

      return response.data;

    } catch (error) {
      this.logger.error('❌ Enhanced Mock Twitter Service call failed:', error.message);
      this.logger.error(`🔍 Error details: ${error.response?.data || error.stack}`);

      return {
        success: false,
        error: `Mock Twitter API call failed: ${error.message}`,
        error_code: 'API_CALL_FAILED'
      };
    }
  }

  private async exchangeWithRealTwitter(code: string, state: string): Promise<any> {
    this.logger.log('🔗 Real Twitter API token exchange requested');
    // TODO: Implement real Twitter API calls
    throw new Error('Real Twitter API not yet implemented');
  }

  async getUserProfile(accessToken: string): Promise<any> {
    if (this.useRealTwitterAPI) {
      return this.getRealTwitterProfile(accessToken);
    } else {
      return this.getMockTwitterProfile(accessToken);
    }
  }

  private async getMockTwitterProfile(accessToken: string): Promise<UserProfileResponse> {
    try {
      this.logger.log('🔧 Calling Enhanced Mock Twitter Service for current user profile');
      this.logger.log(`📡 Request URL: ${this.mockTwitterServiceUrl}/auth/users/me`);
      this.logger.log(`🔑 Access token: ${accessToken.substring(0, 20)}...`);

      const response = await firstValueFrom(
        this.httpService.get(`${this.mockTwitterServiceUrl}/auth/users/me`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'User-Agent': 'Profile-Analysis-Service/2.0',
            'Accept': 'application/json'
          },
          timeout: 10000
        })
      );

      this.logger.log('✅ Enhanced Mock Twitter profile response received');
      this.logger.log(`📊 Response status: ${response.status}`);

      if (response.data.success) {
        this.logger.log(`👤 Profile fetched for user: ${response.data.data.username}`);
        this.logger.log(`✅ Verified: ${response.data.data.verified}`);
        this.logger.log(`👥 Followers: ${response.data.data.public_metrics.followers_count}`);
      } else {
        this.logger.error(`❌ Profile fetch failed: ${response.data.error}`);
      }

      return response.data;

    } catch (error) {
      this.logger.error('❌ Enhanced Mock Twitter profile call failed:', error.message);
      this.logger.error(`🔍 Error details: ${error.response?.data || error.stack}`);

      return {
        success: false,
        error: `Mock Twitter profile call failed: ${error.message}`,
        error_code: 'PROFILE_FETCH_FAILED'
      };
    }
  }

  private async getRealTwitterProfile(accessToken: string): Promise<UserProfileResponse> {
    this.logger.log('🔗 Real Twitter API profile fetch requested');
    // TODO: Implement real Twitter API calls
    return {
      success: false,
      error: 'Real Twitter API not yet implemented',
      error_code: 'NOT_IMPLEMENTED'
    };
  }

  /**
   * ✅ ENHANCED: Validate access token
   */
  async validateToken(accessToken: string): Promise<any> {
    if (this.useRealTwitterAPI) {
      return this.validateRealTwitterToken(accessToken);
    } else {
      return this.validateMockTwitterToken(accessToken);
    }
  }

  private async validateMockTwitterToken(accessToken: string): Promise<any> {
    try {
      this.logger.log('🔍 Validating token with Enhanced Mock Twitter Service');
      this.logger.log(`📡 Request URL: ${this.mockTwitterServiceUrl}/auth/token/validate`);
      this.logger.log(`🔑 Token: ${accessToken.substring(0, 20)}...`);

      const response = await firstValueFrom(
        this.httpService.post(`${this.mockTwitterServiceUrl}/auth/token/validate`, {
          token: accessToken
        }, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Profile-Analysis-Service/2.0',
            'Accept': 'application/json'
          },
          timeout: 10000
        })
      );

      this.logger.log('✅ Token validation response received');
      this.logger.log(`📊 Response status: ${response.status}`);

      if (response.data.success) {
        this.logger.log(`✅ Token is valid for user: ${response.data.data.user.username}`);
        this.logger.log(`⏰ Token expires in: ${response.data.data.token_info.expires_in} seconds`);
      } else {
        this.logger.log(`❌ Token validation failed: ${response.data.error}`);
      }

      return response.data;

    } catch (error) {
      this.logger.error('❌ Token validation call failed:', error.message);
      this.logger.error(`🔍 Error details: ${error.response?.data || error.stack}`);

      return {
        success: false,
        error: `Token validation failed: ${error.message}`,
        error_code: 'VALIDATION_FAILED'
      };
    }
  }

  private async validateRealTwitterToken(accessToken: string): Promise<any> {
    this.logger.log('🔗 Real Twitter API token validation requested');
    // TODO: Implement real Twitter API calls
    return {
      success: false,
      error: 'Real Twitter API not yet implemented',
      error_code: 'NOT_IMPLEMENTED'
    };
  }

  /**
   * ✅ ENHANCED: Health check for the Twitter API client
   */
  async healthCheck(): Promise<{ healthy: boolean; details?: any }> {
    try {
      if (this.useRealTwitterAPI) {
        // TODO: Implement real Twitter API health check
        return {
          healthy: false,
          details: { error: 'Real Twitter API not implemented' }
        };
      } else {
        // Check if Enhanced Mock Twitter Service is reachable
        this.logger.log('🔍 Checking Enhanced Mock Twitter Service health');

        const response = await firstValueFrom(
          this.httpService.get(`${this.mockTwitterServiceUrl}/auth/health`, {
            timeout: 5000,
            headers: {
              'User-Agent': 'Profile-Analysis-Service/2.0'
            }
          })
        );

        const isHealthy = response.status === 200 && response.data.success;

        if (isHealthy) {
          this.logger.log('✅ Enhanced Mock Twitter Service is healthy');
          this.logger.log(`📊 Service statistics: ${JSON.stringify(response.data.statistics)}`);
        }

        return {
          healthy: isHealthy,
          details: response.data
        };
      }
    } catch (error) {
      this.logger.error('❌ Twitter API health check failed:', error.message);
      return {
        healthy: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Get service configuration info
   */
  getServiceInfo(): any {
    return {
      mockTwitterServiceUrl: this.mockTwitterServiceUrl,
      useRealTwitterAPI: this.useRealTwitterAPI,
      serviceName: 'TwitterApiClientService',
      version: '1.0.0'
    };
  }
}
