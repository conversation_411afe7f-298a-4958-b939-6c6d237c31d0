<!DOCTYPE html>
<html>
<head>
    <title>Data Source Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Data Source Analysis Tool</h1>

    <div class="section">
        <h3>Step 1: Analyze Data Sources</h3>
        <button onclick="analyzeDataSources()">Analyze Data Sources</button>
        <div id="dataSourceResult"></div>
    </div>

    <div class="section">
        <h3>Step 2: Analyze Business Rule Compliance</h3>
        <button onclick="analyzeBusinessRule()">Check Data Consistency</button>
        <div id="businessRuleResult"></div>
    </div>

    <div class="section">
        <h3>Step 3: Database vs Mock Data Analysis</h3>
        <button onclick="analyzeDatabaseConnection()">Check Database Connection</button>
        <div id="databaseResult"></div>
    </div>

    <script>
        function analyzeDataSources() {
            const result = document.getElementById('dataSourceResult');

            let output = '<h4>📊 Data Source Analysis:</h4>';

            // Check localStorage data
            const authUser = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');
            const campaignData = localStorage.getItem('campaigns');

            output += '<table>';
            output += '<tr><th>Data Source</th><th>Status</th><th>Type</th><th>Details</th></tr>';

            // Auth User Analysis
            if (authUser) {
                const user = JSON.parse(authUser);
                output += `<tr>
                    <td>auth_user</td>
                    <td class="success">✅ Found</td>
                    <td class="info">localStorage</td>
                    <td>User ID: ${user.id}, Username: ${user.username}</td>
                </tr>`;
            } else {
                output += `<tr>
                    <td>auth_user</td>
                    <td class="error">❌ Missing</td>
                    <td>localStorage</td>
                    <td>No authentication data</td>
                </tr>`;
            }

            // NFT Data Analysis
            if (nftData) {
                const nfts = JSON.parse(nftData);
                const user = authUser ? JSON.parse(authUser) : null;
                const userNFTs = user ? nfts.filter(nft => String(nft.userId) === String(user.id)) : [];

                output += `<tr>
                    <td>user_nfts</td>
                    <td class="success">✅ Found</td>
                    <td class="warning">localStorage (Mock)</td>
                    <td>Total: ${nfts.length}, User NFTs: ${userNFTs.length}</td>
                </tr>`;
            } else {
                output += `<tr>
                    <td>user_nfts</td>
                    <td class="error">❌ Missing</td>
                    <td>localStorage</td>
                    <td>No NFT data</td>
                </tr>`;
            }

            output += '</table>';

            result.innerHTML = output;
        }

        function analyzeBusinessRule() {
            const result = document.getElementById('businessRuleResult');

            const authUser = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');

            if (!authUser || !nftData) {
                result.innerHTML = '<div class="error">❌ Missing required data for analysis</div>';
                return;
            }

            const user = JSON.parse(authUser);
            const nfts = JSON.parse(nftData);
            const userNFTs = nfts.filter(nft => String(nft.userId) === String(user.id));

            // Business rule analysis
            const uniqueCampaigns = [...new Set(userNFTs.map(nft => nft.campaignId))];
            const totalNFTs = userNFTs.length;
            const campaignsJoined = uniqueCampaigns.length;
            const isDataConsistent = totalNFTs === campaignsJoined;

            // Find violations
            const campaignCounts = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                campaignCounts[campaignId] = (campaignCounts[campaignId] || 0) + 1;
            });

            const violations = Object.keys(campaignCounts).filter(id => campaignCounts[id] > 1);

            let output = `<h4>⚖️ Business Rule Analysis:</h4>`;
            output += `<div class="${isDataConsistent ? 'success' : 'error'}">
                <strong>Rule: One NFT Per Campaign</strong><br>
                Status: ${isDataConsistent ? '✅ COMPLIANT' : '❌ VIOLATION DETECTED'}
            </div>`;

            output += `<table>
                <tr><th>Metric</th><th>Value</th><th>Status</th></tr>
                <tr><td>Total NFTs</td><td>${totalNFTs}</td><td>${totalNFTs > 0 ? '✅' : '⚠️'}</td></tr>
                <tr><td>Campaigns Joined</td><td>${campaignsJoined}</td><td>${campaignsJoined > 0 ? '✅' : '⚠️'}</td></tr>
                <tr><td>Data Consistency</td><td>${isDataConsistent ? 'PASS' : 'FAIL'}</td><td>${isDataConsistent ? '✅' : '❌'}</td></tr>
            </table>`;

            if (violations.length > 0) {
                output += `<div class="warning">
                    <strong>⚠️ Violations Found:</strong><br>
                    ${violations.map(id => `Campaign ${id}: ${campaignCounts[id]} NFTs`).join('<br>')}
                </div>`;
            }

            result.innerHTML = output;
        }

        async function analyzeDatabaseConnection() {
            const result = document.getElementById('databaseResult');

            let output = `<h4>🗄️ Database vs Mock Data Analysis:</h4>`;

            // Check if we're using real database or mock data
            output += `<table>
                <tr><th>Data Type</th><th>Current Source</th><th>Status</th><th>Recommendation</th></tr>`;

            // NFT Data Analysis
            const nftData = localStorage.getItem('user_nfts');
            if (nftData) {
                output += `<tr>
                    <td>NFT Data</td>
                    <td class="warning">localStorage (Mock)</td>
                    <td>⚠️ Frontend Only</td>
                    <td>Should use database API</td>
                </tr>`;
            } else {
                output += `<tr>
                    <td>NFT Data</td>
                    <td class="error">None</td>
                    <td>❌ Missing</td>
                    <td>Need database or mock data</td>
                </tr>`;
            }

            // Campaign Data Analysis
            const campaignData = localStorage.getItem('campaigns');
            if (campaignData) {
                output += `<tr>
                    <td>Campaign Data</td>
                    <td class="warning">localStorage (Mock)</td>
                    <td>⚠️ Frontend Only</td>
                    <td>Should use database API</td>
                </tr>`;
            } else {
                output += `<tr>
                    <td>Campaign Data</td>
                    <td class="error">None</td>
                    <td>❌ Missing</td>
                    <td>Need database or mock data</td>
                </tr>`;
            }

            // Check API connectivity
            try {
                const response = await fetch('http://localhost:3010/api/health');
                if (response.ok) {
                    output += `<tr>
                        <td>API Gateway</td>
                        <td class="success">Database Connected</td>
                        <td>✅ Available</td>
                        <td>Can switch to real data</td>
                    </tr>`;
                } else {
                    output += `<tr>
                        <td>API Gateway</td>
                        <td class="error">Connection Failed</td>
                        <td>❌ Unavailable</td>
                        <td>Start backend services</td>
                    </tr>`;
                }
            } catch (error) {
                output += `<tr>
                    <td>API Gateway</td>
                    <td class="error">Connection Error</td>
                    <td>❌ Unavailable</td>
                    <td>Start backend services</td>
                </tr>`;
            }

            output += `</table>`;

            // Explanation
            output += `<div class="info">
                <h5>📝 Current Situation:</h5>
                <ul>
                    <li><strong>Frontend:</strong> Using localStorage mock data</li>
                    <li><strong>Backend:</strong> Database services available but not connected</li>
                    <li><strong>Data Inconsistency:</strong> Caused by mock data not following business rules</li>
                    <li><strong>Solution:</strong> Either fix mock data or connect to real database</li>
                </ul>
            </div>`;

            result.innerHTML = output;
        }
    </script>
</body>
</html>
