'use client'

import {
  Container,
  <PERSON>ing,
  Text,
  VStack,
  SimpleGrid,
  Box,
  Button,
  Badge,
  HStack,
  Input
} from '@chakra-ui/react'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { campaignService, Campaign } from '@/services/campaignService'
import { PageLoading } from '@/components/LoadingSpinner'
import Layout from '@/components/layout/Layout'
import { CampaignsLayout } from '@/components/PageLayout'

export default function CampaignsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Don't redirect during loading (AuthContext initialization)
    if (isLoading) {
      console.log('🔄 Campaigns: AuthContext is loading, waiting...');
      return;
    }

    if (!isAuthenticated) {
      console.log('❌ Campaigns: User not authenticated, redirecting to login');
      router.push('/auth/login')
      return
    }

    console.log('✅ Campaigns: User authenticated, fetching campaigns');
    fetchCampaigns()
  }, [isAuthenticated, isLoading, router])

  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      setError('')

      console.log('🎯 Fetching all campaigns')
      const allCampaigns = await campaignService.getCampaigns()

      console.log('✅ Campaigns fetched:', allCampaigns.length, 'campaigns found')
      setCampaigns(allCampaigns)
    } catch (err: any) {
      console.error('Campaign fetch error:', err)
      setError('Failed to load campaigns')
    } finally {
      setLoading(false)
    }
  }

  const filteredCampaigns = campaigns.filter(campaign =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    campaign.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Show loading during AuthContext initialization
  if (isLoading) {
    return <PageLoading message="Initializing authentication..." />
  }

  // Show loading during campaigns fetch
  if (loading) {
    return <PageLoading message="Loading campaigns..." />
  }

  return (
    <Layout>
      <CampaignsLayout>
        <VStack gap={8} align="stretch">

        {/* Enhanced Header Section */}
        <Box bg="white" p={8} borderRadius="lg" boxShadow="md">
          <VStack align="start" gap={4}>
            <VStack align="start" gap={2}>
              <Heading as="h1" size="xl" color="blue.600">
                🎯 Discover Campaigns
              </Heading>
              <Text color="gray.600" fontSize="lg">
                Join exciting campaigns and mint unique NFTs based on your social media activity
              </Text>
            </VStack>

            {/* Search and Filter Section */}
            <HStack gap={4} w="full" flexWrap="wrap">
              <Input
                placeholder="🔍 Search campaigns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                maxW="400px"
                bg="gray.50"
                border="1px solid"
                borderColor="gray.200"
                _focus={{ borderColor: "blue.400", bg: "white" }}
              />
              <Text fontSize="sm" color="gray.500">
                {filteredCampaigns.length} of {campaigns.length} campaigns
              </Text>
            </HStack>
          </VStack>
        </Box>

        {/* Error Message */}
        {error && (
          <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
            <Text color="red.600" fontWeight="medium">
              {error}
            </Text>
          </Box>
        )}

        {/* Campaigns Grid */}
        {filteredCampaigns.length === 0 ? (
          <Box textAlign="center" py={12}>
            <Text fontSize="lg" color="gray.500">
              {campaigns.length === 0
                ? "No campaigns available at the moment."
                : "No campaigns match your search criteria."
              }
            </Text>
            {campaigns.length === 0 && (
              <Button mt={4} colorScheme="blue" onClick={() => router.push('/dashboard')}>
                Go to Dashboard
              </Button>
            )}
          </Box>
        ) : (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
            {filteredCampaigns.map((campaign) => (
              <Box
                key={campaign.id}
                bg="white"
                borderRadius="lg"
                boxShadow="md"
                overflow="hidden"
                transition="all 0.2s"
                _hover={{
                  transform: "translateY(-4px)",
                  boxShadow: "xl",
                  borderColor: "blue.200"
                }}
                border="1px solid"
                borderColor="gray.100"
              >
                {/* Campaign Header */}
                <Box bg="blue.50" p={4} borderBottom="1px solid" borderColor="gray.100">
                  <HStack justify="space-between" align="start">
                    <VStack align="start" gap={1}>
                      <Heading size="md" color="blue.700">{campaign.name}</Heading>
                      <Badge
                        colorScheme={campaign.status === 'active' ? 'green' : 'gray'}
                        size="sm"
                      >
                        {campaign.status.toUpperCase()}
                      </Badge>
                    </VStack>
                    <Text fontSize="2xl">🎯</Text>
                  </HStack>
                </Box>

                {/* Campaign Content */}
                <VStack align="start" gap={4} p={6}>
                  <Text color="gray.600" fontSize="sm" lineHeight="1.6">
                    {campaign.description}
                  </Text>

                  {/* Campaign Details */}
                  <Box w="full" bg="gray.50" p={3} borderRadius="md">
                    <VStack align="start" gap={2} fontSize="sm">
                      <HStack justify="space-between" w="full">
                        <HStack gap={2}>
                          <Text>📅</Text>
                          <Text fontWeight="medium" color="gray.700">Start:</Text>
                        </HStack>
                        <Text color="gray.600">
                          {new Date(campaign.startDate).toLocaleDateString()}
                        </Text>
                      </HStack>
                      <HStack justify="space-between" w="full">
                        <HStack gap={2}>
                          <Text>🏁</Text>
                          <Text fontWeight="medium" color="gray.700">End:</Text>
                        </HStack>
                        <Text color="gray.600">
                          {new Date(campaign.endDate).toLocaleDateString()}
                        </Text>
                      </HStack>
                    </VStack>
                  </Box>

                  {/* Action Button */}
                  <Button
                    colorScheme="blue"
                    w="full"
                    size="md"
                    onClick={() => router.push(`/campaigns/${campaign.id}`)}
                    _hover={{ transform: "translateY(-1px)" }}
                    transition="all 0.2s"
                  >
                    🚀 Join Campaign
                  </Button>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        )}
        </VStack>
      </CampaignsLayout>
    </Layout>
  )
}
