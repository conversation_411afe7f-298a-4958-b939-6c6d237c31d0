// Enterprise Project Command Service - Requirements-Driven Implementation
import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';
import { CreateProjectCommandDto, UpdateProjectCommandDto, ProjectStatus } from '../models/project-command.model';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class ProjectCommandService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create new project with complete configuration
   * Requirements: Support full project configuration including analysis parameters, NFT settings, blockchain config
   */
  async createProject(dto: CreateProjectCommandDto, correlationId: string): Promise<any> {
    try {
      // Validate analysis configuration
      this.validateAnalysisConfiguration(dto.analysisConfiguration);
      
      // Validate NFT configuration
      this.validateNFTConfiguration(dto.nftConfiguration);

      // Validate blockchain network
      this.validateBlockchainConfiguration(dto.blockchainNetwork, dto.contractAddress);

      // Create project command
      const projectCommand = await this.prisma.projectCommand.create({
        data: {
          name: dto.name,
          description: dto.description,
          ownerId: dto.ownerId,
          category: dto.category,
          tags: dto.participationConditions || [],
          
          // Media & Links (Requirements-driven)
          images: dto.images || [],
          website: dto.website,
          socialMediaLinks: dto.socialMediaLinks ? JSON.parse(JSON.stringify(dto.socialMediaLinks)) : {},

          // Campaign Configuration
          duration: dto.duration ? JSON.parse(JSON.stringify(dto.duration)) : {},
          participationConditions: dto.participationConditions || [],

          // Analysis Configuration (Core requirement)
          analysisConfiguration: JSON.parse(JSON.stringify(dto.analysisConfiguration)),

          // NFT Configuration (Core requirement)
          nftConfiguration: JSON.parse(JSON.stringify(dto.nftConfiguration)),
          
          // Blockchain Configuration (Multi-chain support)
          blockchainNetwork: dto.blockchainNetwork,
          contractAddress: dto.contractAddress,
          networkConfig: this.getNetworkConfig(dto.blockchainNetwork),
          
          // Project Settings
          isPublic: dto.isPublic ?? true,
          allowParticipation: dto.allowParticipation ?? true,
          maxParticipants: dto.maxParticipants,
          
          // Enterprise Audit
          createdBy: dto.ownerId,
          updatedBy: dto.ownerId,
        },
      });

      // Create optimized query model
      await this.createProjectQuery(projectCommand);

      // Emit domain event
      await this.eventEmitter.emitAsync('project.created', {
        projectId: projectCommand.id,
        ownerId: dto.ownerId,
        blockchainNetwork: dto.blockchainNetwork,
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        data: {
          id: projectCommand.id,
          name: projectCommand.name,
          status: projectCommand.status,
          blockchainNetwork: projectCommand.blockchainNetwork,
        },
        message: 'Project created successfully',
        correlationId,
      };

    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException('Project with this name already exists for this owner');
      }
      throw error;
    }
  }

  /**
   * Update existing project
   * Requirements: Support partial updates while maintaining data integrity
   */
  async updateProject(id: string, dto: UpdateProjectCommandDto, correlationId: string): Promise<any> {
    try {
      // Check if project exists
      const existingProject = await this.prisma.projectCommand.findUnique({
        where: { id },
      });

      if (!existingProject) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      // Validate configurations if provided
      if (dto.analysisConfiguration) {
        this.validateAnalysisConfiguration(dto.analysisConfiguration);
      }
      
      if (dto.nftConfiguration) {
        this.validateNFTConfiguration(dto.nftConfiguration);
      }

      if (dto.blockchainNetwork) {
        this.validateBlockchainConfiguration(dto.blockchainNetwork, dto.contractAddress);
      }

      // Prepare update data with proper JSON conversion
      const updateData: any = {
        ...dto,
        updatedBy: existingProject.ownerId,
        version: { increment: 1 },
      };

      // Convert complex objects to JSON
      if (dto.socialMediaLinks) {
        updateData.socialMediaLinks = JSON.parse(JSON.stringify(dto.socialMediaLinks));
      }
      if (dto.duration) {
        updateData.duration = JSON.parse(JSON.stringify(dto.duration));
      }
      if (dto.analysisConfiguration) {
        updateData.analysisConfiguration = JSON.parse(JSON.stringify(dto.analysisConfiguration));
      }
      if (dto.nftConfiguration) {
        updateData.nftConfiguration = JSON.parse(JSON.stringify(dto.nftConfiguration));
      }
      if (dto.blockchainNetwork) {
        updateData.networkConfig = this.getNetworkConfig(dto.blockchainNetwork);
      }

      // Update project command
      const updatedProject = await this.prisma.projectCommand.update({
        where: { id },
        data: updateData,
      });

      // Update query model
      await this.updateProjectQuery(updatedProject);

      // Emit domain event
      await this.eventEmitter.emitAsync('project.updated', {
        projectId: id,
        ownerId: existingProject.ownerId,
        changes: Object.keys(dto),
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        data: {
          id: updatedProject.id,
          name: updatedProject.name,
          status: updatedProject.status,
          version: updatedProject.version,
        },
        message: 'Project updated successfully',
        correlationId,
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete project (soft delete with cascade)
   * Requirements: Maintain data integrity and audit trail
   */
  async deleteProject(id: string, correlationId: string): Promise<any> {
    try {
      const project = await this.prisma.projectCommand.findUnique({
        where: { id },
      });

      if (!project) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      // Soft delete by updating status
      await this.prisma.projectCommand.update({
        where: { id },
        data: {
          status: ProjectStatus.ARCHIVED,
          updatedBy: project.ownerId,
          version: { increment: 1 },
        },
      });

      // Update query model
      await this.prisma.projectQuery.update({
        where: { id },
        data: {
          status: ProjectStatus.ARCHIVED,
        },
      });

      // Emit domain event
      await this.eventEmitter.emitAsync('project.deleted', {
        projectId: id,
        ownerId: project.ownerId,
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        message: 'Project deleted successfully',
        correlationId,
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate analysis configuration
   * Requirements: Ensure parameter weights are mathematically sound
   */
  private validateAnalysisConfiguration(config: any): void {
    // Validate fixed parameters weights sum to reasonable total
    const fixedWeights = Object.values(config.fixedParameters)
      .filter((param: any) => param.enabled)
      .reduce((sum: number, param: any) => sum + param.weight, 0) as number;

    if (fixedWeights > 100) {
      throw new BadRequestException('Fixed parameters weights cannot exceed 100');
    }

    // Validate variable parameters weights
    const variableWeights = Object.values(config.variableParameters)
      .filter((param: any) => param.enabled)
      .reduce((sum: number, param: any) => sum + param.weight, 0) as number;

    if (variableWeights > 100) {
      throw new BadRequestException('Variable parameters weights cannot exceed 100');
    }

    // Validate update frequency
    if (config.updateFrequencyHours < 1 || config.updateFrequencyHours > 168) {
      throw new BadRequestException('Update frequency must be between 1 and 168 hours');
    }
  }

  /**
   * Validate NFT configuration
   * Requirements: Ensure rarity thresholds are logical
   */
  private validateNFTConfiguration(config: any): void {
    const { scoreThresholds } = config;
    
    if (scoreThresholds.common >= scoreThresholds.rare) {
      throw new BadRequestException('Rare threshold must be higher than Common threshold');
    }
    
    if (scoreThresholds.rare >= scoreThresholds.legendary) {
      throw new BadRequestException('Legendary threshold must be higher than Rare threshold');
    }

    if (scoreThresholds.common < 0 || scoreThresholds.legendary > 100) {
      throw new BadRequestException('Score thresholds must be between 0 and 100');
    }
  }

  /**
   * Validate blockchain configuration
   * Requirements: Support multi-chain with proper validation
   */
  private validateBlockchainConfiguration(network: string, contractAddress?: string): void {
    const supportedNetworks = ['ethereum', 'polygon', 'bsc', 'base'];
    
    if (!supportedNetworks.includes(network)) {
      throw new BadRequestException(`Unsupported blockchain network: ${network}`);
    }

    // Validate contract address format if provided
    if (contractAddress && !this.isValidContractAddress(contractAddress)) {
      throw new BadRequestException('Invalid contract address format');
    }
  }

  /**
   * Get network-specific configuration
   * Requirements: Support easy network addition/removal
   */
  private getNetworkConfig(network: string): any {
    const networkConfigs = {
      ethereum: { chainId: 1, rpcUrl: 'https://mainnet.infura.io', gasLimit: 21000 },
      polygon: { chainId: 137, rpcUrl: 'https://polygon-rpc.com', gasLimit: 21000 },
      bsc: { chainId: 56, rpcUrl: 'https://bsc-dataseed.binance.org', gasLimit: 21000 },
      base: { chainId: 8453, rpcUrl: 'https://mainnet.base.org', gasLimit: 21000 },
    };

    return networkConfigs[network] || {};
  }

  /**
   * Validate contract address format
   */
  private isValidContractAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * Create optimized query model
   * Requirements: Optimized read model for frontend
   */
  private async createProjectQuery(projectCommand: any): Promise<void> {
    await this.prisma.projectQuery.create({
      data: {
        id: projectCommand.id,
        displayName: projectCommand.name,
        displayDescription: projectCommand.description,
        ownerId: projectCommand.ownerId,
        category: projectCommand.category,
        featuredImage: projectCommand.images?.[0],
        website: projectCommand.website,
        socialMediaLinks: projectCommand.socialMediaLinks,
        duration: projectCommand.duration,
        blockchainNetwork: projectCommand.blockchainNetwork,
        nftRarityTypes: this.extractRarityTypes(projectCommand.nftConfiguration),
        status: projectCommand.status,
        isPublic: projectCommand.isPublic,
        createdAt: projectCommand.createdAt,
      },
    });
  }

  /**
   * Update optimized query model
   */
  private async updateProjectQuery(projectCommand: any): Promise<void> {
    await this.prisma.projectQuery.update({
      where: { id: projectCommand.id },
      data: {
        displayName: projectCommand.name,
        displayDescription: projectCommand.description,
        category: projectCommand.category,
        featuredImage: projectCommand.images?.[0],
        website: projectCommand.website,
        socialMediaLinks: projectCommand.socialMediaLinks,
        duration: projectCommand.duration,
        blockchainNetwork: projectCommand.blockchainNetwork,
        nftRarityTypes: this.extractRarityTypes(projectCommand.nftConfiguration),
        status: projectCommand.status,
        isPublic: projectCommand.isPublic,
        lastUpdated: new Date(),
      },
    });
  }

  /**
   * Extract rarity types for display
   */
  private extractRarityTypes(nftConfig: any): any {
    if (!nftConfig?.scoreThresholds) return {};
    
    return {
      common: { threshold: nftConfig.scoreThresholds.common, available: true },
      rare: { threshold: nftConfig.scoreThresholds.rare, available: true },
      legendary: { threshold: nftConfig.scoreThresholds.legendary, available: true },
    };
  }
}
