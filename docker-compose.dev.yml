# Enterprise Social NFT Platform - Development Docker Compose
# Complete microservices stack for local development

version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-dev
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1111
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway-dev
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=development
      - PORT=3010
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3010/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Service
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: user-service-dev
    ports:
      - "3011:3011"
    environment:
      - NODE_ENV=development
      - PORT=3011
      - DATABASE_URL=****************************************/user_service
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3011/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Profile Analysis Service
  profile-analysis-service:
    build:
      context: ./services/profile-analysis-service
      dockerfile: Dockerfile
    container_name: profile-analysis-service-dev
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DATABASE_URL=****************************************/profile_analysis_service
    depends_on:
      postgres:
        condition: service_healthy

  # NFT Generation Service
  nft-generation-service:
    build:
      context: ./services/nft-generation-service
      dockerfile: Dockerfile
    container_name: nft-generation-service-dev
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - DATABASE_URL=****************************************/nft_generation_service
    depends_on:
      postgres:
        condition: service_healthy

  # Blockchain Service
  blockchain-service:
    build:
      context: ./services/blockchain-service
      dockerfile: Dockerfile
    container_name: blockchain-service-dev
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_URL=****************************************/blockchain_service
    depends_on:
      postgres:
        condition: service_healthy

  # Project Service
  project-service:
    build:
      context: ./services/project-service
      dockerfile: Dockerfile
    container_name: project-service-dev
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - DATABASE_URL=****************************************/project_service
    depends_on:
      postgres:
        condition: service_healthy

  # Marketplace Service
  marketplace-service:
    build:
      context: ./services/marketplace-service
      dockerfile: Dockerfile
    container_name: marketplace-service-dev
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - DATABASE_URL=****************************************/marketplace_service
    depends_on:
      postgres:
        condition: service_healthy

  # Analytics Service
  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    container_name: analytics-service-dev
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - PORT=3007
      - DATABASE_URL=****************************************/analytics_service
    depends_on:
      postgres:
        condition: service_healthy

  # Notification Service
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: notification-service-dev
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=development
      - PORT=3008
      - DATABASE_URL=****************************************/notification_service
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:

networks:
  default:
    name: social-nft-dev
