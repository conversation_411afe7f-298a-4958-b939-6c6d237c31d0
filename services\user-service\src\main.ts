import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { setupSwagger } from './config/swagger.config';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);

    // Set global prefix
    app.setGlobalPrefix('api');

    // Enable CORS for frontend
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3010'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID', 'X-Session-ID'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    // Enterprise Swagger setup
    setupSwagger(app);

    const port = process.env.PORT || 3011;
    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 Enterprise User Service running on port ${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    logger.log(`🏥 Health Check: http://localhost:${port}/api/health`);
    logger.log(`✅ Enterprise features: CQRS, Event Sourcing, Audit Trails, Monitoring`);

  } catch (error) {
    logger.error('❌ Failed to start Enterprise User Service:', error);
    process.exit(1);
  }
}

bootstrap();
