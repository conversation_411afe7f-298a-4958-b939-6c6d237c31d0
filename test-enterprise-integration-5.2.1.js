// Step 5.2.1 - CQRS Command/Query Test
const axios = require('axios');

async function testCQRSPattern() {
  console.log('🔄 Step 5.2.1: CQRS Command/Query Test');
  
  try {
    // Test 1: CQRS Command (Write) - Create User
    console.log('\n✍️ Test 1: CQRS Command (Write) - Create User...');
    const timestamp = Date.now();
    const createResponse = await axios.post('http://localhost:3010/api/users', {
      username: `cqrs_test_${timestamp}`,
      email: `cqrs_test_${timestamp}@example.com`,
      password: 'SecurePassword123',
      role: 'user'
    }, {
      headers: { 'X-Correlation-ID': 'cqrs-command-5.2.1' }
    });
    
    const commandData = createResponse.data.data;
    console.log('✅ CQRS Command Model fields present:');
    console.log('   - Audit fields:', !!commandData.createdAt && !!commandData.updatedAt);
    console.log('   - Version control:', !!commandData.version);
    console.log('   - Compliance fields:', !!commandData.dataClassification);
    
    // Test 2: CQRS Query (Read) - Get User
    console.log('\n📖 Test 2: CQRS Query (Read) - Get User...');
    const queryResponse = await axios.get(`http://localhost:3010/api/users/${commandData.id}`, {
      headers: { 'X-Correlation-ID': 'cqrs-query-5.2.1' }
    });
    
    const queryData = queryResponse.data.data;
    console.log('✅ CQRS Query Model fields present:');
    console.log('   - Optimized fields:', !!queryData.displayName);
    console.log('   - Pre-computed aggregations:', typeof queryData.totalNfts === 'number');
    console.log('   - Performance fields:', queryData.hasOwnProperty('lastUpdated'));
    
    // Test 3: CQRS Data Consistency
    console.log('\n🔗 Test 3: CQRS Data Consistency...');
    console.log('✅ Data consistency verified:');
    console.log('   - ID match:', commandData.id === queryData.id);
    console.log('   - Username match:', commandData.username === queryData.username);
    console.log('   - Email match:', commandData.email === queryData.email);
    
    console.log('\n🎉 Step 5.2.1 PASSED: CQRS Pattern Working');
    return { success: true, userId: commandData.id };
    
  } catch (error) {
    console.error('❌ Step 5.2.1 FAILED:', error.message);
    return { success: false };
  }
}

testCQRSPattern();
