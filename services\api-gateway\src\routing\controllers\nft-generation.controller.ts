import { <PERSON>, Get, Post, Patch, Body, Param, Query, Headers, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('NFT Generation')
@Controller('nft-generation')
export class NftGenerationController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('generate')
  @ApiOperation({ summary: 'Generate NFT for user based on analysis data' })
  @ApiResponse({ status: 201, description: 'NFT generated successfully' })
  @ApiResponse({ status: 409, description: 'NFT already exists for this user in this campaign' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async generateNft(@Body() generateNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nft-generation/generate',
        'POST',
        generateNftDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Patch('update')
  @ApiOperation({ summary: 'Update NFT based on new score and analysis data' })
  @ApiResponse({ status: 200, description: 'NFT updated successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async updateNft(@Body() updateNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nft-generation/update',
        'PATCH',
        updateNftDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get all NFTs for a specific user' })
  @ApiResponse({ status: 200, description: 'User NFTs retrieved successfully' })
  async getUserNfts(@Param('userId') userId: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nft-generation/user/${userId}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Get('campaign/:campaignId')
  @ApiOperation({ summary: 'Get all NFTs for a specific campaign' })
  @ApiResponse({ status: 200, description: 'Campaign NFTs retrieved successfully' })
  async getCampaignNfts(@Param('campaignId') campaignId: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nft-generation/campaign/${campaignId}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Patch('mint')
  @ApiOperation({ summary: 'Mark NFT as minted on blockchain' })
  @ApiResponse({ status: 200, description: 'NFT marked as minted successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async markAsMinted(@Body() mintNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nft-generation/mint',
        'PATCH',
        mintNftDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Post('generate-from-analysis')
  @ApiOperation({ summary: 'Generate NFT from Profile Analysis results' })
  @ApiResponse({ status: 201, description: 'NFT generated successfully from profile analysis' })
  @ApiResponse({ status: 400, description: 'Invalid input or analysis not found' })
  @ApiResponse({ status: 404, description: 'Profile analysis not found' })
  async generateNftFromAnalysis(@Body() generateDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nfts/generate-from-analysis',
        'POST',
        generateDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }

  @Get('user/:userId/history')
  @ApiOperation({ summary: 'Get user NFT generation history' })
  @ApiResponse({ status: 200, description: 'NFT generation history retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid userId' })
  async getUserNftHistory(@Param('userId') userId: string, @Query() query: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nfts/user/${userId}/history`,
        'GET',
        null,
        req.headers,
        query
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('NFT History Error:', error);
      return res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Check NFT Generation Service health' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/health',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'nft-generation-service'
      });
    }
  }
}
