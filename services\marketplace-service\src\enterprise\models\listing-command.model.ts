// Enterprise Listing Command Model (Write Side) - Template
import { IsString, IsOptional, IsEnum, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ListingType {
  FIXED = 'fixed',
  AUCTION = 'auction'
}

export enum ListingStatus {
  ACTIVE = 'active',
  SOLD = 'sold',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

export class CreateListingCommandDto {
  @ApiProperty({ description: 'NFT ID to list' })
  @IsString()
  nftId: string;

  @ApiProperty({ description: 'Seller user ID' })
  @IsString()
  sellerId: string;

  @ApiProperty({ description: 'Listing price' })
  @IsString()
  price: string;

  @ApiPropertyOptional({ description: 'Currency', default: 'ETH' })
  @IsOptional()
  @IsString()
  currency?: string = 'ETH';

  @ApiProperty({ description: 'Listing type', enum: ListingType })
  @IsEnum(ListingType)
  listingType: ListingType;

  @ApiPropertyOptional({ description: 'Auction end time (for auctions)' })
  @IsOptional()
  @IsDateString()
  auctionEndTime?: string;
}
