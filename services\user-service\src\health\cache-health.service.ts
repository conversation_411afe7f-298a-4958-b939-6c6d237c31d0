import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { CacheService } from '../shared/services/cache.service';

@Injectable()
export class CacheHealthService extends HealthIndicator {
  constructor(private readonly cacheService: CacheService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const healthCheck = await this.cacheService.healthCheck();
      
      if (healthCheck.status === 'healthy') {
        const result = this.getStatus(key, true, {
          cache: 'redis',
          status: 'up',
          latency: `${healthCheck.latency}ms`,
          timestamp: new Date().toISOString(),
        });

        return result;
      } else {
        throw new Error('Cache is unhealthy');
      }
    } catch (error) {
      const result = this.getStatus(key, false, {
        cache: 'redis',
        status: 'down',
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw new HealthCheckError('Cache health check failed', result);
    }
  }
}
