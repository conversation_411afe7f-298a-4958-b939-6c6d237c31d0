import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'ok',
      service: 'notification-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || 3008,
      environment: process.env.NODE_ENV || 'development',
    };
  }

  getNotificationStatus() {
    return {
      status: 'ok',
      service: 'notification-service',
      timestamp: new Date().toISOString(),
      email: {
        smtp_host: process.env.SMTP_HOST || 'smtp.gmail.com',
        smtp_port: parseInt(process.env.SMTP_PORT) || 587,
        from_email: process.env.FROM_EMAIL || '<EMAIL>',
        from_name: process.env.FROM_NAME || 'Social NFT Platform',
        sendgrid_configured: !!process.env.SENDGRID_API_KEY,
      },
      push_notifications: {
        firebase_configured: !!process.env.FIREBASE_PROJECT_ID,
        project_id: process.env.FIREBASE_PROJECT_ID || 'not-configured',
      },
      sms: {
        twilio_configured: !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN),
      },
      settings: {
        max_retry_attempts: parseInt(process.env.MAX_RETRY_ATTEMPTS) || 3,
        retry_delay_ms: parseInt(process.env.RETRY_DELAY_MS) || 5000,
        batch_size: parseInt(process.env.BATCH_SIZE) || 100,
        rate_limit_per_minute: parseInt(process.env.RATE_LIMIT_PER_MINUTE) || 60,
        default_language: process.env.DEFAULT_LANGUAGE || 'en',
      },
      integrations: {
        user_service: process.env.USER_SERVICE_URL || 'http://localhost:3001',
        project_service: process.env.PROJECT_SERVICE_URL || 'http://localhost:3006',
        marketplace_service: process.env.MARKETPLACE_SERVICE_URL || 'http://localhost:3007',
        nft_generation_service: process.env.NFT_GENERATION_SERVICE_URL || 'http://localhost:3004',
        blockchain_service: process.env.BLOCKCHAIN_SERVICE_URL || 'http://localhost:3005',
      },
      external_services: {
        slack_webhook_configured: !!process.env.SLACK_WEBHOOK_URL,
      },
    };
  }
}
