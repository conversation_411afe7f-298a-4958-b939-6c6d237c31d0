# 🎯 WORKING DIRECTORY PROTOCOL

## **MAND<PERSON>ORY PROTOCOL FOR ALL OPERATIONS**

### **🚨 CRITICAL RULE: ALWAYS VERIFY LOCATION FIRST**

Before ANY operation, ALWAYS run:
```bash
pwd && echo "Expected: /c/Users/<USER>/Documents/Augment/social-nft-platform-v2"
```

### **📍 ABSOLUTE PATHS ONLY**

**✅ ALWAYS USE:**
```bash
# Full absolute paths in save-file
path: "C:/Users/<USER>/Documents/Augment/social-nft-platform-v2/docs/filename.md"

# Full absolute paths in cwd
cwd: "/c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/user-service"
```

**❌ NEVER USE:**
```bash
# Relative paths that create duplicates!
path: "social-nft-platform-v2/docs/file.md"  # Creates nested duplicates!
cwd: "services/user-service"  # May resolve wrong!
```

### **🔧 MANDATORY VERIFICATION COMMANDS**

#### **Before Starting Any Service:**
```bash
# 1. Verify location
pwd

# 2. Verify service exists
ls -la /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/

# 3. Verify specific service
ls /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/[SERVICE-NAME]/package.json
```

### **🚨 ERROR PREVENTION CHECKLIST**

Before EVERY operation:
- [ ] Run `pwd` to verify current location
- [ ] Use ABSOLUTE paths in save-file operations
- [ ] Use ABSOLUTE paths in cwd parameter
- [ ] Verify target directory exists
- [ ] Check for potential duplicates

---
**Status**: MANDATORY - Must be followed for ALL operations
