# 🚨 AI AGENT MUST READ FIRST - MANDATORY PROTOCOL

## **⚠️ CRITICAL: READ BEFORE ANY OPERATIONS**

**ALL AI AGENTS MUST READ AND FOLLOW THESE GUIDELINES BEFORE PERFORMING ANY OPERATIONS**

---

## **📍 STEP 1: VERIFY WORKING DIRECTORY**

**ALWAY<PERSON> run this command FIRST:**
```bash
pwd && echo "Expected: /c/Users/<USER>/Documents/Augment/social-nft-platform-v2"
```

**If not in correct directory, STOP and navigate there first.**

---

## **📋 STEP 2: READ EXISTING GUIDELINES**

**MANDATORY: Read these files in order:**

1. **`/docs/guidelines/AI_AGENT_GUIDELINES.md`** - Service creation patterns
2. **`/docs/guidelines/ANTI_DUPLICATION_RULES.md`** - Prevent duplicate directories
3. **`/docs/guidelines/SERVICE_TEMPLATE_GUIDELINES.md`** - Service templates
4. **`/docs/guidelines/WORKING_DIRECTORY_PROTOCOL.md`** - Path protocols

---

## **🏗️ STEP 3: USE EXISTING STRUCTURE**

**✅ ALWAYS USE EXISTING DIRECTORIES:**
- **Scripts**: `/tools/scripts/` (NOT `/scripts/`)
- **Guidelines**: `/docs/guidelines/` (NOT `/docs/`)
- **Services**: `/services/[service-name]/`
- **Frontend**: `/frontend/` or `/frontend-fresh/`

**❌ NEVER CREATE:**
- Duplicate directories
- New top-level directories without checking existing structure
- Nested duplicates like `/social-nft-platform/social-nft-platform-v2/`

---

## **🎯 STEP 4: ABSOLUTE PATHS ONLY**

**✅ ALWAYS USE:**
```bash
# In save-file operations
path: "C:/Users/<USER>/Documents/Augment/social-nft-platform-v2/docs/guidelines/filename.md"

# In launch-process operations
cwd: "/c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/user-service"
```

**❌ NEVER USE:**
```bash
# Relative paths that create duplicates
path: "social-nft-platform-v2/docs/filename.md"
```

---

## **🔧 STEP 5: VERIFICATION CHECKLIST**

**Before ANY operation, verify:**
- [ ] Current working directory is correct
- [ ] Target directory exists in established structure
- [ ] No duplicate directories will be created
- [ ] Using absolute paths in file operations
- [ ] Following existing guidelines and patterns

---

## **🚨 EMERGENCY PROTOCOL**

**If you create wrong directories or files:**
1. **STOP** immediately
2. **Clean up** wrong directories/files
3. **Re-read** this protocol
4. **Start over** with correct paths

---

## **📊 ESTABLISHED STRUCTURE**

```
social-nft-platform-v2/
├── docs/
│   └── guidelines/          ← Guidelines go here
├── tools/
│   └── scripts/            ← Scripts go here
├── services/
│   ├── api-gateway/        ← Working services
│   ├── user-service/
│   └── profile-analysis-service/
├── frontend/               ← Original frontend
├── frontend-fresh/         ← Working frontend
└── .vscode/               ← VS Code settings
```

---

**🎯 SUCCESS = Following existing structure + No duplicates + Absolute paths**
