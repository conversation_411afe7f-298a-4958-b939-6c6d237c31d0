'use client'

import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService, User, RegisterData, LoginData } from '@/services/authService';

interface AuthContextType {
  user: User | null;
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  connectTwitter: () => void;
  handleTwitterCallback: (token: string, userData: any) => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Debug user state changes
  useEffect(() => {
    console.log('🔄 AuthContext: User state changed:', user ? `${user.username} (${user.email})` : 'null');
    console.log('🔄 AuthContext: isAuthenticated is now:', !!user);
  }, [user]);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔄 AuthContext: Starting initialization...');
        const token = authService.getToken();
        console.log('🔄 AuthContext: Token exists:', !!token);

        if (authService.isAuthenticated()) {
          console.log('🔄 AuthContext: Token found, checking if it\'s a mock token...');

          // Check if it's a mock token from Twitter OAuth
          if (token && token.startsWith('mock_jwt_token_')) {
            console.log('🔄 AuthContext: Mock token detected, using stored user data...');
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              setUser(userData);
              console.log('✅ AuthContext: Mock user restored:', userData.username);
            }
          } else {
            console.log('🔄 AuthContext: Real token found, fetching profile...');
            const userData = await authService.getProfile();
            console.log('✅ AuthContext: Profile fetched successfully:', userData.username);
            setUser(userData);
            console.log('✅ AuthContext: User state set, isAuthenticated should be:', !!userData);
          }
        } else {
          console.log('ℹ️ AuthContext: No valid token found');

          // Check if we have user data in localStorage (from Twitter OAuth)
          const storedUser = localStorage.getItem('user');
          if (storedUser) {
            console.log('🔄 AuthContext: Found stored user data, using it...');
            const userData = JSON.parse(storedUser);
            setUser(userData);
            console.log('✅ AuthContext: User restored from localStorage:', userData.username);
          }
        }
      } catch (error) {
        console.error('❌ AuthContext: Failed to initialize auth:', error);
        console.log('🔄 AuthContext: Clearing invalid token...');
        authService.logout();
      } finally {
        console.log('✅ AuthContext: Initialization complete, setting isLoading to false');
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (data: LoginData) => {
    try {
      setIsLoading(true);
      const response = await authService.login(data);
      setUser(response.user);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true);
      const response = await authService.register(data);
      setUser(response.user);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  const connectTwitter = () => {
    // Redirect to Twitter OAuth endpoint (directly to Profile Analysis Service)
    const authUrl = `http://localhost:3002/api/auth/twitter/login`;
    console.log('🐦 Redirecting to Twitter OAuth:', authUrl);
    window.location.href = authUrl;
  };

  const handleTwitterCallback = async (token: string, userData: any) => {
    console.log('🚨 AUTHCONTEXT: handleTwitterCallback CALLED!');
    console.log('🐦 Twitter OAuth Callback - Processing user data:', userData);

    // SIMPLE APPROACH: Just set the user directly with mock data for now
    console.log('🔄 SIMPLE AUTH: Setting user directly...');
    const mockUser = {
      id: userData.twitterId,
      username: userData.username,
      email: userData.email,
      role: 'user' as const,
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store a simple token (use the same key as authService expects)
    const mockToken = 'mock_jwt_token_' + Date.now();
    localStorage.setItem('auth_token', mockToken);
    localStorage.setItem('user', JSON.stringify(mockUser));
    console.log('🔄 SIMPLE AUTH: Stored token and user in localStorage');
    console.log('🔄 SIMPLE AUTH: Token key: auth_token, value:', mockToken.substring(0, 20) + '...');
    console.log('🔄 SIMPLE AUTH: User key: user, value:', JSON.stringify(mockUser).substring(0, 50) + '...');

    setUser(mockUser);
    console.log('✅ SIMPLE AUTH: User set successfully:', mockUser.username);

    // Enable redirect now that authentication is working
    setTimeout(() => {
      console.log('🔄 Redirecting to dashboard...');
      window.location.href = '/dashboard';
    }, 1000);

    return; // Skip complex authentication for now

    try {
      console.log('🔄 handleTwitterCallback: Setting isLoading to true...');
      setIsLoading(true);
      console.log('✅ handleTwitterCallback: isLoading set to true');

      // Register or login the user through the User Service
      // Only send fields that the User Service RegisterDto accepts
      const authData = {
        username: userData.username,
        email: userData.email,
        password: 'twitter_oauth_' + userData.twitterId, // Generate a password for Twitter users
        twitterUsername: userData.twitterUsername || userData.username
      };

      console.log('📤 Sending registration data:', authData);

      console.log('🔄 Attempting to register Twitter user with User Service...');
      console.log('🔄 About to call authService.register...');

      try {
        // Try to register the user first
        console.log('🔄 Calling authService.register with data:', authData);
        const response = await authService.register(authData);
        console.log('🔄 authService.register returned:', response);
        console.log('✅ Twitter user registered successfully:', response.user.username);
        console.log('✅ Registration response:', response);
        console.log('🔄 Setting user state from registration...');
        setUser(response.user);
        console.log('✅ User state set from registration');
      } catch (registerError: any) {
        console.log('ℹ️ Registration failed (user might exist), trying login...');
        console.log('🔄 Registration error details:', registerError);

        // If registration fails (user exists), try to login
        try {
          console.log('🔄 About to call authService.login...');
          console.log('🔄 Login data:', {
            email: userData.email,
            password: 'twitter_oauth_' + userData.twitterId
          });

          const loginResponse = await authService.login({
            email: userData.email,
            password: 'twitter_oauth_' + userData.twitterId
          });

          console.log('🎉 LOGIN SUCCESS! authService.login returned:', loginResponse);
          console.log('✅ Twitter user logged in successfully:', loginResponse.user.username);
          console.log('✅ Login response:', loginResponse);
          console.log('🔄 Setting user state from login...');
          setUser(loginResponse.user);
          console.log('✅ User state set from login');
        } catch (loginError) {
          console.error('❌ Both registration and login failed:', loginError);
          throw new Error('Failed to authenticate Twitter user');
        }
      }

      console.log('✅ Twitter OAuth - User authenticated successfully');

      // TEMPORARILY DISABLE REDIRECT FOR DEBUGGING
      console.log('🔄 DEBUGGING: Redirect disabled to see any errors');
      console.log('🔄 Would redirect to: /dashboard');

      // Uncomment below when debugging is complete:
      // setTimeout(() => {
      //   const redirectPath = '/dashboard';
      //   console.log('🔄 Redirecting to:', redirectPath);
      //   window.location.href = redirectPath;
      // }, 500);

    } catch (error: any) {
      console.error('❌ Twitter OAuth processing error:', error);
      console.error('❌ Error details:', {
        message: error?.message,
        stack: error?.stack,
        response: error?.response?.data,
        status: error?.response?.status
      });
      setIsLoading(false);

      // TEMPORARILY DISABLE ERROR REDIRECT FOR DEBUGGING
      console.log('🔄 DEBUGGING: Error redirect disabled to see full error');
      console.log('🔄 Would redirect to: /auth/login?error=twitter_auth_failed');

      // Uncomment below when debugging is complete:
      // window.location.href = '/auth/login?error=twitter_auth_failed';
    }
  };

  const value = {
    user,
    login,
    register,
    logout,
    connectTwitter,
    handleTwitterCallback,
    isLoading,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
