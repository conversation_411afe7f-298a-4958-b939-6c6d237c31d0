'use client'

import { useAuth } from '@/contexts/auth-context'
import { useState, useEffect } from 'react'

interface UserStats {
  totalNfts: number
  totalCampaigns: number
  totalEarnings: number
  followerCount?: number
  followingCount?: number
  tweetCount?: number
}

export function EnhancedUserProfile() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const [userStats, setUserStats] = useState<UserStats>({
    totalNfts: 0,
    totalCampaigns: 0,
    totalEarnings: 0
  })

  useEffect(() => {
    if (user) {
      // Load user statistics
      setUserStats({
        totalNfts: user.totalNfts || 0,
        totalCampaigns: 0, // Will be loaded from API
        totalEarnings: 0, // Will be loaded from API
        followerCount: (user as any).followerCount,
        followingCount: (user as any).followingCount,
        tweetCount: (user as any).tweetCount
      })
    }
  }, [user])

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="rounded-full bg-gray-300 h-16 w-16"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-300 rounded w-32"></div>
            <div className="h-3 bg-gray-300 rounded w-24"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please log in to view your profile</p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-start space-x-4 mb-6">
        <img
          src={user.profileImage || 'https://via.placeholder.com/150'}
          alt={user.displayName || user.username}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div className="flex-1">
          <h2 className="text-xl font-bold text-gray-900">
            {user.displayName || user.username}
          </h2>
          <p className="text-gray-600">@{user.username}</p>
          <p className="text-sm text-gray-500">{user.email}</p>
          
          {/* Enhanced User Info */}
          <div className="flex items-center space-x-4 mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
            }`}>
              {user.role}
            </span>
            
            {user.isEmailVerified && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ✓ Verified
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-900">{userStats.totalNfts}</div>
          <div className="text-sm text-gray-600">NFTs Created</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-900">{userStats.totalCampaigns}</div>
          <div className="text-sm text-gray-600">Campaigns</div>
        </div>
        
        {userStats.followerCount && (
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {userStats.followerCount.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Followers</div>
          </div>
        )}
        
        {userStats.tweetCount && (
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {userStats.tweetCount.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Tweets</div>
          </div>
        )}
      </div>

      {/* Account Details */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Account Details</h3>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Member since</span>
            <span className="text-sm text-gray-900">
              {new Date(user.createdAt).toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Last updated</span>
            <span className="text-sm text-gray-900">
              {new Date(user.lastUpdated || user.createdAt).toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Account ID</span>
            <span className="text-sm text-gray-900 font-mono">
              {user.id.substring(0, 8)}...
            </span>
          </div>
        </div>
      </div>

      {/* Twitter Integration Status */}
      <div className="border-t border-gray-200 pt-6 mt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Twitter Integration</h3>
        
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">Connected to Twitter</div>
              <div className="text-sm text-gray-600">@{user.username}</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ✓ Active
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
