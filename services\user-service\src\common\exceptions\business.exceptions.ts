import { HttpException, HttpStatus } from '@nestjs/common';

export class BusinessException extends HttpException {
  constructor(message: string, code?: string) {
    super({ message, code: code || 'BUSINESS_ERROR' }, HttpStatus.BAD_REQUEST);
  }
}

export class UserNotFoundException extends HttpException {
  constructor(identifier: string) {
    super(
      { message: `User not found: ${identifier}`, code: 'USER_NOT_FOUND' },
      HttpStatus.NOT_FOUND,
    );
  }
}

export class UserAlreadyExistsException extends HttpException {
  constructor(field: string, value: string) {
    super(
      { message: `User with ${field} '${value}' already exists`, code: 'USER_ALREADY_EXISTS' },
      HttpStatus.CONFLICT,
    );
  }
}

export class ValidationException extends HttpException {
  constructor(message: string) {
    super(
      { message, code: 'VALIDATION_ERROR' },
      HttpStatus.BAD_REQUEST,
    );
  }
}
