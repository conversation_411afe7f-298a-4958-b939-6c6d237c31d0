// Enterprise NFT Generation Module
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';

// Controllers
import { NftCommandController } from './controllers/nft-command.controller';
import { NftQueryController } from './controllers/nft-query.controller';
import { HealthController } from './controllers/health.controller';

// Services
import { NftCommandService } from './services/nft-command.service';
import { NftQueryService } from './services/nft-query.service';
import { ProfileNftGenerationService } from './services/profile-nft-generation.service';
import { ExternalStorageService } from './services/external-storage.service';
import { NFTImageGeneratorService } from './services/nft-image-generator.service';

// Shared Services
import { PrismaService } from './shared/prisma.service';
import { AuditService } from './shared/audit.service';
import { EventService } from './shared/event.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),

    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '7d' }
    }),

    HttpModule.register({
      timeout: 10000,
      maxRedirects: 5,
    }),

    TerminusModule
  ],
  controllers: [
    NftCommandController,
    NftQueryController,
    HealthController
  ],
  providers: [
    // Core Services
    NftCommandService,
    NftQueryService,
    ProfileNftGenerationService,
    ExternalStorageService,
    NFTImageGeneratorService,

    // Shared Services
    PrismaService,
    AuditService,
    EventService
  ],
  exports: [
    // Export services for use in other modules
    NftCommandService,
    NftQueryService,
    ProfileNftGenerationService,
    PrismaService,
    AuditService,
    EventService
  ]
})
export class EnterpriseModule {}
