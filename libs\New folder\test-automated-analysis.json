{"campaignId": "0a3cfd03-52a6-4110-9811-64319e2334a6", "userId": "test-user-456", "analysisId": "9a705ef4-d285-419f-8a15-311e14d345de", "analysisResults": {"profile": {"handle": "testuser_crypto", "followerCount": 352986, "followingCount": 2419, "tweetCount": 14820, "accountAge": 2336}, "metrics": {"engagementRate": 4.25, "averageLikes": 634, "averageRetweets": 84, "averageReplies": 51}, "sentiment": {"overallScore": 0.58, "positive": "0.77", "neutral": "0.98", "negative": "0.25"}, "nftRecommendation": {"rarity": "legendary", "traits": ["High Engagement", "Positive Influence", "Tech Savvy", "Trendsetter"], "score": 15}, "analyzedAt": "2025-05-26T15:18:22.532Z"}, "options": {"updateRarity": true, "triggerNftGeneration": false, "notifyUser": false}}