import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class TwitterUserService {
  private readonly logger = new Logger(TwitterUserService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Find or create a Twitter user in the database
   */
  async findOrCreateTwitterUser(twitterData: any): Promise<any> {
    try {
      this.logger.log('🔍 Finding or creating Twitter user');
      this.logger.log(`📝 Twitter data: ${JSON.stringify(twitterData).substring(0, 200)}...`);

      // Check if user exists by provider ID
      let user = await this.prisma.userCommand.findFirst({
        where: { 
          provider: 'twitter',
          providerId: twitterData.id 
        }
      });

      if (user) {
        this.logger.log(`♻️ Existing user found: ${user.username}`);
        
        // Update existing user data
        user = await this.prisma.userCommand.update({
          where: { id: user.id },
          data: {
            displayName: twitterData.displayName,
            profileImage: twitterData.profileImage,
            twitterUsername: twitterData.username,
            bio: twitterData.description,
            location: twitterData.location,
            providerData: {
              followerCount: twitterData.followerCount,
              followingCount: twitterData.followingCount,
              tweetCount: twitterData.tweetCount,
              isVerified: twitterData.isVerified,
              lastUpdated: new Date().toISOString()
            },
            lastLoginAt: new Date(),
            updatedAt: new Date(),
            updatedBy: 'twitter-auth-system'
          }
        });

        this.logger.log(`✅ User updated successfully: ${user.username}`);
      } else {
        this.logger.log(`🆕 Creating new user for Twitter ID: ${twitterData.id}`);
        
        // Create new user
        user = await this.prisma.userCommand.create({
          data: {
            email: twitterData.email,
            username: twitterData.username,
            password: 'oauth_user', // OAuth users don't have passwords
            displayName: twitterData.displayName,
            profileImage: twitterData.profileImage,
            
            // Provider information
            provider: 'twitter',
            providerId: twitterData.id,
            providerData: {
              followerCount: twitterData.followerCount,
              followingCount: twitterData.followingCount,
              tweetCount: twitterData.tweetCount,
              isVerified: twitterData.isVerified,
              description: twitterData.description,
              location: twitterData.location,
              createdAt: new Date().toISOString()
            },
            
            // Twitter-specific data
            twitterUsername: twitterData.username,
            twitterId: twitterData.id,
            bio: twitterData.description,
            location: twitterData.location,
            
            // User status
            isActive: true,
            isEmailVerified: false, // Twitter OAuth doesn't verify email
            role: 'user',
            
            // Timestamps
            lastLoginAt: new Date(),
            createdBy: 'twitter-auth-system',
            updatedBy: 'twitter-auth-system'
          }
        });

        this.logger.log(`✅ New user created successfully: ${user.username}`);
      }

      // Also update the query model for fast reads
      await this.updateUserQueryModel(user);

      return user;

    } catch (error) {
      this.logger.error('❌ Error finding or creating Twitter user:', error);
      throw new Error(`Failed to find or create Twitter user: ${error.message}`);
    }
  }

  /**
   * Update the user query model (CQRS read side)
   */
  private async updateUserQueryModel(user: any): Promise<void> {
    try {
      const providerData = user.providerData || {};
      
      await this.prisma.userQuery.upsert({
        where: { id: user.id },
        update: {
          username: user.username,
          email: user.email,
          displayName: user.displayName,
          twitterUsername: user.twitterUsername,
          twitterFollowers: providerData.followerCount,
          twitterVerified: providerData.isVerified,
          role: user.role,
          isActive: user.isActive,
          isEmailVerified: user.isEmailVerified,
          bio: user.bio,
          profileImage: user.profileImage,
          location: user.location,
          isProfileComplete: this.isProfileComplete(user),
          lastActivityAt: new Date(),
          lastUpdated: new Date()
        },
        create: {
          id: user.id,
          username: user.username,
          email: user.email,
          displayName: user.displayName,
          twitterUsername: user.twitterUsername,
          twitterFollowers: providerData.followerCount,
          twitterVerified: providerData.isVerified,
          role: user.role,
          isActive: user.isActive,
          isEmailVerified: user.isEmailVerified,
          bio: user.bio,
          profileImage: user.profileImage,
          location: user.location,
          isProfileComplete: this.isProfileComplete(user),
          lastActivityAt: new Date(),
          createdAt: user.createdAt,
          lastUpdated: new Date()
        }
      });

      this.logger.log('✅ User query model updated');
    } catch (error) {
      this.logger.error('❌ Error updating user query model:', error);
      // Don't throw here - query model update is not critical
    }
  }

  /**
   * Check if user profile is complete
   */
  private isProfileComplete(user: any): boolean {
    return !!(
      user.displayName &&
      user.bio &&
      user.profileImage &&
      user.location
    );
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<any> {
    try {
      const user = await this.prisma.userCommand.findUnique({
        where: { id }
      });
      
      if (user) {
        this.logger.log(`✅ User found by ID: ${user.username}`);
      } else {
        this.logger.log(`❌ User not found by ID: ${id}`);
      }
      
      return user;
    } catch (error) {
      this.logger.error('❌ Error finding user by ID:', error);
      throw new Error(`Failed to find user by ID: ${error.message}`);
    }
  }

  /**
   * Find user by provider ID
   */
  async findByProviderId(providerId: string, provider: string = 'twitter'): Promise<any> {
    try {
      const user = await this.prisma.userCommand.findFirst({
        where: { 
          provider,
          providerId 
        }
      });
      
      if (user) {
        this.logger.log(`✅ User found by provider ID: ${user.username}`);
      } else {
        this.logger.log(`❌ User not found by provider ID: ${providerId}`);
      }
      
      return user;
    } catch (error) {
      this.logger.error('❌ Error finding user by provider ID:', error);
      throw new Error(`Failed to find user by provider ID: ${error.message}`);
    }
  }

  /**
   * Update user last login time
   */
  async updateLastLogin(userId: string): Promise<void> {
    try {
      await this.prisma.userCommand.update({
        where: { id: userId },
        data: { 
          lastLoginAt: new Date(),
          updatedAt: new Date(),
          updatedBy: 'auth-system'
        }
      });
      
      this.logger.log(`✅ Last login updated for user: ${userId}`);
    } catch (error) {
      this.logger.error('❌ Error updating last login:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<any> {
    try {
      const totalUsers = await this.prisma.userCommand.count();
      const activeUsers = await this.prisma.userCommand.count({
        where: { isActive: true }
      });
      const twitterUsers = await this.prisma.userCommand.count({
        where: { provider: 'twitter' }
      });
      
      return {
        totalUsers,
        activeUsers,
        twitterUsers,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('❌ Error getting user stats:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        twitterUsers: 0,
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}
