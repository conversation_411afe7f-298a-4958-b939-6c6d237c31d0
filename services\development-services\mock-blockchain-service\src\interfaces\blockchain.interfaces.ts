// Blockchain Service Interfaces for Mock Implementation

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  attributes: Array<{
    trait_type: string;
    value: string | number;
  }>;
  external_url?: string;
  animation_url?: string;
}

export interface NFTMintRequest {
  to: string;
  metadata: NFTMetadata;
  campaignId?: string;
  userId?: string;
}

export interface NFTMintResult {
  success: boolean;
  tokenId?: string;
  transactionHash?: string;
  contractAddress?: string;
  error?: string;
  gasUsed?: number;
  gasPrice?: string;
}

export interface WalletInfo {
  address: string;
  balance: string;
  nftCount: number;
  transactions: number;
}
