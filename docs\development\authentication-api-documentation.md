# Authentication API Documentation

## 🔐 **Phase 1: Core Authentication Integration**

**Date:** 2025-05-29  
**Services:** API Gateway (3010) → User Service (3001)  
**Status:** Ready for frontend integration  

### **📋 AVAILABLE AUTHENTICATION ENDPOINTS**

#### **1. User Registration**
```typescript
POST http://localhost:3010/users/register
Content-Type: application/json

// Request Body
{
  "username": string,     // 3-50 chars, alphanumeric + underscore
  "email": string,        // Valid email, max 100 chars
  "password": string,     // Min 8 chars, max 128 chars
  "twitterUsername"?: string  // Optional, max 50 chars
}

// Success Response (201)
{
  "id": "uuid",
  "username": "string",
  "email": "string",
  "twitterUsername": "string",
  "role": "user",
  "isActive": true,
  "isEmailVerified": false,
  "createdAt": "ISO date",
  "updatedAt": "ISO date"
}

// Error Responses
409: User already exists
400: Invalid input data
502: Service unavailable
```

#### **2. User Login**
```typescript
POST http://localhost:3010/users/login
Content-Type: application/json

// Request Body
{
  "email": string,        // Valid email
  "password": string      // User password
}

// Success Response (200)
{
  "access_token": "JWT_TOKEN",
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "twitterUsername": "string",
    "role": "user",
    "isActive": true,
    "isEmailVerified": false
  }
}

// Error Responses
401: Invalid credentials
400: Invalid input data
502: Service unavailable
```

#### **3. Get User Profile**
```typescript
GET http://localhost:3010/users/profile
Authorization: Bearer JWT_TOKEN

// Success Response (200)
{
  "id": "uuid",
  "username": "string",
  "email": "string",
  "twitterUsername": "string",
  "role": "user",
  "isActive": true,
  "isEmailVerified": false,
  "createdAt": "ISO date",
  "updatedAt": "ISO date"
}

// Error Responses
401: Unauthorized (invalid/expired token)
404: User not found
502: Service unavailable
```

### **🔧 TYPESCRIPT INTERFACES**

#### **User Interface**
```typescript
export interface User {
  id: string;
  username: string;
  email: string;
  twitterUsername?: string;
  twitterId?: string;
  role: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}
```

#### **Authentication Interfaces**
```typescript
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  twitterUsername?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  user: User;
}

export interface AuthError {
  message: string;
  service: string;
  timestamp: string;
}
```

### **🎯 INTEGRATION REQUIREMENTS**

#### **Frontend Changes Needed:**
1. **Update AuthContext** → Replace mock authentication with real API calls
2. **Implement JWT Management** → Store and manage access tokens securely
3. **Update Auth Service** → Use real API endpoints through API Gateway
4. **Add Error Handling** → Handle all authentication error scenarios
5. **Update Type Definitions** → Use real User interface from backend

#### **API Gateway Routing:**
- **Base URL:** `http://localhost:3010`
- **Register:** `POST /users/register`
- **Login:** `POST /users/login`
- **Profile:** `GET /users/profile`

#### **Authentication Flow:**
1. **Registration:** User fills form → API call → Success/Error handling
2. **Login:** User credentials → API call → JWT storage → User context update
3. **Profile:** JWT token → API call → User data retrieval
4. **Logout:** Clear JWT token → Reset user context

### **🔐 SECURITY CONSIDERATIONS**

#### **JWT Token Management:**
- **Storage:** Use secure storage (httpOnly cookies or secure localStorage)
- **Expiration:** Handle token expiration and refresh
- **Headers:** Include Bearer token in all authenticated requests
- **Cleanup:** Clear tokens on logout

#### **Error Handling:**
- **Network Errors:** Handle service unavailable scenarios
- **Validation Errors:** Display user-friendly error messages
- **Authentication Errors:** Redirect to login on 401 errors
- **Service Errors:** Graceful fallback for backend issues

### **📋 NEXT IMPLEMENTATION STEPS**

1. **Update AuthContext** → Replace mock with real API integration
2. **Create Auth Service** → Implement API calls with proper error handling
3. **Update Login/Register Pages** → Connect to real authentication
4. **Test Authentication Flow** → Verify complete workflow
5. **Add JWT Management** → Secure token storage and usage

**Ready to begin implementation with Template-First approach!** 🚀
