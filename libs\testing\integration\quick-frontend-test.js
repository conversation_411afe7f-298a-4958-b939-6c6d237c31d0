// Quick Frontend Integration Test
// Simple test to verify Project Service is ready for frontend integration

const axios = require('axios');

const PROJECT_SERVICE_URL = 'http://localhost:3005';

async function quickTest() {
  console.log('🚀 Quick Frontend Integration Test\n');
  
  try {
    // 1. Health Check
    console.log('1️⃣ Checking service health...');
    const health = await axios.get(`${PROJECT_SERVICE_URL}/health`);
    console.log('✅ Service is healthy:', health.data);
    
    // 2. Create a simple project
    console.log('\n2️⃣ Creating a test project...');
    const projectData = {
      name: 'Frontend Test Project',
      description: 'Quick test for frontend integration',
      ownerId: 'frontend-test-user',
      category: 'Testing',
      blockchainNetwork: 'ethereum',
      isPublic: true,
      allowParticipation: true,
      maxParticipants: 100,
      analysisConfiguration: {
        fixedParameters: {
          hasBio: { weight: 10, enabled: true },
          hasAvatar: { weight: 5, enabled: true }
        },
        variableParameters: {
          activityLevel: { weight: 15, enabled: true }
        },
        updateFrequencyHours: 24
      },
      nftConfiguration: {
        scoreThresholds: { common: 0, rare: 50, legendary: 80 },
        design: {
          theme: 'modern',
          style: 'clean',
          mainColor: '#00ff88',
          fixedElements: ['background', 'border']
        },
        blockchainUpdatePolicy: 'immediate'
      }
    };
    
    const createResponse = await axios.post(`${PROJECT_SERVICE_URL}/enterprise/projects`, projectData);
    const project = createResponse.data.data;
    console.log('✅ Project created:', { id: project.id, name: project.name });
    
    // 3. Read the project back
    console.log('\n3️⃣ Reading project back...');
    const readResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects/${project.id}`);
    console.log('✅ Project retrieved:', { id: readResponse.data.data.id, status: readResponse.data.data.status });
    
    // 4. List projects
    console.log('\n4️⃣ Listing projects...');
    const listResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects?page=1&limit=5`);
    console.log('✅ Projects listed:', { count: listResponse.data.data.projects.length });
    
    // 5. Create a campaign
    console.log('\n5️⃣ Creating a test campaign...');
    const campaignData = {
      projectId: project.id,
      name: 'Frontend Test Campaign',
      description: 'Quick campaign test',
      campaignType: 'user_acquisition',
      startDate: '2025-07-01T00:00:00Z',
      endDate: '2025-08-01T23:59:59Z',
      timezone: 'UTC',
      maxParticipants: 50,
      minParticipants: 5,
      rewards: {
        nftRewards: {
          common: { probability: 60, bonusPoints: 10 }
        }
      }
    };
    
    const campaignResponse = await axios.post(`${PROJECT_SERVICE_URL}/enterprise/campaigns`, campaignData);
    const campaign = campaignResponse.data.data;
    console.log('✅ Campaign created:', { id: campaign.id, name: campaign.name });
    
    // 6. Cleanup
    console.log('\n6️⃣ Cleaning up...');
    await axios.delete(`${PROJECT_SERVICE_URL}/enterprise/campaigns/${campaign.id}`);
    await axios.delete(`${PROJECT_SERVICE_URL}/enterprise/projects/${project.id}`);
    console.log('✅ Cleanup completed');
    
    console.log('\n🎉 SUCCESS! Project Service is ready for frontend integration!');
    console.log('📚 See FRONTEND_INTEGRATION_GUIDE.md for complete API documentation');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data?.message || error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure Project Service is running on port 3005');
    console.log('2. Check service health: curl http://localhost:3005/health');
    console.log('3. Review the error message above for specific issues');
  }
}

quickTest();
