// Enterprise NFT Query Model (Read Side)
import { IsString, IsOptional, IsBoolean, IsInt, IsN<PERSON>ber, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum NftStatus {
  DRAFT = 'draft',
  GENERATED = 'generated',
  MINTED = 'minted',
  LISTED = 'listed',
  SOLD = 'sold'
}

export class NftQueryDto {
  @ApiProperty({ description: 'NFT ID' })
  id: string;

  @ApiProperty({ description: 'Display name' })
  displayName: string;

  @ApiPropertyOptional({ description: 'Display description' })
  displayDescription?: string;

  @ApiPropertyOptional({ description: 'Thumbnail URL' })
  thumbnailUrl?: string;

  @ApiPropertyOptional({ description: 'Full image URL' })
  fullImageUrl?: string;

  @ApiProperty({ description: 'Owner user ID' })
  userId: string;

  @ApiPropertyOptional({ description: 'Owner username' })
  username?: string;

  @ApiPropertyOptional({ description: 'Project ID' })
  projectId?: string;

  @ApiPropertyOptional({ description: 'Project name' })
  projectName?: string;

  @ApiProperty({ description: 'NFT status' })
  status: NftStatus;

  @ApiPropertyOptional({ description: 'Generation time in milliseconds' })
  generationTime?: number;

  @ApiProperty({ description: 'View count' })
  viewCount: number;

  @ApiProperty({ description: 'Like count' })
  likeCount: number;

  @ApiProperty({ description: 'Is listed for sale' })
  isListed: boolean;

  @ApiPropertyOptional({ description: 'Current price' })
  currentPrice?: string;

  @ApiProperty({ description: 'Popularity score' })
  popularityScore: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last updated' })
  lastUpdated: Date;
}

export class NftQueryFiltersDto {
  @ApiPropertyOptional({ description: 'Filter by user ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: 'Filter by project ID' })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiPropertyOptional({ description: 'Filter by status' })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: 'Filter by listed status' })
  @IsOptional()
  @IsBoolean()
  isListed?: boolean;

  @ApiPropertyOptional({ description: 'Number of items to return', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Number of items to skip', minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  offset?: number = 0;

  @ApiPropertyOptional({ description: 'Sort by field' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: 'Sort order' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class NftQueryResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'NFT data', type: [NftQueryDto] })
  data: {
    nfts: NftQueryDto[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };

  @ApiPropertyOptional({ description: 'Error information' })
  error?: {
    code: string;
    message: string;
    timestamp: string;
  };
}
