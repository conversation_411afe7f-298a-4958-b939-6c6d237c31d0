import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class AppService {
  private readonly services = [
    { name: 'user-service', url: process.env.USER_SERVICE_URL || 'http://localhost:3011', health: '/api/health' },
    { name: 'profile-analysis-service', url: process.env.PROFILE_ANALYSIS_SERVICE_URL || 'http://localhost:3002', health: '/health' },
    { name: 'nft-generation-service', url: process.env.NFT_GENERATION_SERVICE_URL || 'http://localhost:3003', health: '/api/health' },
    { name: 'blockchain-service', url: process.env.BLOCKCHAIN_SERVICE_URL || 'http://localhost:3004', health: '/api/health' },
    { name: 'project-service', url: process.env.PROJECT_SERVICE_URL || 'http://localhost:3005', health: '/health' },
    { name: 'marketplace-service', url: process.env.MARKETPLACE_SERVICE_URL || 'http://localhost:3006', health: '/api/health' },
    { name: 'analytics-service', url: process.env.ANALYTICS_SERVICE_URL || 'http://localhost:3007', health: '/api/health' },
    { name: 'notification-service', url: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3008', health: '/api/health' },
  ];

  getStatus(): string {
    return 'API Gateway is running! 🚀';
  }

  async getHealthCheck() {
    const servicesHealth = await this.checkAllServices();

    return {
      status: 'ok',
      service: 'api-gateway',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      services: servicesHealth,
    };
  }

  async getServicesStatus() {
    const servicesStatus = await this.checkAllServices();

    return {
      gateway: 'healthy',
      timestamp: new Date().toISOString(),
      services: servicesStatus,
      summary: {
        total: servicesStatus.length,
        healthy: servicesStatus.filter(s => s.status === 'healthy').length,
        unhealthy: servicesStatus.filter(s => s.status === 'unhealthy').length,
        unreachable: servicesStatus.filter(s => s.status === 'unreachable').length,
      },
    };
  }

  private async checkAllServices() {
    const servicesStatus = [];

    for (const service of this.services) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${service.url}${service.health}`, {
          timeout: 5000,
        });

        if (response.status === 200) {
          servicesStatus.push({
            name: service.name,
            status: 'healthy',
            url: service.url,
            responseTime: Date.now() - startTime,
            health: response.data,
          });
        } else {
          servicesStatus.push({
            name: service.name,
            status: 'unhealthy',
            url: service.url,
            responseTime: Date.now() - startTime,
            error: `HTTP ${response.status}`,
          });
        }
      } catch (error) {
        servicesStatus.push({
          name: service.name,
          status: 'unreachable',
          url: service.url,
          error: error.message,
        });
      }
    }

    return servicesStatus;
  }
}
