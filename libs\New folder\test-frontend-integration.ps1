# Frontend Integration Testing Script
Write-Host "=== FRONTEND INTEGRATION TESTING ===" -ForegroundColor Cyan
Write-Host "Testing Next.js + Chakra UI + Backend Integration" -ForegroundColor Green

# Test Variables
$frontendUrl = "http://localhost:3000"
$backendUrl = "http://localhost:3011"

# Test Results
$testResults = @()

Write-Host "`nStarting frontend integration tests..." -ForegroundColor Yellow

# Test 1: Frontend Health Check
Write-Host "`n1. Testing Frontend Health..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri $frontendUrl -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ Frontend accessible: $($frontendResponse.StatusCode)" -ForegroundColor Green
    $testResults += "Frontend Health: PASS"
} catch {
    Write-Host "❌ Frontend not accessible: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Frontend Health: FAIL"
}

# Test 2: Backend Health Check
Write-Host "`n2. Testing Backend Health..." -ForegroundColor Yellow
try {
    $backendResponse = Invoke-RestMethod -Uri "$backendUrl/health" -Method Get -TimeoutSec 10
    Write-Host "✅ Backend accessible: $($backendResponse.status)" -ForegroundColor Green
    $testResults += "Backend Health: PASS"
} catch {
    Write-Host "❌ Backend not accessible: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Backend Health: FAIL"
}

# Test 3: Registration API Test
Write-Host "`n3. Testing User Registration API..." -ForegroundColor Yellow
$testUser = @{
    username = "frontendtest"
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $regResponse = Invoke-RestMethod -Uri "$backendUrl/auth/register" -Method Post -Body $testUser -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Registration API working: User created" -ForegroundColor Green
    $testResults += "Registration API: PASS"
    $global:testUserId = $regResponse.user.id
    $global:testToken = $regResponse.accessToken
} catch {
    Write-Host "❌ Registration API failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Registration API: FAIL"
}

# Test Summary
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
foreach ($result in $testResults) {
    if ($result -like "*PASS*") {
        Write-Host "✅ $result" -ForegroundColor Green
    } else {
        Write-Host "❌ $result" -ForegroundColor Red
    }
}

$passCount = ($testResults | Where-Object { $_ -like "*PASS*" }).Count
$totalCount = $testResults.Count
Write-Host "`nTests Passed: $passCount/$totalCount" -ForegroundColor $(if($passCount -eq $totalCount){"Green"}else{"Yellow"})
Write-Host "Frontend Integration Testing Complete!" -ForegroundColor Cyan
