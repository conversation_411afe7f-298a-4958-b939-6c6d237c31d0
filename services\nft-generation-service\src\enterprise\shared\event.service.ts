// Enterprise Event Service
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateNftEventDto } from '../models/audit-event.model';

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(private readonly prisma: PrismaService) {}

  async emitEvent(eventData: CreateNftEventDto & {
    causationId?: string;
  }) {
    try {
      if (!process.env.ENABLE_EVENT_SOURCING || process.env.ENABLE_EVENT_SOURCING === 'false') {
        return;
      }

      const event = await this.prisma.nftEvent.create({
        data: {
          eventType: eventData.eventType,
          eventVersion: '1.0',
          aggregateId: eventData.aggregateId,
          eventData: eventData.eventData,
          correlationId: eventData.correlationId,
          causationId: eventData.causationId,
          userId: eventData.userId
        }
      });

      this.logger.debug(`Event emitted: ${eventData.eventType} for ${eventData.aggregateId}`, eventData.correlationId);
      return event;
    } catch (error) {
      this.logger.error(`Failed to emit event: ${error.message}`, error.stack, eventData.correlationId);
      // Don't throw - event sourcing should not break business operations
    }
  }

  async getEventHistory(aggregateId: string, limit: number = 50) {
    try {
      const events = await this.prisma.nftEvent.findMany({
        where: { aggregateId },
        orderBy: { createdAt: 'asc' },
        take: limit,
        select: {
          id: true,
          eventType: true,
          eventVersion: true,
          eventData: true,
          correlationId: true,
          userId: true,
          createdAt: true
        }
      });

      return events;
    } catch (error) {
      this.logger.error(`Failed to get event history: ${error.message}`, error.stack);
      return [];
    }
  }

  async getEventStats() {
    try {
      const stats = await this.prisma.nftEvent.groupBy({
        by: ['eventType'],
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } }
      });

      const totalEvents = await this.prisma.nftEvent.count();
      const recentEvents = await this.prisma.nftEvent.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });

      return {
        totalEvents,
        recentEvents,
        eventBreakdown: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Failed to get event stats: ${error.message}`, error.stack);
      return {
        totalEvents: 0,
        recentEvents: 0,
        eventBreakdown: [],
        timestamp: new Date().toISOString()
      };
    }
  }

  async replayEvents(aggregateId: string) {
    try {
      const events = await this.getEventHistory(aggregateId);
      
      this.logger.log(`Replaying ${events.length} events for aggregate ${aggregateId}`);
      
      // This would typically rebuild the aggregate state from events
      // For now, we'll just return the events for analysis
      return {
        aggregateId,
        eventCount: events.length,
        events,
        replayedAt: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Failed to replay events: ${error.message}`, error.stack);
      throw error;
    }
  }
}
