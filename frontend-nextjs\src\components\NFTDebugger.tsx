'use client'

import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Code
} from '@chakra-ui/react'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export default function NFTDebugger() {
  const { user } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>(null)

  const checkNFTStorage = () => {
    const storedNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]')
    const currentUserId = user?.id
    const filteredNFTs = storedNFTs.filter((nft: any) => nft.userId === currentUserId)

    const info = {
      currentUser: user,
      currentUserId: currentUserId,
      totalStoredNFTs: storedNFTs.length,
      storedNFTs: storedNFTs,
      filteredNFTs: filteredNFTs,
      userIdMatches: storedNFTs.map((nft: any) => ({
        nftId: nft.id,
        nftUserId: nft.userId,
        currentUserId: currentUserId,
        matches: nft.userId === currentUserId
      }))
    }

    setDebugInfo(info)
    console.log('🔍 NFT Debug Info:', info)
  }

  const createTestNFTWithCurrentUser = () => {
    if (!user?.id) {
      alert('No user logged in!')
      return
    }

    const testNFT = {
      id: `debug-nft-${Date.now()}`,
      name: `Debug Test NFT`,
      description: `Test NFT created for debugging with current user ID`,
      rarity: 'Rare',
      currentScore: 75,
      imageUrl: '',
      metadata: { debug: true },
      userId: user.id, // Use current user ID
      campaignId: 'debug-campaign',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const existingNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]')
    existingNFTs.push(testNFT)
    localStorage.setItem('user_nfts', JSON.stringify(existingNFTs))

    alert(`Test NFT created with user ID: ${user.id}`)
    checkNFTStorage()
  }

  const clearAllNFTs = () => {
    localStorage.removeItem('user_nfts')
    setDebugInfo(null)
    alert('All NFTs cleared!')
  }

  const forceRefreshGallery = () => {
    // Try manual refresh first, fallback to page reload
    if ((window as any).refreshNFTGallery) {
      console.log('🔄 Using manual refresh function')
      ;(window as any).refreshNFTGallery()
    } else {
      console.log('🔄 Manual refresh not available, reloading page')
      window.location.reload()
    }
  }

  const showComponentState = () => {
    // Access React component state for debugging
    console.log('🔍 Checking component state...')
    if ((window as any).nftGalleryState) {
      console.log('📊 Gallery Component State:', (window as any).nftGalleryState)
    } else {
      console.log('❌ Gallery state not available')
    }
  }

  return (
    <Box bg="yellow.50" p={6} borderRadius="lg" border="2px solid" borderColor="yellow.200">
      <VStack gap={4} align="stretch">
        <Heading as="h3" size="md" color="yellow.700">
          🔍 NFT Storage Debugger
        </Heading>

        <HStack gap={2} wrap="wrap">
          <Button size="sm" onClick={checkNFTStorage}>
            Check Storage
          </Button>
          <Button size="sm" colorScheme="green" onClick={createTestNFTWithCurrentUser}>
            Create Test NFT
          </Button>
          <Button size="sm" colorScheme="blue" onClick={forceRefreshGallery}>
            Force Refresh Gallery
          </Button>
          <Button size="sm" colorScheme="purple" onClick={showComponentState}>
            Show State
          </Button>
          <Button size="sm" colorScheme="red" onClick={clearAllNFTs}>
            Clear All NFTs
          </Button>
        </HStack>

        {user && (
          <Box bg="white" p={3} borderRadius="md">
            <Text fontSize="sm" fontWeight="bold">Current User:</Text>
            <Code fontSize="xs">{JSON.stringify({ id: user.id, username: user.username }, null, 2)}</Code>
          </Box>
        )}

        {debugInfo && (
          <Box bg="white" p={3} borderRadius="md">
            <VStack gap={2} align="stretch">
              <HStack>
                <Badge colorScheme="blue">Total NFTs: {debugInfo.totalStoredNFTs}</Badge>
                <Badge colorScheme="green">Filtered NFTs: {debugInfo.filteredNFTs.length}</Badge>
              </HStack>

              <Text fontSize="sm" fontWeight="bold">User ID Matches:</Text>
              {debugInfo.userIdMatches.map((match: any, index: number) => (
                <HStack key={index} fontSize="xs">
                  <Text>NFT {index + 1}:</Text>
                  <Badge colorScheme={match.matches ? 'green' : 'red'}>
                    {match.matches ? 'MATCH' : 'NO MATCH'}
                  </Badge>
                  <Text>({match.nftUserId} vs {match.currentUserId})</Text>
                </HStack>
              ))}
            </VStack>
          </Box>
        )}
      </VStack>
    </Box>
  )
}
