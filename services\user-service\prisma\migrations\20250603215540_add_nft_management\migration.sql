-- CreateTable
CREATE TABLE "nfts" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "rarity" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "blockchain" VARCHAR(20) NOT NULL DEFAULT 'polygon',
    "engagement_score" DOUBLE PRECISION NOT NULL,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "image_url" VARCHAR(500),
    "animation_url" VARCHAR(500),
    "external_url" VARCHAR(500),
    "token_id" VARCHAR(100),
    "contract_address" VARCHAR(100),
    "transaction_hash" VARCHAR(100),
    "owner_address" VARCHAR(100),
    "external_nft_id" VARCHAR(100),
    "generation_params" JSONB NOT NULL DEFAULT '{}',
    "additional_metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,
    "minted_at" TIMESTAMP(3),
    "last_transferred_at" TIMESTAMP(3),

    CONSTRAINT "nfts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nft_transfers" (
    "id" TEXT NOT NULL,
    "nft_id" TEXT NOT NULL,
    "from_address" VARCHAR(100) NOT NULL,
    "to_address" VARCHAR(100) NOT NULL,
    "transaction_hash" VARCHAR(100) NOT NULL,
    "block_number" INTEGER,
    "gas_used" INTEGER,
    "gas_price" VARCHAR(50),
    "transfer_reason" VARCHAR(255),
    "transfer_type" VARCHAR(50) NOT NULL DEFAULT 'user_transfer',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "transferred_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "initiated_by" TEXT,

    CONSTRAINT "nft_transfers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "nfts_user_id_idx" ON "nfts"("user_id");

-- CreateIndex
CREATE INDEX "nfts_campaign_id_idx" ON "nfts"("campaign_id");

-- CreateIndex
CREATE INDEX "nfts_rarity_idx" ON "nfts"("rarity");

-- CreateIndex
CREATE INDEX "nfts_status_idx" ON "nfts"("status");

-- CreateIndex
CREATE INDEX "nfts_blockchain_idx" ON "nfts"("blockchain");

-- CreateIndex
CREATE INDEX "nfts_token_id_idx" ON "nfts"("token_id");

-- CreateIndex
CREATE INDEX "nfts_contract_address_idx" ON "nfts"("contract_address");

-- CreateIndex
CREATE INDEX "nfts_owner_address_idx" ON "nfts"("owner_address");

-- CreateIndex
CREATE INDEX "nfts_created_at_idx" ON "nfts"("created_at");

-- CreateIndex
CREATE INDEX "nft_transfers_nft_id_idx" ON "nft_transfers"("nft_id");

-- CreateIndex
CREATE INDEX "nft_transfers_from_address_idx" ON "nft_transfers"("from_address");

-- CreateIndex
CREATE INDEX "nft_transfers_to_address_idx" ON "nft_transfers"("to_address");

-- CreateIndex
CREATE INDEX "nft_transfers_transaction_hash_idx" ON "nft_transfers"("transaction_hash");

-- CreateIndex
CREATE INDEX "nft_transfers_transferred_at_idx" ON "nft_transfers"("transferred_at");

-- AddForeignKey
ALTER TABLE "nfts" ADD CONSTRAINT "nfts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nfts" ADD CONSTRAINT "nfts_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nft_transfers" ADD CONSTRAINT "nft_transfers_nft_id_fkey" FOREIGN KEY ("nft_id") REFERENCES "nfts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
