# Social NFT Platform - Service Startup Guide

## 🚀 COMPLETE PLATFORM STARTUP INSTRUCTIONS

This guide provides step-by-step instructions to start all 9 services of the Social NFT Platform.

## 📋 Prerequisites

### **System Requirements**
- Node.js 18+ installed
- PostgreSQL 13+ running on localhost:5432
- Git for version control
- 8GB+ RAM recommended

### **Database Setup**
Ensure PostgreSQL is running with these credentials:
```
Host: localhost
Port: 5432
Username: postgres
Password: 1111
```

All databases are already created:
- social_nft_users
- profile_analysis_db
- nft_generation_db
- blockchain_service_db
- project_service_db
- marketplace_service_db
- notification_service_db
- analytics_service_db

## 🔄 SERVICE STARTUP SEQUENCE

### **Option 1: Manual Startup (Recommended for Development)**

Open 9 separate terminal windows and run each service:

#### **Terminal 1: User Service (Port 3001)**
```bash
cd services/user-service
npm run start:dev
```

#### **Terminal 2: Profile Analysis Service (Port 3002)**
```bash
cd services/profile-analysis-service
npm run start:dev
```

#### **Terminal 3: NFT Generation Service (Port 3004)**
```bash
cd services/nft-generation-service
npm run start:dev
```

#### **Terminal 4: Blockchain Service (Port 3005)**
```bash
cd services/blockchain-service
npm run start:dev
```

#### **Terminal 5: Project Service (Port 3006)**
```bash
cd services/project-service
npm run start:dev
```

#### **Terminal 6: Marketplace Service (Port 3007)**
```bash
cd services/marketplace-service
npm run start:dev
```

#### **Terminal 7: Notification Service (Port 3008)**
```bash
cd services/notification-service
npm run start:dev
```

#### **Terminal 8: Analytics Service (Port 3009)**
```bash
cd services/analytics-service
npm run start:dev
```

#### **Terminal 9: API Gateway (Port 3010)**
```bash
cd services/api-gateway
npm run start:dev
```

### **Option 2: Background Startup**

Start all services in background mode:

```bash
# Start all services
cd services/user-service && npm run start &
cd services/profile-analysis-service && npm run start &
cd services/nft-generation-service && npm run start &
cd services/blockchain-service && npm run start &
cd services/project-service && npm run start &
cd services/marketplace-service && npm run start &
cd services/notification-service && npm run start &
cd services/analytics-service && npm run start &
cd services/api-gateway && npm run start &
```

## ✅ HEALTH CHECK VERIFICATION

After starting all services, verify they're running:

### **Quick Health Check Script**
```bash
echo "=== SOCIAL NFT PLATFORM HEALTH CHECK ==="
echo "User Service (3001):" && curl -s http://localhost:3001/health | grep -o '"service":"[^"]*"'
echo "Profile Analysis (3002):" && curl -s http://localhost:3002/health | grep -o '"service":"[^"]*"'
echo "NFT Generation (3004):" && curl -s http://localhost:3004/api/health | grep -o '"service":"[^"]*"'
echo "Blockchain Service (3005):" && curl -s http://localhost:3005/api/health | grep -o '"service":"[^"]*"'
echo "Project Service (3006):" && curl -s http://localhost:3006/health | grep -o '"service":"[^"]*"'
echo "Marketplace Service (3007):" && curl -s http://localhost:3007/api/health | grep -o '"service":"[^"]*"'
echo "Notification Service (3008):" && curl -s http://localhost:3008/api/health | grep -o '"service":"[^"]*"'
echo "Analytics Service (3009):" && curl -s http://localhost:3009/api/health | grep -o '"service":"[^"]*"'
echo "API Gateway (3010):" && curl -s http://localhost:3010/api/health | grep -o '"service":"[^"]*"'
```

### **Expected Output**
```
User Service (3001): "service":"user-service"
Profile Analysis (3002): "service":"profile-analysis-service"
NFT Generation (3004): "service":"nft-generation-service"
Blockchain Service (3005): "service":"blockchain-service"
Project Service (3006): "service":"project-service"
Marketplace Service (3007): "service":"marketplace-service"
Notification Service (3008): "service":"notification-service"
Analytics Service (3009): "service":"analytics-service"
API Gateway (3010): "service":"api-gateway"
```

## 🔧 TROUBLESHOOTING

### **Common Issues**

#### **Port Already in Use**
```bash
# Find process using port
netstat -ano | findstr :3001
# Kill process (Windows)
taskkill /PID <PID> /F
```

#### **Database Connection Issues**
- Verify PostgreSQL is running
- Check credentials in .env files
- Ensure all databases exist

#### **Service Won't Start**
```bash
# Rebuild service
cd services/[service-name]
npm run build
npm run start:dev
```

### **Service Dependencies**
Start services in this order for best results:
1. Database services first (User, Profile Analysis, etc.)
2. Core business services (NFT Generation, Blockchain)
3. API Gateway last

## 📊 MONITORING

### **Service URLs**
- **API Gateway:** http://localhost:3010/api/docs
- **User Service:** http://localhost:3001/api/docs
- **Profile Analysis:** http://localhost:3002/api/docs
- **NFT Generation:** http://localhost:3004/api/docs
- **Blockchain Service:** http://localhost:3005/api/docs
- **Project Service:** http://localhost:3006/api/docs
- **Marketplace:** http://localhost:3007/api/docs
- **Notifications:** http://localhost:3008/api/docs
- **Analytics:** http://localhost:3009/api/docs

### **Platform Overview**
Access comprehensive platform statistics:
```bash
curl http://localhost:3009/api/platform-overview
```

## 🎯 NEXT STEPS

Once all services are running:
1. **Run Integration Tests** - Validate E2E workflows
2. **Access API Documentation** - Explore endpoints
3. **Start Frontend Development** - Connect UI to APIs
4. **Monitor Logs** - Check service health and performance

---

**All services ready for integration testing and frontend development!** 🚀
