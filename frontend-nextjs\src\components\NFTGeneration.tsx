'use client'

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>S<PERSON>ck,
  H<PERSON>tack,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Badge
} from '@chakra-ui/react'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface NFTGenerationProps {
  campaignId: string
  campaignName: string
  onNFTGenerated?: (nft: any) => void
}

export default function NFTGeneration({ campaignId, campaignName, onNFTGenerated }: NFTGenerationProps) {
  const { user } = useAuth()
  const [generating, setGenerating] = useState(false)
  const [generatedNFT, setGeneratedNFT] = useState<any>(null)

  const handleGenerateNFT = async () => {
    if (!user) {
      alert('Authentication Required: Please log in to generate NFTs')
      return
    }

    try {
      setGenerating(true)

      console.log('🎨 Generating NFT for user:', user.username, 'in campaign:', campaignName)

      // Prepare NFT generation data
      const nftData = {
        userId: user.id,
        campaignId: campaignId,
        twitterHandle: user.username,
        currentScore: Math.floor(Math.random() * 100) + 1, // Random score for demo
        analysisData: {
          profile: {
            followerCount: Math.floor(Math.random() * 10000) + 100,
            engagementRate: Math.random() * 10 + 1,
            accountAge: Math.floor(Math.random() * 365) + 30,
            verifiedAccount: Math.random() > 0.7
          },
          metrics: {
            contentQuality: Math.floor(Math.random() * 100),
            activityLevel: Math.floor(Math.random() * 100),
            socialInfluence: Math.floor(Math.random() * 100)
          }
        },
        campaignConfiguration: {
          style: 'modern',
          theme: 'abstract',
          scoreThresholds: {
            common: 50,
            rare: 75,
            legendary: 90
          }
        }
      }

      console.log('📊 NFT Generation Data:', nftData)

      // Use mock NFT generation for reliable demo experience
      console.log('🎨 Using mock NFT generation for demo purposes')

      // Mock NFT generation with realistic rarity distribution
      const weights = [0.6, 0.3, 0.1] // 60% Common, 30% Rare, 10% Legendary
      const random = Math.random()
      let rarity = 'Common'

      if (random < weights[2]) {
        rarity = 'Legendary'
      } else if (random < weights[1] + weights[2]) {
        rarity = 'Rare'
      }

      // Ensure consistent user ID (use string version)
      const consistentUserId = String(user.id)

      const newNFT = {
        id: `mock-nft-${Date.now()}`,
        name: `${campaignName} ${rarity} NFT`,
        description: `A unique ${rarity.toLowerCase()} NFT generated for ${user.username} in ${campaignName} campaign`,
        rarity: rarity,
        currentScore: nftData.currentScore,
        imageUrl: '',
        metadata: nftData.analysisData,
        userId: consistentUserId,
        campaignId: campaignId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      console.log('✅ Mock NFT Generated Successfully:', newNFT)
      console.log('🔍 User ID for NFT:', user.id)
      console.log('🔍 User object:', user)

      // Store NFT in localStorage for gallery display
      const existingNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]')
      existingNFTs.push(newNFT)
      localStorage.setItem('user_nfts', JSON.stringify(existingNFTs))
      console.log('💾 NFT saved to local storage for gallery display')
      console.log('📊 Total NFTs in storage:', existingNFTs.length)
      console.log('🔍 All stored NFTs:', existingNFTs)

      setGeneratedNFT(newNFT)

      alert(`🎉 NFT Generated! Your ${newNFT.rarity} NFT "${newNFT.name}" has been created successfully!`)

      if (onNFTGenerated) {
        onNFTGenerated(newNFT)
      }

    } catch (error: any) {
      console.error('NFT Generation Error:', error)

      alert(`❌ NFT Generation Failed: ${error.message || 'Failed to generate NFT. Please try again.'}`)
    } finally {
      setGenerating(false)
    }
  }

  return (
    <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
      <VStack gap={4} align="stretch">
        <Box textAlign="center">
          <Heading as="h3" size="md" mb={2}>
            🎨 Generate Your NFT
          </Heading>
          <Text color="gray.600">
            Create a unique NFT based on your social media presence for {campaignName}
          </Text>
        </Box>

        {!generatedNFT ? (
          <VStack gap={4}>
            <Text fontSize="sm" color="gray.500" textAlign="center">
              Your NFT will be generated based on:
            </Text>
            <HStack gap={4} justify="center" wrap="wrap">
              <Badge colorScheme="blue">Social Metrics</Badge>
              <Badge colorScheme="green">Engagement Rate</Badge>
              <Badge colorScheme="purple">Content Quality</Badge>
              <Badge colorScheme="orange">Activity Level</Badge>
            </HStack>

            <Button
              colorScheme="blue"
              size="lg"
              onClick={handleGenerateNFT}
              loading={generating}
              disabled={generating || !user}
              w="full"
            >
              {generating ? 'Generating Your NFT...' : 'Generate NFT'}
            </Button>

            {generating && (
              <VStack gap={2}>
                <Spinner size="lg" color="blue.500" />
                <Text fontSize="sm" color="gray.500">
                  Analyzing your social presence and creating your unique NFT...
                </Text>
              </VStack>
            )}
          </VStack>
        ) : (
          <VStack gap={4}>
            <Box textAlign="center" p={4} bg="green.50" borderRadius="md">
              <Text fontSize="lg" fontWeight="bold" color="green.700" mb={2}>
                🎉 NFT Generated Successfully!
              </Text>
              <HStack justify="center" gap={2}>
                <Badge colorScheme="purple" fontSize="md" px={3} py={1}>
                  {generatedNFT.rarity}
                </Badge>
                <Badge colorScheme="blue" fontSize="md" px={3} py={1}>
                  Score: {generatedNFT.currentScore}
                </Badge>
              </HStack>
            </Box>

            <Box bg="gray.100" p={4} borderRadius="md" textAlign="center">
              <Text fontSize="sm" color="gray.600" mb={2}>
                NFT Preview
              </Text>
              <Box
                w="200px"
                h="200px"
                bg="gray.200"
                borderRadius="md"
                display="flex"
                alignItems="center"
                justifyContent="center"
                mx="auto"
              >
                <Text color="gray.500">
                  {generatedNFT.name}
                </Text>
              </Box>
            </Box>

            <Button
              colorScheme="green"
              onClick={() => {
                // Force refresh the gallery page to ensure it loads the latest NFTs
                window.open('/nfts?refresh=' + Date.now(), '_blank')
              }}
              w="full"
            >
              View in NFT Gallery
            </Button>
          </VStack>
        )}
      </VStack>
    </Box>
  )
}
