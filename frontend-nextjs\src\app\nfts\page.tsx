'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  <PERSON>ge,
  Spinner,
  Button,
  Input,
  InputGroup
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import NFTDebugger from '@/components/NFTDebugger'

interface NFT {
  id: string
  name: string
  description: string
  rarity: string
  currentScore: number
  imageUrl: string
  metadata: any
  userId: string
  campaignId: string
  createdAt: string
  updatedAt: string
}

export default function NFTGalleryPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [nfts, setNfts] = useState<NFT[]>([])
  const [filteredNfts, setFilteredNfts] = useState<NFT[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [rarityFilter, setRarityFilter] = useState('all')

  useEffect(() => {
    // Don't redirect during loading (AuthContext initialization)
    if (isLoading) {
      console.log('🔄 NFT Gallery: AuthContext is loading, waiting...');
      return;
    }

    if (!isAuthenticated) {
      console.log('❌ NFT Gallery: User not authenticated, redirecting to login');
      router.push('/auth/login')
      return
    }

    console.log('✅ NFT Gallery: User authenticated, fetching NFTs');
    fetchUserNFTs()
  }, [isAuthenticated, isLoading, router])

  // Force refresh when URL has refresh parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('refresh')) {
      console.log('🔄 Force refreshing NFT gallery due to URL parameter')
      if (user?.id) {
        fetchUserNFTs()
      }
    }
  }, [user])

  // Manual refresh function for debugging
  const manualRefresh = () => {
    console.log('🔄 Manual refresh triggered')
    if (user?.id) {
      fetchUserNFTs()
    }
  }

  // Make refresh function available globally for debugging
  useEffect(() => {
    (window as any).refreshNFTGallery = manualRefresh
    // Also expose current state for debugging
    ;(window as any).nftGalleryState = {
      nfts: nfts,
      filteredNfts: filteredNfts,
      loading: loading,
      error: error,
      user: user,
      searchTerm: searchTerm,
      rarityFilter: rarityFilter
    }
  }, [manualRefresh, nfts, filteredNfts, loading, error, user, searchTerm, rarityFilter])

  const fetchUserNFTs = async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      setError('')

      console.log('🎨 Fetching NFTs for user:', user.username)

      // Use localStorage directly for demo (skip backend for now)
      console.log('🔄 Loading NFTs directly from localStorage')

      // Load NFTs from localStorage
      const storedNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]')
      console.log('🔍 Current user ID for filtering:', user.id)
      console.log('🔍 Current user object:', user)
      console.log('📊 Total stored NFTs before filtering:', storedNFTs.length)
      console.log('🔍 All stored NFTs:', storedNFTs)

      // Ensure consistent user ID comparison (both as strings)
      const consistentUserId = String(user.id)
      const userNfts = storedNFTs.filter((nft: any) => String(nft.userId) === consistentUserId)
      console.log('✅ NFTs loaded from localStorage:', userNfts.length, 'NFTs found')
      console.log('🔍 Filtered NFTs for user:', userNfts)

      // Force state update even if array is the same
      if (userNfts.length > 0) {
        console.log('🔄 Forcing state update with found NFTs')
      }

      setNfts(userNfts)
      setFilteredNfts(userNfts)
    } catch (err: any) {
      console.error('NFT fetch error:', err)
      setError('Failed to load your NFT collection')
    } finally {
      setLoading(false)
    }
  }

  // Filter NFTs based on search and rarity
  useEffect(() => {
    let filtered = nfts

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(nft =>
        nft.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        nft.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by rarity
    if (rarityFilter !== 'all') {
      filtered = filtered.filter(nft =>
        nft.rarity.toLowerCase() === rarityFilter.toLowerCase()
      )
    }

    setFilteredNfts(filtered)
  }, [nfts, searchTerm, rarityFilter])

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'purple'
      case 'rare': return 'blue'
      case 'common': return 'green'
      default: return 'gray'
    }
  }

  // Show loading during AuthContext initialization
  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Initializing authentication...</Text>
        </VStack>
      </Container>
    )
  }

  // Show loading during NFT fetch
  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Loading your NFT collection...</Text>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Box>
          <HStack justify="space-between" align="center" mb={4}>
            <Box>
              <Heading as="h1" size="xl">
                Your NFT Collection
              </Heading>
              <Text color="gray.600" mt={2}>
                {filteredNfts.length} NFT{filteredNfts.length !== 1 ? 's' : ''} found
              </Text>
            </Box>
            <Button onClick={() => router.push('/dashboard')}>
              Back to Dashboard
            </Button>
          </HStack>
        </Box>

        {/* Error Message */}
        {error && (
          <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
            <Text color="red.600" fontWeight="medium">
              {error}
            </Text>
          </Box>
        )}

        {/* NFT Debugger */}
        <NFTDebugger />

        {/* Filters */}
        <VStack gap={4} align="stretch">
          <InputGroup maxW="300px">
            <Input
              placeholder="Search NFTs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>

          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Filter by Rarity:
            </Text>
            <HStack gap={2} wrap="wrap">
              <Button
                size="sm"
                variant={rarityFilter === 'all' ? 'solid' : 'outline'}
                colorScheme="gray"
                onClick={() => setRarityFilter('all')}
              >
                All Rarities
              </Button>
              <Button
                size="sm"
                variant={rarityFilter === 'common' ? 'solid' : 'outline'}
                colorScheme="green"
                onClick={() => setRarityFilter('common')}
              >
                Common
              </Button>
              <Button
                size="sm"
                variant={rarityFilter === 'rare' ? 'solid' : 'outline'}
                colorScheme="blue"
                onClick={() => setRarityFilter('rare')}
              >
                Rare
              </Button>
              <Button
                size="sm"
                variant={rarityFilter === 'legendary' ? 'solid' : 'outline'}
                colorScheme="purple"
                onClick={() => setRarityFilter('legendary')}
              >
                Legendary
              </Button>
            </HStack>
          </Box>
        </VStack>

        {/* NFT Grid */}
        {filteredNfts.length === 0 ? (
          <Box textAlign="center" py={12}>
            <Text fontSize="lg" color="gray.500">
              {nfts.length === 0
                ? "You don't have any NFTs yet. Join a campaign to start collecting!"
                : "No NFTs match your search criteria."
              }
            </Text>
            {nfts.length === 0 && (
              <Button mt={4} colorScheme="blue" onClick={() => router.push('/dashboard')}>
                Browse Campaigns
              </Button>
            )}
          </Box>
        ) : (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3, xl: 4 }} gap={6}>
            {filteredNfts.map((nft) => (
              <Box key={nft.id} bg="white" borderRadius="lg" boxShadow="md" overflow="hidden">
                <VStack align="start" gap={3} p={4}>
                  <Box
                    w="full"
                    h="200px"
                    bg="gray.100"
                    borderRadius="md"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text color="gray.500">NFT Image</Text>
                  </Box>
                  <VStack align="start" gap={2} w="full">
                    <Heading size="sm">{nft.name}</Heading>
                    <Text
                      fontSize="sm"
                      color="gray.600"
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {nft.description}
                    </Text>
                    <HStack justify="space-between" w="full">
                      <Badge colorScheme={getRarityColor(nft.rarity)}>
                        {nft.rarity}
                      </Badge>
                      <Text fontSize="sm" fontWeight="medium">
                        Score: {nft.currentScore}
                      </Text>
                    </HStack>
                  </VStack>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        )}
      </VStack>
    </Container>
  )
}