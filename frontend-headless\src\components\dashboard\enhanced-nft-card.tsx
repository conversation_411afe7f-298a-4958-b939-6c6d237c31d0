'use client'

import { 
  SparklesIcon, 
  CalendarIcon, 
  HashtagIcon,
  ChartBarIcon,
  GlobeAltIcon,
  CubeIcon
} from '@heroicons/react/24/outline'

interface NFTMetadata {
  name: string
  description?: string
  image?: string
  external_url?: string
  attributes?: Array<{
    trait_type: string
    value: string | number
    display_type?: string
  }>
  background_color?: string
}

interface NFT {
  id: string
  name: string
  rarity: string
  score: number
  twitterHandle: string
  createdAt: string
  metadata?: NFTMetadata
  status?: string
  blockchain?: string
  tokenId?: string
  generationParams?: Record<string, any>
}

interface EnhancedNFTCardProps {
  nft: NFT
  onClick?: () => void
}

export default function EnhancedNFTCard({ nft, onClick }: EnhancedNFTCardProps) {
  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'bg-gradient-to-br from-yellow-400 to-orange-500 text-white'
      case 'epic':
        return 'bg-gradient-to-br from-purple-500 to-pink-500 text-white'
      case 'rare':
        return 'bg-gradient-to-br from-blue-500 to-cyan-500 text-white'
      case 'common':
        return 'bg-gradient-to-br from-gray-400 to-gray-600 text-white'
      default:
        return 'bg-gradient-to-br from-green-500 to-emerald-500 text-white'
    }
  }

  const getRarityBadgeColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'epic':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'rare':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'common':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Generate SVG image if none provided
  const generateFallbackSVG = (rarity: string, score: number, twitterHandle: string) => {
    const colors = getRarityColors(rarity)

    const svgContent = `
      <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:0.8" />
            <stop offset="70%" style="stop-color:${colors.secondary};stop-opacity:0.4" />
            <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
          </radialGradient>
        </defs>

        <rect width="100%" height="100%" fill="url(#bgGradient)" />

        <circle cx="200" cy="200" r="150" fill="none" stroke="${colors.accent}" stroke-width="2" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="120" fill="none" stroke="${colors.primary}" stroke-width="2" stroke-opacity="0.5" />

        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.accent}" stroke-width="6" stroke-opacity="0.3" />
        <circle cx="200" cy="200" r="60" fill="none" stroke="${colors.primary}" stroke-width="6"
                stroke-linecap="round" stroke-dasharray="377"
                stroke-dashoffset="${377 - (score / 100) * 377}"
                transform="rotate(-90 200 200)" />

        <text x="200" y="200" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
          ${score}
        </text>

        <text x="200" y="225" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="12">
          SCORE
        </text>

        <rect x="150" y="50" width="100" height="30" fill="${colors.primary}"
              stroke="${colors.accent}" stroke-width="2" rx="5" />
        <text x="200" y="70" text-anchor="middle" dominant-baseline="middle"
              fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
          ${rarity.toUpperCase()}
        </text>

        <rect x="0" y="350" width="400" height="50" fill="rgba(0, 0, 0, 0.7)" />
        <text x="200" y="380" text-anchor="middle" dominant-baseline="middle"
              fill="${colors.accent}" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
          @${twitterHandle}
        </text>
      </svg>
    `.trim()

    return `data:image/svg+xml;base64,${btoa(svgContent)}`
  }

  const getRarityColors = (rarity: string) => {
    const colorMap = {
      common: { primary: '#6B7280', secondary: '#9CA3AF', accent: '#D1D5DB' },
      rare: { primary: '#3B82F6', secondary: '#60A5FA', accent: '#93C5FD' },
      epic: { primary: '#8B5CF6', secondary: '#A78BFA', accent: '#C4B5FD' },
      legendary: { primary: '#F59E0B', secondary: '#FBBF24', accent: '#FCD34D' },
      mythic: { primary: '#EF4444', secondary: '#F87171', accent: '#FCA5A5' },
    }
    return colorMap[rarity.toLowerCase()] || colorMap.common
  }

  // Get the image to display
  const getImageSrc = () => {
    if (nft.metadata?.image) {
      return nft.metadata.image
    }
    // Generate fallback SVG
    return generateFallbackSVG(nft.rarity, nft.score, nft.twitterHandle)
  }

  const getKeyAttributes = () => {
    if (!nft.metadata?.attributes) return []
    return nft.metadata.attributes.filter(attr => 
      ['Engagement Score', 'Rarity', 'Generation Date'].includes(attr.trait_type)
    ).slice(0, 3)
  }

  return (
    <div 
      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02]"
      onClick={onClick}
    >
      {/* NFT Image/Visual */}
      <div className="h-48 relative overflow-hidden">
        <img
          src={getImageSrc()}
          alt={nft.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            // Fallback if image fails to load
            const target = e.target as HTMLImageElement
            target.src = generateFallbackSVG(nft.rarity, nft.score, nft.twitterHandle)
          }}
        />
        
        {/* Rarity Badge */}
        <div className="absolute top-3 right-3">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRarityBadgeColor(nft.rarity)}`}>
            {nft.rarity}
          </span>
        </div>

        {/* Score Badge */}
        <div className="absolute top-3 left-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 border border-gray-200">
            <ChartBarIcon className="h-3 w-3 mr-1" />
            {nft.score}
          </span>
        </div>
      </div>

      {/* NFT Details */}
      <div className="p-4">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {nft.metadata?.name || nft.name}
          </h3>
          <p className="text-sm text-gray-600 flex items-center mt-1">
            <span className="mr-2">@{nft.twitterHandle}</span>
            {nft.tokenId && (
              <>
                <HashtagIcon className="h-3 w-3 mr-1" />
                <span className="text-xs">#{nft.tokenId}</span>
              </>
            )}
          </p>
        </div>

        {/* Description */}
        {nft.metadata?.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {nft.metadata.description}
          </p>
        )}

        {/* Key Attributes */}
        {getKeyAttributes().length > 0 && (
          <div className="mb-3">
            <div className="grid grid-cols-1 gap-2">
              {getKeyAttributes().map((attr, index) => (
                <div key={index} className="flex justify-between items-center text-xs">
                  <span className="text-gray-500">{attr.trait_type}:</span>
                  <span className="font-medium text-gray-900">
                    {attr.display_type === 'number' ? attr.value : attr.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center text-xs text-gray-500">
            <CalendarIcon className="h-3 w-3 mr-1" />
            {formatDate(nft.createdAt)}
          </div>
          
          <div className="flex items-center space-x-2">
            {nft.blockchain && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                <GlobeAltIcon className="h-3 w-3 mr-1" />
                {nft.blockchain}
              </span>
            )}
            
            {nft.status && (
              <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                nft.status === 'completed' 
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : nft.status === 'pending'
                  ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                  : 'bg-gray-50 text-gray-700 border border-gray-200'
              }`}>
                <CubeIcon className="h-3 w-3 mr-1" />
                {nft.status}
              </span>
            )}
          </div>
        </div>

        {/* External Link */}
        {nft.metadata?.external_url && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <a 
              href={nft.metadata.external_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-500 flex items-center"
              onClick={(e) => e.stopPropagation()}
            >
              <GlobeAltIcon className="h-3 w-3 mr-1" />
              View Details
            </a>
          </div>
        )}
      </div>
    </div>
  )
}
