'use client';

import React, { useState, useEffect } from 'react';
import {
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowPathIcon,
  CalendarIcon,
  PresentationChartLineIcon
} from '@heroicons/react/24/outline';
import { analyticsApi } from '@/lib/api';
import {
  PlatformGrowthChart,
  RarityDistributionChart,
  GainersLosersChart,
  MarketValueChart,
  EngagementMetricsChart,
} from '@/components/charts/analytics-charts';
import {
  TopGainersLosersSection,
  CollectionMarketValueSection,
  RecentTransactionsSection,
} from '@/components/analytics/yaps-kaito-style';

interface PlatformOverview {
  timestamp: string;
  period: string;
  overview: {
    total_users: number;
    active_users_today: number;
    total_campaigns: number;
    active_campaigns: number;
    total_nfts_generated: number;
    nfts_generated_today: number;
    total_nfts_minted: number;
    nfts_minted_today: number;
    total_marketplace_sales: number;
    marketplace_sales_today: number;
    total_volume_eth: number;
    volume_today_eth: number;
  };
  growth_metrics: {
    user_growth_rate: number;
    nft_generation_growth_rate: number;
    marketplace_volume_growth_rate: number;
    campaign_participation_rate: number;
  };
  top_performing: {
    campaigns: Array<{
      id: string;
      name: string;
      participants: number;
      nfts_generated: number;
    }>;
    nft_rarities: Array<{
      rarity: string;
      count: number;
      percentage: number;
    }>;
  };
}

interface TopGainersLosers {
  timestamp: string;
  period: string;
  limit: number;
  top_gainers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
    trend: string;
  }>;
  top_losers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
    trend: string;
  }>;
  market_summary: {
    total_volume_24h: number;
    volume_change_24h: string;
    average_score_change: string;
    most_active_rarity: string;
    trending_themes: string[];
  };
}

interface CollectionMarketValue {
  timestamp: string;
  bubble_map_data: Array<{
    id: string;
    name: string;
    value: number;
    size: number;
    change_24h: string;
    color: string;
    rarity: string;
  }>;
  market_overview: {
    total_market_value: number;
    market_cap_change_24h: string;
    total_collections: number;
    total_nfts: number;
    average_nft_value: number;
  };
}

export default function AnalyticsDashboard() {
  const [platformData, setPlatformData] = useState<PlatformOverview | null>(null);
  const [gainersLosers, setGainersLosers] = useState<TopGainersLosers | null>(null);
  const [marketValue, setMarketValue] = useState<CollectionMarketValue | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [activeTab, setActiveTab] = useState('yaps-kaito');

  // Mock chart data - in production this would come from the analytics API
  const mockGrowthData = [
    { date: '2025-06-01', users: 1200, nfts: 3200, volume: 220 },
    { date: '2025-06-02', users: 1210, nfts: 3350, volume: 225 },
    { date: '2025-06-03', users: 1225, nfts: 3400, volume: 230 },
    { date: '2025-06-04', users: 1240, nfts: 3420, volume: 232 },
    { date: '2025-06-05', users: 1245, nfts: 3450, volume: 234 },
    { date: '2025-06-06', users: 1247, nfts: 3456, volume: 235 },
  ];

  const mockEngagementData = [
    { date: '2025-06-01', active_users: 89, nft_generations: 45, shares: 23 },
    { date: '2025-06-02', active_users: 94, nft_generations: 52, shares: 28 },
    { date: '2025-06-03', active_users: 76, nft_generations: 38, shares: 19 },
    { date: '2025-06-04', active_users: 112, nft_generations: 67, shares: 34 },
    { date: '2025-06-05', active_users: 98, nft_generations: 43, shares: 25 },
    { date: '2025-06-06', active_users: 105, nft_generations: 58, shares: 31 },
  ];

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch platform overview using proper API client
      const platformResult = await analyticsApi.getPlatformOverview();
      if (platformResult.success) {
        setPlatformData(platformResult.data);
      }

      // Fetch top gainers/losers using proper API client
      const gainersResult = await analyticsApi.getTopGainersLosers(selectedPeriod, 10);
      if (gainersResult.success) {
        setGainersLosers(gainersResult.data);
      }

      // Fetch collection market value using proper API client
      const marketResult = await analyticsApi.getCollectionMarketValue();
      if (marketResult.success) {
        setMarketValue(marketResult.data);
      }

      // Fetch recent transactions using proper API client
      const transactionsResult = await analyticsApi.getRecentTransactions(15);
      if (transactionsResult.success) {
        setRecentTransactions(transactionsResult.data.transactions);
      }

    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatEth = (eth: number) => `${eth.toFixed(2)} ETH`;

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'bg-yellow-500';
      case 'epic': return 'bg-purple-500';
      case 'rare': return 'bg-blue-500';
      case 'common': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <ArrowPathIcon className="h-6 w-6 animate-spin" />
          <span>Loading analytics data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={fetchAnalyticsData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">
            Comprehensive platform insights and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={fetchAnalyticsData}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Platform Overview Cards */}
      {platformData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(platformData.overview.total_users)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.user_growth_rate}% from last month
                </p>
              </div>
              <UsersIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">NFTs Generated</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(platformData.overview.total_nfts_generated)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.nft_generation_growth_rate}% growth rate
                </p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Volume</p>
                <p className="text-2xl font-bold text-gray-900">{formatEth(platformData.overview.total_volume_eth)}</p>
                <p className="text-xs text-gray-500">
                  +{platformData.growth_metrics.marketplace_volume_growth_rate}% volume growth
                </p>
              </div>
              <CurrencyDollarIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Campaigns</p>
                <p className="text-2xl font-bold text-gray-900">{platformData.overview.active_campaigns}</p>
                <p className="text-xs text-gray-500">
                  {platformData.growth_metrics.campaign_participation_rate}% participation rate
                </p>
              </div>
              <PresentationChartLineIcon className="h-8 w-8 text-gray-400" />
            </div>
          </div>
        </div>
      )}

      {/* Main Analytics Tabs */}
      <div className="space-y-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'yaps-kaito', name: '🎯 Yaps Kaito Style' },
              { id: 'overview', name: 'Overview' },
              { id: 'gainers-losers', name: 'Top Gainers/Losers' },
              { id: 'market-value', name: 'Market Value' },
              { id: 'performance', name: 'Performance' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Yaps Kaito Style Tab */}
        {activeTab === 'yaps-kaito' && (
          <div className="space-y-8">
            {/* Section 1: Top Gainers/Losers */}
            {gainersLosers && (
              <TopGainersLosersSection
                gainers={gainersLosers.top_gainers}
                losers={gainersLosers.top_losers}
                selectedPeriod={selectedPeriod}
                onPeriodChange={setSelectedPeriod}
              />
            )}

            {/* Section 2: Collection Market Value */}
            {marketValue && (
              <CollectionMarketValueSection
                collections={marketValue.bubble_map_data}
                totalMarketValue={marketValue.market_overview.total_market_value}
                marketCapChange={marketValue.market_overview.market_cap_change_24h}
                onCollectionClick={(collectionId) => {
                  console.log('Navigate to collection:', collectionId);
                  // TODO: Implement navigation to collection page
                }}
              />
            )}

            {/* Section 3: Recent Transactions */}
            {recentTransactions.length > 0 && (
              <RecentTransactionsSection transactions={recentTransactions} />
            )}

            {/* Loading State */}
            {loading && (
              <div className="flex items-center justify-center py-12">
                <ArrowPathIcon className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-500">Loading Yaps Kaito-style analytics...</span>
              </div>
            )}
          </div>
        )}

        {/* Tab Content */}
        {activeTab === 'overview' && platformData && (
          <div className="space-y-6">
            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PlatformGrowthChart data={mockGrowthData} />
              <RarityDistributionChart data={platformData.top_performing.nft_rarities} />
            </div>

            {/* Data Tables Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Performing Campaigns */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Top Performing Campaigns</h3>
                  <p className="text-sm text-gray-500">Campaigns with highest engagement</p>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {platformData.top_performing.campaigns.map((campaign, index) => (
                      <div key={campaign.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{campaign.name}</p>
                          <p className="text-sm text-gray-500">
                            {formatNumber(campaign.participants)} participants
                          </p>
                        </div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {formatNumber(campaign.nfts_generated)} NFTs
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Engagement Metrics Chart */}
              <EngagementMetricsChart data={mockEngagementData} />
            </div>
          </div>
        )}

        {/* Gainers/Losers Tab */}
        {activeTab === 'gainers-losers' && (
          <div className="space-y-6">
            {/* Period Selector */}
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Period:</span>
              {['24h', '7d', '30d'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-3 py-1 text-sm font-medium rounded-md ${
                    selectedPeriod === period
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>

            {gainersLosers ? (
              <div className="space-y-6">
                {/* Performance Chart */}
                <GainersLosersChart
                  gainers={gainersLosers.top_gainers}
                  losers={gainersLosers.top_losers}
                />

                {/* Market Summary */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Market Summary</h3>
                    <p className="text-sm text-gray-500">Overall market performance for {selectedPeriod}</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{formatEth(gainersLosers.market_summary.total_volume_24h)}</p>
                      <p className="text-sm text-gray-500">Total Volume</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 ${
                        gainersLosers.market_summary.volume_change_24h.startsWith('+')
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {gainersLosers.market_summary.volume_change_24h}
                      </span>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{gainersLosers.market_summary.average_score_change}</p>
                      <p className="text-sm text-gray-500">Avg Score Change</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{gainersLosers.market_summary.most_active_rarity}</p>
                      <p className="text-sm text-gray-500">Most Active Rarity</p>
                    </div>
                    <div className="text-center">
                      <div className="flex flex-wrap justify-center gap-1">
                        {gainersLosers.market_summary.trending_themes.map((theme) => (
                          <span key={theme} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {theme}
                          </span>
                        ))}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">Trending Themes</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg p-8">
                <div className="text-center">
                  <ArrowPathIcon className="h-8 w-8 mx-auto text-gray-400 animate-spin mb-4" />
                  <p className="text-gray-500">Loading gainers and losers data...</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Market Value Tab */}
        {activeTab === 'market-value' && (
          <div className="space-y-6">
            {marketValue ? (
              <div className="space-y-6">
                {/* Market Value Chart */}
                <MarketValueChart data={marketValue.bubble_map_data} />

                {/* Market Overview */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Market Overview</h3>
                    <p className="text-sm text-gray-500">Collection market value and statistics</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{formatEth(marketValue.market_overview.total_market_value)}</p>
                      <p className="text-sm text-gray-500">Total Market Value</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 ${
                        marketValue.market_overview.market_cap_change_24h.startsWith('+')
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {marketValue.market_overview.market_cap_change_24h}
                      </span>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{marketValue.market_overview.total_collections}</p>
                      <p className="text-sm text-gray-500">Collections</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{formatNumber(marketValue.market_overview.total_nfts)}</p>
                      <p className="text-sm text-gray-500">Total NFTs</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{formatEth(marketValue.market_overview.average_nft_value)}</p>
                      <p className="text-sm text-gray-500">Avg NFT Value</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg p-8">
                <div className="text-center">
                  <ArrowPathIcon className="h-8 w-8 mx-auto text-gray-400 animate-spin mb-4" />
                  <p className="text-gray-500">Loading market value data...</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <div className="bg-white shadow rounded-lg p-8">
            <div className="text-center">
              <PresentationChartLineIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Performance Analytics</h3>
              <p className="text-gray-500">
                Coming soon: Real-time performance tracking, predictive analytics, and advanced insights.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
