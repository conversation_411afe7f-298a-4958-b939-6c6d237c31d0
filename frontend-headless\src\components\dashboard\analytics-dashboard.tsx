'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Activity, 
  DollarSign, 
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';

interface PlatformOverview {
  timestamp: string;
  period: string;
  overview: {
    total_users: number;
    active_users_today: number;
    total_campaigns: number;
    active_campaigns: number;
    total_nfts_generated: number;
    nfts_generated_today: number;
    total_nfts_minted: number;
    nfts_minted_today: number;
    total_marketplace_sales: number;
    marketplace_sales_today: number;
    total_volume_eth: number;
    volume_today_eth: number;
  };
  growth_metrics: {
    user_growth_rate: number;
    nft_generation_growth_rate: number;
    marketplace_volume_growth_rate: number;
    campaign_participation_rate: number;
  };
  top_performing: {
    campaigns: Array<{
      id: string;
      name: string;
      participants: number;
      nfts_generated: number;
    }>;
    nft_rarities: Array<{
      rarity: string;
      count: number;
      percentage: number;
    }>;
  };
}

interface TopGainersLosers {
  timestamp: string;
  period: string;
  limit: number;
  top_gainers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
    trend: string;
  }>;
  top_losers: Array<{
    id: string;
    name: string;
    current_score: number;
    previous_score: number;
    change: string;
    change_percentage: string;
    rarity: string;
    volume_24h: number;
    trend: string;
  }>;
  market_summary: {
    total_volume_24h: number;
    volume_change_24h: string;
    average_score_change: string;
    most_active_rarity: string;
    trending_themes: string[];
  };
}

interface CollectionMarketValue {
  timestamp: string;
  bubble_map_data: Array<{
    id: string;
    name: string;
    value: number;
    size: number;
    change_24h: string;
    color: string;
    rarity: string;
  }>;
  market_overview: {
    total_market_value: number;
    market_cap_change_24h: string;
    total_collections: number;
    total_nfts: number;
    average_nft_value: number;
  };
}

export default function AnalyticsDashboard() {
  const [platformData, setPlatformData] = useState<PlatformOverview | null>(null);
  const [gainersLosers, setGainersLosers] = useState<TopGainersLosers | null>(null);
  const [marketValue, setMarketValue] = useState<CollectionMarketValue | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('24h');

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch platform overview
      const platformResponse = await fetch('/api/analytics/platform-overview');
      const platformResult = await platformResponse.json();
      if (platformResult.success) {
        setPlatformData(platformResult.data);
      }

      // Fetch top gainers/losers
      const gainersResponse = await fetch(`/api/analytics/top-gainers-losers?period=${selectedPeriod}&limit=5`);
      const gainersResult = await gainersResponse.json();
      if (gainersResult.success) {
        setGainersLosers(gainersResult.data);
      }

      // Fetch collection market value
      const marketResponse = await fetch('/api/analytics/collection-market-value');
      const marketResult = await marketResponse.json();
      if (marketResult.success) {
        setMarketValue(marketResult.data);
      }

    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatEth = (eth: number) => `${eth.toFixed(2)} ETH`;

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'bg-yellow-500';
      case 'epic': return 'bg-purple-500';
      case 'rare': return 'bg-blue-500';
      case 'common': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>Loading analytics data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchAnalyticsData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive platform insights and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={fetchAnalyticsData}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Platform Overview Cards */}
      {platformData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(platformData.overview.total_users)}</div>
              <p className="text-xs text-muted-foreground">
                +{platformData.growth_metrics.user_growth_rate}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">NFTs Generated</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(platformData.overview.total_nfts_generated)}</div>
              <p className="text-xs text-muted-foreground">
                +{platformData.growth_metrics.nft_generation_growth_rate}% growth rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Volume</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatEth(platformData.overview.total_volume_eth)}</div>
              <p className="text-xs text-muted-foreground">
                +{platformData.growth_metrics.marketplace_volume_growth_rate}% volume growth
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{platformData.overview.active_campaigns}</div>
              <p className="text-xs text-muted-foreground">
                {platformData.growth_metrics.campaign_participation_rate}% participation rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="gainers-losers">Top Gainers/Losers</TabsTrigger>
          <TabsTrigger value="market-value">Market Value</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {platformData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Performing Campaigns */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Campaigns</CardTitle>
                  <CardDescription>Campaigns with highest engagement</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {platformData.top_performing.campaigns.map((campaign, index) => (
                      <div key={campaign.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{campaign.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatNumber(campaign.participants)} participants
                          </p>
                        </div>
                        <Badge variant="secondary">
                          {formatNumber(campaign.nfts_generated)} NFTs
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* NFT Rarity Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>NFT Rarity Distribution</CardTitle>
                  <CardDescription>Distribution of NFT rarities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {platformData.top_performing.nft_rarities.map((rarity) => (
                      <div key={rarity.rarity} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getRarityColor(rarity.rarity)}`} />
                          <span className="font-medium">{rarity.rarity}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatNumber(rarity.count)}</p>
                          <p className="text-sm text-muted-foreground">{rarity.percentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="gainers-losers" className="space-y-4">
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span className="text-sm font-medium">Period:</span>
            {['24h', '7d', '30d'].map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
              >
                {period}
              </Button>
            ))}
          </div>

          {gainersLosers && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Gainers */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    <span>Top Gainers</span>
                  </CardTitle>
                  <CardDescription>Best performing NFTs in {selectedPeriod}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {gainersLosers.top_gainers.map((nft) => (
                      <div key={nft.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{nft.name}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className={getRarityColor(nft.rarity)}>
                              {nft.rarity}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatEth(nft.volume_24h)} volume
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-green-600">{nft.change_percentage}</p>
                          <p className="text-sm text-muted-foreground">Score: {nft.current_score}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Losers */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingDown className="h-5 w-5 text-red-500" />
                    <span>Top Losers</span>
                  </CardTitle>
                  <CardDescription>Declining NFTs in {selectedPeriod}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {gainersLosers.top_losers.map((nft) => (
                      <div key={nft.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{nft.name}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className={getRarityColor(nft.rarity)}>
                              {nft.rarity}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatEth(nft.volume_24h)} volume
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-red-600">{nft.change_percentage}</p>
                          <p className="text-sm text-muted-foreground">Score: {nft.current_score}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Market Summary */}
          {gainersLosers && (
            <Card>
              <CardHeader>
                <CardTitle>Market Summary</CardTitle>
                <CardDescription>Overall market performance for {selectedPeriod}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">{formatEth(gainersLosers.market_summary.total_volume_24h)}</p>
                    <p className="text-sm text-muted-foreground">Total Volume</p>
                    <Badge variant="outline" className="mt-1">
                      {gainersLosers.market_summary.volume_change_24h}
                    </Badge>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{gainersLosers.market_summary.average_score_change}</p>
                    <p className="text-sm text-muted-foreground">Avg Score Change</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{gainersLosers.market_summary.most_active_rarity}</p>
                    <p className="text-sm text-muted-foreground">Most Active Rarity</p>
                  </div>
                  <div className="text-center">
                    <div className="flex flex-wrap justify-center gap-1">
                      {gainersLosers.market_summary.trending_themes.map((theme) => (
                        <Badge key={theme} variant="secondary" className="text-xs">
                          {theme}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">Trending Themes</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="market-value" className="space-y-4">
          {marketValue && (
            <div className="space-y-6">
              {/* Market Overview */}
              <Card>
                <CardHeader>
                  <CardTitle>Market Overview</CardTitle>
                  <CardDescription>Collection market value and statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatEth(marketValue.market_overview.total_market_value)}</p>
                      <p className="text-sm text-muted-foreground">Total Market Value</p>
                      <Badge variant="outline" className="mt-1">
                        {marketValue.market_overview.market_cap_change_24h}
                      </Badge>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{marketValue.market_overview.total_collections}</p>
                      <p className="text-sm text-muted-foreground">Collections</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatNumber(marketValue.market_overview.total_nfts)}</p>
                      <p className="text-sm text-muted-foreground">Total NFTs</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatEth(marketValue.market_overview.average_nft_value)}</p>
                      <p className="text-sm text-muted-foreground">Avg NFT Value</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Collection Bubble Map Data */}
              <Card>
                <CardHeader>
                  <CardTitle>Collection Market Value</CardTitle>
                  <CardDescription>Market value by collection (Bubble Map Data)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {marketValue.bubble_map_data.map((collection) => (
                      <div
                        key={collection.id}
                        className="p-4 border rounded-lg"
                        style={{ borderColor: collection.color }}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold">{collection.name}</h3>
                          <Badge 
                            variant="outline" 
                            className={getRarityColor(collection.rarity)}
                          >
                            {collection.rarity}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">Value</p>
                            <p className="font-bold">{formatEth(collection.value)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Size</p>
                            <p className="font-bold">{formatNumber(collection.size)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">24h Change</p>
                            <p className={`font-bold ${collection.change_24h.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                              {collection.change_24h}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Color</p>
                            <div 
                              className="w-6 h-6 rounded-full border"
                              style={{ backgroundColor: collection.color }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>Coming soon - Advanced performance metrics and charts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <LineChart className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">Advanced charts and performance metrics will be available here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
