# 🤖 AI Agent Implementation Guidelines

## Overview
These guidelines are extracted from our working User Service, Profile Analysis Service, and API Gateway implementations. Follow these patterns for consistent, production-ready code.

## 🏗️ Service Creation Process

### Step 1: Copy Template Structure
```bash
# Use user-service as template for new services
cp -r user-service new-service-name
```

### Step 2: Customize Service Identity
```json
// package.json changes
{
  "name": "new-service-name",
  "description": "Service description for Social NFT Platform"
}
```

### Step 3: Update Port and Database
```typescript
// main.ts - Assign unique port
const port = process.env.PORT || 3003; // Next available port

// app.module.ts - Update database name
database: process.env.DB_DATABASE || 'new_service_db',
```

### Step 4: Replace Business Logic
```typescript
// Replace authentication/ with your-feature/
// Update controllers, services, DTOs for your domain
```

## 📋 Required File Structure

### Every Service Must Have:
```
service-name/
├── src/
│   ├── main.ts                    # Port: 3001, 3002, 3003, etc.
│   ├── app.module.ts              # Database + feature modules
│   ├── app.controller.ts          # Health endpoints
│   ├── app.service.ts             # Service info
│   ├── business-feature/          # Main business logic
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── dto/
│   │   ├── entities/
│   │   └── feature.module.ts
│   └── shared/
│       └── shared.module.ts
├── package.json                   # Standard dependencies
├── tsconfig.json                  # Standard TS config
├── nest-cli.json                  # NestJS config
├── .env.example                   # Environment template
└── README.md                      # Service documentation
```

## 🎯 Mandatory Patterns

### 1. Health Check Pattern
```typescript
// app.controller.ts - ALWAYS include
@Get('health')
getHealth() {
  return {
    status: 'ok',
    service: 'service-name',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    port: process.env.PORT || 3001,
  };
}
```

### 2. Swagger Documentation Pattern
```typescript
// main.ts - ALWAYS include
const config = new DocumentBuilder()
  .setTitle('Service Name API')
  .setDescription('Service description')
  .setVersion('1.0')
  .addBearerAuth()
  .build();
```

### 3. Validation Pattern
```typescript
// main.ts - ALWAYS include
app.useGlobalPipes(new ValidationPipe({
  whitelist: true,
  transform: true,
  forbidNonWhitelisted: true,
}));
```

### 4. Database Pattern
```typescript
// app.module.ts - ALWAYS include
TypeOrmModule.forRoot({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'service_db',
  autoLoadEntities: true,
  synchronize: process.env.NODE_ENV === 'development',
}),
```

## 🔧 API Gateway Integration

### Adding New Service to Gateway
1. **Add to service list** in `app.service.ts`:
```typescript
private readonly services = [
  { name: 'user-service', url: 'http://localhost:3001', health: '/health' },
  { name: 'new-service', url: 'http://localhost:3003', health: '/health' },
];
```

2. **Add to proxy service** in `proxy.service.ts`:
```typescript
private readonly serviceUrls = {
  'user-service': 'http://localhost:3001',
  'new-service': 'http://localhost:3003',
};
```

3. **Create proxy controller** in `routing/controllers/`:
```typescript
@Controller('new-feature')
export class NewFeatureController {
  // Proxy methods to forward requests
}
```

## 📊 Port Assignment Rules

| Service | Port | Database |
|---------|------|----------|
| api-gateway | 3000 | None |
| user-service | 3001 | user_service_db |
| profile-analysis-service | 3002 | profile_analysis_db |
| nft-generation-service | 3003 | nft_generation_db |
| blockchain-service | 3004 | blockchain_db |
| marketplace-service | 3005 | marketplace_db |
| project-service | 3006 | project_db |
| analytics-service | 3007 | analytics_db |
| notification-service | 3008 | notification_db |

## ✅ Quality Checklist

Before completing any service:
- [ ] Service starts on assigned port
- [ ] Health endpoint returns 200 OK
- [ ] Swagger docs accessible at /api/docs
- [ ] Database connection configured
- [ ] Environment variables documented
- [ ] README.md updated with service info
- [ ] API Gateway routes configured
- [ ] Error handling implemented
- [ ] Input validation added
- [ ] TypeScript types defined

## 🚫 Common Mistakes to Avoid

### DON'T:
- Use same port for multiple services
- Skip health check endpoints
- Forget to update API Gateway routing
- Use 'any' types in TypeScript
- Skip input validation
- Hardcode configuration values
- Forget to document environment variables
- **Allow users to create projects** (users only participate in campaigns)
- **Implement user project creation features** (project owners only)
- **Skip requirements compliance** (always check core requirements)

### DO:
- Follow the proven template pattern
- Use feature-based organization
- Include comprehensive error handling
- Add proper TypeScript types
- Document all endpoints with Swagger
- Use environment variables for configuration
- Test health endpoints before integration
- **Follow core workflow:** Twitter connect → Campaign participation → NFT evolution
- **Respect user roles:** Users participate, Project owners create campaigns
- **Check requirements compliance** before implementing any feature

## 📋 MANDATORY: Requirements Compliance

### Before Implementing ANY Feature:
1. **Read Core Requirements:** `docs/requirments/CORE_REQUIREMENTS_SUMMARY.md`
2. **Verify User Roles:**
   - Users: Twitter connect → Campaign participation only
   - Project Owners: Campaign creation and management
   - Admins: Platform management
3. **Check Workflow Compliance:**
   - Users do NOT create projects
   - Users participate in existing campaigns
   - NFTs evolve based on social activity
4. **Validate Service Purpose:**
   - Project Service: Campaign management (not user projects)
   - Profile Analysis: Twitter analysis with configurable parameters
   - NFT Generation: Evolving NFTs based on scores
   - Blockchain: Multi-network minting
   - Marketplace: NFT trading

### Requirements Reference Files:
- `docs/requirments/0-requirments.txt` - Detailed platform requirements
- `docs/requirments/1- guideline.txt` - Architecture guidelines
- `docs/requirments/CORE_REQUIREMENTS_SUMMARY.md` - Implementation summary

## 🎯 Success Metrics

A properly implemented service should:
- Build without TypeScript errors
- Start and respond to health checks
- Integrate seamlessly with API Gateway
- Have complete Swagger documentation
- Follow consistent naming patterns
- Include proper error handling
- Support environment configuration
- **Comply with core requirements and user workflows**
- **Respect stakeholder roles and permissions**
- **Follow proven template-based approach**
