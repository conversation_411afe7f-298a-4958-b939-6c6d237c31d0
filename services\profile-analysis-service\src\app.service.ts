import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getStatus(): string {
    return 'Profile Analysis Service is running! 🚀';
  }

  getHealth() {
    return {
      status: 'ok',
      service: 'profile-analysis-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || 3002,
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
