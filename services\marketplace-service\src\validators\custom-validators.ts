import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Custom validator for Ethereum addresses
 * Validates that the address is a valid Ethereum address format (0x followed by 40 hex characters)
 */
export function IsEthereumAddress(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isEthereumAddress',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          
          // Ethereum address regex: 0x followed by 40 hexadecimal characters
          const ethereumAddressRegex = /^0x[a-fA-F0-9]{40}$/;
          return ethereumAddressRegex.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid Ethereum address (0x followed by 40 hex characters)`;
        },
      },
    });
  };
}

/**
 * Custom validator for price values
 * Validates that the price is a valid decimal number within reasonable bounds
 */
export function IsValidPrice(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidPrice',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          
          // Check if it's a valid decimal number
          const numericValue = parseFloat(value);
          
          // Must be a valid number
          if (isNaN(numericValue)) {
            return false;
          }
          
          // Must be positive
          if (numericValue <= 0) {
            return false;
          }
          
          // Must be within reasonable bounds (max 1 billion ETH)
          if (numericValue > 1000000000) {
            return false;
          }
          
          // Must have reasonable decimal precision (max 18 decimal places for ETH)
          const decimalPlaces = (value.split('.')[1] || '').length;
          if (decimalPlaces > 18) {
            return false;
          }
          
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid price (positive number, max 1 billion, max 18 decimal places)`;
        },
      },
    });
  };
}

/**
 * Custom validator for percentage values
 * Validates that the percentage is between 0 and 100
 */
export function IsValidPercentage(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidPercentage',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          
          const numericValue = parseFloat(value);
          
          // Must be a valid number
          if (isNaN(numericValue)) {
            return false;
          }
          
          // Must be between 0 and 100
          return numericValue >= 0 && numericValue <= 100;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid percentage between 0 and 100`;
        },
      },
    });
  };
}
