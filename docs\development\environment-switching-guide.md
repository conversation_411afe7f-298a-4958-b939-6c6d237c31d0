# Environment Switching Guide

## Overview
This guide explains how to switch between mock and real services in the Social NFT Platform using the automated environment switching system.

## Quick Start

### Switch to Mock Services (Development)
```bash
./tools/scripts/switch-environment.sh mock
```

### Switch to Real Services (Production)
```bash
./tools/scripts/switch-environment.sh real
```

## Environment Configuration

### Current Environment Detection
```bash
# Check current environment through API Gateway
curl -s http://localhost:3010/api/api/environment/info | jq '.data.useMockServices'

# Check .env file directly
grep "USE_MOCK_SERVICES" .env
```

## Service Port Mapping

### Mock Services (Development Environment)
```
Mock Twitter Service:     Port 3020
Mock Blockchain Service:  Port 3021
Mock NFT Storage Service: Port 3022
```

### Real Services (Production Environment)
```
User Service:             Port 3001
Profile Analysis Service: Port 3002
NFT Generation Service:   Port 3003
Project Service:          Port 3005
Marketplace Service:      Port 3006
Analytics Service:        Port 3007
Notification Service:     Port 3008
```

### Core Services (Always Active)
```
Frontend (Next.js):       Port 3000
API Gateway:              Port 3010
PostgreSQL Database:      Port 5432
```

## Environment Variables

### Mock Environment (.env)
```bash
USE_MOCK_SERVICES=true
NODE_ENV=development
```

### Production Environment (.env)
```bash
USE_MOCK_SERVICES=false
NODE_ENV=production
```
