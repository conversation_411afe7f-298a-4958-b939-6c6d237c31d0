<!DOCTYPE html>
<html>
<head>
    <title>NFT Storage Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .nft { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; }
        button { margin: 5px; padding: 10px 15px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎨 NFT Storage Debugging Tool</h1>
    
    <div class="section">
        <h3>Current NFT Storage</h3>
        <button onclick="checkNFTStorage()">Check Stored NFTs</button>
        <button onclick="clearNFTStorage()">Clear All NFTs</button>
        <div id="nftStorage"></div>
    </div>
    
    <div class="section">
        <h3>User Information</h3>
        <button onclick="checkUserInfo()">Check Current User</button>
        <div id="userInfo"></div>
    </div>
    
    <div class="section">
        <h3>Test NFT Creation</h3>
        <button onclick="createTestNFT()">Create Test NFT</button>
        <button onclick="createMultipleNFTs()">Create 3 Test NFTs</button>
        <div id="testResults"></div>
    </div>

    <script>
        function checkNFTStorage() {
            const storedNFTs = localStorage.getItem('user_nfts');
            const status = document.getElementById('nftStorage');
            
            if (storedNFTs) {
                try {
                    const nfts = JSON.parse(storedNFTs);
                    status.innerHTML = `
                        <div class="success">✅ Found ${nfts.length} stored NFTs</div>
                        <pre>${JSON.stringify(nfts, null, 2)}</pre>
                    `;
                } catch (error) {
                    status.innerHTML = `<div class="error">❌ Error parsing NFTs: ${error.message}</div>`;
                }
            } else {
                status.innerHTML = '<div class="error">❌ No NFTs found in localStorage</div>';
            }
        }

        function clearNFTStorage() {
            localStorage.removeItem('user_nfts');
            document.getElementById('nftStorage').innerHTML = '<div class="success">✅ NFT storage cleared</div>';
        }

        function checkUserInfo() {
            const authToken = localStorage.getItem('auth_token');
            const userInfo = document.getElementById('userInfo');
            
            if (authToken) {
                try {
                    // Try to decode JWT token to get user info
                    const payload = JSON.parse(atob(authToken.split('.')[1]));
                    userInfo.innerHTML = `
                        <div class="success">✅ User authenticated</div>
                        <pre>Token Payload: ${JSON.stringify(payload, null, 2)}</pre>
                    `;
                } catch (error) {
                    userInfo.innerHTML = `
                        <div class="success">✅ Token exists but can't decode</div>
                        <div>Token: ${authToken.substring(0, 50)}...</div>
                    `;
                }
            } else {
                userInfo.innerHTML = '<div class="error">❌ No authentication token found</div>';
            }
        }

        function createTestNFT() {
            const testNFT = {
                id: `test-nft-${Date.now()}`,
                name: `Test Campaign Rare NFT`,
                description: `A test NFT for debugging purposes`,
                rarity: 'Rare',
                currentScore: 85,
                imageUrl: '',
                metadata: { test: true },
                userId: 'test-user-id',
                campaignId: 'test-campaign-id',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            const existingNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            existingNFTs.push(testNFT);
            localStorage.setItem('user_nfts', JSON.stringify(existingNFTs));
            
            document.getElementById('testResults').innerHTML = `
                <div class="success">✅ Test NFT created</div>
                <div class="nft">
                    <strong>${testNFT.name}</strong><br>
                    Rarity: ${testNFT.rarity} | Score: ${testNFT.currentScore}<br>
                    User ID: ${testNFT.userId}
                </div>
            `;
            
            checkNFTStorage();
        }

        function createMultipleNFTs() {
            const rarities = ['Common', 'Rare', 'Legendary'];
            const existingNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            
            for (let i = 0; i < 3; i++) {
                const rarity = rarities[i];
                const testNFT = {
                    id: `test-nft-${Date.now()}-${i}`,
                    name: `Test ${rarity} NFT`,
                    description: `A test ${rarity.toLowerCase()} NFT`,
                    rarity: rarity,
                    currentScore: 50 + (i * 20),
                    imageUrl: '',
                    metadata: { test: true, index: i },
                    userId: 'test-user-id',
                    campaignId: 'test-campaign-id',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                existingNFTs.push(testNFT);
            }
            
            localStorage.setItem('user_nfts', JSON.stringify(existingNFTs));
            
            document.getElementById('testResults').innerHTML = `
                <div class="success">✅ Created 3 test NFTs (Common, Rare, Legendary)</div>
            `;
            
            checkNFTStorage();
        }

        // Check storage on page load
        window.onload = function() {
            checkNFTStorage();
            checkUserInfo();
        };
    </script>
</body>
</html>
