// Enterprise Project Query Model (Read Side) - Template
import { ApiProperty } from '@nestjs/swagger';

export class ProjectQueryDto {
  @ApiProperty({ description: 'Project ID' })
  id: string;

  @ApiProperty({ description: 'Display name' })
  displayName: string;

  @ApiProperty({ description: 'Project owner ID' })
  ownerId: string;

  @ApiProperty({ description: 'Project status' })
  status: string;

  @ApiProperty({ description: 'Participant count' })
  participantCount: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;
}
