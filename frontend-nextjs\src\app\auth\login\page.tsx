'use client'

import {
  <PERSON>,
  But<PERSON>,
  Container,
  Heading,
  Input,
  VStack,
  Text,
  Link
} from '@chakra-ui/react'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import NextLink from 'next/link'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { login, connectTwitter } = useAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      await login({ email, password })
      router.push('/dashboard')
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Container maxW="md" py={12}>
      <VStack gap={8}>
        <Box textAlign="center">
          <Heading as="h1" size="xl" mb={2}>
            Welcome Back
          </Heading>
          <Text color="gray.600">
            Sign in to your Social NFT Platform account
          </Text>
        </Box>

        <Box w="full" bg="white" p={8} borderRadius="lg" boxShadow="lg">
          <form onSubmit={handleSubmit}>
            <VStack gap={4}>
              {error && (
                <Box p={3} bg="red.50" borderRadius="md" color="red.600">
                  {error}
                </Box>
              )}

              <Box>
                <Text mb={2} fontWeight="medium">Email *</Text>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                />
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Password *</Text>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                />
              </Box>

              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                w="full"
                loading={isLoading}
              >
                Sign In
              </Button>

              {/* Divider */}
              <Box position="relative" py={4}>
                <Box position="absolute" inset="0" display="flex" alignItems="center">
                  <Box w="full" h="1px" bg="gray.200" />
                </Box>
                <Box position="relative" display="flex" justifyContent="center">
                  <Text bg="white" px={4} color="gray.500" fontSize="sm">
                    or continue with
                  </Text>
                </Box>
              </Box>

              {/* Twitter OAuth Button */}
              <Button
                onClick={connectTwitter}
                colorScheme="blue"
                size="lg"
                w="full"
                variant="outline"
                bg="twitter.50"
                borderColor="twitter.200"
                _hover={{ bg: "twitter.100" }}
              >
                🐦 Continue with Twitter
              </Button>
            </VStack>
          </form>

          <Box textAlign="center" mt={6}>
            <Text>
              Don't have an account?{' '}
              <Link as={NextLink} href="/auth/register" color="blue.500">
                Sign up here
              </Link>
            </Text>
          </Box>
        </Box>
      </VStack>
    </Container>
  )
}
