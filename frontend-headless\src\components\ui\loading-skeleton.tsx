interface LoadingSkeletonProps {
  className?: string
  rows?: number
  showAvatar?: boolean
}

export function LoadingSkeleton({ className = '', rows = 3, showAvatar = false }: LoadingSkeletonProps) {
  return (
    <div className={`animate-pulse ${className}`}>
      {[...Array(rows)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg mb-4 last:mb-0">
          {showAvatar && (
            <div className="h-16 w-16 bg-gray-300 rounded-lg flex-shrink-0"></div>
          )}
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
            <div className="h-3 bg-gray-300 rounded w-1/4"></div>
          </div>
          <div className="text-right space-y-2">
            <div className="h-4 bg-gray-300 rounded w-16"></div>
            <div className="h-3 bg-gray-300 rounded w-12"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function CardLoadingSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`bg-white shadow rounded-lg ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded w-1/3"></div>
        </div>
      </div>
      <div className="p-6">
        <LoadingSkeleton rows={3} showAvatar={true} />
      </div>
    </div>
  )
}

export function ButtonLoadingSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="h-10 bg-gray-300 rounded-md w-full"></div>
    </div>
  )
}

export function StatsLoadingSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-300 rounded"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-300 rounded w-16 mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
