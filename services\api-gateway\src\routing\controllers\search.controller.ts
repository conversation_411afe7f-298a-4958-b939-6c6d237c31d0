import { <PERSON>, Get, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('Search')
@Controller('search')
export class SearchController {
  constructor(private readonly proxyService: ProxyService) {}

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Perform advanced search across platform entities' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'type', type: String, required: true, description: 'Search type (global, users, campaigns, nfts, marketplace)' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', type: String, required: false, description: 'Sort order (asc, desc)' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiQuery({ name: 'includeSuggestions', type: Boolean, required: false, description: 'Include search suggestions' })
  @ApiQuery({ name: 'includeAnalytics', type: Boolean, required: false, description: 'Include search analytics' })
  @ApiQuery({ name: 'dateFrom', type: String, required: false, description: 'Filter from date (ISO string)' })
  @ApiQuery({ name: 'dateTo', type: String, required: false, description: 'Filter to date (ISO string)' })
  @ApiQuery({ name: 'location', type: String, required: false, description: 'Location filter' })
  @ApiQuery({ name: 'language', type: String, required: false, description: 'Language filter' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid search parameters' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  async search(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search?${queryString}` : '/api/search';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Search request failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('users')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Search users with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', type: String, required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', type: String, required: false, description: 'Sort order' })
  @ApiQuery({ name: 'verified', type: Boolean, required: false, description: 'Filter by verified users' })
  @ApiQuery({ name: 'includeFacets', type: Boolean, required: false, description: 'Include faceted results' })
  @ApiResponse({ status: 200, description: 'User search results retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  async searchUsers(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-users-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/users?${queryString}` : '/api/search/users';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'User search failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('campaigns')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Search campaigns with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'status', type: String, required: false, description: 'Campaign status filter' })
  @ApiQuery({ name: 'campaignType', type: String, required: false, description: 'Campaign type filter' })
  @ApiResponse({ status: 200, description: 'Campaign search results retrieved successfully' })
  async searchCampaigns(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-campaigns-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/campaigns?${queryString}` : '/api/search/campaigns';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Campaign search failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('nfts')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Search NFTs with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'rarity', type: String, required: false, description: 'NFT rarity filter' })
  @ApiQuery({ name: 'collection', type: String, required: false, description: 'Collection filter' })
  @ApiResponse({ status: 200, description: 'NFT search results retrieved successfully' })
  async searchNFTs(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-nfts-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/nfts?${queryString}` : '/api/search/nfts';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'NFT search failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('marketplace')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Search marketplace listings with advanced filtering' })
  @ApiQuery({ name: 'query', type: String, required: false, description: 'Search query string' })
  @ApiQuery({ name: 'minPrice', type: Number, required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', type: Number, required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'currency', type: String, required: false, description: 'Currency filter' })
  @ApiResponse({ status: 200, description: 'Marketplace search results retrieved successfully' })
  async searchMarketplace(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-marketplace-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/marketplace?${queryString}` : '/api/search/marketplace';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Marketplace search failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('autocomplete')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get autocomplete suggestions for search queries' })
  @ApiQuery({ name: 'query', type: String, required: true, description: 'Search query for autocomplete' })
  @ApiQuery({ name: 'type', type: String, required: true, description: 'Search type' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum suggestions' })
  @ApiResponse({ status: 200, description: 'Autocomplete suggestions retrieved successfully' })
  async autocomplete(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-autocomplete-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/autocomplete?${queryString}` : '/api/search/autocomplete';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Autocomplete request failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('popular')
  @ApiOperation({ summary: 'Get popular search queries (public endpoint)' })
  @ApiQuery({ name: 'type', type: String, required: false, description: 'Search type filter' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum results' })
  @ApiResponse({ status: 200, description: 'Popular searches retrieved successfully' })
  async getPopularSearches(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-popular-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/popular?${queryString}` : '/api/search/popular';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Popular searches request failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('analytics')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get search analytics and insights' })
  @ApiQuery({ name: 'timeframe', type: String, required: false, description: 'Analytics timeframe (7d, 30d, 90d)' })
  @ApiQuery({ name: 'type', type: String, required: false, description: 'Filter by search type' })
  @ApiResponse({ status: 200, description: 'Search analytics retrieved successfully' })
  async getSearchAnalytics(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-search-analytics-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/analytics?${queryString}` : '/api/search/analytics';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Search analytics request failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('suggestions/:query')
  @ApiOperation({ summary: 'Get search suggestions for a query (public endpoint)' })
  @ApiParam({ name: 'query', description: 'Search query to get suggestions for' })
  @ApiQuery({ name: 'type', type: String, required: false, description: 'Search type (default: global)' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Maximum suggestions (default: 5)' })
  @ApiResponse({ status: 200, description: 'Search suggestions retrieved successfully' })
  async getSearchSuggestions(
    @Param('query') queryParam: string,
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-suggestions-${Date.now()}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/search/suggestions/${queryParam}?${queryString}` : `/api/search/suggestions/${queryParam}`;

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Search suggestions request failed',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }
}
