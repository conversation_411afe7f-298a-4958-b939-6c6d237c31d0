// Step 5.1.3 - Gateway-to-Service Routing Test
const axios = require('axios');

async function testGatewayRouting() {
  console.log('🔀 Step 5.1.3: Gateway-to-Service Routing Test');
  
  try {
    // Test 1: Gateway User Creation
    console.log('\n🚪 Test 1: Gateway User Creation...');
    const timestamp = Date.now();
    const createResponse = await axios.post('http://localhost:3010/api/users', {
      username: `gateway_test_${timestamp}`,
      email: `gateway_test_${timestamp}@example.com`,
      password: 'SecurePassword123',
      role: 'user'
    }, {
      headers: { 'X-Correlation-ID': 'gateway-create-5.1.3' }
    });
    console.log('✅ Gateway user creation:', createResponse.data.success);
    const userId = createResponse.data.data.id;
    
    // Test 2: Gateway User Retrieval
    console.log('\n🔍 Test 2: Gateway User Retrieval...');
    const getResponse = await axios.get(`http://localhost:3010/api/users/${userId}`, {
      headers: { 'X-Correlation-ID': 'gateway-get-5.1.3' }
    });
    console.log('✅ Gateway user retrieval:', getResponse.data.success);
    
    // Test 3: Gateway Pagination
    console.log('\n📄 Test 3: Gateway Pagination...');
    const paginationResponse = await axios.get('http://localhost:3010/api/users?limit=5&offset=0', {
      headers: { 'X-Correlation-ID': 'gateway-pagination-5.1.3' }
    });
    console.log('✅ Gateway pagination:', paginationResponse.data.success);
    console.log('   Users returned:', paginationResponse.data.data.users.length);
    console.log('   Pagination info:', !!paginationResponse.data.data.pagination);
    
    console.log('\n🎉 Step 5.1.3 PASSED: Gateway Routing Working');
    return { success: true, userId };
    
  } catch (error) {
    console.error('❌ Step 5.1.3 FAILED:', error.message);
    return { success: false };
  }
}

testGatewayRouting();
