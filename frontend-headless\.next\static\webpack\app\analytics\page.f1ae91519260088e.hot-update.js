"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/analytics-dashboard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [platformData, setPlatformData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gainersLosers, setGainersLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [marketValue, setMarketValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('24h');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch platform overview\n            const platformResponse = await fetch('/api/analytics/platform-overview');\n            const platformResult = await platformResponse.json();\n            if (platformResult.success) {\n                setPlatformData(platformResult.data);\n            }\n            // Fetch top gainers/losers\n            const gainersResponse = await fetch(\"/api/analytics/top-gainers-losers?period=\".concat(selectedPeriod, \"&limit=5\"));\n            const gainersResult = await gainersResponse.json();\n            if (gainersResult.success) {\n                setGainersLosers(gainersResult.data);\n            }\n            // Fetch collection market value\n            const marketResponse = await fetch('/api/analytics/collection-market-value');\n            const marketResult = await marketResponse.json();\n            if (marketResult.success) {\n                setMarketValue(marketResult.data);\n            }\n        } catch (err) {\n            setError('Failed to fetch analytics data');\n            console.error('Analytics fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsDashboard.useEffect\"], [\n        selectedPeriod\n    ]);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n        return num.toString();\n    };\n    const formatEth = (eth)=>\"\".concat(eth.toFixed(2), \" ETH\");\n    const getRarityColor = (rarity)=>{\n        switch(rarity.toLowerCase()){\n            case 'legendary':\n                return 'bg-yellow-500';\n            case 'epic':\n                return 'bg-purple-500';\n            case 'rare':\n                return 'bg-blue-500';\n            case 'common':\n                return 'bg-gray-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading analytics data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchAnalyticsData,\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            \"Retry\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Comprehensive platform insights and performance metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.user_growth_rate,\n                                                \"% from last month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"NFTs Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_nfts_generated)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.nft_generation_growth_rate,\n                                                \"% growth rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatEth(platformData.overview.total_volume_eth)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.marketplace_volume_growth_rate,\n                                                \"% volume growth\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Active Campaigns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: platformData.overview.active_campaigns\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                platformData.growth_metrics.campaign_participation_rate,\n                                                \"% participation rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"gainers-losers\",\n                                children: \"Top Gainers/Losers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"market-value\",\n                                children: \"Market Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"performance\",\n                                children: \"Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    children: \"Top Performing Campaigns\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                    children: \"Campaigns with highest engagement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: platformData.top_performing.campaigns.map((campaign, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: campaign.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            formatNumber(campaign.participants),\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                variant: \"secondary\",\n                                                                children: [\n                                                                    formatNumber(campaign.nfts_generated),\n                                                                    \" NFTs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, campaign.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    children: \"NFT Rarity Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                    children: \"Distribution of NFT rarities\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: platformData.top_performing.nft_rarities.map((rarity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full \".concat(getRarityColor(rarity.rarity))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: rarity.rarity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: formatNumber(rarity.count)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            rarity.percentage,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, rarity.rarity, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"gainers-losers\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Calendar, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Period:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    [\n                                        '24h',\n                                        '7d',\n                                        '30d'\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                            variant: selectedPeriod === period ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedPeriod(period),\n                                            children: period\n                                        }, period, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            gainersLosers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingUp, {\n                                                                className: \"h-5 w-5 text-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Top Gainers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                        children: [\n                                                            \"Best performing NFTs in \",\n                                                            selectedPeriod\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: gainersLosers.top_gainers.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: nft.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: getRarityColor(nft.rarity),\n                                                                                    children: nft.rarity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        formatEth(nft.volume_24h),\n                                                                                        \" volume\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 382,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-bold text-green-600\",\n                                                                            children: nft.change_percentage\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                \"Score: \",\n                                                                                nft.current_score\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, nft.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingDown, {\n                                                                className: \"h-5 w-5 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Top Losers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                        children: [\n                                                            \"Declining NFTs in \",\n                                                            selectedPeriod\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: gainersLosers.top_losers.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: nft.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: getRarityColor(nft.rarity),\n                                                                                    children: nft.rarity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 413,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        formatEth(nft.volume_24h),\n                                                                                        \" volume\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 416,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-bold text-red-600\",\n                                                                            children: nft.change_percentage\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                \"Score: \",\n                                                                                nft.current_score\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, nft.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            gainersLosers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                children: \"Market Summary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                children: [\n                                                    \"Overall market performance for \",\n                                                    selectedPeriod\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: formatEth(gainersLosers.market_summary.total_volume_24h)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Total Volume\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"mt-1\",\n                                                            children: gainersLosers.market_summary.volume_change_24h\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: gainersLosers.market_summary.average_score_change\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Avg Score Change\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: gainersLosers.market_summary.most_active_rarity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Most Active Rarity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap justify-center gap-1\",\n                                                            children: gainersLosers.market_summary.trending_themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: theme\n                                                                }, theme, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground mt-1\",\n                                                            children: \"Trending Themes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"market-value\",\n                        className: \"space-y-4\",\n                        children: marketValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    children: \"Market Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                    children: \"Collection market value and statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold\",\n                                                                children: formatEth(marketValue.market_overview.total_market_value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Total Market Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"mt-1\",\n                                                                children: marketValue.market_overview.market_cap_change_24h\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold\",\n                                                                children: marketValue.market_overview.total_collections\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Collections\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold\",\n                                                                children: formatNumber(marketValue.market_overview.total_nfts)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Total NFTs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold\",\n                                                                children: formatEth(marketValue.market_overview.average_nft_value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Avg NFT Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    children: \"Collection Market Value\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                                    children: \"Market value by collection (Bubble Map Data)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: marketValue.bubble_map_data.map((collection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg\",\n                                                        style: {\n                                                            borderColor: collection.color\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: collection.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: getRarityColor(collection.rarity),\n                                                                        children: collection.rarity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Value\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-bold\",\n                                                                                children: formatEth(collection.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Size\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-bold\",\n                                                                                children: formatNumber(collection.size)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"24h Change\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-bold \".concat(collection.change_24h.startsWith('+') ? 'text-green-600' : 'text-red-600'),\n                                                                                children: collection.change_24h\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Color\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 rounded-full border\",\n                                                                                style: {\n                                                                                    backgroundColor: collection.color\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, collection.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"performance\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                            children: \"Performance Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                            children: \"Coming soon - Advanced performance metrics and charts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-32\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LineChart, {\n                                                    className: \"h-12 w-12 mx-auto text-muted-foreground mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Advanced charts and performance metrics will be available here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"f7c2j0OrDvpHwYxeRnBil/GM8vc=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9hbmFseXRpY3MtZGFzaGJvYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUVtRDtBQVdkO0FBK0Z0QixTQUFTUTs7SUFDdEIsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1QsK0NBQVFBLENBQTBCO0lBQzFFLE1BQU0sQ0FBQ1UsZUFBZUMsaUJBQWlCLEdBQUdYLCtDQUFRQSxDQUEwQjtJQUM1RSxNQUFNLENBQUNZLGFBQWFDLGVBQWUsR0FBR2IsK0NBQVFBLENBQStCO0lBQzdFLE1BQU0sQ0FBQ2MsU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnQixPQUFPQyxTQUFTLEdBQUdqQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDa0IsZ0JBQWdCQyxrQkFBa0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ29CLFdBQVdDLGFBQWEsR0FBR3JCLCtDQUFRQSxDQUFDO0lBRTNDLE1BQU1zQixxQkFBcUI7UUFDekIsSUFBSTtZQUNGUCxXQUFXO1lBQ1hFLFNBQVM7WUFFVCwwQkFBMEI7WUFDMUIsTUFBTU0sbUJBQW1CLE1BQU1DLE1BQU07WUFDckMsTUFBTUMsaUJBQWlCLE1BQU1GLGlCQUFpQkcsSUFBSTtZQUNsRCxJQUFJRCxlQUFlRSxPQUFPLEVBQUU7Z0JBQzFCbEIsZ0JBQWdCZ0IsZUFBZUcsSUFBSTtZQUNyQztZQUVBLDJCQUEyQjtZQUMzQixNQUFNQyxrQkFBa0IsTUFBTUwsTUFBTSw0Q0FBMkQsT0FBZk4sZ0JBQWU7WUFDL0YsTUFBTVksZ0JBQWdCLE1BQU1ELGdCQUFnQkgsSUFBSTtZQUNoRCxJQUFJSSxjQUFjSCxPQUFPLEVBQUU7Z0JBQ3pCaEIsaUJBQWlCbUIsY0FBY0YsSUFBSTtZQUNyQztZQUVBLGdDQUFnQztZQUNoQyxNQUFNRyxpQkFBaUIsTUFBTVAsTUFBTTtZQUNuQyxNQUFNUSxlQUFlLE1BQU1ELGVBQWVMLElBQUk7WUFDOUMsSUFBSU0sYUFBYUwsT0FBTyxFQUFFO2dCQUN4QmQsZUFBZW1CLGFBQWFKLElBQUk7WUFDbEM7UUFFRixFQUFFLE9BQU9LLEtBQUs7WUFDWmhCLFNBQVM7WUFDVGlCLFFBQVFsQixLQUFLLENBQUMsMEJBQTBCaUI7UUFDMUMsU0FBVTtZQUNSbEIsV0FBVztRQUNiO0lBQ0Y7SUFFQWQsZ0RBQVNBO3dDQUFDO1lBQ1JxQjtRQUNGO3VDQUFHO1FBQUNKO0tBQWU7SUFFbkIsTUFBTWlCLGVBQWUsQ0FBQ0M7UUFDcEIsSUFBSUEsT0FBTyxTQUFTLE9BQU8sR0FBOEIsT0FBM0IsQ0FBQ0EsTUFBTSxPQUFNLEVBQUdDLE9BQU8sQ0FBQyxJQUFHO1FBQ3pELElBQUlELE9BQU8sTUFBTSxPQUFPLEdBQTJCLE9BQXhCLENBQUNBLE1BQU0sSUFBRyxFQUFHQyxPQUFPLENBQUMsSUFBRztRQUNuRCxPQUFPRCxJQUFJRSxRQUFRO0lBQ3JCO0lBRUEsTUFBTUMsWUFBWSxDQUFDQyxNQUFnQixHQUFrQixPQUFmQSxJQUFJSCxPQUFPLENBQUMsSUFBRztJQUVyRCxNQUFNSSxpQkFBaUIsQ0FBQ0M7UUFDdEIsT0FBUUEsT0FBT0MsV0FBVztZQUN4QixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFVLE9BQU87WUFDdEI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsSUFBSTdCLFNBQVM7UUFDWCxxQkFDRSw4REFBQzhCO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3hDLDJLQUFhQTt3QkFBQ3dDLFdBQVU7Ozs7OztrQ0FDekIsOERBQUNDO2tDQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlkO0lBRUEsSUFBSTlCLE9BQU87UUFDVCxxQkFDRSw4REFBQzRCO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQXFCN0I7Ozs7OztrQ0FDbEMsOERBQUNnQzt3QkFDQ0MsU0FBUzNCO3dCQUNUdUIsV0FBVTs7MENBRVYsOERBQUN4QywyS0FBYUE7Z0NBQUN3QyxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNcEQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDTTtnQ0FBR0wsV0FBVTswQ0FBbUM7Ozs7OzswQ0FDakQsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O2tDQUkvQiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNHOzRCQUNDQyxTQUFTM0I7NEJBQ1R1QixXQUFVOzs4Q0FFViw4REFBQ3hDLDJLQUFhQTtvQ0FBQ3dDLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU8vQ3JDLDhCQUNDLDhEQUFDb0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNHOzRDQUFFRixXQUFVO3NEQUFvQzs7Ozs7O3NEQUNqRCw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQW9DVixhQUFhM0IsYUFBYTJDLFFBQVEsQ0FBQ0MsV0FBVzs7Ozs7O3NEQUMvRiw4REFBQ0w7NENBQUVGLFdBQVU7O2dEQUF3QjtnREFDakNyQyxhQUFhNkMsY0FBYyxDQUFDQyxnQkFBZ0I7Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBR25ELDhEQUFDcEQsMktBQVNBO29DQUFDMkMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXpCLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNHOzRDQUFFRixXQUFVO3NEQUFvQzs7Ozs7O3NEQUNqRCw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQW9DVixhQUFhM0IsYUFBYTJDLFFBQVEsQ0FBQ0ksb0JBQW9COzs7Ozs7c0RBQ3hHLDhEQUFDUjs0Q0FBRUYsV0FBVTs7Z0RBQXdCO2dEQUNqQ3JDLGFBQWE2QyxjQUFjLENBQUNHLDBCQUEwQjtnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FHN0QsOERBQUNyRCwyS0FBWUE7b0NBQUMwQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJNUIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ0c7NENBQUVGLFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBb0NOLFVBQVUvQixhQUFhMkMsUUFBUSxDQUFDTSxnQkFBZ0I7Ozs7OztzREFDakcsOERBQUNWOzRDQUFFRixXQUFVOztnREFBd0I7Z0RBQ2pDckMsYUFBYTZDLGNBQWMsQ0FBQ0ssOEJBQThCO2dEQUFDOzs7Ozs7Ozs7Ozs7OzhDQUdqRSw4REFBQ3RELDJLQUFrQkE7b0NBQUN5QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJbEMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ0c7NENBQUVGLFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBb0NyQyxhQUFhMkMsUUFBUSxDQUFDUSxnQkFBZ0I7Ozs7OztzREFDdkYsOERBQUNaOzRDQUFFRixXQUFVOztnREFDVnJDLGFBQWE2QyxjQUFjLENBQUNPLDJCQUEyQjtnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FHN0QsOERBQUN0RCwyS0FBeUJBO29DQUFDdUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzdDLDhEQUFDZ0I7Z0JBQUtDLGNBQWE7Z0JBQVdqQixXQUFVOztrQ0FDdEMsOERBQUNrQjs7MENBQ0MsOERBQUNDO2dDQUFZQyxPQUFNOzBDQUFXOzs7Ozs7MENBQzlCLDhEQUFDRDtnQ0FBWUMsT0FBTTswQ0FBaUI7Ozs7OzswQ0FDcEMsOERBQUNEO2dDQUFZQyxPQUFNOzBDQUFlOzs7Ozs7MENBQ2xDLDhEQUFDRDtnQ0FBWUMsT0FBTTswQ0FBYzs7Ozs7Ozs7Ozs7O2tDQUduQyw4REFBQ0M7d0JBQVlELE9BQU07d0JBQVdwQixXQUFVO2tDQUNyQ3JDLDhCQUNDLDhEQUFDb0M7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDc0I7O3NEQUNDLDhEQUFDQzs7OERBQ0MsOERBQUNDOzhEQUFVOzs7Ozs7OERBQ1gsOERBQUNDOzhEQUFnQjs7Ozs7Ozs7Ozs7O3NEQUVuQiw4REFBQ0M7c0RBQ0MsNEVBQUMzQjtnREFBSUMsV0FBVTswREFDWnJDLGFBQWFnRSxjQUFjLENBQUNDLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLFVBQVVDLHNCQUNwRCw4REFBQ2hDO3dEQUFzQkMsV0FBVTs7MEVBQy9CLDhEQUFDRDs7a0ZBQ0MsOERBQUNHO3dFQUFFRixXQUFVO2tGQUFlOEIsU0FBU0UsSUFBSTs7Ozs7O2tGQUN6Qyw4REFBQzlCO3dFQUFFRixXQUFVOzs0RUFDVlYsYUFBYXdDLFNBQVNHLFlBQVk7NEVBQUU7Ozs7Ozs7Ozs7Ozs7MEVBR3pDLDhEQUFDQztnRUFBTUMsU0FBUTs7b0VBQ1o3QyxhQUFhd0MsU0FBU00sY0FBYztvRUFBRTs7Ozs7Ozs7dURBUmpDTixTQUFTTyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBaUI3Qiw4REFBQ2Y7O3NEQUNDLDhEQUFDQzs7OERBQ0MsOERBQUNDOzhEQUFVOzs7Ozs7OERBQ1gsOERBQUNDOzhEQUFnQjs7Ozs7Ozs7Ozs7O3NEQUVuQiw4REFBQ0M7c0RBQ0MsNEVBQUMzQjtnREFBSUMsV0FBVTswREFDWnJDLGFBQWFnRSxjQUFjLENBQUNXLFlBQVksQ0FBQ1QsR0FBRyxDQUFDLENBQUNoQyx1QkFDN0MsOERBQUNFO3dEQUF3QkMsV0FBVTs7MEVBQ2pDLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFXLHdCQUFzRCxPQUE5QkosZUFBZUMsT0FBT0EsTUFBTTs7Ozs7O2tGQUNwRSw4REFBQ0k7d0VBQUtELFdBQVU7a0ZBQWVILE9BQU9BLE1BQU07Ozs7Ozs7Ozs7OzswRUFFOUMsOERBQUNFO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0U7d0VBQUVGLFdBQVU7a0ZBQWVWLGFBQWFPLE9BQU8wQyxLQUFLOzs7Ozs7a0ZBQ3JELDhEQUFDckM7d0VBQUVGLFdBQVU7OzRFQUFpQ0gsT0FBTzJDLFVBQVU7NEVBQUM7Ozs7Ozs7Ozs7Ozs7O3VEQVAxRDNDLE9BQU9BLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWtCckMsOERBQUN3Qjt3QkFBWUQsT0FBTTt3QkFBaUJwQixXQUFVOzswQ0FFNUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3lDO3dDQUFTekMsV0FBVTs7Ozs7O2tEQUNwQiw4REFBQ0M7d0NBQUtELFdBQVU7a0RBQXNCOzs7Ozs7b0NBQ3JDO3dDQUFDO3dDQUFPO3dDQUFNO3FDQUFNLENBQUM2QixHQUFHLENBQUMsQ0FBQ2EsdUJBQ3pCLDhEQUFDQzs0Q0FFQ1IsU0FBUzlELG1CQUFtQnFFLFNBQVMsWUFBWTs0Q0FDakRFLE1BQUs7NENBQ0x4QyxTQUFTLElBQU05QixrQkFBa0JvRTtzREFFaENBOzJDQUxJQTs7Ozs7Ozs7Ozs7NEJBVVY3RSwrQkFDQyw4REFBQ2tDO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ3NCOzswREFDQyw4REFBQ0M7O2tFQUNDLDhEQUFDQzt3REFBVXhCLFdBQVU7OzBFQUNuQiw4REFBQzZDO2dFQUFXN0MsV0FBVTs7Ozs7OzBFQUN0Qiw4REFBQ0M7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFUiw4REFBQ3dCOzs0REFBZ0I7NERBQXlCcEQ7Ozs7Ozs7Ozs7Ozs7MERBRTVDLDhEQUFDcUQ7MERBQ0MsNEVBQUMzQjtvREFBSUMsV0FBVTs4REFDWm5DLGNBQWNpRixXQUFXLENBQUNqQixHQUFHLENBQUMsQ0FBQ2tCLG9CQUM5Qiw4REFBQ2hEOzREQUFpQkMsV0FBVTs7OEVBQzFCLDhEQUFDRDs7c0ZBQ0MsOERBQUNHOzRFQUFFRixXQUFVO3NGQUF1QitDLElBQUlmLElBQUk7Ozs7OztzRkFDNUMsOERBQUNqQzs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNrQztvRkFBTUMsU0FBUTtvRkFBVW5DLFdBQVdKLGVBQWVtRCxJQUFJbEQsTUFBTTs4RkFDMURrRCxJQUFJbEQsTUFBTTs7Ozs7OzhGQUViLDhEQUFDSTtvRkFBS0QsV0FBVTs7d0ZBQ2JOLFVBQVVxRCxJQUFJQyxVQUFVO3dGQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUlqQyw4REFBQ2pEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0U7NEVBQUVGLFdBQVU7c0ZBQTRCK0MsSUFBSUUsaUJBQWlCOzs7Ozs7c0ZBQzlELDhEQUFDL0M7NEVBQUVGLFdBQVU7O2dGQUFnQztnRkFBUStDLElBQUlHLGFBQWE7Ozs7Ozs7Ozs7Ozs7OzJEQWRoRUgsSUFBSVYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQXVCeEIsOERBQUNmOzswREFDQyw4REFBQ0M7O2tFQUNDLDhEQUFDQzt3REFBVXhCLFdBQVU7OzBFQUNuQiw4REFBQ21EO2dFQUFhbkQsV0FBVTs7Ozs7OzBFQUN4Qiw4REFBQ0M7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFUiw4REFBQ3dCOzs0REFBZ0I7NERBQW1CcEQ7Ozs7Ozs7Ozs7Ozs7MERBRXRDLDhEQUFDcUQ7MERBQ0MsNEVBQUMzQjtvREFBSUMsV0FBVTs4REFDWm5DLGNBQWN1RixVQUFVLENBQUN2QixHQUFHLENBQUMsQ0FBQ2tCLG9CQUM3Qiw4REFBQ2hEOzREQUFpQkMsV0FBVTs7OEVBQzFCLDhEQUFDRDs7c0ZBQ0MsOERBQUNHOzRFQUFFRixXQUFVO3NGQUF1QitDLElBQUlmLElBQUk7Ozs7OztzRkFDNUMsOERBQUNqQzs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNrQztvRkFBTUMsU0FBUTtvRkFBVW5DLFdBQVdKLGVBQWVtRCxJQUFJbEQsTUFBTTs4RkFDMURrRCxJQUFJbEQsTUFBTTs7Ozs7OzhGQUViLDhEQUFDSTtvRkFBS0QsV0FBVTs7d0ZBQ2JOLFVBQVVxRCxJQUFJQyxVQUFVO3dGQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUlqQyw4REFBQ2pEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0U7NEVBQUVGLFdBQVU7c0ZBQTBCK0MsSUFBSUUsaUJBQWlCOzs7Ozs7c0ZBQzVELDhEQUFDL0M7NEVBQUVGLFdBQVU7O2dGQUFnQztnRkFBUStDLElBQUlHLGFBQWE7Ozs7Ozs7Ozs7Ozs7OzJEQWRoRUgsSUFBSVYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQXlCM0J4RSwrQkFDQyw4REFBQ3lEOztrREFDQyw4REFBQ0M7OzBEQUNDLDhEQUFDQzswREFBVTs7Ozs7OzBEQUNYLDhEQUFDQzs7b0RBQWdCO29EQUFnQ3BEOzs7Ozs7Ozs7Ozs7O2tEQUVuRCw4REFBQ3FEO2tEQUNDLDRFQUFDM0I7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNFOzREQUFFRixXQUFVO3NFQUFzQk4sVUFBVTdCLGNBQWN3RixjQUFjLENBQUNDLGdCQUFnQjs7Ozs7O3NFQUMxRiw4REFBQ3BEOzREQUFFRixXQUFVO3NFQUFnQzs7Ozs7O3NFQUM3Qyw4REFBQ2tDOzREQUFNQyxTQUFROzREQUFVbkMsV0FBVTtzRUFDaENuQyxjQUFjd0YsY0FBYyxDQUFDRSxpQkFBaUI7Ozs7Ozs7Ozs7Ozs4REFHbkQsOERBQUN4RDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNFOzREQUFFRixXQUFVO3NFQUFzQm5DLGNBQWN3RixjQUFjLENBQUNHLG9CQUFvQjs7Ozs7O3NFQUNwRiw4REFBQ3REOzREQUFFRixXQUFVO3NFQUFnQzs7Ozs7Ozs7Ozs7OzhEQUUvQyw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBRUYsV0FBVTtzRUFBc0JuQyxjQUFjd0YsY0FBYyxDQUFDSSxrQkFBa0I7Ozs7OztzRUFDbEYsOERBQUN2RDs0REFBRUYsV0FBVTtzRUFBZ0M7Ozs7Ozs7Ozs7Ozs4REFFL0MsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1puQyxjQUFjd0YsY0FBYyxDQUFDSyxlQUFlLENBQUM3QixHQUFHLENBQUMsQ0FBQzhCLHNCQUNqRCw4REFBQ3pCO29FQUFrQkMsU0FBUTtvRUFBWW5DLFdBQVU7OEVBQzlDMkQ7bUVBRFNBOzs7Ozs7Ozs7O3NFQUtoQiw4REFBQ3pEOzREQUFFRixXQUFVO3NFQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUTlELDhEQUFDcUI7d0JBQVlELE9BQU07d0JBQWVwQixXQUFVO2tDQUN6Q2pDLDZCQUNDLDhEQUFDZ0M7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDc0I7O3NEQUNDLDhEQUFDQzs7OERBQ0MsOERBQUNDOzhEQUFVOzs7Ozs7OERBQ1gsOERBQUNDOzhEQUFnQjs7Ozs7Ozs7Ozs7O3NEQUVuQiw4REFBQ0M7c0RBQ0MsNEVBQUMzQjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQXNCTixVQUFVM0IsWUFBWTZGLGVBQWUsQ0FBQ0Msa0JBQWtCOzs7Ozs7MEVBQzNGLDhEQUFDM0Q7Z0VBQUVGLFdBQVU7MEVBQWdDOzs7Ozs7MEVBQzdDLDhEQUFDa0M7Z0VBQU1DLFNBQVE7Z0VBQVVuQyxXQUFVOzBFQUNoQ2pDLFlBQVk2RixlQUFlLENBQUNFLHFCQUFxQjs7Ozs7Ozs7Ozs7O2tFQUd0RCw4REFBQy9EO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQXNCakMsWUFBWTZGLGVBQWUsQ0FBQ0csaUJBQWlCOzs7Ozs7MEVBQ2hGLDhEQUFDN0Q7Z0VBQUVGLFdBQVU7MEVBQWdDOzs7Ozs7Ozs7Ozs7a0VBRS9DLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNFO2dFQUFFRixXQUFVOzBFQUFzQlYsYUFBYXZCLFlBQVk2RixlQUFlLENBQUNJLFVBQVU7Ozs7OzswRUFDdEYsOERBQUM5RDtnRUFBRUYsV0FBVTswRUFBZ0M7Ozs7Ozs7Ozs7OztrRUFFL0MsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQXNCTixVQUFVM0IsWUFBWTZGLGVBQWUsQ0FBQ0ssaUJBQWlCOzs7Ozs7MEVBQzFGLDhEQUFDL0Q7Z0VBQUVGLFdBQVU7MEVBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPckQsOERBQUNzQjs7c0RBQ0MsOERBQUNDOzs4REFDQyw4REFBQ0M7OERBQVU7Ozs7Ozs4REFDWCw4REFBQ0M7OERBQWdCOzs7Ozs7Ozs7Ozs7c0RBRW5CLDhEQUFDQztzREFDQyw0RUFBQzNCO2dEQUFJQyxXQUFVOzBEQUNaakMsWUFBWW1HLGVBQWUsQ0FBQ3JDLEdBQUcsQ0FBQyxDQUFDc0MsMkJBQ2hDLDhEQUFDcEU7d0RBRUNDLFdBQVU7d0RBQ1ZvRSxPQUFPOzREQUFFQyxhQUFhRixXQUFXRyxLQUFLO3dEQUFDOzswRUFFdkMsOERBQUN2RTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN1RTt3RUFBR3ZFLFdBQVU7a0ZBQWlCbUUsV0FBV25DLElBQUk7Ozs7OztrRkFDOUMsOERBQUNFO3dFQUNDQyxTQUFRO3dFQUNSbkMsV0FBV0osZUFBZXVFLFdBQVd0RSxNQUFNO2tGQUUxQ3NFLFdBQVd0RSxNQUFNOzs7Ozs7Ozs7Ozs7MEVBR3RCLDhEQUFDRTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEOzswRkFDQyw4REFBQ0c7Z0ZBQUVGLFdBQVU7MEZBQXdCOzs7Ozs7MEZBQ3JDLDhEQUFDRTtnRkFBRUYsV0FBVTswRkFBYU4sVUFBVXlFLFdBQVcvQyxLQUFLOzs7Ozs7Ozs7Ozs7a0ZBRXRELDhEQUFDckI7OzBGQUNDLDhEQUFDRztnRkFBRUYsV0FBVTswRkFBd0I7Ozs7OzswRkFDckMsOERBQUNFO2dGQUFFRixXQUFVOzBGQUFhVixhQUFhNkUsV0FBV3ZCLElBQUk7Ozs7Ozs7Ozs7OztrRkFFeEQsOERBQUM3Qzs7MEZBQ0MsOERBQUNHO2dGQUFFRixXQUFVOzBGQUF3Qjs7Ozs7OzBGQUNyQyw4REFBQ0U7Z0ZBQUVGLFdBQVcsYUFBdUYsT0FBMUVtRSxXQUFXSyxVQUFVLENBQUNDLFVBQVUsQ0FBQyxPQUFPLG1CQUFtQjswRkFDbkZOLFdBQVdLLFVBQVU7Ozs7Ozs7Ozs7OztrRkFHMUIsOERBQUN6RTs7MEZBQ0MsOERBQUNHO2dGQUFFRixXQUFVOzBGQUF3Qjs7Ozs7OzBGQUNyQyw4REFBQ0Q7Z0ZBQ0NDLFdBQVU7Z0ZBQ1ZvRSxPQUFPO29GQUFFTSxpQkFBaUJQLFdBQVdHLEtBQUs7Z0ZBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBaEM1Q0gsV0FBVzlCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQTZDbEMsOERBQUNoQjt3QkFBWUQsT0FBTTt3QkFBY3BCLFdBQVU7a0NBQ3pDLDRFQUFDc0I7OzhDQUNDLDhEQUFDQzs7c0RBQ0MsOERBQUNDO3NEQUFVOzs7Ozs7c0RBQ1gsOERBQUNDO3NEQUFnQjs7Ozs7Ozs7Ozs7OzhDQUVuQiw4REFBQ0M7OENBQ0MsNEVBQUMzQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDMkU7b0RBQVUzRSxXQUFVOzs7Ozs7OERBQ3JCLDhEQUFDRTtvREFBRUYsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVN2RDtHQXhkd0J0QztLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyelxcRG9jdW1lbnRzXFxBdWdtZW50XFxzb2NpYWwtbmZ0LXBsYXRmb3JtLXYyXFxmcm9udGVuZC1oZWFkbGVzc1xcc3JjXFxjb21wb25lbnRzXFxkYXNoYm9hcmRcXGFuYWx5dGljcy1kYXNoYm9hcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICBUcmVuZGluZ1VwSWNvbixcbiAgVHJlbmRpbmdEb3duSWNvbixcbiAgVXNlcnNJY29uLFxuICBDaGFydEJhckljb24sXG4gIEN1cnJlbmN5RG9sbGFySWNvbixcbiAgQXJyb3dQYXRoSWNvbixcbiAgQ2FsZW5kYXJJY29uLFxuICBGdW5uZWxJY29uLFxuICBQcmVzZW50YXRpb25DaGFydExpbmVJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmludGVyZmFjZSBQbGF0Zm9ybU92ZXJ2aWV3IHtcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIHBlcmlvZDogc3RyaW5nO1xuICBvdmVydmlldzoge1xuICAgIHRvdGFsX3VzZXJzOiBudW1iZXI7XG4gICAgYWN0aXZlX3VzZXJzX3RvZGF5OiBudW1iZXI7XG4gICAgdG90YWxfY2FtcGFpZ25zOiBudW1iZXI7XG4gICAgYWN0aXZlX2NhbXBhaWduczogbnVtYmVyO1xuICAgIHRvdGFsX25mdHNfZ2VuZXJhdGVkOiBudW1iZXI7XG4gICAgbmZ0c19nZW5lcmF0ZWRfdG9kYXk6IG51bWJlcjtcbiAgICB0b3RhbF9uZnRzX21pbnRlZDogbnVtYmVyO1xuICAgIG5mdHNfbWludGVkX3RvZGF5OiBudW1iZXI7XG4gICAgdG90YWxfbWFya2V0cGxhY2Vfc2FsZXM6IG51bWJlcjtcbiAgICBtYXJrZXRwbGFjZV9zYWxlc190b2RheTogbnVtYmVyO1xuICAgIHRvdGFsX3ZvbHVtZV9ldGg6IG51bWJlcjtcbiAgICB2b2x1bWVfdG9kYXlfZXRoOiBudW1iZXI7XG4gIH07XG4gIGdyb3d0aF9tZXRyaWNzOiB7XG4gICAgdXNlcl9ncm93dGhfcmF0ZTogbnVtYmVyO1xuICAgIG5mdF9nZW5lcmF0aW9uX2dyb3d0aF9yYXRlOiBudW1iZXI7XG4gICAgbWFya2V0cGxhY2Vfdm9sdW1lX2dyb3d0aF9yYXRlOiBudW1iZXI7XG4gICAgY2FtcGFpZ25fcGFydGljaXBhdGlvbl9yYXRlOiBudW1iZXI7XG4gIH07XG4gIHRvcF9wZXJmb3JtaW5nOiB7XG4gICAgY2FtcGFpZ25zOiBBcnJheTx7XG4gICAgICBpZDogc3RyaW5nO1xuICAgICAgbmFtZTogc3RyaW5nO1xuICAgICAgcGFydGljaXBhbnRzOiBudW1iZXI7XG4gICAgICBuZnRzX2dlbmVyYXRlZDogbnVtYmVyO1xuICAgIH0+O1xuICAgIG5mdF9yYXJpdGllczogQXJyYXk8e1xuICAgICAgcmFyaXR5OiBzdHJpbmc7XG4gICAgICBjb3VudDogbnVtYmVyO1xuICAgICAgcGVyY2VudGFnZTogbnVtYmVyO1xuICAgIH0+O1xuICB9O1xufVxuXG5pbnRlcmZhY2UgVG9wR2FpbmVyc0xvc2VycyB7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBwZXJpb2Q6IHN0cmluZztcbiAgbGltaXQ6IG51bWJlcjtcbiAgdG9wX2dhaW5lcnM6IEFycmF5PHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBjdXJyZW50X3Njb3JlOiBudW1iZXI7XG4gICAgcHJldmlvdXNfc2NvcmU6IG51bWJlcjtcbiAgICBjaGFuZ2U6IHN0cmluZztcbiAgICBjaGFuZ2VfcGVyY2VudGFnZTogc3RyaW5nO1xuICAgIHJhcml0eTogc3RyaW5nO1xuICAgIHZvbHVtZV8yNGg6IG51bWJlcjtcbiAgICB0cmVuZDogc3RyaW5nO1xuICB9PjtcbiAgdG9wX2xvc2VyczogQXJyYXk8e1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIGN1cnJlbnRfc2NvcmU6IG51bWJlcjtcbiAgICBwcmV2aW91c19zY29yZTogbnVtYmVyO1xuICAgIGNoYW5nZTogc3RyaW5nO1xuICAgIGNoYW5nZV9wZXJjZW50YWdlOiBzdHJpbmc7XG4gICAgcmFyaXR5OiBzdHJpbmc7XG4gICAgdm9sdW1lXzI0aDogbnVtYmVyO1xuICAgIHRyZW5kOiBzdHJpbmc7XG4gIH0+O1xuICBtYXJrZXRfc3VtbWFyeToge1xuICAgIHRvdGFsX3ZvbHVtZV8yNGg6IG51bWJlcjtcbiAgICB2b2x1bWVfY2hhbmdlXzI0aDogc3RyaW5nO1xuICAgIGF2ZXJhZ2Vfc2NvcmVfY2hhbmdlOiBzdHJpbmc7XG4gICAgbW9zdF9hY3RpdmVfcmFyaXR5OiBzdHJpbmc7XG4gICAgdHJlbmRpbmdfdGhlbWVzOiBzdHJpbmdbXTtcbiAgfTtcbn1cblxuaW50ZXJmYWNlIENvbGxlY3Rpb25NYXJrZXRWYWx1ZSB7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBidWJibGVfbWFwX2RhdGE6IEFycmF5PHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICB2YWx1ZTogbnVtYmVyO1xuICAgIHNpemU6IG51bWJlcjtcbiAgICBjaGFuZ2VfMjRoOiBzdHJpbmc7XG4gICAgY29sb3I6IHN0cmluZztcbiAgICByYXJpdHk6IHN0cmluZztcbiAgfT47XG4gIG1hcmtldF9vdmVydmlldzoge1xuICAgIHRvdGFsX21hcmtldF92YWx1ZTogbnVtYmVyO1xuICAgIG1hcmtldF9jYXBfY2hhbmdlXzI0aDogc3RyaW5nO1xuICAgIHRvdGFsX2NvbGxlY3Rpb25zOiBudW1iZXI7XG4gICAgdG90YWxfbmZ0czogbnVtYmVyO1xuICAgIGF2ZXJhZ2VfbmZ0X3ZhbHVlOiBudW1iZXI7XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFuYWx5dGljc0Rhc2hib2FyZCgpIHtcbiAgY29uc3QgW3BsYXRmb3JtRGF0YSwgc2V0UGxhdGZvcm1EYXRhXSA9IHVzZVN0YXRlPFBsYXRmb3JtT3ZlcnZpZXcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2dhaW5lcnNMb3NlcnMsIHNldEdhaW5lcnNMb3NlcnNdID0gdXNlU3RhdGU8VG9wR2FpbmVyc0xvc2VycyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbWFya2V0VmFsdWUsIHNldE1hcmtldFZhbHVlXSA9IHVzZVN0YXRlPENvbGxlY3Rpb25NYXJrZXRWYWx1ZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkUGVyaW9kLCBzZXRTZWxlY3RlZFBlcmlvZF0gPSB1c2VTdGF0ZSgnMjRoJyk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZSgnb3ZlcnZpZXcnKTtcblxuICBjb25zdCBmZXRjaEFuYWx5dGljc0RhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgLy8gRmV0Y2ggcGxhdGZvcm0gb3ZlcnZpZXdcbiAgICAgIGNvbnN0IHBsYXRmb3JtUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hbmFseXRpY3MvcGxhdGZvcm0tb3ZlcnZpZXcnKTtcbiAgICAgIGNvbnN0IHBsYXRmb3JtUmVzdWx0ID0gYXdhaXQgcGxhdGZvcm1SZXNwb25zZS5qc29uKCk7XG4gICAgICBpZiAocGxhdGZvcm1SZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRQbGF0Zm9ybURhdGEocGxhdGZvcm1SZXN1bHQuZGF0YSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEZldGNoIHRvcCBnYWluZXJzL2xvc2Vyc1xuICAgICAgY29uc3QgZ2FpbmVyc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYW5hbHl0aWNzL3RvcC1nYWluZXJzLWxvc2Vycz9wZXJpb2Q9JHtzZWxlY3RlZFBlcmlvZH0mbGltaXQ9NWApO1xuICAgICAgY29uc3QgZ2FpbmVyc1Jlc3VsdCA9IGF3YWl0IGdhaW5lcnNSZXNwb25zZS5qc29uKCk7XG4gICAgICBpZiAoZ2FpbmVyc1Jlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIHNldEdhaW5lcnNMb3NlcnMoZ2FpbmVyc1Jlc3VsdC5kYXRhKTtcbiAgICAgIH1cblxuICAgICAgLy8gRmV0Y2ggY29sbGVjdGlvbiBtYXJrZXQgdmFsdWVcbiAgICAgIGNvbnN0IG1hcmtldFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYW5hbHl0aWNzL2NvbGxlY3Rpb24tbWFya2V0LXZhbHVlJyk7XG4gICAgICBjb25zdCBtYXJrZXRSZXN1bHQgPSBhd2FpdCBtYXJrZXRSZXNwb25zZS5qc29uKCk7XG4gICAgICBpZiAobWFya2V0UmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0TWFya2V0VmFsdWUobWFya2V0UmVzdWx0LmRhdGEpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGZldGNoIGFuYWx5dGljcyBkYXRhJyk7XG4gICAgICBjb25zb2xlLmVycm9yKCdBbmFseXRpY3MgZmV0Y2ggZXJyb3I6JywgZXJyKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hBbmFseXRpY3NEYXRhKCk7XG4gIH0sIFtzZWxlY3RlZFBlcmlvZF0pO1xuXG4gIGNvbnN0IGZvcm1hdE51bWJlciA9IChudW06IG51bWJlcikgPT4ge1xuICAgIGlmIChudW0gPj0gMTAwMDAwMCkgcmV0dXJuIGAkeyhudW0gLyAxMDAwMDAwKS50b0ZpeGVkKDEpfU1gO1xuICAgIGlmIChudW0gPj0gMTAwMCkgcmV0dXJuIGAkeyhudW0gLyAxMDAwKS50b0ZpeGVkKDEpfUtgO1xuICAgIHJldHVybiBudW0udG9TdHJpbmcoKTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRFdGggPSAoZXRoOiBudW1iZXIpID0+IGAke2V0aC50b0ZpeGVkKDIpfSBFVEhgO1xuXG4gIGNvbnN0IGdldFJhcml0eUNvbG9yID0gKHJhcml0eTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChyYXJpdHkudG9Mb3dlckNhc2UoKSkge1xuICAgICAgY2FzZSAnbGVnZW5kYXJ5JzogcmV0dXJuICdiZy15ZWxsb3ctNTAwJztcbiAgICAgIGNhc2UgJ2VwaWMnOiByZXR1cm4gJ2JnLXB1cnBsZS01MDAnO1xuICAgICAgY2FzZSAncmFyZSc6IHJldHVybiAnYmctYmx1ZS01MDAnO1xuICAgICAgY2FzZSAnY29tbW9uJzogcmV0dXJuICdiZy1ncmF5LTUwMCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktNDAwJztcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1bNDAwcHhdXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPEFycm93UGF0aEljb24gY2xhc3NOYW1lPVwiaC02IHctNiBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgIDxzcGFuPkxvYWRpbmcgYW5hbHl0aWNzIGRhdGEuLi48L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLVs0MDBweF1cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBtYi00XCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtmZXRjaEFuYWx5dGljc0RhdGF9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd1BhdGhJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICBSZXRyeVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5BbmFseXRpY3MgRGFzaGJvYXJkPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICBDb21wcmVoZW5zaXZlIHBsYXRmb3JtIGluc2lnaHRzIGFuZCBwZXJmb3JtYW5jZSBtZXRyaWNzXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtmZXRjaEFuYWx5dGljc0RhdGF9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd1BhdGhJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQbGF0Zm9ybSBPdmVydmlldyBDYXJkcyAqL31cbiAgICAgIHtwbGF0Zm9ybURhdGEgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5Ub3RhbCBVc2VyczwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntmb3JtYXROdW1iZXIocGxhdGZvcm1EYXRhLm92ZXJ2aWV3LnRvdGFsX3VzZXJzKX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAre3BsYXRmb3JtRGF0YS5ncm93dGhfbWV0cmljcy51c2VyX2dyb3d0aF9yYXRlfSUgZnJvbSBsYXN0IG1vbnRoXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPFVzZXJzSWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5ORlRzIEdlbmVyYXRlZDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntmb3JtYXROdW1iZXIocGxhdGZvcm1EYXRhLm92ZXJ2aWV3LnRvdGFsX25mdHNfZ2VuZXJhdGVkKX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAre3BsYXRmb3JtRGF0YS5ncm93dGhfbWV0cmljcy5uZnRfZ2VuZXJhdGlvbl9ncm93dGhfcmF0ZX0lIGdyb3d0aCByYXRlXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5Ub3RhbCBWb2x1bWU8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57Zm9ybWF0RXRoKHBsYXRmb3JtRGF0YS5vdmVydmlldy50b3RhbF92b2x1bWVfZXRoKX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAre3BsYXRmb3JtRGF0YS5ncm93dGhfbWV0cmljcy5tYXJrZXRwbGFjZV92b2x1bWVfZ3Jvd3RoX3JhdGV9JSB2b2x1bWUgZ3Jvd3RoXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEN1cnJlbmN5RG9sbGFySWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5BY3RpdmUgQ2FtcGFpZ25zPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3BsYXRmb3JtRGF0YS5vdmVydmlldy5hY3RpdmVfY2FtcGFpZ25zfTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtwbGF0Zm9ybURhdGEuZ3Jvd3RoX21ldHJpY3MuY2FtcGFpZ25fcGFydGljaXBhdGlvbl9yYXRlfSUgcGFydGljaXBhdGlvbiByYXRlXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPFByZXNlbnRhdGlvbkNoYXJ0TGluZUljb24gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBNYWluIEFuYWx5dGljcyBUYWJzICovfVxuICAgICAgPFRhYnMgZGVmYXVsdFZhbHVlPVwib3ZlcnZpZXdcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgPFRhYnNMaXN0PlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cIm92ZXJ2aWV3XCI+T3ZlcnZpZXc8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImdhaW5lcnMtbG9zZXJzXCI+VG9wIEdhaW5lcnMvTG9zZXJzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJtYXJrZXQtdmFsdWVcIj5NYXJrZXQgVmFsdWU8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInBlcmZvcm1hbmNlXCI+UGVyZm9ybWFuY2U8L1RhYnNUcmlnZ2VyPlxuICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIm92ZXJ2aWV3XCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAge3BsYXRmb3JtRGF0YSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgey8qIFRvcCBQZXJmb3JtaW5nIENhbXBhaWducyAqL31cbiAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlRvcCBQZXJmb3JtaW5nIENhbXBhaWduczwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5DYW1wYWlnbnMgd2l0aCBoaWdoZXN0IGVuZ2FnZW1lbnQ8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAge3BsYXRmb3JtRGF0YS50b3BfcGVyZm9ybWluZy5jYW1wYWlnbnMubWFwKChjYW1wYWlnbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y2FtcGFpZ24uaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Y2FtcGFpZ24ubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihjYW1wYWlnbi5wYXJ0aWNpcGFudHMpfSBwYXJ0aWNpcGFudHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKGNhbXBhaWduLm5mdHNfZ2VuZXJhdGVkKX0gTkZUc1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgICAgey8qIE5GVCBSYXJpdHkgRGlzdHJpYnV0aW9uICovfVxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+TkZUIFJhcml0eSBEaXN0cmlidXRpb248L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+RGlzdHJpYnV0aW9uIG9mIE5GVCByYXJpdGllczwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICB7cGxhdGZvcm1EYXRhLnRvcF9wZXJmb3JtaW5nLm5mdF9yYXJpdGllcy5tYXAoKHJhcml0eSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtyYXJpdHkucmFyaXR5fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtnZXRSYXJpdHlDb2xvcihyYXJpdHkucmFyaXR5KX1gfSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntyYXJpdHkucmFyaXR5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Zvcm1hdE51bWJlcihyYXJpdHkuY291bnQpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57cmFyaXR5LnBlcmNlbnRhZ2V9JTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZ2FpbmVycy1sb3NlcnNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICB7LyogUGVyaW9kIFNlbGVjdG9yICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+UGVyaW9kOjwvc3Bhbj5cbiAgICAgICAgICAgIHtbJzI0aCcsICc3ZCcsICczMGQnXS5tYXAoKHBlcmlvZCkgPT4gKFxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtwZXJpb2R9XG4gICAgICAgICAgICAgICAgdmFyaWFudD17c2VsZWN0ZWRQZXJpb2QgPT09IHBlcmlvZCA/ICdkZWZhdWx0JyA6ICdvdXRsaW5lJ31cbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUGVyaW9kKHBlcmlvZCl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7cGVyaW9kfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge2dhaW5lcnNMb3NlcnMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgIHsvKiBUb3AgR2FpbmVycyAqL31cbiAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+VG9wIEdhaW5lcnM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+QmVzdCBwZXJmb3JtaW5nIE5GVHMgaW4ge3NlbGVjdGVkUGVyaW9kfTwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICB7Z2FpbmVyc0xvc2Vycy50b3BfZ2FpbmVycy5tYXAoKG5mdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtuZnQuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntuZnQubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPXtnZXRSYXJpdHlDb2xvcihuZnQucmFyaXR5KX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmZ0LnJhcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RXRoKG5mdC52b2x1bWVfMjRoKX0gdm9sdW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPntuZnQuY2hhbmdlX3BlcmNlbnRhZ2V9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlNjb3JlOiB7bmZ0LmN1cnJlbnRfc2NvcmV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgICAgIHsvKiBUb3AgTG9zZXJzICovfVxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ0Rvd24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Ub3AgTG9zZXJzPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPkRlY2xpbmluZyBORlRzIGluIHtzZWxlY3RlZFBlcmlvZH08L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAge2dhaW5lcnNMb3NlcnMudG9wX2xvc2Vycy5tYXAoKG5mdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtuZnQuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntuZnQubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPXtnZXRSYXJpdHlDb2xvcihuZnQucmFyaXR5KX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmZ0LnJhcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RXRoKG5mdC52b2x1bWVfMjRoKX0gdm9sdW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXJlZC02MDBcIj57bmZ0LmNoYW5nZV9wZXJjZW50YWdlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5TY29yZToge25mdC5jdXJyZW50X3Njb3JlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogTWFya2V0IFN1bW1hcnkgKi99XG4gICAgICAgICAge2dhaW5lcnNMb3NlcnMgJiYgKFxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+TWFya2V0IFN1bW1hcnk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPk92ZXJhbGwgbWFya2V0IHBlcmZvcm1hbmNlIGZvciB7c2VsZWN0ZWRQZXJpb2R9PC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57Zm9ybWF0RXRoKGdhaW5lcnNMb3NlcnMubWFya2V0X3N1bW1hcnkudG90YWxfdm9sdW1lXzI0aCl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlRvdGFsIFZvbHVtZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwibXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnYWluZXJzTG9zZXJzLm1hcmtldF9zdW1tYXJ5LnZvbHVtZV9jaGFuZ2VfMjRofVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntnYWluZXJzTG9zZXJzLm1hcmtldF9zdW1tYXJ5LmF2ZXJhZ2Vfc2NvcmVfY2hhbmdlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmcgU2NvcmUgQ2hhbmdlPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntnYWluZXJzTG9zZXJzLm1hcmtldF9zdW1tYXJ5Lm1vc3RfYWN0aXZlX3Jhcml0eX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TW9zdCBBY3RpdmUgUmFyaXR5PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAganVzdGlmeS1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2FpbmVyc0xvc2Vycy5tYXJrZXRfc3VtbWFyeS50cmVuZGluZ190aGVtZXMubWFwKCh0aGVtZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGtleT17dGhlbWV9IHZhcmlhbnQ9XCJzZWNvbmRhcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0aGVtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0xXCI+VHJlbmRpbmcgVGhlbWVzPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJtYXJrZXQtdmFsdWVcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICB7bWFya2V0VmFsdWUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgey8qIE1hcmtldCBPdmVydmlldyAqL31cbiAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPk1hcmtldCBPdmVydmlldzwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5Db2xsZWN0aW9uIG1hcmtldCB2YWx1ZSBhbmQgc3RhdGlzdGljczwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTUgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRFdGgobWFya2V0VmFsdWUubWFya2V0X292ZXJ2aWV3LnRvdGFsX21hcmtldF92YWx1ZSl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+VG90YWwgTWFya2V0IFZhbHVlPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cIm10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHttYXJrZXRWYWx1ZS5tYXJrZXRfb3ZlcnZpZXcubWFya2V0X2NhcF9jaGFuZ2VfMjRofVxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e21hcmtldFZhbHVlLm1hcmtldF9vdmVydmlldy50b3RhbF9jb2xsZWN0aW9uc308L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Db2xsZWN0aW9uczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57Zm9ybWF0TnVtYmVyKG1hcmtldFZhbHVlLm1hcmtldF9vdmVydmlldy50b3RhbF9uZnRzKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Ub3RhbCBORlRzPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRFdGgobWFya2V0VmFsdWUubWFya2V0X292ZXJ2aWV3LmF2ZXJhZ2VfbmZ0X3ZhbHVlKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmcgTkZUIFZhbHVlPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICB7LyogQ29sbGVjdGlvbiBCdWJibGUgTWFwIERhdGEgKi99XG4gICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZT5Db2xsZWN0aW9uIE1hcmtldCBWYWx1ZTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5NYXJrZXQgdmFsdWUgYnkgY29sbGVjdGlvbiAoQnViYmxlIE1hcCBEYXRhKTwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAge21hcmtldFZhbHVlLmJ1YmJsZV9tYXBfZGF0YS5tYXAoKGNvbGxlY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NvbGxlY3Rpb24uaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgYm9yZGVyIHJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYm9yZGVyQ29sb3I6IGNvbGxlY3Rpb24uY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e2NvbGxlY3Rpb24ubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldFJhcml0eUNvbG9yKGNvbGxlY3Rpb24ucmFyaXR5KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb2xsZWN0aW9uLnJhcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5WYWx1ZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LWJvbGRcIj57Zm9ybWF0RXRoKGNvbGxlY3Rpb24udmFsdWUpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+U2l6ZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LWJvbGRcIj57Zm9ybWF0TnVtYmVyKGNvbGxlY3Rpb24uc2l6ZSl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj4yNGggQ2hhbmdlPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YGZvbnQtYm9sZCAke2NvbGxlY3Rpb24uY2hhbmdlXzI0aC5zdGFydHNXaXRoKCcrJykgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29sbGVjdGlvbi5jaGFuZ2VfMjRofVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Q29sb3I8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgcm91bmRlZC1mdWxsIGJvcmRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbGxlY3Rpb24uY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwicGVyZm9ybWFuY2VcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlBlcmZvcm1hbmNlIEFuYWx5dGljczwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPkNvbWluZyBzb29uIC0gQWR2YW5jZWQgcGVyZm9ybWFuY2UgbWV0cmljcyBhbmQgY2hhcnRzPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0zMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxMaW5lQ2hhcnQgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QWR2YW5jZWQgY2hhcnRzIGFuZCBwZXJmb3JtYW5jZSBtZXRyaWNzIHdpbGwgYmUgYXZhaWxhYmxlIGhlcmU8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICA8L1RhYnM+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlVzZXJzSWNvbiIsIkNoYXJ0QmFySWNvbiIsIkN1cnJlbmN5RG9sbGFySWNvbiIsIkFycm93UGF0aEljb24iLCJQcmVzZW50YXRpb25DaGFydExpbmVJY29uIiwiQW5hbHl0aWNzRGFzaGJvYXJkIiwicGxhdGZvcm1EYXRhIiwic2V0UGxhdGZvcm1EYXRhIiwiZ2FpbmVyc0xvc2VycyIsInNldEdhaW5lcnNMb3NlcnMiLCJtYXJrZXRWYWx1ZSIsInNldE1hcmtldFZhbHVlIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic2VsZWN0ZWRQZXJpb2QiLCJzZXRTZWxlY3RlZFBlcmlvZCIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImZldGNoQW5hbHl0aWNzRGF0YSIsInBsYXRmb3JtUmVzcG9uc2UiLCJmZXRjaCIsInBsYXRmb3JtUmVzdWx0IiwianNvbiIsInN1Y2Nlc3MiLCJkYXRhIiwiZ2FpbmVyc1Jlc3BvbnNlIiwiZ2FpbmVyc1Jlc3VsdCIsIm1hcmtldFJlc3BvbnNlIiwibWFya2V0UmVzdWx0IiwiZXJyIiwiY29uc29sZSIsImZvcm1hdE51bWJlciIsIm51bSIsInRvRml4ZWQiLCJ0b1N0cmluZyIsImZvcm1hdEV0aCIsImV0aCIsImdldFJhcml0eUNvbG9yIiwicmFyaXR5IiwidG9Mb3dlckNhc2UiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMSIsIm92ZXJ2aWV3IiwidG90YWxfdXNlcnMiLCJncm93dGhfbWV0cmljcyIsInVzZXJfZ3Jvd3RoX3JhdGUiLCJ0b3RhbF9uZnRzX2dlbmVyYXRlZCIsIm5mdF9nZW5lcmF0aW9uX2dyb3d0aF9yYXRlIiwidG90YWxfdm9sdW1lX2V0aCIsIm1hcmtldHBsYWNlX3ZvbHVtZV9ncm93dGhfcmF0ZSIsImFjdGl2ZV9jYW1wYWlnbnMiLCJjYW1wYWlnbl9wYXJ0aWNpcGF0aW9uX3JhdGUiLCJUYWJzIiwiZGVmYXVsdFZhbHVlIiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsInZhbHVlIiwiVGFic0NvbnRlbnQiLCJDYXJkIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRDb250ZW50IiwidG9wX3BlcmZvcm1pbmciLCJjYW1wYWlnbnMiLCJtYXAiLCJjYW1wYWlnbiIsImluZGV4IiwibmFtZSIsInBhcnRpY2lwYW50cyIsIkJhZGdlIiwidmFyaWFudCIsIm5mdHNfZ2VuZXJhdGVkIiwiaWQiLCJuZnRfcmFyaXRpZXMiLCJjb3VudCIsInBlcmNlbnRhZ2UiLCJDYWxlbmRhciIsInBlcmlvZCIsIkJ1dHRvbiIsInNpemUiLCJUcmVuZGluZ1VwIiwidG9wX2dhaW5lcnMiLCJuZnQiLCJ2b2x1bWVfMjRoIiwiY2hhbmdlX3BlcmNlbnRhZ2UiLCJjdXJyZW50X3Njb3JlIiwiVHJlbmRpbmdEb3duIiwidG9wX2xvc2VycyIsIm1hcmtldF9zdW1tYXJ5IiwidG90YWxfdm9sdW1lXzI0aCIsInZvbHVtZV9jaGFuZ2VfMjRoIiwiYXZlcmFnZV9zY29yZV9jaGFuZ2UiLCJtb3N0X2FjdGl2ZV9yYXJpdHkiLCJ0cmVuZGluZ190aGVtZXMiLCJ0aGVtZSIsIm1hcmtldF9vdmVydmlldyIsInRvdGFsX21hcmtldF92YWx1ZSIsIm1hcmtldF9jYXBfY2hhbmdlXzI0aCIsInRvdGFsX2NvbGxlY3Rpb25zIiwidG90YWxfbmZ0cyIsImF2ZXJhZ2VfbmZ0X3ZhbHVlIiwiYnViYmxlX21hcF9kYXRhIiwiY29sbGVjdGlvbiIsInN0eWxlIiwiYm9yZGVyQ29sb3IiLCJjb2xvciIsImgzIiwiY2hhbmdlXzI0aCIsInN0YXJ0c1dpdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJMaW5lQ2hhcnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx\n"));

/***/ })

});