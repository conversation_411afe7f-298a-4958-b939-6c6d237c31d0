// Twitter Service Interface Contract
// Location: services/api-gateway/src/shared/interfaces/twitter-service.interface.ts
// This interface must be implemented by both mock and real Twitter services

export interface TwitterCredentials {
  accessToken?: string;
  accessTokenSecret?: string;
  consumerKey?: string;
  consumerSecret?: string;
}

export interface TwitterUser {
  id: string;
  username: string;
  displayName: string;
  profileImageUrl: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  verified: boolean;
  description?: string;
  location?: string;
  website?: string;
  createdAt: string;
}

export interface TwitterProfile extends TwitterUser {
  email?: string;
  twitterId: string;
  twitterUsername: string;
  accessToken: string;
}

export interface AuthResult {
  success: boolean;
  user?: TwitterProfile;
  accessToken?: string;
  error?: string;
}

export interface TweetResult {
  success: boolean;
  tweetId?: string;
  error?: string;
}

export interface TwitterAnalytics {
  engagementRate: number;
  averageLikes: number;
  averageRetweets: number;
  averageReplies: number;
  influenceScore: number;
}

/**
 * Twitter Service Interface
 * Must be implemented by both mock and real Twitter services
 */
export interface ITwitterService {
  /**
   * Authenticate user with Twitter OAuth
   */
  authenticateUser(credentials: TwitterCredentials): Promise<AuthResult>;

  /**
   * Get user profile by Twitter ID
   */
  getUserProfile(userId: string): Promise<TwitterProfile>;

  /**
   * Get user followers
   */
  getFollowers(userId: string, limit?: number): Promise<TwitterUser[]>;

  /**
   * Get user following
   */
  getFollowing(userId: string, limit?: number): Promise<TwitterUser[]>;

  /**
   * Post a tweet
   */
  postTweet(userId: string, content: string): Promise<TweetResult>;

  /**
   * Get user analytics
   */
  getUserAnalytics(userId: string): Promise<TwitterAnalytics>;

  /**
   * Verify service health
   */
  healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; timestamp: string }>;
}
