// Test script to verify backend integration
const axios = require('axios');

const API_BASE = 'http://localhost:3010/api';

async function testBackendIntegration() {
  console.log('🚀 Testing Backend Integration...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health Check:', healthResponse.data);
    console.log('');

    // Test 2: User Registration
    console.log('2. Testing User Registration...');
    const registerData = {
      username: `testuser${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123',
      confirmPassword: 'TestPassword123',
      displayName: 'Test User'
    };

    const registerResponse = await axios.post(`${API_BASE}/auth/register`, registerData);
    console.log('✅ Registration Success:', {
      success: registerResponse.data.success,
      user: registerResponse.data.data?.user?.username,
      hasToken: !!registerResponse.data.data?.accessToken
    });
    console.log('📄 Full Registration Response:', JSON.stringify(registerResponse.data, null, 2));
    console.log('');

    // Test 3: User Login
    console.log('3. Testing User Login...');
    const loginData = {
      emailOrUsername: registerData.email,
      password: registerData.password
    };

    const loginResponse = await axios.post(`${API_BASE}/auth/login`, loginData);
    console.log('✅ Login Success:', {
      success: loginResponse.data.success,
      user: loginResponse.data.data?.user?.username,
      hasToken: !!loginResponse.data.data?.accessToken
    });
    console.log('📄 Full Login Response:', JSON.stringify(loginResponse.data, null, 2));

    const token = loginResponse.data.data?.accessToken;
    console.log('');

    // Test 4: Protected Route (Profile)
    console.log('4. Testing Protected Route...');
    console.log('🔑 Using token:', token ? 'Token present' : 'No token');
    console.log('🔑 Token length:', token?.length || 0);

    const profileResponse = await axios.get(`${API_BASE}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ Profile Access:', {
      success: profileResponse.data.success,
      username: profileResponse.data.data?.username,
      email: profileResponse.data.data?.email
    });
    console.log('');

    // Test 5: Search API
    console.log('5. Testing Search API...');
    const searchResponse = await axios.get(`${API_BASE}/search?query=test&type=global&page=1&limit=5`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ Search API:', {
      success: searchResponse.data.success,
      resultsCount: searchResponse.data.data?.results?.length || 0
    });
    console.log('');

    console.log('🎉 All Backend Integration Tests Passed!');
    console.log('');
    console.log('📊 Summary:');
    console.log('- ✅ Health Check: Working');
    console.log('- ✅ User Registration: Working');
    console.log('- ✅ User Login: Working');
    console.log('- ✅ Protected Routes: Working');
    console.log('- ✅ Search API: Working');
    console.log('');
    console.log('🚀 Frontend is ready to connect to backend!');

  } catch (error) {
    console.error('❌ Test Failed:', error.response?.data || error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure backend is running on port 3011');
    console.log('2. Check if all services are properly started');
    console.log('3. Verify database connections');
  }
}

// Run the test
testBackendIntegration();
