'use client'

import { useState } from 'react';
import { Box, Button, VStack, Text, Input, Alert, AlertIcon } from '@chakra-ui/react';
import { authService } from '@/services/authService';

export default function AuthTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [username, setUsername] = useState('testuser123');

  const testRegistration = async () => {
    setLoading(true);
    setResult('');
    try {
      console.log('🧪 Testing registration with API Gateway...');
      const response = await authService.register({
        username,
        email,
        password,
        twitterUsername: 'testtwitter'
      });
      console.log('✅ Registration successful:', response);
      setResult(`✅ Registration successful! User: ${response.user.username}, Token: ${response.access_token?.substring(0, 20)}...`);
    } catch (error: any) {
      console.error('❌ Registration failed:', error);
      setResult(`❌ Registration failed: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResult('');
    try {
      console.log('🧪 Testing login with API Gateway...');
      const response = await authService.login({
        email,
        password
      });
      console.log('✅ Login successful:', response);
      setResult(`✅ Login successful! User: ${response.user.username}, Token: ${response.access_token?.substring(0, 20)}...`);
    } catch (error: any) {
      console.error('❌ Login failed:', error);
      setResult(`❌ Login failed: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testProfile = async () => {
    setLoading(true);
    setResult('');
    try {
      console.log('🧪 Testing profile fetch with API Gateway...');
      const profile = await authService.getProfile();
      console.log('✅ Profile fetch successful:', profile);
      setResult(`✅ Profile fetch successful! User: ${profile.username}, Email: ${profile.email}`);
    } catch (error: any) {
      console.error('❌ Profile fetch failed:', error);
      setResult(`❌ Profile fetch failed: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box p={6} bg="white" borderRadius="lg" boxShadow="md" maxW="600px" mx="auto">
      <VStack gap={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold" color="blue.600">
          🧪 Authentication API Test
        </Text>
        
        <VStack gap={2} align="stretch">
          <Input
            placeholder="Username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
          />
          <Input
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Input
            placeholder="Password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
        </VStack>

        <VStack gap={2}>
          <Button
            colorScheme="green"
            onClick={testRegistration}
            isLoading={loading}
            w="full"
          >
            Test Registration
          </Button>
          <Button
            colorScheme="blue"
            onClick={testLogin}
            isLoading={loading}
            w="full"
          >
            Test Login
          </Button>
          <Button
            colorScheme="purple"
            onClick={testProfile}
            isLoading={loading}
            w="full"
          >
            Test Profile Fetch
          </Button>
        </VStack>

        {result && (
          <Alert status={result.includes('✅') ? 'success' : 'error'}>
            <AlertIcon />
            <Text fontSize="sm">{result}</Text>
          </Alert>
        )}

        <Text fontSize="sm" color="gray.500">
          API Gateway: http://localhost:3010/api/users/*
        </Text>
      </VStack>
    </Box>
  );
}
