'use client'

import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Button,
  Textarea
} from '@chakra-ui/react'
import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface ProfileFormData {
  username: string
  email: string
  bio: string
  location: string
  website: string
  twitterHandle: string
  discordHandle: string
  isPublic: boolean
  emailNotifications: boolean
  marketingEmails: boolean
  timezone: string
}

interface ProfileFormProps {
  onSave: (data: ProfileFormData) => void
  onCancel: () => void
  isLoading?: boolean
}

export default function ProfileForm({ onSave, onCancel, isLoading = false }: ProfileFormProps) {
  const { user } = useAuth()
  const [formData, setFormData] = useState<ProfileFormData>({
    username: '',
    email: '',
    bio: '',
    location: '',
    website: '',
    twitterHandle: '',
    discordHandle: '',
    isPublic: true,
    emailNotifications: true,
    marketingEmails: false,
    timezone: 'UTC'
  })

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        email: user.email || '',
        bio: user.bio || '',
        location: user.location || '',
        website: user.website || '',
        twitterHandle: user.twitterHandle || '',
        discordHandle: user.discordHandle || '',
        isPublic: user.isPublic ?? true,
        emailNotifications: user.emailNotifications ?? true,
        marketingEmails: user.marketingEmails ?? false,
        timezone: user.timezone || 'UTC'
      })
    }
  }, [user])

  const handleInputChange = (field: keyof ProfileFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <Box as="form" onSubmit={handleSubmit}>
      <VStack gap={6} align="stretch">
        {/* Basic Information */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            📝 Basic Information
          </Text>
          <VStack gap={4} align="stretch">
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Username</Text>
              <Input
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="Enter your username"
              />
            </Box>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Email</Text>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter your email"
              />
            </Box>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Bio</Text>
              <Textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={3}
              />
            </Box>

            <HStack gap={4}>
              <Box flex="1">
                <Text fontSize="sm" fontWeight="medium" mb={2}>Location</Text>
                <Input
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="City, Country"
                />
              </Box>

              <Box flex="1">
                <Text fontSize="sm" fontWeight="medium" mb={2}>Website</Text>
                <Input
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://yourwebsite.com"
                />
              </Box>
            </HStack>
          </VStack>
        </Box>

        {/* Social Media Connections */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            🔗 Social Media Connections
          </Text>
          <VStack gap={4} align="stretch">
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Twitter Handle</Text>
              <Input
                value={formData.twitterHandle}
                onChange={(e) => handleInputChange('twitterHandle', e.target.value)}
                placeholder="@yourtwitterhandle"
              />
            </Box>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Discord Handle</Text>
              <Input
                value={formData.discordHandle}
                onChange={(e) => handleInputChange('discordHandle', e.target.value)}
                placeholder="username#1234"
              />
            </Box>
          </VStack>
        </Box>

        {/* Privacy Settings */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            🔒 Privacy Settings
          </Text>
          <VStack gap={4} align="stretch">
            <HStack justify="space-between" align="center">
              <Text fontSize="sm" fontWeight="medium">
                Public Profile
              </Text>
              <Button
                size="sm"
                colorScheme={formData.isPublic ? 'green' : 'gray'}
                onClick={() => handleInputChange('isPublic', !formData.isPublic)}
              >
                {formData.isPublic ? 'Public' : 'Private'}
              </Button>
            </HStack>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Timezone</Text>
              <select
                value={formData.timezone}
                onChange={(e) => handleInputChange('timezone', e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              >
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
                <option value="Europe/London">London</option>
                <option value="Europe/Paris">Paris</option>
                <option value="Asia/Tokyo">Tokyo</option>
                <option value="Asia/Shanghai">Shanghai</option>
              </select>
            </Box>
          </VStack>
        </Box>

        {/* Notification Settings */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            🔔 Notification Settings
          </Text>
          <VStack gap={4} align="stretch">
            <HStack justify="space-between" align="center">
              <Text fontSize="sm" fontWeight="medium">
                Email Notifications
              </Text>
              <Button
                size="sm"
                colorScheme={formData.emailNotifications ? 'green' : 'gray'}
                onClick={() => handleInputChange('emailNotifications', !formData.emailNotifications)}
              >
                {formData.emailNotifications ? 'Enabled' : 'Disabled'}
              </Button>
            </HStack>

            <HStack justify="space-between" align="center">
              <Text fontSize="sm" fontWeight="medium">
                Marketing Emails
              </Text>
              <Button
                size="sm"
                colorScheme={formData.marketingEmails ? 'green' : 'gray'}
                onClick={() => handleInputChange('marketingEmails', !formData.marketingEmails)}
              >
                {formData.marketingEmails ? 'Enabled' : 'Disabled'}
              </Button>
            </HStack>
          </VStack>
        </Box>

        {/* Action Buttons */}
        <HStack justify="end" gap={4} pt={4}>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type="submit"
            colorScheme="blue"
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </HStack>
      </VStack>
    </Box>
  )
}
