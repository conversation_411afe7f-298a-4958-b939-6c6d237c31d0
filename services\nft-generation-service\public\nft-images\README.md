# NFT Image Assets

This directory contains the generated NFT images and placeholder assets for the Social NFT Platform.

## Directory Structure

```
nft-images/
├── legendary-placeholder.png    # Placeholder for Legendary NFTs
├── rare-placeholder.png         # Placeholder for Rare NFTs  
├── common-placeholder.png       # Placeholder for Common NFTs
├── default.png                  # Default fallback image
├── thumbnails/                  # Thumbnail versions
└── generated/                   # Dynamically generated NFTs
```

## Image Specifications

### Legendary NFTs
- **Resolution:** 1024x1024px
- **Format:** PNG with transparency
- **Special Effects:** Golden aura, animated elements
- **File Size:** ~2MB

### Rare NFTs  
- **Resolution:** 1024x1024px
- **Format:** PNG with transparency
- **Special Effects:** Purple glow, enhanced details
- **File Size:** ~1.5MB

### Common NFTs
- **Resolution:** 1024x1024px
- **Format:** PNG
- **Special Effects:** Basic styling
- **File Size:** ~1MB

## Production Implementation

In production, this system would integrate with:

1. **AI Image Generation APIs** (DALL-E, Midjourney, Stable Diffusion)
2. **Canvas-based Dynamic Generation** 
3. **Template-based Composition System**
4. **IPFS Storage** for decentralized hosting
5. **CDN Distribution** for fast global access

## Current Status

✅ Placeholder system implemented  
✅ Metadata generation working  
✅ Rarity-based image selection  
⚠️ Actual image generation pending  
⚠️ IPFS integration pending  

## Usage

The ImageGenerationService automatically:
- Generates unique filenames
- Creates metadata files
- Returns appropriate placeholder URLs
- Handles error fallbacks

Generated NFTs are accessible via:
`/nft-images/{filename}`
