// Enterprise NFT Command Model (Write Side)
import { IsString, IsOptional, IsObject, IsEnum, IsInt, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum GenerationStatus {
  PENDING = 'pending',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum BlockchainStatus {
  NOT_MINTED = 'not_minted',
  MINTING = 'minting',
  MINTED = 'minted',
  FAILED = 'failed'
}

export enum DataClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted'
}

export class CreateNftCommandDto {
  @ApiProperty({ description: 'User ID who owns the NFT' })
  @IsString()
  userId: string;

  @ApiPropertyOptional({ description: 'Project ID if part of a project' })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiPropertyOptional({ description: 'Campaign ID if part of a campaign' })
  @IsOptional()
  @IsString()
  campaignId?: string;

  @ApiProperty({ description: 'Template ID used for generation' })
  @IsString()
  templateId: string;

  @ApiProperty({ description: 'NFT name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'NFT description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Generation parameters' })
  @IsObject()
  generationParams: any;

  @ApiPropertyOptional({ description: 'NFT attributes' })
  @IsOptional()
  @IsObject()
  attributes?: any;
}

export class UpdateNftCommandDto {
  @ApiPropertyOptional({ description: 'NFT name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'NFT description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Image URL' })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Metadata URL' })
  @IsOptional()
  @IsString()
  metadataUrl?: string;

  @ApiPropertyOptional({ description: 'Generation status' })
  @IsOptional()
  @IsEnum(GenerationStatus)
  generationStatus?: GenerationStatus;

  @ApiPropertyOptional({ description: 'Blockchain status' })
  @IsOptional()
  @IsEnum(BlockchainStatus)
  blockchainStatus?: BlockchainStatus;

  @ApiPropertyOptional({ description: 'Token ID after minting' })
  @IsOptional()
  @IsString()
  tokenId?: string;

  @ApiPropertyOptional({ description: 'Contract address' })
  @IsOptional()
  @IsString()
  contractAddress?: string;

  @ApiPropertyOptional({ description: 'Blockchain transaction hash' })
  @IsOptional()
  @IsString()
  blockchainTxHash?: string;
}
