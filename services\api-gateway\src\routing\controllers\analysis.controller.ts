import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Headers,
  Query,
  Param,
  Res,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ProxyService } from '../services/proxy.service';
import { Response } from 'express';

@ApiTags('Profile Analysis')
@Controller('analysis')
export class AnalysisController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('twitter-profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Analyze Twitter profile and generate comprehensive analysis' })
  @ApiResponse({ status: 201, description: 'Profile analysis completed successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Invalid Twitter username or analysis failed' })
  async analyzeTwitterProfile(
    @Body() analysisDto: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        '/api/analysis/twitter-profile',
        'POST',
        analysisDto,
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('results/:analysisId')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get analysis results by ID' })
  @ApiResponse({ status: 200, description: 'Analysis results retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async getAnalysisResults(
    @Param('analysisId') analysisId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        `/api/analysis/results/${analysisId}`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('history')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user analysis history' })
  @ApiResponse({ status: 200, description: 'Analysis history retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Missing or invalid userId' })
  async getAnalysisHistory(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        '/api/analysis/history',
        'GET',
        null,
        headers,
        query
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Put(':id/reanalyze')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Re-analyze existing Twitter profile' })
  @ApiResponse({ status: 200, description: 'Re-analysis completed successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async reanalyzeProfile(
    @Param('id') id: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        `/api/analysis/${id}/reanalyze`,
        'PUT',
        {},
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for profile analysis service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        '/api/analysis/health',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: {
          ...response.data.data,
          gateway: 'api-gateway',
          timestamp: new Date().toISOString(),
        }
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          message: 'Profile analysis service unavailable',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }
}
