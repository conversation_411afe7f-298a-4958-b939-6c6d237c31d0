version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: social-nft-platform-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1111
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/database/init:/docker-entrypoint-initdb.d
    networks:
      - social-nft-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: social-nft-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - social-nft-network
    restart: unless-stopped

  # User Service
  user-service:
    build:
      context: .
      dockerfile: services/user-service/Dockerfile.dev
      target: development
    container_name: user-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=****************************************/user_service
      - REDIS_URL=redis://redis:6379
      - PORT=3011
    ports:
      - "3011:3011"
    volumes:
      - ./services/user-service:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - social-nft-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 896M
        reservations:
          memory: 448M

volumes:
  postgres_data:
  redis_data:

networks:
  social-nft-network:
    driver: bridge
