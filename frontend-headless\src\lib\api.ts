import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { storage } from './utils'

// API Configuration - All requests go through API Gateway
const API_GATEWAY_URL = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'

// All requests route through API Gateway - Single Entry Point
const API_BASE_URL = API_GATEWAY_URL + '/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = storage.get('auth_token', null)
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth token on unauthorized
      storage.remove('auth_token')
      storage.remove('user')
      // Redirect to login if in browser
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
    return Promise.reject(error)
  }
)

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: {
    results: T[]
    totalCount: number
    page: number
    limit: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Generic API methods
export const api = {
  // GET request
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.get(url, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // POST request
  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.post(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // PUT request
  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.put(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // PATCH request
  patch: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.patch(url, data, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },

  // DELETE request
  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
      const response = await apiClient.delete(url, config)
      return response.data
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Request failed'
      }
    }
  },
}

// Auth API
export const authApi = {
  register: (data: {
    username: string
    email: string
    password: string
    confirmPassword: string
    displayName?: string
  }) => api.post('/auth/register', data),

  login: (data: {
    emailOrUsername: string
    password: string
  }) => api.post('/auth/login', data),

  // Twitter OAuth endpoints
  twitterAuth: () => api.get('/auth/twitter'),
  twitterCallback: (code: string, state: string) => api.post('/auth/twitter/callback', { code, state }),

  // Token validation
  validateToken: (token: string) => api.post('/auth/validate-token', { token }),

  logout: () => api.post('/auth/logout'),

  refreshToken: () => api.post('/auth/refresh'),

  getProfile: () => api.get('/auth/profile'),

  updateProfile: (data: any) => api.patch('/auth/profile', data),
}

// User API
export const userApi = {
  getUsers: (params?: {
    page?: number
    limit?: number
    search?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/users', { params }),

  getUser: (id: string) => api.get(`/users/${id}`),

  updateUser: (id: string, data: any) => api.patch(`/users/${id}`, data),

  deleteUser: (id: string) => api.delete(`/users/${id}`),

  getUserStats: (id: string) => api.get(`/users/${id}/stats`),
}

// Campaign API
export const campaignApi = {
  getCampaigns: (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    type?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/campaigns', { params }),

  getCampaign: (id: string) => api.get(`/campaigns/${id}`),

  createCampaign: (data: any) => api.post('/campaigns', data),

  updateCampaign: (id: string, data: any) => api.patch(`/campaigns/${id}`, data),

  deleteCampaign: (id: string) => api.delete(`/campaigns/${id}`),

  joinCampaign: (id: string) => api.post(`/campaigns/${id}/join`),

  leaveCampaign: (id: string) => api.post(`/campaigns/${id}/leave`),

  getCampaignParticipants: (id: string) => api.get(`/campaigns/${id}/participants`),
}

// NFT API - All requests through API Gateway
export const nftApi = {
  // Get user's NFT history via API Gateway
  getUserNFTs: (userId: string) => api.get(`/nfts/user/${userId}/history?limit=50`),

  // Generate NFT from analysis via API Gateway
  generateNFTFromAnalysis: (data: {
    userId: string
    analysisId: string
    customization?: {
      style?: string
      theme?: string
    }
  }) => api.post('/nfts/generate-from-analysis', data),

  // Standard NFT operations via API Gateway
  getNFTs: (params?: any) => api.get('/nfts', { params }),
  getNFT: (id: string) => api.get(`/nfts/${id}`),
  generateNFT: (data: any) => api.post('/nfts/generate', data),
  updateNFT: (id: string, data: any) => api.patch(`/nfts/${id}`, data),
  deleteNFT: (id: string) => api.delete(`/nfts/${id}`),
  getCampaignNFTs: (campaignId: string) => api.get(`/nfts/campaign/${campaignId}`),
}

// Marketplace API
export const marketplaceApi = {
  getListings: (params?: {
    page?: number
    limit?: number
    search?: string
    minPrice?: number
    maxPrice?: number
    currency?: string
    rarity?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) => api.get('/marketplace', { params }),

  getListing: (id: string) => api.get(`/marketplace/${id}`),

  createListing: (data: {
    nftId: string
    price: number
    currency: string
    title: string
    description?: string
  }) => api.post('/marketplace', data),

  updateListing: (id: string, data: any) => api.patch(`/marketplace/${id}`, data),

  deleteListing: (id: string) => api.delete(`/marketplace/${id}`),

  purchaseListing: (id: string) => api.post(`/marketplace/${id}/purchase`),
}

// Search API
export const searchApi = {
  search: (params: {
    query?: string
    type: 'global' | 'users' | 'campaigns' | 'nfts' | 'marketplace'
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    includeFacets?: boolean
    includeSuggestions?: boolean
  }) => api.get('/search', { params }),

  autocomplete: (params: {
    query: string
    type: 'global' | 'users' | 'campaigns' | 'nfts' | 'marketplace'
    limit?: number
  }) => api.get('/search/autocomplete', { params }),

  getPopularSearches: (params?: {
    type?: string
    limit?: number
  }) => api.get('/search/popular', { params }),

  getSearchAnalytics: (params?: {
    timeframe?: string
    type?: string
  }) => api.get('/search/analytics', { params }),
}

// Profile Analysis API - All requests through API Gateway
export const analysisApi = {
  // Analyze Twitter profile via API Gateway
  analyzeTwitterProfile: (data: {
    twitterHandle: string
    userId: string
    analysisType?: string
  }) => api.post('/analysis/twitter-profile', data),

  // Get user's analysis history via API Gateway
  getUserAnalysisHistory: (userId: string, limit: number = 10) =>
    api.get(`/analysis/history?userId=${userId}&limit=${limit}`),
}

// Analytics API
export const analyticsApi = {
  getDashboardStats: () => api.get('/analytics/dashboard'),

  getUserAnalytics: (userId: string, timeframe?: string) =>
    api.get(`/analytics/users/${userId}`, { params: { timeframe } }),

  getCampaignAnalytics: (campaignId: string, timeframe?: string) =>
    api.get(`/analytics/campaigns/${campaignId}`, { params: { timeframe } }),

  getPlatformAnalytics: (timeframe?: string) =>
    api.get('/analytics/platform', { params: { timeframe } }),

  getRealtimeMetrics: () => api.get('/analytics/realtime'),
}

export default apiClient
