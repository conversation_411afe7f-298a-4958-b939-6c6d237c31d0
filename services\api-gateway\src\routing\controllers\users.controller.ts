import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Headers,
  Query,
  Param,
  Req,
  Res,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProxyService } from '../services/proxy.service';
import { Request, Response } from 'express';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async register(
    @Body() registerDto: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/register',
        'POST',
        registerDto,
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Post('login')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(
    @Body() loginDto: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/login',
        'POST',
        loginDto,
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/profile',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for user service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/health',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: {
          ...response.data,
          gateway: 'api-gateway',
          timestamp: new Date().toISOString(),
        }
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          message: 'User service unavailable',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('auth/twitter')
  @ApiOperation({ summary: 'Initiate Twitter OAuth flow' })
  @ApiResponse({ status: 302, description: 'Redirects to Twitter for authentication' })
  async twitterAuth(
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      // Forward to Profile Analysis Service for Twitter OAuth
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        '/api/auth/twitter/login',
        'GET',
        null,
        headers
      );

      // If it's a redirect response, handle it properly
      if (response.status === 302 && response.headers?.location) {
        return res.redirect(response.headers.location);
      }

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('auth/twitter/callback')
  @ApiOperation({ summary: 'Handle Twitter OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirects to frontend with success or error' })
  async twitterCallback(
    @Query() query: any,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      // Forward to Profile Analysis Service for Twitter OAuth callback
      const queryString = new URLSearchParams(query).toString();
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        `/api/auth/twitter/callback?${queryString}`,
        'GET',
        null,
        headers
      );

      // If it's a redirect response, handle it properly
      if (response.status === 302 && response.headers?.location) {
        return res.redirect(response.headers.location);
      }

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  // ========================================
  // ENTERPRISE USER SERVICE ENDPOINTS
  // ========================================

  @Post()
  @ApiOperation({ summary: 'Create a new user (Enterprise CQRS)' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async createUser(
    @Body() createUserDto: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      // Add correlation ID if not present
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/users',
        'POST',
        createUserDto,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Failed to create user',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID (Enterprise CQRS Query)' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(
    @Param('id') id: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const response = await this.proxyService.forwardRequest(
        'user-service',
        `/api/users/${id}`,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Failed to retrieve user',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get users with pagination (Enterprise CQRS Query)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of users to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of users to skip' })
  @ApiQuery({ name: 'includeInactive', required: false, description: 'Include inactive users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getUsers(
    @Query() query: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const queryString = new URLSearchParams(query).toString();
      const path = queryString ? `/api/users?${queryString}` : '/api/users';

      const response = await this.proxyService.forwardRequest(
        'user-service',
        path,
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Failed to retrieve users',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update user by ID (Enterprise CQRS Command)' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      const response = await this.proxyService.forwardRequest(
        'user-service',
        `/api/users/${id}`,
        'PUT',
        updateUserDto,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: {
          message: error.message || 'Failed to update user',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('health/enterprise')
  @ApiOperation({ summary: 'Enterprise User Service Health Check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  async enterpriseHealthCheck(
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const enhancedHeaders = {
        ...headers,
        'x-correlation-id': headers['x-correlation-id'] || `gw-health-${Date.now()}`,
      };

      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/health',
        'GET',
        null,
        enhancedHeaders
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          message: 'Enterprise User Service is unavailable',
          service: 'user-service',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }
}
