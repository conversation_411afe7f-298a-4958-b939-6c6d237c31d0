// Enterprise Campaign Command Controller (Write Side) - Requirements-Driven Implementation
import { 
  <PERSON>, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  HttpStatus,
  UseGuards,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiHeader } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateCampaignCommandDto, UpdateCampaignCommandDto } from '../models/campaign-command.model';
import { CampaignCommandService } from '../services/campaign-command.service';

@ApiTags('Campaign Commands (Write Operations)')
@Controller('enterprise/campaigns')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class CampaignCommandController {
  constructor(private readonly campaignCommandService: CampaignCommandService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Create new campaign for project',
    description: 'Creates a new campaign with complete configuration linked to a project'
  })
  @ApiResponse({ status: 201, description: 'Campaign created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data or configuration' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 409, description: 'Campaign with this name already exists' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async createCampaign(
    @Body() createCampaignDto: CreateCampaignCommandDto,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.campaignCommandService.createCampaign(
        createCampaignDto,
        correlationId || `campaign-create-${Date.now()}`
      );

      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }

  @Put(':id')
  @ApiOperation({ 
    summary: 'Update existing campaign',
    description: 'Updates campaign configuration including timeline, rewards, and targeting'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign updated successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data or campaign cannot be updated' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async updateCampaign(
    @Param('id') id: string,
    @Body() updateCampaignDto: UpdateCampaignCommandDto,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.campaignCommandService.updateCampaign(
        id,
        updateCampaignDto,
        correlationId || `campaign-update-${Date.now()}`
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete campaign (soft delete)',
    description: 'Cancels the campaign while maintaining audit trail and data integrity'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign deleted successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 400, description: 'Campaign cannot be deleted' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async deleteCampaign(
    @Param('id') id: string,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.campaignCommandService.deleteCampaign(
        id,
        correlationId || `campaign-delete-${Date.now()}`
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }

  @Post(':id/approve')
  @ApiOperation({ 
    summary: 'Approve campaign for launch',
    description: 'Approves campaign for activation and public participation'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: 200, description: 'Campaign approved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 409, description: 'Campaign already approved' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  @ApiHeader({ name: 'X-Approved-By', description: 'Approver user ID' })
  async approveCampaign(
    @Param('id') id: string,
    @Headers('x-approved-by') approvedBy: string,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      if (!approvedBy) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Approver ID is required in X-Approved-By header',
          correlationId,
        });
      }

      const result = await this.campaignCommandService.approveCampaign(
        id,
        approvedBy,
        correlationId || `campaign-approve-${Date.now()}`
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }
}
