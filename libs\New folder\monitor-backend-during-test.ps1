# Backend Monitor During Live Testing
Write-Host "=== BACKEND MONITOR FOR LIVE USER TESTING ===" -ForegroundColor Cyan
Write-Host "Monitoring backend responses during frontend form testing" -ForegroundColor Green

$backendUrl = "http://localhost:3011"
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

Write-Host "`n[$timestamp] Starting backend monitoring..." -ForegroundColor Yellow

# Monitor 1: Check backend health
Write-Host "`nChecking backend health..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "$backendUrl/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Backend Status: $($health.status)" -ForegroundColor Green
    Write-Host "✅ Service: $($health.service)" -ForegroundColor Green
    Write-Host "✅ Uptime: $([math]::Round($health.uptime, 2)) seconds" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend health check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Monitor 2: Check database connection
Write-Host "`nTesting database connectivity..." -ForegroundColor Yellow
try {
    # This will test if the backend can handle requests
    $testResponse = Invoke-WebRequest -Uri "$backendUrl/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Database connection: Ready" -ForegroundColor Green
} catch {
    Write-Host "❌ Database connection issue: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 Backend is ready for live user testing!" -ForegroundColor Green
Write-Host "You can now test the frontend forms at:" -ForegroundColor Yellow
Write-Host "- Registration: http://localhost:3000/auth/register" -ForegroundColor Cyan
Write-Host "- Login: http://localhost:3000/auth/login" -ForegroundColor Cyan
