import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('assets')
@Controller('assets')
export class AssetsController {

  @Get(':hash')
  @ApiOperation({ summary: 'Get asset by hash' })
  @ApiResponse({ status: 200, description: 'Asset retrieved successfully' })
  async getAsset(@Param('hash') hash: string) {
    return {
      status: 'success',
      data: {
        hash,
        url: `https://mock-ipfs.io/ipfs/${hash}`,
        type: 'image/png',
        size: 2048576,
        name: 'nft-asset.png',
        uploadedAt: new Date().toISOString()
      }
    };
  }

  @Get('image/:hash')
  @ApiOperation({ summary: 'Get image asset by hash' })
  @ApiResponse({ status: 200, description: 'Image asset retrieved successfully' })
  async getImageAsset(@Param('hash') hash: string) {
    return {
      status: 'success',
      data: {
        hash,
        url: `https://mock-ipfs.io/ipfs/${hash}`,
        type: 'image/png',
        width: 512,
        height: 512,
        size: 2048576
      }
    };
  }
}
