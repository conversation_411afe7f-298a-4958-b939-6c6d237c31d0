'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  TrendingUpIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ArrowPathIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function SimpleAnalytics() {
  const [loading, setLoading] = useState(true);
  const [gainersData, setGainersData] = useState<any[]>([]);
  const [collectionsData, setCollectionsData] = useState<any[]>([]);
  const [transactionsData, setTransactionsData] = useState<any[]>([]);

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
      // Mock data
      setGainersData([
        { id: '1', name: '@alice_crypto', score: 85, change: '+12%', rarity: 'Epic' },
        { id: '2', name: '@bob_nft', score: 78, change: '+8%', rarity: 'Rare' },
        { id: '3', name: '@charlie_art', score: 92, change: '+15%', rarity: 'Legendary' }
      ]);
      setCollectionsData([
        { id: '1', name: 'CryptoArt', value: 45.2, change: '+5%', color: '#3B82F6' },
        { id: '2', name: 'DigitalPunks', value: 38.7, change: '-2%', color: '#8B5CF6' },
        { id: '3', name: 'MetaVerse', value: 52.1, change: '+8%', color: '#10B981' }
      ]);
      setTransactionsData([
        { id: '1', user: 'alice_crypto', nft: 'Digital Art #123', type: 'generation', value: 0.5, time: '5m ago' },
        { id: '2', user: 'bob_nft', nft: 'Punk Avatar #456', type: 'share', value: 0.3, time: '12m ago' },
        { id: '3', user: 'charlie_art', nft: 'Meta World #789', type: 'favorite', value: 0.8, time: '18m ago' }
      ]);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <ArrowPathIcon className="h-6 w-6 animate-spin" />
          <span>Loading analytics data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yaps Kaito Analytics Dashboard</h1>
          <p className="text-gray-600">Platform insights with bubble maps and performance tracking</p>
        </div>
      </div>

      {/* Section 1: Top Gainers */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Top Gainers & Losers</h2>
            <p className="text-sm text-gray-500">Users' final score status and performance tracking</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Side: List */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <TrendingUpIcon className="h-5 w-5 text-green-500 mr-2" />
              Top Gainers ({gainersData.length})
            </h3>
            <div className="space-y-3">
              {gainersData.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <UsersIcon className="h-6 w-6 text-gray-500" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">{item.name}</span>
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {item.rarity}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">Score: {item.score}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1 text-green-600">
                      <TrendingUpIcon className="h-4 w-4" />
                      <span className="font-bold">{item.change}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side: Bubble Map */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Bubble Map</h3>
            <div className="relative h-96 bg-gray-50 rounded-lg p-4">
              <div className="relative w-full h-full">
                {gainersData.map((item, index) => {
                  const size = 60 + (item.score / 100) * 60; // 60-120px
                  const x = (index % 2) * 40 + 20;
                  const y = Math.floor(index / 2) * 30 + 20;

                  return (
                    <div
                      key={item.id}
                      className="absolute cursor-pointer transition-all duration-300 hover:scale-110"
                      style={{
                        left: `${x}%`,
                        top: `${y}%`,
                        width: `${size}px`,
                        height: `${size}px`,
                      }}
                    >
                      <div className="w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500">
                        <div className="text-center">
                          <div className="text-xs">{item.name.slice(0, 6)}</div>
                          <div className="text-xs font-bold">{item.score}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Collection Market Value */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Collection Market Value</h2>
            <p className="text-sm text-gray-500">Project collections growth and decline tracking</p>
          </div>
        </div>

        <div className="relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6">
          <div className="relative w-full h-full">
            {collectionsData.map((collection, index) => {
              const size = 80 + (collection.value / 60) * 60; // 80-140px
              const x = (index % 2) * 45 + 15;
              const y = Math.floor(index / 2) * 40 + 15;

              return (
                <div
                  key={collection.id}
                  className="absolute cursor-pointer transition-all duration-300 hover:scale-105"
                  style={{
                    left: `${x}%`,
                    top: `${y}%`,
                    width: `${size}px`,
                    height: `${size}px`,
                  }}
                >
                  <div
                    className="w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg"
                    style={{ backgroundColor: collection.color }}
                  >
                    <div className="text-center p-2">
                      <div className="text-xs mb-1">{collection.name}</div>
                      <div className="text-sm font-bold">{collection.value} ETH</div>
                      <div className="text-xs">{collection.change}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Section 3: Recent Transactions */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Recent Transactions</h2>
            <p className="text-sm text-gray-500">Latest NFT activities and interactions</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <ClockIcon className="h-4 w-4" />
            <span>Real-time updates</span>
          </div>
        </div>

        <div className="space-y-3">
          {transactionsData.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-blue-100 text-blue-800">
                  <span className="text-lg">
                    {transaction.type === 'generation' ? '🎨' :
                     transaction.type === 'share' ? '📤' : '❤️'}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">{transaction.nft}</div>
                  <div className="text-sm text-gray-500">
                    User: @{transaction.user} • {transaction.type} • {transaction.time}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                  <span className="font-bold text-gray-900">{transaction.value} ETH</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
