// Enterprise Campaign Query Service - Requirements-Driven Implementation
import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';

export interface CampaignListQuery {
  page?: number;
  limit?: number;
  projectId?: string;
  status?: string;
  campaignType?: string;
  ownerId?: string;
  search?: string;
  isActive?: boolean;
}

export interface CampaignStatsQuery {
  projectId?: string;
  ownerId?: string;
  timeRange?: '7d' | '30d' | '90d' | 'all';
}

@Injectable()
export class CampaignQueryService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get campaign by ID with complete details
   * Requirements: Return full campaign configuration
   */
  async getCampaignById(id: string, includeConfig: boolean = false): Promise<any> {
    const campaign = await this.prisma.campaignQuery.findUnique({
      where: { id },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    let campaignDetails = {
      id: campaign.id,
      projectId: campaign.projectId,
      projectName: campaign.projectName,
      projectOwner: campaign.projectOwner,
      name: campaign.displayName,
      description: campaign.displayDescription,
      campaignType: campaign.campaignType,
      status: campaign.status,
      startDate: campaign.startDate,
      endDate: campaign.endDate,
      timezone: campaign.timezone,
      isActive: campaign.isActive,
      
      // Participation metrics
      participantCount: campaign.participantCount,
      maxParticipants: campaign.maxParticipants,
      minParticipants: campaign.minParticipants,
      participationRate: campaign.participationRate,
      
      // Performance metrics
      totalNftsMinted: campaign.totalNftsMinted,
      averageScore: campaign.averageScore,
      engagementRate: campaign.engagementRate,
      conversionRate: campaign.conversionRate,
      
      // Social media metrics
      totalTweets: campaign.totalTweets,
      totalLikes: campaign.totalLikes,
      totalRetweets: campaign.totalRetweets,
      totalMentions: campaign.totalMentions,
      
      // Success metrics
      successRate: campaign.successRate,
      
      // Display configuration
      featuredImage: campaign.featuredImage,
      socialPlatforms: campaign.socialPlatforms,
      rewards: campaign.rewards,
      
      // Approval status
      launchApproval: campaign.launchApproval,
      approvedBy: campaign.approvedBy,
      approvedAt: campaign.approvedAt,
      
      createdAt: campaign.createdAt,
      lastUpdated: campaign.lastUpdated,
    };

    // Include full configuration for authorized users
    if (includeConfig) {
      const fullCampaign = await this.prisma.campaignCommand.findUnique({
        where: { id },
      });

      if (fullCampaign) {
        (campaignDetails as any).fullConfiguration = {
          targetAudience: fullCampaign.targetAudience,
          requirements: fullCampaign.requirements,
          trackingParameters: fullCampaign.trackingParameters,
          nftGenerationRules: fullCampaign.nftGenerationRules,
          scoreCalculation: fullCampaign.scoreCalculation,
          targetMetrics: fullCampaign.targetMetrics,
          currentMetrics: fullCampaign.currentMetrics,
        };
      }
    }

    return {
      success: true,
      data: campaignDetails,
    };
  }

  /**
   * Get campaigns list with filtering and pagination
   * Requirements: Support search, filtering, and pagination
   */
  async getCampaigns(query: CampaignListQuery): Promise<any> {
    const {
      page = 1,
      limit = 20,
      projectId,
      status,
      campaignType,
      ownerId,
      search,
      isActive,
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (projectId) {
      where.projectId = projectId;
    }

    if (status) {
      where.status = status;
    }

    if (campaignType) {
      where.campaignType = campaignType;
    }

    if (ownerId) {
      where.projectOwner = ownerId;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (search) {
      where.OR = [
        { displayName: { contains: search, mode: 'insensitive' } },
        { displayDescription: { contains: search, mode: 'insensitive' } },
        { projectName: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get campaigns with pagination
    const [campaigns, total] = await Promise.all([
      this.prisma.campaignQuery.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { isActive: 'desc' },
          { startDate: 'desc' },
        ],
      }),
      this.prisma.campaignQuery.count({ where }),
    ]);

    return {
      success: true,
      data: {
        campaigns: campaigns.map(campaign => ({
          id: campaign.id,
          projectId: campaign.projectId,
          projectName: campaign.projectName,
          name: campaign.displayName,
          description: campaign.displayDescription,
          campaignType: campaign.campaignType,
          status: campaign.status,
          startDate: campaign.startDate,
          endDate: campaign.endDate,
          isActive: campaign.isActive,
          
          // Key metrics for display
          participantCount: campaign.participantCount,
          maxParticipants: campaign.maxParticipants,
          participationRate: campaign.participationRate,
          engagementRate: campaign.engagementRate,
          successRate: campaign.successRate,
          totalNftsMinted: campaign.totalNftsMinted,
          
          // Social metrics
          totalTweets: campaign.totalTweets,
          totalLikes: campaign.totalLikes,
          
          createdAt: campaign.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      },
    };
  }

  /**
   * Get campaigns by project
   * Requirements: Project-specific campaign management
   */
  async getCampaignsByProject(projectId: string, includeConfig: boolean = false): Promise<any> {
    const campaigns = await this.prisma.campaignQuery.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' },
    });

    let campaignsData = campaigns.map(campaign => ({
      id: campaign.id,
      name: campaign.displayName,
      description: campaign.displayDescription,
      campaignType: campaign.campaignType,
      status: campaign.status,
      startDate: campaign.startDate,
      endDate: campaign.endDate,
      isActive: campaign.isActive,
      
      // Metrics
      participantCount: campaign.participantCount,
      totalNftsMinted: campaign.totalNftsMinted,
      engagementRate: campaign.engagementRate,
      successRate: campaign.successRate,
      
      // Approval status
      launchApproval: campaign.launchApproval,
      approvedBy: campaign.approvedBy,
      
      createdAt: campaign.createdAt,
      lastUpdated: campaign.lastUpdated,
    }));

    // Include full configuration if requested
    if (includeConfig && campaigns.length > 0) {
      const fullCampaigns = await this.prisma.campaignCommand.findMany({
        where: { 
          id: { in: campaigns.map(c => c.id) },
          projectId,
        },
      });

      campaignsData = campaignsData.map(campaign => {
        const fullCampaign = fullCampaigns.find(fc => fc.id === campaign.id);
        if (fullCampaign) {
          const extendedCampaign = campaign as any;
          extendedCampaign.fullConfiguration = {
            targetAudience: fullCampaign.targetAudience,
            requirements: fullCampaign.requirements,
            nftGenerationRules: fullCampaign.nftGenerationRules,
            targetMetrics: fullCampaign.targetMetrics,
          };
          return extendedCampaign;
        }
        return campaign;
      });
    }

    return {
      success: true,
      data: {
        campaigns: campaignsData,
        total: campaigns.length,
        projectId,
      },
    };
  }

  /**
   * Get campaign statistics
   * Requirements: Analytics data for dashboard and monitoring
   */
  async getCampaignStats(query: CampaignStatsQuery): Promise<any> {
    const { projectId, ownerId, timeRange = '30d' } = query;

    // Calculate date range
    const now = new Date();
    const dateRanges = {
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      'all': new Date(0),
    };

    const startDate = dateRanges[timeRange];

    // Build where clause
    const where: any = {
      createdAt: { gte: startDate },
    };

    if (projectId) {
      where.projectId = projectId;
    }

    if (ownerId) {
      where.projectOwner = ownerId;
    }

    // Get aggregated statistics
    const [
      totalCampaigns,
      activeCampaigns,
      totalParticipants,
      totalNfts,
      avgEngagement,
      avgSuccess,
    ] = await Promise.all([
      this.prisma.campaignQuery.count({ where }),
      this.prisma.campaignQuery.count({ 
        where: { ...where, isActive: true } 
      }),
      this.prisma.campaignQuery.aggregate({
        where,
        _sum: { participantCount: true },
      }),
      this.prisma.campaignQuery.aggregate({
        where,
        _sum: { totalNftsMinted: true },
      }),
      this.prisma.campaignQuery.aggregate({
        where,
        _avg: { engagementRate: true },
      }),
      this.prisma.campaignQuery.aggregate({
        where,
        _avg: { successRate: true },
      }),
    ]);

    // Get top performing campaigns
    const topCampaigns = await this.prisma.campaignQuery.findMany({
      where,
      orderBy: { successRate: 'desc' },
      take: 5,
      select: {
        id: true,
        displayName: true,
        projectName: true,
        successRate: true,
        participantCount: true,
        engagementRate: true,
      },
    });

    // Get campaign type distribution
    const typeStats = await this.prisma.campaignQuery.groupBy({
      by: ['campaignType'],
      where,
      _count: { campaignType: true },
      _avg: { successRate: true },
    });

    return {
      success: true,
      data: {
        overview: {
          totalCampaigns,
          activeCampaigns,
          totalParticipants: totalParticipants._sum.participantCount || 0,
          totalNfts: totalNfts._sum.totalNftsMinted || 0,
          averageEngagement: avgEngagement._avg.engagementRate || 0,
          averageSuccess: avgSuccess._avg.successRate || 0,
        },
        topCampaigns,
        typeDistribution: typeStats.map(stat => ({
          type: stat.campaignType,
          count: stat._count.campaignType,
          averageSuccess: stat._avg.successRate || 0,
        })),
        timeRange,
      },
    };
  }

  /**
   * Search campaigns with advanced filtering
   * Requirements: Advanced search for campaign discovery
   */
  async searchCampaigns(searchTerm: string, filters: any = {}): Promise<any> {
    const where: any = {
      OR: [
        { displayName: { contains: searchTerm, mode: 'insensitive' } },
        { displayDescription: { contains: searchTerm, mode: 'insensitive' } },
        { projectName: { contains: searchTerm, mode: 'insensitive' } },
      ],
    };

    // Apply additional filters
    if (filters.campaignType) {
      where.campaignType = filters.campaignType;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.minParticipants) {
      where.participantCount = { gte: filters.minParticipants };
    }

    const campaigns = await this.prisma.campaignQuery.findMany({
      where,
      orderBy: [
        { isActive: 'desc' },
        { successRate: 'desc' },
      ],
      take: 50, // Limit search results
    });

    return {
      success: true,
      data: {
        campaigns: campaigns.map(campaign => ({
          id: campaign.id,
          projectId: campaign.projectId,
          projectName: campaign.projectName,
          name: campaign.displayName,
          description: campaign.displayDescription,
          campaignType: campaign.campaignType,
          status: campaign.status,
          isActive: campaign.isActive,
          participantCount: campaign.participantCount,
          engagementRate: campaign.engagementRate,
          successRate: campaign.successRate,
        })),
        total: campaigns.length,
        searchTerm,
      },
    };
  }
}
