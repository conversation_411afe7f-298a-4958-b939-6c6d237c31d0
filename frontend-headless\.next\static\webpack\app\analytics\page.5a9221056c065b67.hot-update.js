"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/analytics-dashboard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [platformData, setPlatformData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gainersLosers, setGainersLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [marketValue, setMarketValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('24h');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch platform overview using proper API client\n            const platformResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getPlatformOverview();\n            if (platformResult.success) {\n                setPlatformData(platformResult.data);\n            }\n            // Fetch top gainers/losers using proper API client\n            const gainersResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getTopGainersLosers(selectedPeriod, 5);\n            if (gainersResult.success) {\n                setGainersLosers(gainersResult.data);\n            }\n            // Fetch collection market value using proper API client\n            const marketResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getCollectionMarketValue();\n            if (marketResult.success) {\n                setMarketValue(marketResult.data);\n            }\n        } catch (err) {\n            setError('Failed to fetch analytics data');\n            console.error('Analytics fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsDashboard.useEffect\"], [\n        selectedPeriod\n    ]);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n        return num.toString();\n    };\n    const formatEth = (eth)=>\"\".concat(eth.toFixed(2), \" ETH\");\n    const getRarityColor = (rarity)=>{\n        switch(rarity.toLowerCase()){\n            case 'legendary':\n                return 'bg-yellow-500';\n            case 'epic':\n                return 'bg-purple-500';\n            case 'rare':\n                return 'bg-blue-500';\n            case 'common':\n                return 'bg-gray-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading analytics data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchAnalyticsData,\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            \"Retry\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 191,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Comprehensive platform insights and performance metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.user_growth_rate,\n                                                \"% from last month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"NFTs Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_nfts_generated)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.nft_generation_growth_rate,\n                                                \"% growth rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatEth(platformData.overview.total_volume_eth)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.marketplace_volume_growth_rate,\n                                                \"% volume growth\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Active Campaigns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: platformData.overview.active_campaigns\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                platformData.growth_metrics.campaign_participation_rate,\n                                                \"% participation rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: 'overview',\n                                    name: 'Overview'\n                                },\n                                {\n                                    id: 'gainers-losers',\n                                    name: 'Top Gainers/Losers'\n                                },\n                                {\n                                    id: 'market-value',\n                                    name: 'Market Value'\n                                },\n                                {\n                                    id: 'performance',\n                                    name: 'Performance'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab.name\n                                }, tab.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Top Performing Campaigns\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Campaigns with highest engagement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: platformData.top_performing.campaigns.map((campaign, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: campaign.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        formatNumber(campaign.participants),\n                                                                        \" participants\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                            children: [\n                                                                formatNumber(campaign.nfts_generated),\n                                                                \" NFTs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, campaign.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"NFT Rarity Distribution\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Distribution of NFT rarities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: platformData.top_performing.nft_rarities.map((rarity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full \".concat(getRarityColor(rarity.rarity))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: rarity.rarity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: formatNumber(rarity.count)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        rarity.percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, rarity.rarity, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    activeTab !== 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: [\n                                        activeTab === 'gainers-losers' && 'Top Gainers/Losers',\n                                        activeTab === 'market-value' && 'Market Value Analytics',\n                                        activeTab === 'performance' && 'Performance Analytics'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Advanced analytics features coming soon. The backend is ready and providing comprehensive data.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"f7c2j0OrDvpHwYxeRnBil/GM8vc=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx\n"));

/***/ })

});