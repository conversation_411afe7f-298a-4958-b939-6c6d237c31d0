import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // Create NestJS application
  const app = await NestFactory.create(AppModule);

  // Get configuration service
  const configService = app.get(ConfigService);

  // Set global prefix
  app.setGlobalPrefix('api');

  // Enable CORS
  app.enableCors({
    origin: configService.get<string>('ALLOWED_ORIGINS', 'http://localhost:3003').split(','),
    credentials: true,
  });

  // Use global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Set up Swagger
  const config = new DocumentBuilder()
    .setTitle('{{SERVICE_NAME}} Service API')
    .setDescription('The {{SERVICE_NAME}} Service API description')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('{{SERVICE_NAME}}')
    .addTag('health')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Connect to microservice transport
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('MICROSERVICE_HOST', 'localhost'),
      port: configService.get<number>('MICROSERVICE_PORT', {{SERVICE_PORT}}),
    },
  });

  // Connect to RabbitMQ for event-driven communication - Temporarily disabled
  // app.connectMicroservice<MicroserviceOptions>({
  //   transport: Transport.RMQ,
  //   options: {
  //     urls: [configService.get<string>('RABBITMQ_URL', 'amqp://admin:admin@localhost:5672')],
  //     queue: configService.get<string>('RABBITMQ_QUEUE', '{{SERVICE_NAME_SNAKE}}_queue'),
  //     queueOptions: {
  //       durable: true,
  //     },
  //   },
  // });

  // Start microservices - Temporarily disabled
  // await app.startAllMicroservices();

  // Start HTTP server
  const port = configService.get<number>('HTTP_PORT', {{SERVICE_PORT}});
  await app.listen(port);

  logger.log(`{{SERVICE_NAME}} Service is running on: http://localhost:${port}`);
  logger.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
  logger.log(`Health check endpoint: http://localhost:${port}/api/health`);
}

bootstrap().catch((error) => {
  console.error('Failed to start {{SERVICE_NAME}} Service:', error);
  process.exit(1);
});
