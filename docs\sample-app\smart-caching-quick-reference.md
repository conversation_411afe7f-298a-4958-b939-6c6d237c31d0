# Smart Docker Caching - Quick Reference

## 🚀 **Common Commands**

### **Daily Development**
```bash
# Rebuild service after code changes (FAST - 30-60s)
./scripts/dev-restart-service.sh service-name

# Rebuild after package.json changes (SLOWER - 120-180s)
./scripts/dev-restart-service.sh service-name --force-deps

# Rebuild all services
./scripts/standardize-security.sh
```

### **Service Status**
```bash
# Check running services
docker-compose ps

# View service logs
docker-compose logs -f service-name

# Check build times
docker images | grep service-name
```

## 📊 **Performance Expectations**

| **Operation** | **Expected Time** | **Bandwidth** |
|---------------|------------------|---------------|
| **Code Change** | 30-60 seconds | ~5 MB |
| **Package Change** | 120-180 seconds | 50-150 MB |
| **Fresh Build** | 60-120 seconds | 300-600 MB |

## ✅ **Success Indicators**

Look for these in build output:
```bash
✅ CACHED [service dependencies 5/5] RUN npm install
✅ [service development 9/11] RUN npm run build
✅ [+] Building 45.2s (21/21) FINISHED
```

## 🚨 **Troubleshooting**

### **Build Too Slow?**
```bash
# Check if dependencies are cached
# Should see "CACHED" for npm install step
# If not, use --force-deps
```

### **Module Not Found?**
```bash
# Check libs/common is copied
docker run --rm service-image ls -la /app/libs/
```

### **Complete Reset**
```bash
# Nuclear option
docker system prune -a
./scripts/dev-restart-service.sh service-name --force-deps
```

## 📁 **File Structure**

```
services/
├── service-name/
│   ├── Dockerfile          # Production
│   ├── Dockerfile.dev      # Smart caching
│   ├── package.json        # Dependencies
│   └── src/               # Source code
├── docker-compose.yml      # Production
└── docker-compose.override.yml  # Development
```

## 🎯 **Best Practices**

### **DO:**
- ✅ Use smart caching scripts
- ✅ Monitor build times
- ✅ Use `--force-deps` for package changes
- ✅ Watch for "CACHED" messages

### **DON'T:**
- ❌ Use `--no-cache` for regular development
- ❌ Ignore slow build times
- ❌ Modify Dockerfile.dev without understanding
- ❌ Mix production/development configs

## 🔧 **Adding New Service**

1. **Copy template:**
   ```bash
   cp docs/templates/Dockerfile.dev.template services/new-service/Dockerfile.dev
   ```

2. **Edit placeholders:**
   - Replace `SERVICE_NAME` with actual name
   - Replace `PORT_NUMBER` with actual port

3. **Add to docker-compose.override.yml:**
   ```yaml
   new-service:
     build:
       dockerfile: services/new-service/Dockerfile.dev
       target: development
   ```

4. **Test:**
   ```bash
   ./scripts/dev-restart-service.sh new-service
   ```

## 📞 **Need Help?**

- 📖 **Full Documentation**: `docs/development/smart-docker-caching.md`
- 🛠️ **Templates**: `docs/templates/Dockerfile.dev.template`
- 🔧 **Scripts**: `scripts/dev-restart-service.sh`
