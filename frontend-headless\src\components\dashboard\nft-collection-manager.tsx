'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { nftApi } from '@/lib/api'
import { 
  FunnelIcon,
  MagnifyingGlassIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  TrashIcon,
  TagIcon
} from '@heroicons/react/24/outline'
import EnhancedNFTCard from './enhanced-nft-card'
import NFTDetailModal from './nft-detail-modal'
import EnhancedShareModal from '@/components/ui/enhanced-share-modal'

interface NFT {
  id: string
  name: string
  rarity: string
  score: number
  twitterHandle: string
  createdAt: string
  metadata?: any
  status?: string
  imageUrl?: string
}

interface NFTCollectionManagerProps {
  refreshTrigger?: number
}

export default function NFTCollectionManager({ refreshTrigger }: NFTCollectionManagerProps) {
  const { user } = useAuth()
  const [nfts, setNfts] = useState<NFT[]>([])
  const [filteredNfts, setFilteredNfts] = useState<NFT[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  // Filters and search
  const [searchQuery, setSearchQuery] = useState('')
  const [rarityFilter, setRarityFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'score' | 'rarity'>('newest')
  const [selectedNFTs, setSelectedNFTs] = useState<string[]>([])

  useEffect(() => {
    if (user?.id) {
      fetchUserNFTs()
    }
  }, [user?.id, refreshTrigger])

  useEffect(() => {
    applyFilters()
  }, [nfts, searchQuery, rarityFilter, sortBy])

  const fetchUserNFTs = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const response = await nftApi.getUserNFTs(user.id)
      
      if (response.success && response.data) {
        const nftData = Array.isArray(response.data) ? response.data : (response.data.nfts || [])
        setNfts(nftData)
      }
    } catch (error) {
      console.error('Error fetching NFTs:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...nfts]

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(nft => 
        nft.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        nft.twitterHandle.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Rarity filter
    if (rarityFilter !== 'all') {
      filtered = filtered.filter(nft => nft.rarity.toLowerCase() === rarityFilter.toLowerCase())
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'score':
          return b.score - a.score
        case 'rarity':
          const rarityOrder = { legendary: 4, epic: 3, rare: 2, common: 1 }
          return (rarityOrder[b.rarity.toLowerCase() as keyof typeof rarityOrder] || 0) - 
                 (rarityOrder[a.rarity.toLowerCase() as keyof typeof rarityOrder] || 0)
        default:
          return 0
      }
    })

    setFilteredNfts(filtered)
  }

  const handleNFTClick = (nft: NFT) => {
    setSelectedNFT(nft)
    setShowDetailModal(true)
  }

  const handleShare = (nft: NFT) => {
    setSelectedNFT(nft)
    setShowShareModal(true)
  }

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on NFTs:`, selectedNFTs)
    // TODO: Implement bulk actions
  }

  const toggleNFTSelection = (nftId: string) => {
    setSelectedNFTs(prev => 
      prev.includes(nftId) 
        ? prev.filter(id => id !== nftId)
        : [...prev, nftId]
    )
  }

  const getRarityStats = () => {
    const stats = nfts.reduce((acc, nft) => {
      acc[nft.rarity.toLowerCase()] = (acc[nft.rarity.toLowerCase()] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return stats
  }

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">My NFT Collection</h3>
            <p className="text-sm text-gray-500">{nfts.length} NFTs total</p>
          </div>
          
          <div className="flex items-center space-x-2">
            {selectedNFTs.length > 0 && (
              <div className="flex items-center space-x-2 mr-4">
                <span className="text-sm text-gray-600">{selectedNFTs.length} selected</span>
                <button
                  onClick={() => handleBulkAction('share')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Share selected"
                >
                  <ShareIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleBulkAction('download')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Download selected"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
              </div>
            )}
            
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 text-gray-400 hover:text-gray-600"
              title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
            >
              {viewMode === 'grid' ? (
                <ListBulletIcon className="h-5 w-5" />
              ) : (
                <Squares2X2Icon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search NFTs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <select
              value={rarityFilter}
              onChange={(e) => setRarityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Rarities</option>
              <option value="common">Common</option>
              <option value="rare">Rare</option>
              <option value="epic">Epic</option>
              <option value="legendary">Legendary</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="score">Highest Score</option>
              <option value="rarity">Rarity</option>
            </select>
          </div>
        </div>

        {/* Rarity Stats */}
        <div className="mt-4 flex flex-wrap gap-2">
          {Object.entries(getRarityStats()).map(([rarity, count]) => (
            <span
              key={rarity}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
            >
              <TagIcon className="h-3 w-3 mr-1" />
              {rarity}: {count}
            </span>
          ))}
        </div>
      </div>

      {/* NFT Grid/List */}
      <div className="p-6">
        {filteredNfts.length === 0 ? (
          <div className="text-center py-12">
            <Squares2X2Icon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No NFTs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery || rarityFilter !== 'all' 
                ? 'Try adjusting your filters or search terms.'
                : 'Start by analyzing Twitter profiles to generate your first NFT!'
              }
            </p>
          </div>
        ) : (
          <div className={`grid gap-4 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredNfts.map((nft) => (
              <div key={nft.id} className="relative">
                {/* Selection checkbox for bulk actions */}
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    checked={selectedNFTs.includes(nft.id)}
                    onChange={() => toggleNFTSelection(nft.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                
                <EnhancedNFTCard
                  nft={nft}
                  onClick={() => handleNFTClick(nft)}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <NFTDetailModal
        nft={selectedNFT}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
      />

      <EnhancedShareModal
        nft={selectedNFT}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
      />
    </div>
  )
}
