import { SetMetadata } from '@nestjs/common';
import { Permission } from '../dto/rbac.dto';
import { PERMISSIONS_KEY, REQUIRE_ALL_PERMISSIONS_KEY } from '../guards/rbac.guard';

/**
 * Decorator to specify required permissions for a route
 * @param permissions - Array of permissions required
 * @param requireAll - Whether all permissions are required (default: false, meaning ANY permission is sufficient)
 */
export const RequirePermissions = (permissions: Permission[], requireAll: boolean = false) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    SetMetadata(PERMISSIONS_KEY, permissions)(target, propertyKey, descriptor);
    SetMetadata(REQUIRE_ALL_PERMISSIONS_KEY, requireAll)(target, propertyKey, descriptor);
  };
};

/**
 * Decorator to require ANY of the specified permissions
 * @param permissions - Array of permissions, user needs at least one
 */
export const RequireAnyPermission = (...permissions: Permission[]) => {
  return RequirePermissions(permissions, false);
};

/**
 * Decorator to require ALL of the specified permissions
 * @param permissions - Array of permissions, user needs all of them
 */
export const RequireAllPermissions = (...permissions: Permission[]) => {
  return RequirePermissions(permissions, true);
};

/**
 * Common permission decorators for convenience
 */

// User management permissions
export const RequireUserRead = () => RequirePermissions([Permission.USER_READ]);
export const RequireUserWrite = () => RequirePermissions([Permission.USER_WRITE]);
export const RequireUserDelete = () => RequirePermissions([Permission.USER_DELETE]);
export const RequireUserAdmin = () => RequirePermissions([Permission.USER_ADMIN]);

// Profile management permissions
export const RequireProfileRead = () => RequirePermissions([Permission.PROFILE_READ]);
export const RequireProfileWrite = () => RequirePermissions([Permission.PROFILE_WRITE]);
export const RequireProfileAdmin = () => RequirePermissions([Permission.PROFILE_ADMIN]);

// Campaign management permissions
export const RequireCampaignRead = () => RequirePermissions([Permission.CAMPAIGN_READ]);
export const RequireCampaignWrite = () => RequirePermissions([Permission.CAMPAIGN_WRITE]);
export const RequireCampaignDelete = () => RequirePermissions([Permission.CAMPAIGN_DELETE]);
export const RequireCampaignAdmin = () => RequirePermissions([Permission.CAMPAIGN_ADMIN]);

// NFT management permissions
export const RequireNFTRead = () => RequirePermissions([Permission.NFT_READ]);
export const RequireNFTWrite = () => RequirePermissions([Permission.NFT_WRITE]);
export const RequireNFTDelete = () => RequirePermissions([Permission.NFT_DELETE]);
export const RequireNFTAdmin = () => RequirePermissions([Permission.NFT_ADMIN]);

// Marketplace permissions
export const RequireMarketplaceRead = () => RequirePermissions([Permission.MARKETPLACE_READ]);
export const RequireMarketplaceWrite = () => RequirePermissions([Permission.MARKETPLACE_WRITE]);
export const RequireMarketplaceDelete = () => RequirePermissions([Permission.MARKETPLACE_DELETE]);
export const RequireMarketplaceAdmin = () => RequirePermissions([Permission.MARKETPLACE_ADMIN]);

// Analytics permissions
export const RequireAnalyticsRead = () => RequirePermissions([Permission.ANALYTICS_READ]);
export const RequireAnalyticsWrite = () => RequirePermissions([Permission.ANALYTICS_WRITE]);
export const RequireAnalyticsAdmin = () => RequirePermissions([Permission.ANALYTICS_ADMIN]);

// System permissions
export const RequireSystemRead = () => RequirePermissions([Permission.SYSTEM_READ]);
export const RequireSystemWrite = () => RequirePermissions([Permission.SYSTEM_WRITE]);
export const RequireSystemAdmin = () => RequirePermissions([Permission.SYSTEM_ADMIN]);

// Audit permissions
export const RequireAuditRead = () => RequirePermissions([Permission.AUDIT_READ]);
export const RequireAuditWrite = () => RequirePermissions([Permission.AUDIT_WRITE]);
export const RequireAuditAdmin = () => RequirePermissions([Permission.AUDIT_ADMIN]);

// Combined permissions for common use cases
export const RequireModeratorOrAdmin = () => RequireAnyPermission(
  Permission.USER_WRITE,
  Permission.USER_ADMIN,
  Permission.SYSTEM_ADMIN
);

export const RequireAdminOnly = () => RequireAnyPermission(
  Permission.USER_ADMIN,
  Permission.SYSTEM_ADMIN
);

export const RequireSuperAdminOnly = () => RequirePermissions([Permission.SYSTEM_ADMIN]);

// Resource-specific permissions
export const RequireOwnershipOrAdmin = () => RequireAnyPermission(
  Permission.USER_ADMIN,
  Permission.SYSTEM_ADMIN
  // Note: Ownership check would be implemented in the guard logic
);

/**
 * Decorator for public endpoints that don't require authentication
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * Decorator for endpoints that require authentication but no specific permissions
 */
export const Authenticated = () => RequirePermissions([]);
