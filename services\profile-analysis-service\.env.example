# Profile Analysis Service Configuration
PORT=3002
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/profile_analysis_db"

# Twitter API Configuration
# Option 1: Use Real Twitter API v2 (Production)
USE_REAL_TWITTER_API=false
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here

# Option 2: Use External Twitter Service (Development/Mock)
TWITTER_SERVICE_URL=http://localhost:3020

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3010

# Rate Limiting
TWITTER_RATE_LIMIT_REQUESTS_PER_WINDOW=100
TWITTER_RATE_LIMIT_WINDOW_MS=900000

# Logging
LOG_LEVEL=info
