# Development Environment with Real Services
# Use this configuration for testing with actual external APIs

NODE_ENV=development
USE_MOCK_SERVICES=false

# API Gateway Configuration
API_GATEWAY_PORT=3010

# Real Service URLs (Production Services)
TWITTER_SERVICE_URL=http://localhost:3002
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
NFT_STORAGE_SERVICE_URL=http://localhost:3006
USER_SERVICE_URL=http://localhost:3011
PROJECT_SERVICE_URL=http://localhost:3005
ANALYTICS_SERVICE_URL=http://localhost:3001

# Database Configuration (Real database for all environments)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-specific Database Names
USER_DB_NAME=user_service_db
PROJECT_DB_NAME=project_service_db
ANALYTICS_DB_NAME=analytics_service_db

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# External API Configuration (Real APIs)
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=1WzbsQXOrskAx4tP231AtUSKm6QOnDklbvXMHozDwP8nOpnK3c
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAKEC1AEAAAAAfnWqV8LvjCmxyIUfLf1cslix%2FTI%3DmgfXwKx3Mm5warOLTUyqcbUzbiN7lrQ1UmE5GoMTOOY051IkPZ

BLOCKCHAIN_RPC_URL=your-blockchain-rpc-url
BLOCKCHAIN_PRIVATE_KEY=your-blockchain-private-key

NFT_STORAGE_API_KEY=your-nft-storage-api-key

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=false
