// Systematic API Gateway Diagnostic Test
// Identifies the exact cause of JSON parsing issues after Prisma migration

const axios = require('axios');

const PROJECT_SERVICE_URL = 'http://localhost:3005';
const API_GATEWAY_URL = 'http://localhost:3010/api';

class APIGatewayDiagnostic {
  constructor() {
    this.findings = [];
  }

  async runDiagnostic() {
    console.log('🔍 SYSTEMATIC API GATEWAY DIAGNOSTIC\n');
    console.log('Investigating JSON parsing issues after Prisma migration...\n');
    
    try {
      // Step 1: Verify Project Service is working
      await this.testProjectServiceDirect();
      
      // Step 2: Test API Gateway basic connectivity
      await this.testAPIGatewayConnectivity();
      
      // Step 3: Test API Gateway health endpoint
      await this.testAPIGatewayHealth();
      
      // Step 4: Test API Gateway proxy with detailed logging
      await this.testAPIGatewayProxy();
      
      // Step 5: Test response content types and headers
      await this.testResponseHeaders();
      
      // Step 6: Test with different endpoints
      await this.testDifferentEndpoints();
      
      // Step 7: Test JSON serialization issues
      await this.testJSONSerialization();
      
      // Summary
      this.printDiagnosticSummary();
      
    } catch (error) {
      console.error('❌ Diagnostic failed:', error.message);
    }
  }

  async testProjectServiceDirect() {
    console.log('1️⃣ Testing Project Service Direct Access...');
    
    try {
      // Test health endpoint
      const healthResponse = await axios.get(`${PROJECT_SERVICE_URL}/health`);
      this.logFinding('Project Service Health', 'PASS', {
        status: healthResponse.status,
        contentType: healthResponse.headers['content-type'],
        data: healthResponse.data
      });

      // Test projects endpoint
      const projectsResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects`);
      this.logFinding('Project Service Projects', 'PASS', {
        status: projectsResponse.status,
        contentType: projectsResponse.headers['content-type'],
        dataType: typeof projectsResponse.data,
        hasData: !!projectsResponse.data,
        dataStructure: this.analyzeDataStructure(projectsResponse.data)
      });

    } catch (error) {
      this.logFinding('Project Service Direct', 'FAIL', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
    }
  }

  async testAPIGatewayConnectivity() {
    console.log('\n2️⃣ Testing API Gateway Basic Connectivity...');
    
    try {
      // Test if API Gateway is running
      const response = await axios.get(`${API_GATEWAY_URL.replace('/api', '')}/`, { timeout: 5000 });
      this.logFinding('API Gateway Connectivity', 'PASS', {
        status: response.status,
        contentType: response.headers['content-type']
      });
    } catch (error) {
      this.logFinding('API Gateway Connectivity', 'FAIL', {
        error: error.message,
        code: error.code,
        status: error.response?.status
      });
    }
  }

  async testAPIGatewayHealth() {
    console.log('\n3️⃣ Testing API Gateway Health Endpoint...');
    
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/health`, { timeout: 10000 });
      this.logFinding('API Gateway Health', 'PASS', {
        status: response.status,
        contentType: response.headers['content-type'],
        data: response.data
      });
    } catch (error) {
      this.logFinding('API Gateway Health', 'FAIL', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
    }
  }

  async testAPIGatewayProxy() {
    console.log('\n4️⃣ Testing API Gateway Proxy with Detailed Logging...');
    
    try {
      // Configure axios to capture raw response
      const response = await axios({
        method: 'GET',
        url: `${API_GATEWAY_URL}/projects`,
        timeout: 15000,
        validateStatus: () => true, // Accept any status
        transformResponse: [(data) => {
          // Log raw response data
          console.log('🔍 Raw Response Analysis:', {
            dataType: typeof data,
            dataLength: data ? data.length : 'null',
            isString: typeof data === 'string',
            isNull: data === null,
            isUndefined: data === undefined,
            isEmpty: data === '',
            firstChars: data ? data.substring(0, 100) : 'N/A'
          });
          
          // Try to parse if it's a string
          if (typeof data === 'string' && data.trim()) {
            try {
              return JSON.parse(data);
            } catch (parseError) {
              console.log('❌ JSON Parse Error:', parseError.message);
              return { parseError: parseError.message, rawData: data };
            }
          }
          
          return data;
        }]
      });

      this.logFinding('API Gateway Proxy', response.status < 400 ? 'PASS' : 'FAIL', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers['content-type'],
        contentLength: response.headers['content-length'],
        dataType: typeof response.data,
        data: response.data
      });

    } catch (error) {
      this.logFinding('API Gateway Proxy', 'FAIL', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
    }
  }

  async testResponseHeaders() {
    console.log('\n5️⃣ Testing Response Headers and Content Types...');
    
    try {
      // Test Project Service headers
      const directResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects`);
      
      // Test API Gateway headers
      const gatewayResponse = await axios.get(`${API_GATEWAY_URL}/projects`, { 
        validateStatus: () => true 
      });

      this.logFinding('Response Headers Comparison', 'INFO', {
        direct: {
          contentType: directResponse.headers['content-type'],
          contentLength: directResponse.headers['content-length'],
          server: directResponse.headers['server'],
          encoding: directResponse.headers['content-encoding']
        },
        gateway: {
          contentType: gatewayResponse.headers['content-type'],
          contentLength: gatewayResponse.headers['content-length'],
          server: gatewayResponse.headers['server'],
          encoding: gatewayResponse.headers['content-encoding']
        }
      });

    } catch (error) {
      this.logFinding('Response Headers', 'FAIL', { error: error.message });
    }
  }

  async testDifferentEndpoints() {
    console.log('\n6️⃣ Testing Different Endpoints...');
    
    const endpoints = [
      '/projects/stats',
      '/campaigns',
      '/campaigns/stats'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${API_GATEWAY_URL}${endpoint}`, { 
          validateStatus: () => true,
          timeout: 10000
        });
        
        this.logFinding(`Endpoint ${endpoint}`, response.status < 400 ? 'PASS' : 'FAIL', {
          status: response.status,
          dataType: typeof response.data,
          hasData: !!response.data
        });

      } catch (error) {
        this.logFinding(`Endpoint ${endpoint}`, 'FAIL', { error: error.message });
      }
    }
  }

  async testJSONSerialization() {
    console.log('\n7️⃣ Testing JSON Serialization Issues...');
    
    try {
      // Test if Prisma is returning non-serializable data
      const directResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects`);
      
      // Try to re-serialize the data
      const reSerializedData = JSON.parse(JSON.stringify(directResponse.data));
      
      this.logFinding('JSON Serialization', 'PASS', {
        originalDataType: typeof directResponse.data,
        reSerializedDataType: typeof reSerializedData,
        hasDateObjects: this.hasDateObjects(directResponse.data),
        hasBigIntObjects: this.hasBigIntObjects(directResponse.data),
        hasCircularReferences: this.hasCircularReferences(directResponse.data)
      });

    } catch (error) {
      this.logFinding('JSON Serialization', 'FAIL', { error: error.message });
    }
  }

  analyzeDataStructure(data) {
    if (!data) return 'null/undefined';
    
    const structure = {
      type: typeof data,
      isArray: Array.isArray(data),
      hasSuccess: data.hasOwnProperty && data.hasOwnProperty('success'),
      hasData: data.hasOwnProperty && data.hasOwnProperty('data'),
      keys: data && typeof data === 'object' ? Object.keys(data) : []
    };
    
    return structure;
  }

  hasDateObjects(obj) {
    return JSON.stringify(obj).includes('"createdAt"') || JSON.stringify(obj).includes('"updatedAt"');
  }

  hasBigIntObjects(obj) {
    try {
      JSON.stringify(obj, (key, value) => {
        if (typeof value === 'bigint') {
          throw new Error('BigInt detected');
        }
        return value;
      });
      return false;
    } catch (error) {
      return error.message.includes('BigInt');
    }
  }

  hasCircularReferences(obj) {
    try {
      JSON.stringify(obj);
      return false;
    } catch (error) {
      return error.message.includes('circular');
    }
  }

  logFinding(test, status, details) {
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : 'ℹ️';
    console.log(`${statusIcon} ${test}:`, details);
    this.findings.push({ test, status, details });
  }

  printDiagnosticSummary() {
    console.log('\n📊 DIAGNOSTIC SUMMARY:');
    console.log('======================');
    
    const passed = this.findings.filter(f => f.status === 'PASS').length;
    const failed = this.findings.filter(f => f.status === 'FAIL').length;
    const info = this.findings.filter(f => f.status === 'INFO').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`ℹ️ Info: ${info}`);
    
    console.log('\n🔍 ROOT CAUSE ANALYSIS:');
    
    // Analyze findings to determine root cause
    const projectServiceWorking = this.findings.find(f => f.test === 'Project Service Projects')?.status === 'PASS';
    const gatewayConnectivity = this.findings.find(f => f.test === 'API Gateway Connectivity')?.status === 'PASS';
    const gatewayHealth = this.findings.find(f => f.test === 'API Gateway Health')?.status === 'PASS';
    const gatewayProxy = this.findings.find(f => f.test === 'API Gateway Proxy')?.status === 'PASS';
    
    if (!projectServiceWorking) {
      console.log('❌ ISSUE: Project Service is not working properly');
    } else if (!gatewayConnectivity) {
      console.log('❌ ISSUE: API Gateway is not running or accessible');
    } else if (!gatewayHealth) {
      console.log('❌ ISSUE: API Gateway health endpoint is failing');
    } else if (!gatewayProxy) {
      console.log('❌ ISSUE: API Gateway proxy is failing to forward requests properly');
      console.log('🔧 LIKELY CAUSE: JSON serialization issue in proxy service');
    } else {
      console.log('✅ All systems appear to be working');
    }
    
    console.log('\n🎯 RECOMMENDED ACTIONS:');
    if (!gatewayProxy && projectServiceWorking) {
      console.log('1. Check API Gateway proxy service response handling');
      console.log('2. Verify JSON serialization in proxy service');
      console.log('3. Check for Prisma Date/BigInt serialization issues');
      console.log('4. Review error handling in proxy service');
    }
  }
}

// Run the diagnostic
const diagnostic = new APIGatewayDiagnostic();
diagnostic.runDiagnostic().catch(console.error);
