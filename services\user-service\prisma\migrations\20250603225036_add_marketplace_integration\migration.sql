-- CreateTable
CREATE TABLE "marketplace_listings" (
    "id" TEXT NOT NULL,
    "nft_id" TEXT NOT NULL,
    "seller_id" TEXT NOT NULL,
    "listing_type" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'draft',
    "price" VARCHAR(100) NOT NULL,
    "currency" VARCHAR(10) NOT NULL,
    "starting_price" VARCHAR(100),
    "reserve_price" VARCHAR(100),
    "buy_now_price" VARCHAR(100),
    "current_bid" VARCHAR(100),
    "bid_count" INTEGER NOT NULL DEFAULT 0,
    "title" VARCHAR(100) NOT NULL,
    "description" TEXT NOT NULL,
    "tags" TEXT[],
    "accept_offers" BOOLEAN NOT NULL DEFAULT true,
    "min_offer_amount" VARCHAR(100),
    "external_listing_id" VARCHAR(100),
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "view_count" INTEGER NOT NULL DEFAULT 0,
    "favorite_count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "sold_at" TIMESTAMP(3),
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "marketplace_listings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "marketplace_offers" (
    "id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "buyer_id" TEXT NOT NULL,
    "amount" VARCHAR(100) NOT NULL,
    "currency" VARCHAR(10) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "message" TEXT,
    "external_offer_id" VARCHAR(100),
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP(3),
    "accepted_at" TIMESTAMP(3),
    "rejected_at" TIMESTAMP(3),

    CONSTRAINT "marketplace_offers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "marketplace_transactions" (
    "id" TEXT NOT NULL,
    "nft_id" TEXT NOT NULL,
    "listing_id" TEXT,
    "seller_id" TEXT NOT NULL,
    "buyer_id" TEXT NOT NULL,
    "type" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "amount" VARCHAR(100) NOT NULL,
    "currency" VARCHAR(10) NOT NULL,
    "transaction_hash" VARCHAR(100),
    "block_number" INTEGER,
    "gas_used" INTEGER,
    "gas_price" VARCHAR(50),
    "platform_fee" VARCHAR(100),
    "royalty_fee" VARCHAR(100),
    "external_transaction_id" VARCHAR(100),
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "confirmed_at" TIMESTAMP(3),
    "failed_at" TIMESTAMP(3),

    CONSTRAINT "marketplace_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "marketplace_listings_nft_id_idx" ON "marketplace_listings"("nft_id");

-- CreateIndex
CREATE INDEX "marketplace_listings_seller_id_idx" ON "marketplace_listings"("seller_id");

-- CreateIndex
CREATE INDEX "marketplace_listings_status_idx" ON "marketplace_listings"("status");

-- CreateIndex
CREATE INDEX "marketplace_listings_listing_type_idx" ON "marketplace_listings"("listing_type");

-- CreateIndex
CREATE INDEX "marketplace_listings_currency_idx" ON "marketplace_listings"("currency");

-- CreateIndex
CREATE INDEX "marketplace_listings_price_idx" ON "marketplace_listings"("price");

-- CreateIndex
CREATE INDEX "marketplace_listings_created_at_idx" ON "marketplace_listings"("created_at");

-- CreateIndex
CREATE INDEX "marketplace_listings_expires_at_idx" ON "marketplace_listings"("expires_at");

-- CreateIndex
CREATE INDEX "marketplace_offers_listing_id_idx" ON "marketplace_offers"("listing_id");

-- CreateIndex
CREATE INDEX "marketplace_offers_buyer_id_idx" ON "marketplace_offers"("buyer_id");

-- CreateIndex
CREATE INDEX "marketplace_offers_status_idx" ON "marketplace_offers"("status");

-- CreateIndex
CREATE INDEX "marketplace_offers_amount_idx" ON "marketplace_offers"("amount");

-- CreateIndex
CREATE INDEX "marketplace_offers_created_at_idx" ON "marketplace_offers"("created_at");

-- CreateIndex
CREATE INDEX "marketplace_offers_expires_at_idx" ON "marketplace_offers"("expires_at");

-- CreateIndex
CREATE INDEX "marketplace_transactions_nft_id_idx" ON "marketplace_transactions"("nft_id");

-- CreateIndex
CREATE INDEX "marketplace_transactions_listing_id_idx" ON "marketplace_transactions"("listing_id");

-- CreateIndex
CREATE INDEX "marketplace_transactions_seller_id_idx" ON "marketplace_transactions"("seller_id");

-- CreateIndex
CREATE INDEX "marketplace_transactions_buyer_id_idx" ON "marketplace_transactions"("buyer_id");

-- CreateIndex
CREATE INDEX "marketplace_transactions_type_idx" ON "marketplace_transactions"("type");

-- CreateIndex
CREATE INDEX "marketplace_transactions_status_idx" ON "marketplace_transactions"("status");

-- CreateIndex
CREATE INDEX "marketplace_transactions_amount_idx" ON "marketplace_transactions"("amount");

-- CreateIndex
CREATE INDEX "marketplace_transactions_transaction_hash_idx" ON "marketplace_transactions"("transaction_hash");

-- CreateIndex
CREATE INDEX "marketplace_transactions_created_at_idx" ON "marketplace_transactions"("created_at");

-- AddForeignKey
ALTER TABLE "marketplace_listings" ADD CONSTRAINT "marketplace_listings_nft_id_fkey" FOREIGN KEY ("nft_id") REFERENCES "nfts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_listings" ADD CONSTRAINT "marketplace_listings_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_offers" ADD CONSTRAINT "marketplace_offers_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "marketplace_listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_offers" ADD CONSTRAINT "marketplace_offers_buyer_id_fkey" FOREIGN KEY ("buyer_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_transactions" ADD CONSTRAINT "marketplace_transactions_nft_id_fkey" FOREIGN KEY ("nft_id") REFERENCES "nfts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_transactions" ADD CONSTRAINT "marketplace_transactions_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "marketplace_listings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_transactions" ADD CONSTRAINT "marketplace_transactions_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_transactions" ADD CONSTRAINT "marketplace_transactions_buyer_id_fkey" FOREIGN KEY ("buyer_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;
