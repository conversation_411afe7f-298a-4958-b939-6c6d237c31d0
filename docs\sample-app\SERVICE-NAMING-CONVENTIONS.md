# Service Naming Conventions & Standardization Guidelines

## Overview
This document establishes comprehensive naming conventions for all components of the social commerce platform to ensure consistency, maintainability, and clear identification across development, testing, and production environments.

## 🗄️ Database Naming Rules

### Primary Database Names
- **Pattern:** `{service_name}_service`
- **Format:** lowercase with underscores
- **Examples:**
  - ✅ `user_service`
  - ✅ `store_service`
  - ✅ `product_service`
  - ✅ `order_service`

### Environment-Specific Database Names
- **Development:** `{service_name}_service` (no prefix)
- **Testing:** `test_{service_name}_service`
- **Staging:** `staging_{service_name}_service`
- **Production:** `prod_{service_name}_service`

### Database Configuration Variables
```bash
DB_DATABASE_USER=user_service
DB_DATABASE_STORE=store_service
DB_DATABASE_PRODUCT=product_service
```

### Multiple Database Creation
```bash
POSTGRES_MULTIPLE_DATABASES=user_service,store_service,product_service,order_service
```

## 🐳 Docker Image Naming Rules

### Service Images
- **Pattern:** `{service-name}-service`
- **Format:** lowercase with hyphens
- **Examples:**
  - ✅ `user-service`
  - ✅ `store-service`
  - ✅ `product-service`
  - ✅ `order-service`

### Infrastructure/Platform Images
- **Pattern:** `social-commerce-{component}`
- **Examples:**
  - ✅ `social-commerce-frontend`
  - ✅ `social-commerce-api-gateway`
  - ✅ `social-commerce-admin-dashboard`

### Image Tagging Strategy
```bash
# Development
user-service:latest
store-service:latest

# Feature Development
user-service:feature-auth-improvements
store-service:feature-inventory-management

# Releases
user-service:v1.0.0
user-service:v1.1.0
social-commerce-frontend:v2.0.0

# Environment Specific
user-service:prod-v1.0.0
user-service:staging-v1.1.0-rc1
```

## 📦 Container Naming Rules

### Service Containers
- **Pattern:** `social-commerce-{service-name}-service`
- **Examples:**
  - ✅ `social-commerce-user-service`
  - ✅ `social-commerce-store-service`
  - ✅ `social-commerce-product-service`

### Infrastructure Containers
- **Pattern:** `social-commerce-{infrastructure-component}`
- **Examples:**
  - ✅ `social-commerce-postgres`
  - ✅ `social-commerce-rabbitmq`
  - ✅ `social-commerce-api-gateway`

### Frontend/UI Containers
- **Pattern:** `social-commerce-{frontend-component}`
- **Examples:**
  - ✅ `social-commerce-frontend`
  - ✅ `social-commerce-admin-dashboard`

### Environment-Specific Containers
```bash
# Development (default)
social-commerce-user-service

# Testing Environment
social-commerce-user-service-test

# Production Environment
social-commerce-user-service-prod
```

## 🐰 Queue & Messaging Naming Rules

### RabbitMQ Queue Names
- **Pattern:** `{service_name}_queue`
- **Format:** lowercase with underscores
- **Examples:**
  - ✅ `user_queue`
  - ✅ `store_queue`
  - ✅ `product_queue`
  - ✅ `order_queue`

### Event-Specific Queues
- **Pattern:** `{service_name}_{event_type}_queue`
- **Examples:**
  - ✅ `user_registration_queue`
  - ✅ `order_created_queue`
  - ✅ `payment_processed_queue`

### Dead Letter Queues
- **Pattern:** `{service_name}_dlq`
- **Examples:**
  - ✅ `user_dlq`
  - ✅ `order_dlq`

### Exchange Names
- **Pattern:** `{service_name}_exchange`
- **Examples:**
  - ✅ `user_exchange`
  - ✅ `store_exchange`

## 🔧 Environment Variable Rules

### Service URL Variables
- **Pattern:** `{SERVICE_NAME}_SERVICE_URL`
- **Format:** UPPERCASE with underscores
- **Examples:**
```bash
USER_SERVICE_URL=http://user-service:3001/api
STORE_SERVICE_URL=http://store-service:3002/api
PRODUCT_SERVICE_URL=http://product-service:3003/api
```

### Database Configuration Variables
```bash
# General Database Config
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-Specific Databases
DB_DATABASE_USER=user_service
DB_DATABASE_STORE=store_service
DB_DATABASE_PRODUCT=product_service
```

### RabbitMQ Configuration Variables
```bash
# General RabbitMQ Config
RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin

# Service-Specific Queues
RABBITMQ_USER_QUEUE=user_queue
RABBITMQ_STORE_QUEUE=store_queue
RABBITMQ_PRODUCT_QUEUE=product_queue
```

### Service Port Variables
```bash
# API Gateway
API_GATEWAY_PORT=3000

# Services
USER_SERVICE_PORT=3001
STORE_SERVICE_PORT=3002
PRODUCT_SERVICE_PORT=3003
ORDER_SERVICE_PORT=3004

# Frontend
FRONTEND_PORT=3003
```

### Feature Flag Variables
```bash
# Global Features
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_RATE_LIMITING=false

# Service-Specific Features
USER_ENABLE_EMAIL_VERIFICATION=true
STORE_ENABLE_INVENTORY_TRACKING=true
```

## 📁 Directory Structure Rules

### Service Directory Names
- **Pattern:** `{service-name}-service`
- **Examples:**
  - ✅ `services/user-service/`
  - ✅ `services/store-service/`
  - ✅ `services/product-service/`

### Shared Libraries
- **Pattern:** `libs/{library-name}/`
- **Examples:**
  - ✅ `libs/common/`
  - ✅ `libs/messaging/`
  - ✅ `libs/testing/`

## 🚫 Naming Restrictions

### General Rules
- **No uppercase letters** in database names, queue names, Docker images
- **No spaces or special characters** (except underscore for databases/queues, hyphen for Docker)
- **No consecutive separators** (-- or __)
- **Must start and end with alphanumeric character**
- **No reserved keywords** (SQL, Docker, system reserved names)

### Length Limits
- **Database names:** Maximum 63 characters (PostgreSQL limit)
- **Container names:** Maximum 63 characters (Docker limit)
- **Docker image names:** Maximum 128 characters
- **Environment variables:** Maximum 255 characters

## ✅ Valid Examples Summary

```bash
# Database
user_service ✅
store_service ✅

# Docker Images
user-service:v1.0.0 ✅
social-commerce-frontend:latest ✅

# Containers
social-commerce-user-service ✅
social-commerce-postgres ✅

# Queues
user_queue ✅
order_created_queue ✅

# Environment Variables
USER_SERVICE_URL ✅
DB_DATABASE_USER ✅
RABBITMQ_USER_QUEUE ✅
```

## ❌ Invalid Examples Summary

```bash
# Database
User-Service ❌ (uppercase, hyphen)
user service ❌ (space)

# Docker Images
User_Service ❌ (uppercase, underscore)
user--service ❌ (consecutive hyphens)

# Containers
social_commerce_user_service ❌ (underscores)
Social-Commerce-User-Service ❌ (uppercase)

# Queues
User-Queue ❌ (uppercase, hyphen)
user queue ❌ (space)

# Environment Variables
user-service-url ❌ (lowercase, hyphens)
USER SERVICE URL ❌ (spaces)
```

## 🛠️ Service Creation Checklist

When creating a new service, ensure the following naming conventions are applied:

### ✅ Pre-Creation Checklist
- [ ] Service name follows `{service-name}-service` pattern
- [ ] Database name follows `{service_name}_service` pattern
- [ ] Docker image name follows `{service-name}-service` pattern
- [ ] Container name follows `social-commerce-{service-name}-service` pattern
- [ ] Queue name follows `{service_name}_queue` pattern
- [ ] Environment variables follow `{SERVICE_NAME}_*` pattern

### ✅ Implementation Checklist
- [ ] Directory created as `services/{service-name}-service/`
- [ ] Dockerfile created with proper image naming
- [ ] Docker Compose service added with proper container naming
- [ ] Database added to `POSTGRES_MULTIPLE_DATABASES`
- [ ] Environment variables added to `.env.example`
- [ ] Queue configuration added to RabbitMQ setup
- [ ] Service URL variable defined

### ✅ Documentation Checklist
- [ ] Service documented in architecture diagrams
- [ ] API endpoints documented
- [ ] Database schema documented
- [ ] Integration points documented

## 🔄 Migration Guide for Existing Services

### Current Compliance Status
- ✅ **user-service** - Fully compliant
- ✅ **store-service** - Fully compliant
- ⚠️ **frontend** - Image naming could be standardized

### Future Services
All new services must follow these conventions from creation.

---

**Status:** ✅ **MANDATORY** - All new services must follow these conventions
**Date:** 2025-05-26
**Maintainer:** Development Team
**Review:** Required before service creation
