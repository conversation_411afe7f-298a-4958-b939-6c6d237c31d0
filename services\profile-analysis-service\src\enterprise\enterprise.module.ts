import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ProfileAnalysisCommandController } from './controllers/profile-analysis-command.controller';
import { ProfileAnalysisQueryController } from './controllers/profile-analysis-query.controller';
import { TwitterAuthController } from './controllers/twitter-auth.controller';
import { PrismaService } from './shared/prisma.service';
import { TwitterAnalysisService } from './services/twitter-analysis.service';
import { TwitterApiClientService } from './services/twitter-api-client.service';
import { JwtService } from './services/jwt.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    })
  ],
  controllers: [
    ProfileAnalysisCommandController,
    ProfileAnalysisQueryController,
    TwitterAuthController
  ],
  providers: [
    PrismaService,
    TwitterAnalysisService,
    TwitterApiClientService,
    JwtService
  ],
  exports: [
    PrismaService,
    TwitterAnalysisService,
    TwitterApiClientService,
    JwtService
  ]
})
export class EnterpriseModule {}
