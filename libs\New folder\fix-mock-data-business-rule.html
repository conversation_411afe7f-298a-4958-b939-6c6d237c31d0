<!DOCTYPE html>
<html>
<head>
    <title>Fix Mock Data Business Rule</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Fix Mock Data Business Rule</h1>

    <div class="section">
        <h3>Fix "⚠️ Data inconsistent" Warning</h3>
        <button onclick="fixMockDataBusinessRule()">Fix Mock Data Business Rule</button>
        <div id="fixResult"></div>
    </div>

    <script>
        function fixMockDataBusinessRule() {
            const result = document.getElementById('fixResult');

            const authUser = localStorage.getItem('auth_user');
            if (!authUser) {
                result.innerHTML = '<div class="error">❌ No auth user found</div>';
                return;
            }

            const user = JSON.parse(authUser);
            const currentUserId = user.id;

            // Get existing NFT data
            const existingNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            const otherUserNFTs = existingNFTs.filter(nft => String(nft.userId) !== String(currentUserId));

            // Create compliant mock data: exactly 1 NFT per campaign
            const compliantNFTs = [
                {
                    id: `nft_${currentUserId}_1`,
                    userId: currentUserId,
                    campaignId: 'campaign_1',
                    name: 'Digital Warrior #001',
                    description: 'A fierce digital warrior NFT',
                    imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=warrior1',
                    rarity: 'Common',
                    currentScore: 750,
                    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: `nft_${currentUserId}_2`,
                    userId: currentUserId,
                    campaignId: 'campaign_2',
                    name: 'Cyber Guardian #002',
                    description: 'A protective cyber guardian NFT',
                    imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guardian2',
                    rarity: 'Rare',
                    currentScore: 920,
                    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: `nft_${currentUserId}_3`,
                    userId: currentUserId,
                    campaignId: 'campaign_3',
                    name: 'Tech Mystic #003',
                    description: 'A mystical tech-savvy NFT',
                    imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=mystic3',
                    rarity: 'Legendary',
                    currentScore: 1150,
                    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: `nft_${currentUserId}_4`,
                    userId: currentUserId,
                    campaignId: 'campaign_4',
                    name: 'Data Sage #004',
                    description: 'A wise data manipulation NFT',
                    imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sage4',
                    rarity: 'Common',
                    currentScore: 680,
                    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
                }
            ];

            // Combine with other users' NFTs
            const allNFTs = [...otherUserNFTs, ...compliantNFTs];

            // Save compliant data
            localStorage.setItem('user_nfts', JSON.stringify(allNFTs));

            // Verify compliance
            const uniqueCampaigns = [...new Set(compliantNFTs.map(nft => nft.campaignId))];
            const isCompliant = compliantNFTs.length === uniqueCampaigns.length;

            result.innerHTML = `
                <div class="success">✅ Mock Data Fixed Successfully</div>
                <pre>Business Rule: One NFT Per Campaign
User NFTs: ${compliantNFTs.length}
Unique Campaigns: ${uniqueCampaigns.length}
Compliance: ${isCompliant ? 'PASS ✅' : 'FAIL ❌'}
Data Source: localStorage (Mock - Business Rule Compliant)</pre>
                <div class="info">
                    <strong>📝 What was fixed:</strong>
                    <ul>
                        <li>Removed duplicate NFTs per campaign</li>
                        <li>Ensured exactly 1 NFT per campaign</li>
                        <li>Dashboard will now show consistent data</li>
                        <li>"⚠️ Data inconsistent" warning will disappear</li>
                    </ul>
                </div>
                <button onclick="location.reload()">Refresh Page</button>
            `;
        }
    </script>
</body>
</html>
