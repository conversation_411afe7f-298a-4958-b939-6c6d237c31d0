<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Data Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Dashboard Data Debug Tool</h1>

    <div class="section">
        <h3>Step 1: Check Current User Data</h3>
        <button onclick="checkCurrentUser()">Check Current User</button>
        <div id="currentUserResult"></div>
    </div>

    <div class="section">
        <h3>Step 2: Check NFT Data</h3>
        <button onclick="checkNFTData()">Check NFT Data</button>
        <div id="nftDataResult"></div>
    </div>

    <div class="section">
        <h3>Step 3: Validate Business Rule</h3>
        <button onclick="validateBusinessRule()">Check One NFT Per Campaign Rule</button>
        <div id="businessRuleResult"></div>
    </div>

    <div class="section">
        <h3>Step 4: Fix Data Issues</h3>
        <button onclick="fixDataIssues()">Fix Business Rule Violations</button>
        <div id="dataFixResult"></div>
    </div>

    <script>
        function checkCurrentUser() {
            const result = document.getElementById('currentUserResult');
            const authData = localStorage.getItem('auth_user');

            if (!authData) {
                result.innerHTML = '<div class="error">❌ No auth user found in localStorage</div>';
                return;
            }

            const user = JSON.parse(authData);
            result.innerHTML = `
                <div class="success">✅ Current User Found</div>
                <pre>User ID: ${user.id || 'N/A'}
Username: ${user.username || 'N/A'}
Email: ${user.email || 'N/A'}</pre>
            `;
        }

        function checkNFTData() {
            const result = document.getElementById('nftDataResult');
            const nftData = localStorage.getItem('user_nfts');

            if (!nftData) {
                result.innerHTML = '<div class="error">❌ No NFT data found in localStorage</div>';
                return;
            }

            const nfts = JSON.parse(nftData);
            result.innerHTML = `
                <div class="success">✅ NFT Data Found</div>
                <pre>Total NFTs in storage: ${nfts.length}
First NFT sample: ${JSON.stringify(nfts[0] || {}, null, 2)}</pre>
            `;
        }

        function validateBusinessRule() {
            const result = document.getElementById('businessRuleResult');
            const authData = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');

            if (!authData || !nftData) {
                result.innerHTML = '<div class="error">❌ Missing user or NFT data</div>';
                return;
            }

            const user = JSON.parse(authData);
            const nfts = JSON.parse(nftData);
            const userNFTs = nfts.filter(nft => String(nft.userId) === String(user.id));

            // Group by campaign
            const campaignCounts = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                campaignCounts[campaignId] = (campaignCounts[campaignId] || 0) + 1;
            });

            const violations = Object.keys(campaignCounts).filter(id => campaignCounts[id] > 1);
            const totalCampaigns = Object.keys(campaignCounts).length;

            result.innerHTML = `
                <div class="${violations.length === 0 ? 'success' : 'error'}">
                    ${violations.length === 0 ? '✅' : '❌'} Business Rule Check
                </div>
                <pre>User NFTs: ${userNFTs.length}
Unique Campaigns: ${totalCampaigns}
Rule Compliance: ${violations.length === 0 ? 'PASS' : 'FAIL'}
${violations.length > 0 ? 'Violations: ' + violations.join(', ') : ''}</pre>
            `;
        }

        function fixDataIssues() {
            const result = document.getElementById('dataFixResult');
            const authData = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');

            if (!authData || !nftData) {
                result.innerHTML = '<div class="error">❌ Missing user or NFT data</div>';
                return;
            }

            const user = JSON.parse(authData);
            const allNFTs = JSON.parse(nftData);
            const userNFTs = allNFTs.filter(nft => String(nft.userId) === String(user.id));
            const otherNFTs = allNFTs.filter(nft => String(nft.userId) !== String(user.id));

            // Keep only most recent NFT per campaign
            const fixedNFTs = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                if (!fixedNFTs[campaignId] || new Date(nft.createdAt) > new Date(fixedNFTs[campaignId].createdAt)) {
                    fixedNFTs[campaignId] = nft;
                }
            });

            const fixedUserNFTs = Object.values(fixedNFTs);
            const newAllNFTs = [...otherNFTs, ...fixedUserNFTs];

            localStorage.setItem('user_nfts', JSON.stringify(newAllNFTs));

            result.innerHTML = `
                <div class="success">✅ Data Fixed Successfully</div>
                <pre>Before: ${userNFTs.length} NFTs
After: ${fixedUserNFTs.length} NFTs
Removed: ${userNFTs.length - fixedUserNFTs.length} duplicates
Rule: One NFT per campaign enforced</pre>
                <button onclick="location.reload()">Refresh Page</button>
            `;
        }
    </script>
</body>
</html>
