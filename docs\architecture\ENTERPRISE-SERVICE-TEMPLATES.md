# Enterprise Service Implementation Templates

## 🎯 **COMPLETE ENTERPRISE SERVICE PATTERNS**

**Based on:** Sample app enterprise patterns + platform best practices  
**Features:** CQRS, error handling, health checks, monitoring, Swagger  
**Approach:** Template-First implementation (max 30 lines per template)  

## 📋 **ENTERPRISE AUTHENTICATION SERVICE**

### **CQRS Command Service Template:**
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EventBus } from '@nestjs/cqrs';
import { AuditService } from '../audit/audit.service';
import { PerformanceMonitoringService } from '../monitoring/performance-monitoring.service';

@Injectable()
export class UserCommandService {
  private readonly logger = new Logger(UserCommandService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventBus: EventBus,
    private readonly auditService: AuditService,
    private readonly performanceMonitoring: PerformanceMonitoringService,
  ) {}

  async createUser(createUserDto: CreateUserDto, context: RequestContext): Promise<UserCommandResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;
    
    try {
      this.logger.log(`Creating user: ${createUserDto.email}`, { correlationId });
      
      // Business rule validation
      await this.validateBusinessRules(createUserDto);
      
      // Create user with transaction
      const user = await this.prisma.$transaction(async (tx) => {
        const newUser = await tx.userCommand.create({
          data: {
            ...createUserDto,
            createdBy: context.userId,
            version: 1,
          },
        });
        
        // Create audit log
        await this.auditService.logAction({
          entityType: 'User',
          entityId: newUser.id,
          action: 'CREATE',
          newValues: newUser,
          userId: context.userId,
          correlationId,
        });
        
        return newUser;
      });
      
      // Publish domain event
      await this.eventBus.publish(new UserCreatedEvent(user.id, user, correlationId));
      
      // Record performance metrics
      await this.performanceMonitoring.recordMetric({
        serviceName: 'user-service',
        metricType: 'QUERY_DURATION',
        metricName: 'create_user',
        value: Date.now() - startTime,
        unit: 'ms',
        correlationId,
      });
      
      return { success: true, data: user };
      
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, { 
        correlationId, 
        error: error.stack 
      });
      
      // Record error metrics
      await this.performanceMonitoring.recordMetric({
        serviceName: 'user-service',
        metricType: 'ERROR_RATE',
        metricName: 'create_user_error',
        value: 1,
        unit: 'count',
        correlationId,
      });
      
      throw new BusinessException('Failed to create user', error);
    }
  }
  
  private async validateBusinessRules(createUserDto: CreateUserDto): Promise<void> {
    // Implement business rule validation
    // Example: Check email domain whitelist, username restrictions, etc.
  }
}
```

### **CQRS Query Service Template:**
```typescript
@Injectable()
export class UserQueryService {
  private readonly logger = new Logger(UserQueryService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly performanceMonitoring: PerformanceMonitoringService,
  ) {}

  async getUserById(id: string, context: RequestContext): Promise<UserQueryResult> {
    const startTime = Date.now();
    const cacheKey = `user:${id}`;
    
    try {
      // Try cache first
      const cachedUser = await this.cacheService.get(cacheKey);
      if (cachedUser) {
        await this.performanceMonitoring.recordMetric({
          serviceName: 'user-service',
          metricType: 'CACHE_HIT_RATE',
          metricName: 'get_user_cache_hit',
          value: 1,
          unit: 'count',
        });
        return { success: true, data: cachedUser };
      }
      
      // Query from read model
      const user = await this.prisma.userQuery.findUnique({
        where: { id },
        include: {
          // Include pre-computed aggregations
        },
      });
      
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      
      // Cache the result
      await this.cacheService.set(cacheKey, user, 300); // 5 minutes TTL
      
      // Record performance metrics
      await this.performanceMonitoring.recordMetric({
        serviceName: 'user-service',
        metricType: 'QUERY_DURATION',
        metricName: 'get_user_by_id',
        value: Date.now() - startTime,
        unit: 'ms',
      });
      
      return { success: true, data: user };
      
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error.message}`, {
        userId: id,
        correlationId: context.correlationId,
      });
      
      // Graceful fallback - try command model
      return this.fallbackToCommandModel(id, context);
    }
  }
  
  private async fallbackToCommandModel(id: string, context: RequestContext): Promise<UserQueryResult> {
    try {
      const user = await this.prisma.userCommand.findUnique({ where: { id } });
      if (user) {
        this.logger.warn(`Fallback to command model successful for user ${id}`);
        return { success: true, data: user, fallback: true };
      }
    } catch (fallbackError) {
      this.logger.error(`Fallback also failed: ${fallbackError.message}`);
    }
    
    throw new ServiceUnavailableException('User service temporarily unavailable');
  }
}
```

---
**Status:** ✅ Enterprise Service Templates Created in docs/architecture  
**Location:** docs/architecture/ENTERPRISE-SERVICE-TEMPLATES.md  
**Next:** Add Health Check and Controller templates
