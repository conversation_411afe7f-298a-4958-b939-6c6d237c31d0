const http = require('http');

const services = [
  { name: 'Frontend', port: 3000, path: '/' },
  { name: 'Profile Analysis Service', port: 3002, path: '/api/health' },
  { name: 'NFT Generation Service', port: 3003, path: '/api/health' },
  { name: 'API Gateway', port: 3010, path: '/api/health' },
  { name: 'User Service', port: 3011, path: '/api/health' },
  { name: 'Mock Twitter Service', port: 3020, path: '/health' }
];

async function checkService(service) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: service.port,
      path: service.path,
      method: 'GET',
      timeout: 3000
    }, (res) => {
      resolve({
        ...service,
        status: res.statusCode === 200 ? '✅ Running' : `❌ Error (${res.statusCode})`,
        available: res.statusCode === 200
      });
    });

    req.on('error', () => {
      resolve({
        ...service,
        status: '❌ Down',
        available: false
      });
    });

    req.on('timeout', () => {
      resolve({
        ...service,
        status: '⏱️ Timeout',
        available: false
      });
    });

    req.end();
  });
}

async function checkAllServices() {
  console.log('🔍 Checking service status...\n');
  
  const results = await Promise.all(services.map(checkService));
  
  results.forEach(result => {
    console.log(`${result.status} ${result.name} (${result.port})`);
  });
  
  const runningCount = results.filter(r => r.available).length;
  console.log(`\n📊 Status: ${runningCount}/${services.length} services running`);
  
  if (runningCount === services.length) {
    console.log('🎉 All services are ready!');
  } else {
    console.log('⏳ Some services are still starting...');
  }
}

checkAllServices();
