'use client'

import React, { useEffect, useState } from 'react';
import { Box, Spinner, Text, VStack, Container } from '@chakra-ui/react';
import { useAuth } from '@/contexts/AuthContext';

export default function TwitterCallbackPage() {
  const authContext = useAuth();
  const { handleTwitterCallback } = authContext;
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing Twitter authentication...');

  console.log('🔍 Twitter Callback: AuthContext:', authContext);
  console.log('🔍 Twitter Callback: handleTwitterCallback function:', handleTwitterCallback);

  useEffect(() => {
    // Prevent infinite loops by checking if already processed
    if (status !== 'loading') {
      console.log('🔄 Twitter Callback: Already processed, status:', status);
      return;
    }

    const processCallback = async () => {
      try {
        console.log('🐦 Twitter Callback: Starting to process callback...');
        console.log('🐦 Twitter Callback: Current URL:', window.location.href);

        // Parse URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        console.log('🐦 Twitter Callback: URL params:', Object.fromEntries(urlParams.entries()));

        const error = urlParams.get('error');
        const message = urlParams.get('message');

        if (error || message) {
          console.error('❌ Twitter Callback: Error in URL params:', { error, message });
          setStatus('error');
          setMessage(decodeURIComponent(error || message || 'Authentication failed'));
          return;
        }

        // Handle mock Twitter OAuth data format
        const twitterId = urlParams.get('twitterId');
        const screenName = urlParams.get('screenName');
        const accessToken = urlParams.get('accessToken');
        const profileImageUrl = urlParams.get('profileImageUrl');

        console.log('🐦 Twitter Callback: Extracted data:', {
          twitterId,
          screenName,
          accessToken: accessToken ? accessToken.substring(0, 20) + '...' : null,
          profileImageUrl
        });

        if (!twitterId || !screenName || !accessToken) {
          console.error('❌ Twitter Callback: Missing required data:', {
            twitterId: !!twitterId,
            screenName: !!screenName,
            accessToken: !!accessToken
          });
          setStatus('error');
          setMessage('Missing Twitter authentication data. Please try again.');
          return;
        }

        // Create user data object
        const userData = {
          id: twitterId,
          username: screenName,
          email: `${screenName}@twitter.mock`,
          twitterId: twitterId,
          twitterUsername: screenName,
          profileImageUrl: profileImageUrl,
          role: 'user',
          isActive: true,
          createdAt: new Date().toISOString(),
        };

        console.log('🐦 Twitter OAuth Success:', userData);
        console.log('🔄 Twitter Callback: About to call handleTwitterCallback...');
        console.log('🔄 Twitter Callback: handleTwitterCallback function:', typeof handleTwitterCallback);

        // Handle successful authentication
        console.log('🚨 CALLBACK: CALLING handleTwitterCallback NOW!');
        console.log('🔍 CALLBACK: Function type check:', typeof handleTwitterCallback);
        console.log('🔍 CALLBACK: Function details:', handleTwitterCallback.toString().substring(0, 100));

        // Try calling the function
        try {
          const result = await handleTwitterCallback(accessToken, userData);
          console.log('🔍 CALLBACK: Function result:', result);
        } catch (error) {
          console.error('❌ CALLBACK: Function call error:', error);
        }

        console.log('🚨 CALLBACK: handleTwitterCallback RETURNED!');

        console.log('✅ Twitter Callback: handleTwitterCallback completed successfully');
        setStatus('success');
        setMessage('Twitter authentication successful! DEBUGGING: No redirect to see login completion');

        // DEBUGGING: Disable redirect to see if login completes
        console.log('🔄 DEBUGGING: Redirect disabled, staying on callback page');
      } catch (error) {
        console.error('❌ Twitter OAuth processing error:', error);
        setStatus('error');
        setMessage('Failed to process Twitter authentication. Please try again.');
      }
    };

    processCallback();
  }, [status]); // Only depend on status to prevent infinite loops

  return (
    <Container maxW="md" py={12}>
      <VStack gap={8} textAlign="center">
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={4}>
            🐦 Twitter Authentication
          </Text>
        </Box>

        {status === 'loading' && (
          <VStack gap={4}>
            <Spinner size="xl" color="blue.500" />
            <Text color="gray.600">{message}</Text>
          </VStack>
        )}

        {status === 'success' && (
          <Box
            p={4}
            borderRadius="lg"
            maxW="md"
            bg="green.50"
            border="1px solid"
            borderColor="green.200"
          >
            <Text color="green.800">✅ {message}</Text>
          </Box>
        )}

        {status === 'error' && (
          <Box
            p={4}
            borderRadius="lg"
            maxW="md"
            bg="red.50"
            border="1px solid"
            borderColor="red.200"
          >
            <Text color="red.800">❌ {message}</Text>
            <Text color="red.600" fontSize="sm" mt={2}>
              You can close this window and try again.
            </Text>
          </Box>
        )}
      </VStack>
    </Container>
  );
}
