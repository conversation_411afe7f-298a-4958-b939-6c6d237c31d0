// Enterprise Analytics Module - Template
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AnalyticsCommandController } from './controllers/analytics-command.controller';
import { AnalyticsQueryController } from './controllers/analytics-query.controller';
import { PrismaService } from './shared/prisma.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    })
  ],
  controllers: [
    AnalyticsCommandController,
    AnalyticsQueryController
  ],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
