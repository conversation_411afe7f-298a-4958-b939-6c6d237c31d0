// Twitter Service Interfaces for Mock Implementation

export interface TwitterUser {
  id: string;
  username: string;
  displayName: string;
  profileImageUrl: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  verified: boolean;
  description?: string;
  location?: string;
  website?: string;
  createdAt: string;
}

export interface TwitterCredentials {
  accessToken?: string;
  accessTokenSecret?: string;
  consumerKey?: string;
  consumerSecret?: string;
}

export interface AuthResult {
  success: boolean;
  user?: TwitterUser;
  accessToken?: string;
  error?: string;
}

export interface TwitterAnalytics {
  userId: string;
  engagementRate: number;
  averageLikes: number;
  averageRetweets: number;
  averageReplies: number;
  influenceScore: number;
}
