<!DOCTYPE html>
<html>
<head>
    <title>Real vs Mock Data Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Real vs Mock Data Analysis</h1>

    <div class="section">
        <h3>Step 1: Confirm Current User Source</h3>
        <button onclick="analyzeCurrentUser()">Analyze Current User</button>
        <div id="userResult"></div>
    </div>

    <div class="section">
        <h3>Step 2: Analyze Data Sources</h3>
        <button onclick="analyzeDataSources()">Analyze NFT Data Sources</button>
        <div id="dataSourceResult"></div>
    </div>

    <div class="section">
        <h3>Step 3: Systematic Data Architecture Fix</h3>
        <button onclick="createCleanDataArchitecture()">Create Clean Data Architecture</button>
        <div id="cleanArchitectureResult"></div>
    </div>

    <script>
        function analyzeCurrentUser() {
            const result = document.getElementById('userResult');

            const authUser = localStorage.getItem('auth_user');
            if (!authUser) {
                result.innerHTML = '<div class="error">❌ No auth user found</div>';
                return;
            }

            const user = JSON.parse(authUser);
            const isRealUser = user.id === 'persisttest20250528191812';

            let output = `<h4>👤 Current User Analysis:</h4>`;
            output += `<table>
                <tr><th>Property</th><th>Value</th><th>Source</th></tr>
                <tr><td>User ID</td><td>${user.id}</td><td>${isRealUser ? '✅ Database' : '⚠️ Unknown'}</td></tr>
                <tr><td>Username</td><td>${user.username}</td><td>${isRealUser ? '✅ Database' : '⚠️ Unknown'}</td></tr>
                <tr><td>Email</td><td>${user.email}</td><td>${isRealUser ? '✅ Database' : '⚠️ Unknown'}</td></tr>
                <tr><td>User Type</td><td>${isRealUser ? 'Real Database User' : 'Mock/Test User'}</td><td>${isRealUser ? '✅ Confirmed' : '⚠️ Unconfirmed'}</td></tr>
            </table>`;

            if (isRealUser) {
                output += `<div class="success">
                    ✅ <strong>Confirmed:</strong> This is the real database user "persisttest20250528191812" created during implementation.
                </div>`;
            } else {
                output += `<div class="warning">
                    ⚠️ <strong>Warning:</strong> This is not the expected database user. Current user: ${user.id}
                </div>`;
            }

            result.innerHTML = output;
        }

        function analyzeDataSources() {
            const result = document.getElementById('dataSourceResult');

            const authUser = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');

            if (!authUser) {
                result.innerHTML = '<div class="error">❌ No auth user found</div>';
                return;
            }

            const user = JSON.parse(authUser);
            const isRealUser = user.id === 'persisttest20250528191812';

            let output = `<h4>📊 Data Source Analysis:</h4>`;

            // Current Dashboard Data Analysis
            if (nftData) {
                const allNFTs = JSON.parse(nftData);
                const userNFTs = allNFTs.filter(nft => String(nft.userId) === String(user.id));
                const uniqueCampaigns = [...new Set(userNFTs.map(nft => nft.campaignId))];
                const rareNFTs = userNFTs.filter(nft => nft.rarity === 'Rare' || nft.rarity === 'Legendary');

                output += `<table>
                    <tr><th>Metric</th><th>Current Value</th><th>Expected (Real User)</th><th>Status</th></tr>
                    <tr><td>Total NFTs</td><td>${userNFTs.length}</td><td>Should equal campaigns</td><td>${userNFTs.length === uniqueCampaigns.length ? '✅' : '❌'}</td></tr>
                    <tr><td>Campaigns Joined</td><td>${uniqueCampaigns.length}</td><td>Should equal NFTs</td><td>${userNFTs.length === uniqueCampaigns.length ? '✅' : '❌'}</td></tr>
                    <tr><td>Rare NFTs</td><td>${rareNFTs.length}</td><td>User reports: 4</td><td>${rareNFTs.length === 4 ? '✅' : '❌'}</td></tr>
                    <tr><td>Data Source</td><td>localStorage</td><td>${isRealUser ? 'Should be Database' : 'Mock OK'}</td><td>${isRealUser ? '❌ Wrong Source' : '⚠️ Mock'}</td></tr>
                </table>`;

                // Problem Analysis
                output += `<div class="error">
                    <h5>🚨 Problems Identified:</h5>
                    <ul>`;

                if (userNFTs.length !== uniqueCampaigns.length) {
                    output += `<li><strong>Business Rule Violation:</strong> ${userNFTs.length} NFTs but ${uniqueCampaigns.length} campaigns (should be equal)</li>`;
                }

                if (rareNFTs.length !== 4) {
                    output += `<li><strong>Rare NFT Mismatch:</strong> Dashboard shows ${rareNFTs.length} but user reports 4</li>`;
                }

                if (isRealUser) {
                    output += `<li><strong>Data Source Issue:</strong> Real database user using localStorage mock data instead of database</li>`;
                }

                output += `</ul></div>`;

            } else {
                output += `<div class="error">❌ No NFT data found in localStorage</div>`;
            }

            result.innerHTML = output;
        }

        function createCleanDataArchitecture() {
            const result = document.getElementById('cleanArchitectureResult');

            const authUser = localStorage.getItem('auth_user');
            if (!authUser) {
                result.innerHTML = '<div class="error">❌ No auth user found</div>';
                return;
            }

            const user = JSON.parse(authUser);
            const isRealUser = user.id === 'persisttest20250528191812';

            if (!isRealUser) {
                result.innerHTML = '<div class="error">❌ This fix is only for the real database user "persisttest20250528191812"</div>';
                return;
            }

            // Clear all localStorage mock data
            localStorage.removeItem('user_nfts');
            localStorage.removeItem('campaigns');
            localStorage.removeItem('projects');

            let output = `<h4>🔧 Clean Data Architecture Created:</h4>`;
            output += `<div class="success">
                ✅ <strong>localStorage Mock Data Cleared</strong><br>
                - Removed user_nfts (mock data)<br>
                - Removed campaigns (mock data)<br>
                - Removed projects (mock data)<br>
                - Kept auth_user (real authentication)
            </div>`;

            output += `<div class="info">
                <h5>📋 Next Steps for Clean Architecture:</h5>
                <ol>
                    <li><strong>Dashboard Components:</strong> Update to use database APIs instead of localStorage</li>
                    <li><strong>NFT Data:</strong> Fetch from NFT Service API (port 3002)</li>
                    <li><strong>Campaign Data:</strong> Fetch from Campaign Service API</li>
                    <li><strong>Real Business Rules:</strong> Enforce "1 NFT per campaign" in backend</li>
                    <li><strong>No Mock Data:</strong> Only use real database data</li>
                </ol>
            </div>`;

            output += `<div class="warning">
                <h5>⚠️ Current State After Cleanup:</h5>
                <ul>
                    <li>Dashboard will show empty until connected to database APIs</li>
                    <li>This is correct - no mixing of mock and real data</li>
                    <li>All data should come from database services</li>
                </ul>
            </div>`;

            result.innerHTML = output;
        }
    </script>
</body>
</html>
