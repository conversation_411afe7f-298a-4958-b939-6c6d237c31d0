{"userId": "test-user-001", "campaignId": "test-campaign-001", "twitterHandle": "@techinfluencer", "currentScore": 75, "analysisData": {"twitterHandle": "@techinfluencer", "followers": 25000, "following": 1500, "tweets": 3200, "engagement": 0.045, "accountAge": 1095, "verified": true, "hasBio": true, "hasAvatar": true, "hasBanner": true, "profileMetrics": {"avgLikes": 150, "avgRetweets": 25, "avgReplies": 12, "topTweetLikes": 2500, "recentActivity": "high"}, "contentAnalysis": {"primaryTopics": ["technology", "startups", "AI"], "postFrequency": "daily", "engagementTrend": "increasing"}}, "campaignConfiguration": {"name": "Tech Influencer Campaign", "blockchain": "polygon", "scoreThresholds": {"legendary": 90, "rare": 70, "common": 40}, "bonusFactors": {"highEngagement": true, "verified": true, "accountAge": 1095, "followerRatio": 16.67}, "templateSettings": {"allowDynamicAttributes": true, "enableVisualEffects": true, "colorSchemeOverride": null}}}