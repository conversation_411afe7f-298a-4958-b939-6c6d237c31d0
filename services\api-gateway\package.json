{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for Social NFT Platform - Single entry point for all services", "main": "dist/main.js", "scripts": {"start:dev": "nest start --watch", "build": "nest build", "start": "nest start", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.1.1", "@nestjs/swagger": "^7.1.13", "@nestjs/throttler": "^5.0.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "axios": "^1.6.0", "http-proxy-middleware": "^2.0.6", "helmet": "^7.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "typescript": "^5.1.3"}}