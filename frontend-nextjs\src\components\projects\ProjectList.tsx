'use client'

import {
  SimpleGrid,
  Box,
  Text,
  Spinner,
  VStack
} from '@chakra-ui/react'
import ProjectCard from './ProjectCard'

interface Project {
  id: string
  name: string
  description: string
  status: string
  imageUrl?: string
  campaignCount?: number
}

interface ProjectListProps {
  projects: Project[]
  loading?: boolean
  error?: string
}

export default function ProjectList({ projects, loading, error }: ProjectListProps) {
  if (loading) {
    return (
      <Box textAlign="center" py={12}>
        <VStack gap={4}>
          <Spinner size="xl" color="blue.500" />
          <Text color="gray.600">Loading projects...</Text>
        </VStack>
      </Box>
    )
  }

  if (error) {
    return (
      <Box textAlign="center" py={12}>
        <Text color="red.500" fontSize="lg">
          {error}
        </Text>
      </Box>
    )
  }

  if (projects.length === 0) {
    return (
      <Box textAlign="center" py={12}>
        <Text color="gray.500" fontSize="lg">
          No projects available at the moment.
        </Text>
      </Box>
    )
  }

  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          id={project.id}
          name={project.name}
          description={project.description}
          status={project.status}
          imageUrl={project.imageUrl}
          campaignCount={project.campaignCount}
        />
      ))}
    </SimpleGrid>
  )
}
