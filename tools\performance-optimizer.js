#!/usr/bin/env node

/**
 * Social NFT Platform - Performance Optimizer
 * Optimizes VS Code performance by managing large directories
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class PerformanceOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
  }

  /**
   * Check directory sizes
   */
  async checkDirectorySizes() {
    console.log('📊 Checking directory sizes...\n');
    
    const directories = [
      'frontend/node_modules',
      'frontend-nextjs/node_modules',
      'services/analytics-service/node_modules',
      'services/api-gateway/node_modules',
      'services/blockchain-service/node_modules',
      'services/marketplace-service/node_modules',
      'services/nft-generation-service/node_modules',
      'services/notification-service/node_modules',
      'services/profile-analysis-service/node_modules',
      'services/project-service/node_modules',
      'services/user-service/node_modules'
    ];

    for (const dir of directories) {
      const fullPath = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullPath)) {
        try {
          const size = await this.getDirectorySize(fullPath);
          console.log(`${dir.padEnd(40)} | ${size}`);
        } catch (error) {
          console.log(`${dir.padEnd(40)} | ERROR: ${error.message}`);
        }
      } else {
        console.log(`${dir.padEnd(40)} | NOT FOUND`);
      }
    }
  }

  /**
   * Get directory size (Windows compatible)
   */
  async getDirectorySize(dirPath) {
    return new Promise((resolve) => {
      exec(`dir "${dirPath}" /s /-c | find "File(s)"`, (error, stdout) => {
        if (error) {
          resolve('Unknown');
          return;
        }
        
        const match = stdout.match(/(\d+) File\(s\)\s+([\d,]+) bytes/);
        if (match) {
          const bytes = parseInt(match[2].replace(/,/g, ''));
          resolve(this.formatBytes(bytes));
        } else {
          resolve('Unknown');
        }
      });
    });
  }

  /**
   * Format bytes to human readable
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Clean unnecessary files
   */
  async cleanProject() {
    console.log('🧹 Cleaning project...\n');
    
    const cleanCommands = [
      'npm cache clean --force',
      'del /s /q "frontend\\node_modules\\.cache" 2>nul',
      'del /s /q "frontend-nextjs\\.next" 2>nul',
      'del /s /q "services\\*\\dist" 2>nul',
      'del /s /q "services\\*\\build" 2>nul'
    ];

    for (const command of cleanCommands) {
      console.log(`Running: ${command}`);
      await this.runCommand(command);
    }
    
    console.log('✅ Project cleaned!');
  }

  /**
   * Run command with promise
   */
  async runCommand(command) {
    return new Promise((resolve) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.log(`  ⚠️  ${error.message}`);
        } else {
          console.log(`  ✅ Success`);
        }
        resolve();
      });
    });
  }

  /**
   * Check VS Code processes
   */
  async checkVSCodeProcesses() {
    console.log('🔍 Checking VS Code processes...\n');
    
    return new Promise((resolve) => {
      exec('tasklist | findstr Code', (error, stdout) => {
        if (error) {
          console.log('No VS Code processes found or error checking.');
        } else {
          console.log('VS Code processes:');
          console.log(stdout);
        }
        resolve();
      });
    });
  }

  /**
   * Show optimization recommendations
   */
  showRecommendations() {
    console.log(`
🚀 VS Code Performance Optimization Recommendations:

1. ✅ COMPLETED: Updated .vscode/settings.json to exclude node_modules
2. ✅ COMPLETED: Updated .gitignore to ignore large directories

3. 🔄 RESTART VS Code to apply new settings

4. 📝 Additional optimizations:
   - Close unused tabs and windows
   - Disable unnecessary extensions
   - Use "Developer: Reload Window" command
   - Consider using VS Code Insiders for better performance

5. 🗂️  Project structure optimization:
   - Consider using a monorepo tool like Lerna or Nx
   - Use shared dependencies where possible
   - Clean node_modules regularly

6. 💾 System optimization:
   - Increase VS Code memory limit: "code --max-memory=8192"
   - Add more RAM if possible
   - Use SSD for better I/O performance

Current memory usage should improve significantly after restart!
`);
  }
}

// CLI Interface
const optimizer = new PerformanceOptimizer();
const command = process.argv[2];

switch (command) {
  case 'check':
    optimizer.checkDirectorySizes();
    break;
  case 'clean':
    optimizer.cleanProject();
    break;
  case 'processes':
    optimizer.checkVSCodeProcesses();
    break;
  case 'recommendations':
    optimizer.showRecommendations();
    break;
  default:
    console.log(`
🔧 Social NFT Platform - Performance Optimizer

Usage:
  node tools/performance-optimizer.js check           # Check directory sizes
  node tools/performance-optimizer.js clean           # Clean project files
  node tools/performance-optimizer.js processes       # Check VS Code processes
  node tools/performance-optimizer.js recommendations # Show optimization tips
`);
}
