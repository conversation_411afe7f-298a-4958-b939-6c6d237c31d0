{
  "terminal.integrated.cwd": "${workspaceFolder}",
  "terminal.integrated.defaultProfile.windows": "Git Bash",
  "terminal.integrated.profiles.windows": {
    "Git Bash": {
      "path": "C:\\Program Files\\Git\\bin\\bash.exe",
      "args": ["--login"],
      "env": {
        "CHERE_INVOKING": "1"
      }
    }
  },
  "files.defaultLanguage": "typescript",
  "typescript.preferences.importModuleSpecifier": "relative",
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "workbench.startupEditor": "none",
  "terminal.integrated.automationProfile.windows": {
    "path": "C:\\Program Files\\Git\\bin\\bash.exe",
    "args": ["--login"]
  },

  // PERFORMANCE OPTIMIZATION: Exclude node_modules from indexing
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.git/**": true
  },
  "typescript.exclude": [
    "**/node_modules",
    "**/dist",
    "**/build"
  ]
}
