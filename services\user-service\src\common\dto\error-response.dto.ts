import { ApiProperty } from '@nestjs/swagger';

export class ErrorDto {
  @ApiProperty({ description: 'Error code', example: 'USER_NOT_FOUND' })
  code: string;

  @ApiProperty({ description: 'Error message', example: 'User not found' })
  message: string;

  @ApiProperty({ description: 'Timestamp when error occurred', example: '2024-01-01T00:00:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: 'Request path where error occurred', example: '/api/users/123' })
  path: string;

  @ApiProperty({ description: 'Correlation ID for tracking', example: 'abc-123-def' })
  correlationId?: string;
}

export class ErrorResponseDto {
  @ApiProperty({ description: 'Success status', example: false })
  success: boolean;

  @ApiProperty({ description: 'Error details', type: ErrorDto })
  error: ErrorDto;
}

export class ValidationErrorDto extends ErrorDto {
  @ApiProperty({ description: 'Validation error details', example: ['username is required'] })
  details?: string[];
}
