// Step 5.2.2 - Correlation ID Tracking Test
const axios = require('axios');

async function testCorrelationTracking() {
  console.log('🔗 Step 5.2.2: Correlation ID Tracking Test');
  
  try {
    // Test 1: Custom Correlation ID Forwarding
    console.log('\n🏷️ Test 1: Custom Correlation ID Forwarding...');
    const customCorrelationId = `custom-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const response1 = await axios.get('http://localhost:3010/api/users/health/enterprise', {
      headers: { 'X-Correlation-ID': customCorrelationId }
    });
    
    console.log('✅ Custom correlation ID forwarded:');
    console.log('   - Sent:', customCorrelationId);
    console.log('   - Received in response headers:', response1.headers['x-correlation-id']);
    console.log('   - Match:', response1.headers['x-correlation-id'] === customCorrelationId);
    
    // Test 2: Auto-Generated Correlation ID
    console.log('\n🤖 Test 2: Auto-Generated Correlation ID...');
    const response2 = await axios.get('http://localhost:3010/api/users/health/enterprise');
    const autoCorrelationId = response2.headers['x-correlation-id'];
    
    console.log('✅ Auto-generated correlation ID:');
    console.log('   - Generated:', !!autoCorrelationId);
    console.log('   - Format valid:', autoCorrelationId.startsWith('gw-'));
    console.log('   - Unique:', autoCorrelationId.length > 10);
    
    // Test 3: Request ID Generation
    console.log('\n🆔 Test 3: Request ID Generation...');
    const response3 = await axios.get('http://localhost:3010/api/users/health/enterprise', {
      headers: { 'X-Correlation-ID': 'request-id-test-5.2.2' }
    });
    const requestId = response3.headers['x-request-id'];
    
    console.log('✅ Request ID generation:');
    console.log('   - Generated:', !!requestId);
    console.log('   - Unique format:', requestId.length > 20);
    console.log('   - Different from correlation ID:', requestId !== response3.headers['x-correlation-id']);
    
    console.log('\n🎉 Step 5.2.2 PASSED: Correlation ID Tracking Working');
    return true;
    
  } catch (error) {
    console.error('❌ Step 5.2.2 FAILED:', error.message);
    return false;
  }
}

testCorrelationTracking();
