# Enterprise Social NFT Platform - Production Docker Compose
# Optimized for production deployment with security and performance

version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-prod
    environment:
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - backend

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway-prod
    ports:
      - "80:3010"
    environment:
      - NODE_ENV=production
      - PORT=3010
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - frontend
      - backend

  # User Service
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: user-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3011
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/user_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Profile Analysis Service
  profile-analysis-service:
    build:
      context: ./services/profile-analysis-service
      dockerfile: Dockerfile
    container_name: profile-analysis-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3002
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/profile_analysis_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # NFT Generation Service
  nft-generation-service:
    build:
      context: ./services/nft-generation-service
      dockerfile: Dockerfile
    container_name: nft-generation-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3003
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/nft_generation_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Blockchain Service
  blockchain-service:
    build:
      context: ./services/blockchain-service
      dockerfile: Dockerfile
    container_name: blockchain-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3004
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/blockchain_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Project Service
  project-service:
    build:
      context: ./services/project-service
      dockerfile: Dockerfile
    container_name: project-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3005
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/project_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Marketplace Service
  marketplace-service:
    build:
      context: ./services/marketplace-service
      dockerfile: Dockerfile
    container_name: marketplace-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3006
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/marketplace_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Analytics Service
  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    container_name: analytics-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3007
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/analytics_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

  # Notification Service
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: notification-service-prod
    environment:
      - NODE_ENV=production
      - PORT=3008
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@postgres:5432/notification_service
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - backend

volumes:
  postgres_data:

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
