# Enterprise Social NFT Platform - Production Environment Template
# Copy this file to .env.production and fill in the values

# Database Configuration
DB_USER=postgres
DB_PASSWORD=your-secure-database-password-here

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here

# API Keys (External Services)
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
BLOCKCHAIN_RPC_URL=your-blockchain-rpc-url
NFT_STORAGE_API_KEY=your-nft-storage-api-key

# Email Configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password

# Security
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_MAX=100

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=info
