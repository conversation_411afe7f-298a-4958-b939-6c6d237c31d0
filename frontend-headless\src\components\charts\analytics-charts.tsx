'use client';

import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Chart color palette that matches our design system
const COLORS = {
  primary: '#3B82F6',
  secondary: '#8B5CF6',
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  gray: '#6B7280',
  light: '#E5E7EB',
};

const RARITY_COLORS = {
  legendary: '#FFD700',
  epic: '#9932CC',
  rare: '#4169E1',
  common: '#808080',
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Platform Growth Chart
interface PlatformGrowthChartProps {
  data: Array<{
    date: string;
    users: number;
    nfts: number;
    volume: number;
  }>;
}

export const PlatformGrowthChart: React.FC<PlatformGrowthChartProps> = ({ data }) => {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Platform Growth</h3>
        <p className="text-sm text-gray-500">User growth, NFT generation, and volume trends</p>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke={COLORS.light} />
          <XAxis 
            dataKey="date" 
            stroke={COLORS.gray}
            fontSize={12}
          />
          <YAxis stroke={COLORS.gray} fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Line
            type="monotone"
            dataKey="users"
            stroke={COLORS.primary}
            strokeWidth={2}
            name="Users"
            dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
          />
          <Line
            type="monotone"
            dataKey="nfts"
            stroke={COLORS.secondary}
            strokeWidth={2}
            name="NFTs Generated"
            dot={{ fill: COLORS.secondary, strokeWidth: 2, r: 4 }}
          />
          <Line
            type="monotone"
            dataKey="volume"
            stroke={COLORS.success}
            strokeWidth={2}
            name="Volume (ETH)"
            dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// NFT Rarity Distribution Pie Chart
interface RarityDistributionChartProps {
  data: Array<{
    rarity: string;
    count: number;
    percentage: number;
  }>;
}

export const RarityDistributionChart: React.FC<RarityDistributionChartProps> = ({ data }) => {
  const chartData = data.map(item => ({
    name: item.rarity,
    value: item.count,
    percentage: item.percentage,
  }));

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">NFT Rarity Distribution</h3>
        <p className="text-sm text-gray-500">Distribution of NFT rarities across the platform</p>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percentage }) => `${name} ${percentage}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={RARITY_COLORS[entry.name.toLowerCase() as keyof typeof RARITY_COLORS] || COLORS.gray} 
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Top Gainers/Losers Chart
interface GainersLosersChartProps {
  gainers: Array<{
    name: string;
    change_percentage: string;
    current_score: number;
  }>;
  losers: Array<{
    name: string;
    change_percentage: string;
    current_score: number;
  }>;
}

export const GainersLosersChart: React.FC<GainersLosersChartProps> = ({ gainers, losers }) => {
  const chartData = [
    ...gainers.map(item => ({
      name: item.name.split(' - ')[0], // Shorten name
      change: parseFloat(item.change_percentage.replace('%', '')),
      score: item.current_score,
      type: 'gainer',
    })),
    ...losers.map(item => ({
      name: item.name.split(' - ')[0], // Shorten name
      change: parseFloat(item.change_percentage.replace('%', '')),
      score: item.current_score,
      type: 'loser',
    })),
  ];

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Performance Changes</h3>
        <p className="text-sm text-gray-500">Top gainers and losers by percentage change</p>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData} layout="horizontal">
          <CartesianGrid strokeDasharray="3 3" stroke={COLORS.light} />
          <XAxis type="number" stroke={COLORS.gray} fontSize={12} />
          <YAxis 
            type="category" 
            dataKey="name" 
            stroke={COLORS.gray} 
            fontSize={12}
            width={100}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="change" 
            fill={(entry: any) => entry.change > 0 ? COLORS.success : COLORS.danger}
            name="Change %"
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.change > 0 ? COLORS.success : COLORS.danger} 
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Market Value Area Chart
interface MarketValueChartProps {
  data: Array<{
    name: string;
    value: number;
    change_24h: string;
  }>;
}

export const MarketValueChart: React.FC<MarketValueChartProps> = ({ data }) => {
  const chartData = data.map((item, index) => ({
    name: item.name,
    value: item.value,
    change: parseFloat(item.change_24h.replace('%', '').replace('+', '')),
    index,
  }));

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Collection Market Value</h3>
        <p className="text-sm text-gray-500">Market value by collection</p>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" stroke={COLORS.light} />
          <XAxis 
            dataKey="name" 
            stroke={COLORS.gray}
            fontSize={12}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis stroke={COLORS.gray} fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="value"
            stroke={COLORS.primary}
            fill={COLORS.primary}
            fillOpacity={0.3}
            name="Market Value (ETH)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Engagement Metrics Chart
interface EngagementMetricsChartProps {
  data: Array<{
    date: string;
    active_users: number;
    nft_generations: number;
    shares: number;
  }>;
}

export const EngagementMetricsChart: React.FC<EngagementMetricsChartProps> = ({ data }) => {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">User Engagement</h3>
        <p className="text-sm text-gray-500">Daily active users, NFT generations, and shares</p>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke={COLORS.light} />
          <XAxis 
            dataKey="date" 
            stroke={COLORS.gray}
            fontSize={12}
          />
          <YAxis stroke={COLORS.gray} fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Area
            type="monotone"
            dataKey="active_users"
            stackId="1"
            stroke={COLORS.primary}
            fill={COLORS.primary}
            fillOpacity={0.6}
            name="Active Users"
          />
          <Area
            type="monotone"
            dataKey="nft_generations"
            stackId="1"
            stroke={COLORS.secondary}
            fill={COLORS.secondary}
            fillOpacity={0.6}
            name="NFT Generations"
          />
          <Area
            type="monotone"
            dataKey="shares"
            stackId="1"
            stroke={COLORS.success}
            fill={COLORS.success}
            fillOpacity={0.6}
            name="Shares"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};
