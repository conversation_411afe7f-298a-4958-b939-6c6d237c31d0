'use client'

import {
  <PERSON>,
  But<PERSON>,
  Container,
  <PERSON>ing,
  Input,
  VStack,
  Text,
  Link
} from '@chakra-ui/react'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import NextLink from 'next/link'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    twitterUsername: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { register } = useAuth()
  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      await register({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        twitterUsername: formData.twitterUsername || undefined
      })
      router.push('/dashboard')
    } catch (err: any) {
      setError(err.response?.data?.message || 'Registration failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Container maxW="md" py={12}>
      <VStack gap={8}>
        <Box textAlign="center">
          <Heading as="h1" size="xl" mb={2}>
            Join Social NFT Platform
          </Heading>
          <Text color="gray.600">
            Create your account and start collecting evolving NFTs
          </Text>
        </Box>

        <Box w="full" bg="white" p={8} borderRadius="lg" boxShadow="lg">
          <form onSubmit={handleSubmit}>
            <VStack gap={4}>
              {error && (
                <Box p={3} bg="red.50" borderRadius="md" color="red.600">
                  {error}
                </Box>
              )}

              <Box>
                <Text mb={2} fontWeight="medium">Username *</Text>
                <Input
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  placeholder="Choose a username"
                />
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Email *</Text>
                <Input
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email"
                />
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Twitter Username (Optional)</Text>
                <Input
                  name="twitterUsername"
                  value={formData.twitterUsername}
                  onChange={handleChange}
                  placeholder="@your_twitter_handle"
                />
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Password *</Text>
                <Input
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Create a password"
                />
              </Box>

              <Box>
                <Text mb={2} fontWeight="medium">Confirm Password *</Text>
                <Input
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirm your password"
                />
              </Box>

              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                w="full"
                loading={isLoading}
              >
                Create Account
              </Button>
            </VStack>
          </form>

          <Box textAlign="center" mt={6}>
            <Text>
              Already have an account?{' '}
              <Link as={NextLink} href="/auth/login" color="blue.500">
                Sign in here
              </Link>
            </Text>
          </Box>
        </Box>
      </VStack>
    </Container>
  )
}
