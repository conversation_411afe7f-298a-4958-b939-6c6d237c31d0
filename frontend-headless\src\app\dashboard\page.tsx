'use client'

import { useState } from 'react'
import Layout from '@/components/layout/layout'
import { ProtectedRoute, useAuth } from '@/contexts/auth-context'
import RealNFTGallery from '@/components/dashboard/real-nft-gallery'
import NFTCollectionManager from '@/components/dashboard/nft-collection-manager'
import <PERSON>Analyzer from '@/components/dashboard/profile-analyzer'
import DashboardStats from '@/components/dashboard/dashboard-stats'
import { EnhancedUserProfile } from '@/components/user/enhanced-user-profile'
import {
  ChartBarIcon,
  CubeIcon,
  UserGroupIcon,
  TrophyIcon,
  PlusIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

const stats = [
  { name: 'Total NFTs', value: '12', icon: CubeIcon, change: '+2', changeType: 'positive' },
  { name: 'Active Campaigns', value: '3', icon: UserGroupIcon, change: '+1', changeType: 'positive' },
  { name: 'Rewards Earned', value: '$1,234', icon: TrophyIcon, change: '+$234', changeType: 'positive' },
  { name: 'Engagement Score', value: '85%', icon: ChartBarIcon, change: '+5%', changeType: 'positive' },
]

const recentNFTs = [
  {
    id: 1,
    name: 'Cosmic Explorer #1234',
    campaign: 'Space Campaign',
    rarity: 'Rare',
    image: 'https://via.placeholder.com/100x100/6366f1/ffffff?text=NFT',
    value: '$45.00'
  },
  {
    id: 2,
    name: 'Digital Warrior #5678',
    campaign: 'Gaming Campaign',
    rarity: 'Epic',
    image: 'https://via.placeholder.com/100x100/8b5cf6/ffffff?text=NFT',
    value: '$78.00'
  },
  {
    id: 3,
    name: 'Cyber Punk #9012',
    campaign: 'Tech Campaign',
    rarity: 'Legendary',
    image: 'https://via.placeholder.com/100x100/ec4899/ffffff?text=NFT',
    value: '$156.00'
  },
]

const activeCampaigns = [
  {
    id: 1,
    name: 'DeFi Revolution',
    description: 'Promote the future of decentralized finance',
    progress: 75,
    reward: '$50 + NFT',
    deadline: '3 days left',
    participants: 1234
  },
  {
    id: 2,
    name: 'Green Blockchain',
    description: 'Spread awareness about eco-friendly crypto',
    progress: 45,
    reward: '$30 + NFT',
    deadline: '1 week left',
    participants: 856
  },
]

export default function DashboardPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [nftRefreshTrigger, setNftRefreshTrigger] = useState(0)
  const [showAdvancedCollection, setShowAdvancedCollection] = useState(false)
  const { user } = useAuth()

  // Function to trigger NFT gallery refresh
  const handleNFTGenerated = () => {
    console.log('🔄 Triggering NFT gallery refresh...')
    setNftRefreshTrigger(prev => prev + 1)
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="min-h-screen bg-gray-50">
          {/* Header */}
          <div className="bg-white shadow">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="py-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                    <p className="mt-1 text-sm text-gray-500">
                      Welcome back, {user?.displayName || user?.username}! Here's what's happening with your NFTs and campaigns.
                    </p>
                  </div>
                  <div className="flex space-x-3">
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                      <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                      Explore
                    </button>
                    <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Join Campaign
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Real Dashboard Stats */}
            <DashboardStats />

            <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 animate-fade-in">
              {/* Enhanced User Profile */}
              <div className="xl:col-span-1">
                <div className="transform transition-all duration-300 hover:scale-[1.02]">
                  <EnhancedUserProfile />
                </div>
              </div>

              {/* Main Content Area */}
              <div className="xl:col-span-2 space-y-8">
                {/* NFT Collection - Toggle between simple and advanced view */}
                <div className="transform transition-all duration-300 hover:scale-[1.02]">
                  <div className="mb-4 flex justify-end">
                    <button
                      onClick={() => setShowAdvancedCollection(!showAdvancedCollection)}
                      className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                    >
                      {showAdvancedCollection ? 'Simple View' : 'Advanced Collection Manager'}
                    </button>
                  </div>

                  {showAdvancedCollection ? (
                    <NFTCollectionManager refreshTrigger={nftRefreshTrigger} />
                  ) : (
                    <RealNFTGallery limit={6} refreshTrigger={nftRefreshTrigger} />
                  )}
                </div>

                {/* Profile Analyzer */}
                <div className="transform transition-all duration-300 hover:scale-[1.02]">
                  <ProfileAnalyzer onNFTGenerated={handleNFTGenerated} />
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-8 bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                    <CubeIcon className="h-5 w-5 mr-2" />
                    Create NFT
                  </button>
                  <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    Join Campaign
                  </button>
                  <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                    <ChartBarIcon className="h-5 w-5 mr-2" />
                    View Analytics
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
