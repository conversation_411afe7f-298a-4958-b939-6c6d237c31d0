'use client'

import {
  <PERSON>,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  <PERSON>Grid,
  <PERSON>ge,
  Spinner
} from '@chakra-ui/react'
import { useAuth } from '@/contexts/AuthContext'
import Layout from '@/components/layout/Layout'
import { DashboardLayout } from '@/components/PageLayout'
import DashboardStats from '@/components/DashboardStats'
import ActivityFeed from '@/components/ActivityFeed'
import QuickActions from '@/components/QuickActions'
import RecentNFTs from '@/components/RecentNFTs'


import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { campaignService, Campaign } from '@/services/campaignService'
import { nftService, NFT } from '@/services/nftService'

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth()
  const router = useRouter()
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [nfts, setNfts] = useState<NFT[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    // Don't redirect during loading (AuthContext initialization)
    if (isLoading) {
      console.log('🔄 Dashboard: AuthContext is loading, waiting...');
      return;
    }

    if (!isAuthenticated) {
      console.log('❌ Dashboard: User not authenticated, redirecting to login');
      router.push('/auth/login')
      return
    }

    console.log('✅ Dashboard: User authenticated, fetching data');
    fetchDashboardData()
  }, [isAuthenticated, isLoading, router])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Temporarily disable campaign loading until Project Service is running
      // TODO: Re-enable when Project Service is started
      console.log('📊 Loading dashboard data (campaigns temporarily disabled)...')

      // Fetch user NFTs if user exists
      if (user?.id) {
        const nftsData = await nftService.getUserNFTs(user.id)
        setNfts(nftsData)
      }
    } catch (err: any) {
      setError('Failed to load dashboard data')
      console.error('Dashboard error:', err)
    } finally {
      setLoading(false)
    }
  }

  // Show loading during AuthContext initialization
  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Initializing authentication...</Text>
        </VStack>
      </Container>
    )
  }

  // Show loading during data fetch
  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Loading your dashboard...</Text>
        </VStack>
      </Container>
    )
  }

  return (
    <Layout>
      <DashboardLayout>
        <VStack gap={8} align="stretch">
        {/* Welcome Section */}
        <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
          <VStack align="start" gap={2}>
            <Heading as="h2" size="lg" color="blue.600">
              Welcome back, {user?.username}! 👋
            </Heading>
            <Text color="gray.600">
              Here's what's happening with your Social NFT journey
            </Text>
          </VStack>
        </Box>

        {error && (
          <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
            <Text color="red.600" fontWeight="medium">
              {error}
            </Text>
          </Box>
        )}

        {/* Dashboard Statistics */}
        <DashboardStats userId={user?.id} />



        {/* Quick Actions */}
        <QuickActions userId={user?.id} />

        {/* Recent NFTs */}
        <RecentNFTs limit={6} />

        {/* Activity Feed */}
        <ActivityFeed userId={user?.id} limit={5} />



        </VStack>
      </DashboardLayout>
    </Layout>
  )
}
