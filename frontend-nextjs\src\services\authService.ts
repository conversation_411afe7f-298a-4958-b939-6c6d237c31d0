import axios from 'axios';
import { API_CONFIG, API_TIMEOUT } from '@/config/api';

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  twitterUsername?: string;
  twitterId?: string;
  profileImageUrl?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  twitterUsername?: string;
  twitterId?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;  // Backend uses accessToken
  tokenType: string;
}

export class AuthService {
  private baseURL = API_CONFIG.BASE_URL;

  constructor() {
    // Set up axios defaults
    axios.defaults.timeout = API_TIMEOUT;

    // Set up axios interceptor to include token in requests
    axios.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor to handle auth errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout();
          // Optionally redirect to login page
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await axios.post(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`,
        data
      );

      // Store JWT token
      if (response.data.accessToken) {
        this.setToken(response.data.accessToken);
      }

      return response.data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await axios.post(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`,
        data
      );

      // Store JWT token
      if (response.data.accessToken) {
        this.setToken(response.data.accessToken);
      }

      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async getProfile(): Promise<User> {
    try {
      const response = await axios.get(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.AUTH.PROFILE}`
      );
      return response.data;
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }

  setToken(token: string): void {
    if (typeof window !== 'undefined') {
      console.log('🔐 Setting JWT token in localStorage:', token.substring(0, 20) + '...');
      localStorage.setItem('auth_token', token);
      console.log('✅ JWT token stored successfully');
    } else {
      console.log('❌ Cannot set token: window is undefined (SSR)');
    }
  }

  getToken(): string | null {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      console.log('🔍 Getting JWT token from localStorage:', token ? token.substring(0, 20) + '...' : 'No token found');
      return token;
    }
    console.log('❌ Cannot get token: window is undefined (SSR)');
    return null;
  }

  logout(): void {
    if (typeof window !== 'undefined') {
      console.log('🚪 Logging out: Removing JWT token from localStorage');
      localStorage.removeItem('auth_token');
      console.log('✅ JWT token removed successfully');
    } else {
      console.log('❌ Cannot logout: window is undefined (SSR)');
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export const authService = new AuthService();
