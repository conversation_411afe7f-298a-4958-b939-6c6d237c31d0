import {
  Controller,
  Get,
  Post,
  Body,
  Headers,
  Query,
  Req,
  Res,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ProxyService } from '../services/proxy.service';
import { Request, Response } from 'express';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async register(
    @Body() body: any,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('📝 API Gateway: User registration request received');

      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/register',
        'POST',
        body,
        headers
      );

      console.log('✅ API Gateway: User Service registration response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Registration error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Post('login')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(
    @Body() body: any,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('🔐 API Gateway: User login request received');

      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/login',
        'POST',
        body,
        headers
      );

      console.log('✅ API Gateway: User Service login response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Login error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('👤 API Gateway: User profile request received');
      console.log('🔑 API Gateway: Authorization header:', headers.authorization ? 'Present' : 'Missing');
      console.log('🔑 API Gateway: All headers:', Object.keys(headers));

      const response = await this.proxyService.forwardRequest(
        'user-service',
        '/api/auth/profile',
        'GET',
        null,
        headers
      );

      console.log('✅ API Gateway: User Service profile response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Profile error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'user-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('twitter')
  @ApiOperation({ summary: 'Initiate Twitter OAuth flow' })
  @ApiResponse({ status: 302, description: 'Redirects to Twitter for authentication' })
  async twitterAuth(
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('🐦 API Gateway: Twitter OAuth request received');

      // Forward to Profile Analysis Service for Twitter OAuth
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        '/api/auth/twitter/login',
        'GET',
        null,
        headers
      );

      console.log('🔄 API Gateway: Profile Analysis Service response:', response.status);

      // If it's a redirect response, handle it properly
      if (response.status === 302 && response.headers?.location) {
        console.log('↩️ API Gateway: Redirecting to:', response.headers.location);
        return res.redirect(response.headers.location);
      }

      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Twitter OAuth error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Get('twitter/callback')
  @ApiOperation({ summary: 'Handle Twitter OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirects to frontend with success or error' })
  async twitterCallback(
    @Query() query: any,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('🐦 API Gateway: Twitter OAuth callback received');

      // Forward to Profile Analysis Service for Twitter OAuth callback
      const queryString = new URLSearchParams(query).toString();
      const response = await this.proxyService.forwardRequest(
        'profile-analysis-service',
        `/api/auth/twitter/callback?${queryString}`,
        'GET',
        null,
        headers
      );

      console.log('🔄 API Gateway: Profile Analysis Service callback response:', response.status);

      // If it's a redirect response, handle it properly
      if (response.status === 302 && response.headers?.location) {
        console.log('↩️ API Gateway: Callback redirecting to:', response.headers.location);
        return res.redirect(response.headers.location);
      }

      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Twitter OAuth callback error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message,
        service: 'profile-analysis-service',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Post('validate-token')
  @ApiOperation({ summary: 'Validate JWT token (Universal)' })
  @ApiResponse({ status: 200, description: 'Token validation result' })
  async validateToken(
    @Body() body: any,
    @Headers() headers: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      console.log('🔍 API Gateway: Universal token validation request received');

      if (!body.token) {
        return res.status(400).json({
          success: false,
          error: 'Token is required',
          timestamp: new Date().toISOString(),
        });
      }

      // ✅ ENHANCED: Try User Service first (for email/password users)
      try {
        console.log('🔍 API Gateway: Trying User Service token validation');
        const userServiceResponse = await this.proxyService.forwardRequest(
          'user-service',
          '/api/auth/validate-token',
          'POST',
          body,
          headers
        );

        if (userServiceResponse.data.success) {
          console.log('✅ API Gateway: User Service token validation successful');
          return res.status(userServiceResponse.status).json(userServiceResponse.data);
        }
      } catch (userServiceError) {
        console.log('⚠️ API Gateway: User Service token validation failed, trying Profile Analysis Service');
      }

      // ✅ ENHANCED: Fallback to Profile Analysis Service (for Twitter OAuth users)
      try {
        console.log('🔍 API Gateway: Trying Profile Analysis Service token validation');
        const profileServiceResponse = await this.proxyService.forwardRequest(
          'profile-analysis-service',
          '/api/auth/twitter/validate-token',
          'POST',
          body,
          headers
        );

        if (profileServiceResponse.data.success) {
          console.log('✅ API Gateway: Profile Analysis Service token validation successful');
          return res.status(profileServiceResponse.status).json(profileServiceResponse.data);
        }
      } catch (profileServiceError) {
        console.log('⚠️ API Gateway: Profile Analysis Service token validation failed');
      }

      // ✅ ENHANCED: Both services failed
      console.log('❌ API Gateway: Both token validation services failed');
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('❌ API Gateway: Universal token validation failed:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        success: false,
        error: 'Token validation service unavailable',
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }
}
