'use client'

import {
  Box,
  HStack,
  Input,
  Button,
  Text
} from '@chakra-ui/react'

interface ProjectFiltersProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  statusFilter: string
  onStatusChange: (value: string) => void
  onClearFilters: () => void
  resultCount: number
  totalCount: number
}

export default function ProjectFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusChange,
  onClearFilters,
  resultCount,
  totalCount
}: ProjectFiltersProps) {
  return (
    <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
      <HStack gap={4} flexWrap="wrap" justify="space-between">
        {/* Search Input */}
        <Input
          placeholder="🔍 Search projects..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          maxW="300px"
          bg="gray.50"
          border="1px solid"
          borderColor="gray.200"
          _focus={{ borderColor: "blue.400", bg: "white" }}
        />

        {/* Status Filter */}
        <Box maxW="200px">
          <select
            value={statusFilter}
            onChange={(e) => onStatusChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: '#f7fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="upcoming">Upcoming</option>
            <option value="completed">Completed</option>
          </select>
        </Box>

        {/* Clear Filters */}
        <Button variant="outline" onClick={onClearFilters} size="sm">
          Clear Filters
        </Button>

        {/* Results Count */}
        <Text fontSize="sm" color="gray.500">
          {resultCount} of {totalCount} projects
        </Text>
      </HStack>
    </Box>
  )
}
