<!DOCTYPE html>
<html>
<head>
    <title>Fix User ID Matching</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Fix User ID Matching</h1>
    
    <div class="section">
        <h3>Fix User ID Matching Issues</h3>
        <button onclick="fixUserIdMatching()">Fix User ID Matching</button>
        <div id="fixResult"></div>
    </div>

    <script>
        function fixUserIdMatching() {
            const result = document.getElementById('fixResult');
            
            // Get current auth user
            const authData = localStorage.getItem('auth_user');
            if (!authData) {
                result.innerHTML = '<div class="error">❌ No auth user found</div>';
                return;
            }
            
            const user = JSON.parse(authData);
            const currentUserId = user.id;
            
            // Get NFT data
            const nftData = localStorage.getItem('user_nfts');
            if (!nftData) {
                result.innerHTML = '<div class="error">❌ No NFT data found</div>';
                return;
            }
            
            const allNFTs = JSON.parse(nftData);
            
            // Find NFTs that might belong to current user but have different ID format
            const exactMatches = allNFTs.filter(nft => String(nft.userId) === String(currentUserId));
            const partialMatches = allNFTs.filter(nft => 
                String(nft.userId).includes(String(currentUserId)) || 
                String(currentUserId).includes(String(nft.userId))
            );
            
            // Update NFTs to use correct user ID
            const updatedNFTs = allNFTs.map(nft => {
                if (String(nft.userId).includes(String(currentUserId)) || 
                    String(currentUserId).includes(String(nft.userId))) {
                    return { ...nft, userId: currentUserId };
                }
                return nft;
            });
            
            // Save updated NFTs
            localStorage.setItem('user_nfts', JSON.stringify(updatedNFTs));
            
            // Calculate final stats
            const finalUserNFTs = updatedNFTs.filter(nft => String(nft.userId) === String(currentUserId));
            const uniqueCampaigns = [...new Set(finalUserNFTs.map(nft => nft.campaignId))];
            
            result.innerHTML = `
                <div class="success">✅ User ID Matching Fixed</div>
                <pre>Current User ID: "${currentUserId}"
Exact Matches Before: ${exactMatches.length}
Partial Matches Before: ${partialMatches.length}
Final User NFTs: ${finalUserNFTs.length}
Unique Campaigns: ${uniqueCampaigns.length}
Business Rule: ${finalUserNFTs.length === uniqueCampaigns.length ? 'PASS' : 'FAIL'}</pre>
                <button onclick="location.reload()">Refresh Page</button>
            `;
        }
    </script>
</body>
</html>
