import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { CacheService } from '../../shared/services/cache.service';
import { AuditService } from '../../audit/audit.service';
import { RBACService } from './rbac.service';
import { 
  CreateListingDto, 
  UpdateListingDto, 
  CreateOfferDto, 
  AcceptOfferDto,
  BuyNowDto,
  MarketplaceListingResponseDto,
  MarketplaceOfferResponseDto,
  MarketplaceTransactionResponseDto,
  MarketplaceAnalyticsDto,
  ListingStatus,
  ListingType,
  TransactionType,
  TransactionStatus,
  PaymentMethod 
} from '../dto/marketplace.dto';
import { Permission } from '../dto/rbac.dto';
import { RequestContext } from '../interfaces/request-context.interface';
import { firstValueFrom } from 'rxjs';

export interface MarketplaceResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface ExternalMarketplaceRequest {
  action: string;
  nftId: string;
  userId: string;
  listingData?: any;
  offerData?: any;
  transactionData?: any;
}

export interface ExternalMarketplaceResponse {
  success: boolean;
  data?: any;
  transactionHash?: string;
  error?: string;
}

@Injectable()
export class MarketplaceService {
  private readonly logger = new Logger(MarketplaceService.name);
  private readonly marketplaceServiceUrl: string;
  private readonly blockchainServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly auditService: AuditService,
    private readonly rbacService: RBACService,
  ) {
    this.marketplaceServiceUrl = this.configService.get<string>('MARKETPLACE_SERVICE_URL') || 'http://localhost:3015';
    this.blockchainServiceUrl = this.configService.get<string>('BLOCKCHAIN_SERVICE_URL') || 'http://localhost:3014';
  }

  /**
   * Create NFT listing on marketplace
   */
  async createListing(createListingDto: CreateListingDto, userId: string, context: RequestContext): Promise<MarketplaceResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Creating marketplace listing for NFT: ${createListingDto.nftId}`, { correlationId });

      // Check permissions
      const canCreateListings = await this.rbacService.hasPermission(userId, Permission.MARKETPLACE_WRITE);
      if (!canCreateListings) {
        throw new ForbiddenException('Insufficient permissions to create marketplace listings');
      }

      // Validate NFT exists and user owns it
      const nft = await this.prisma.nFT.findUnique({
        where: { id: createListingDto.nftId },
        include: {
          user: {
            select: { id: true, username: true, displayName: true },
          },
        },
      });

      if (!nft) {
        throw new NotFoundException(`NFT with ID ${createListingDto.nftId} not found`);
      }

      if (nft.userId !== userId) {
        throw new ForbiddenException('You can only list NFTs that you own');
      }

      if (nft.status !== 'minted') {
        throw new BadRequestException('NFT must be minted before listing');
      }

      // Check if NFT is already listed
      const existingListing = await this.prisma.marketplaceListing.findFirst({
        where: {
          nftId: createListingDto.nftId,
          status: {
            in: [ListingStatus.ACTIVE, ListingStatus.DRAFT],
          },
        },
      });

      if (existingListing) {
        throw new BadRequestException('NFT is already listed on marketplace');
      }

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + (createListingDto.duration || 604800));

      // Create listing in database
      const listing = await this.prisma.marketplaceListing.create({
        data: {
          nftId: createListingDto.nftId,
          sellerId: userId,
          listingType: createListingDto.listingType,
          status: ListingStatus.DRAFT,
          price: createListingDto.price,
          currency: createListingDto.currency,
          startingPrice: createListingDto.startingPrice,
          reservePrice: createListingDto.reservePrice,
          buyNowPrice: createListingDto.buyNowPrice,
          title: createListingDto.title || (nft.metadata as any)?.name || 'Untitled NFT',
          description: createListingDto.description || (nft.metadata as any)?.description || '',
          tags: createListingDto.tags || [],
          acceptOffers: createListingDto.acceptOffers ?? true,
          minOfferAmount: createListingDto.minOfferAmount,
          metadata: createListingDto.metadata || {},
          expiresAt: expiresAt,
          createdBy: userId,
        },
      });

      // Call external marketplace service to create listing
      try {
        const externalResponse = await this.callExternalMarketplaceService({
          action: 'create_listing',
          nftId: createListingDto.nftId,
          userId: userId,
          listingData: {
            ...createListingDto,
            listingId: listing.id,
            nftMetadata: nft.metadata,
            tokenId: nft.tokenId,
            contractAddress: nft.contractAddress,
          },
        }, correlationId);

        if (externalResponse.success) {
          // Update listing status to active
          await this.prisma.marketplaceListing.update({
            where: { id: listing.id },
            data: {
              status: ListingStatus.ACTIVE,
              externalListingId: externalResponse.data?.listingId,
              updatedBy: userId,
            },
          });
        } else {
          throw new Error(externalResponse.error || 'External marketplace service failed');
        }
      } catch (error) {
        // Update listing status to failed and keep in database for retry
        await this.prisma.marketplaceListing.update({
          where: { id: listing.id },
          data: {
            status: ListingStatus.CANCELLED,
            updatedBy: userId,
          },
        });

        throw new BadRequestException(`Marketplace listing failed: ${error.message}`);
      }

      // Get updated listing with relationships
      const updatedListing = await this.getListingWithRelations(listing.id);

      // Log audit trail
      await this.auditService.logAction({
        entityType: 'MarketplaceListing',
        entityId: listing.id,
        action: 'CREATE',
        newValues: updatedListing,
        userId: userId,
        correlationId: context.correlationId,
        metadata: { 
          nftId: createListingDto.nftId,
          listingType: createListingDto.listingType,
          price: createListingDto.price,
        },
      });

      this.logger.log(`Marketplace listing created successfully: ${listing.id}`, {
        correlationId,
        listingId: listing.id,
        nftId: createListingDto.nftId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: this.mapToListingResponse(updatedListing),
        message: 'Marketplace listing created successfully',
      };

    } catch (error) {
      this.logger.error(`Marketplace listing creation failed: ${error.message}`, {
        correlationId,
        createListingDto,
        userId,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof BadRequestException || error instanceof ForbiddenException || error instanceof NotFoundException) {
        throw error;
      }

      return { success: false, error: 'Marketplace listing creation failed' };
    }
  }

  /**
   * Get marketplace listing by ID
   */
  async getListingById(listingId: string, context: RequestContext): Promise<MarketplaceResult> {
    const startTime = Date.now();
    const correlationId = context.correlationId;

    try {
      this.logger.log(`Getting marketplace listing: ${listingId}`, { correlationId });

      // Check permissions
      const canReadListings = await this.rbacService.hasPermission(context.userId, Permission.MARKETPLACE_READ);
      if (!canReadListings) {
        throw new ForbiddenException('Insufficient permissions to view marketplace listings');
      }

      // Try cache first
      const cacheKey = `marketplace_listing:${listingId}`;
      let listing = await this.cacheService.get<any>(cacheKey);

      if (!listing) {
        listing = await this.getListingWithRelations(listingId);

        if (!listing) {
          throw new NotFoundException(`Marketplace listing with ID ${listingId} not found`);
        }

        // Cache for 2 minutes (shorter than NFT cache due to price volatility)
        await this.cacheService.set(cacheKey, listing, 120);
      }

      // Increment view count (async, don't wait)
      this.incrementViewCount(listingId).catch(error => {
        this.logger.warn(`Failed to increment view count for listing ${listingId}: ${error.message}`);
      });

      this.logger.log(`Marketplace listing retrieved successfully: ${listingId}`, {
        correlationId,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        data: this.mapToListingResponse(listing),
      };

    } catch (error) {
      this.logger.error(`Failed to get marketplace listing ${listingId}: ${error.message}`, {
        correlationId,
        error: error.stack,
        duration: Date.now() - startTime,
      });

      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }

      return { success: false, error: 'Failed to retrieve marketplace listing' };
    }
  }

  /**
   * Get listing with all relationships
   */
  private async getListingWithRelations(listingId: string): Promise<any> {
    return await this.prisma.marketplaceListing.findUnique({
      where: { id: listingId },
      include: {
        nft: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
        },
        seller: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
        offers: {
          where: {
            status: TransactionStatus.PENDING,
          },
          include: {
            buyer: {
              select: {
                id: true,
                username: true,
                displayName: true,
              },
            },
          },
          orderBy: {
            amount: 'desc',
          },
        },
        transactions: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });
  }

  /**
   * Call external marketplace service
   */
  private async callExternalMarketplaceService(
    request: ExternalMarketplaceRequest, 
    correlationId: string
  ): Promise<ExternalMarketplaceResponse> {
    try {
      // In production, this would make an actual HTTP call to the Marketplace Service
      // For now, we'll simulate the response
      this.logger.log(`Calling Marketplace Service for ${request.action}`, { correlationId });

      // Simulate external service call
      const response = await this.simulateMarketplaceService(request);
      
      return response;
    } catch (error) {
      this.logger.error(`Marketplace Service call failed: ${error.message}`, { correlationId });
      throw error;
    }
  }

  /**
   * Simulate marketplace service (placeholder for actual service integration)
   */
  private async simulateMarketplaceService(request: ExternalMarketplaceRequest): Promise<ExternalMarketplaceResponse> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    switch (request.action) {
      case 'create_listing':
        return {
          success: true,
          data: {
            listingId: `ext_listing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            marketplaceUrl: `https://marketplace.example.com/listing/${request.listingData.listingId}`,
          },
        };

      case 'create_offer':
        return {
          success: true,
          data: {
            offerId: `ext_offer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            status: 'pending',
          },
        };

      case 'execute_sale':
        return {
          success: true,
          data: {
            transactionId: `ext_tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
            blockNumber: Math.floor(Math.random() * 1000000),
          },
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
        };

      default:
        throw new Error(`Unknown marketplace action: ${request.action}`);
    }
  }

  /**
   * Increment view count for listing
   */
  private async incrementViewCount(listingId: string): Promise<void> {
    try {
      await this.prisma.marketplaceListing.update({
        where: { id: listingId },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      // Silently fail - view count is not critical
      this.logger.debug(`Failed to increment view count: ${error.message}`);
    }
  }

  /**
   * Map database listing to response DTO
   */
  private mapToListingResponse(listing: any): MarketplaceListingResponseDto {
    return {
      id: listing.id,
      nftId: listing.nftId,
      nft: {
        id: listing.nft.id,
        metadata: listing.nft.metadata,
        rarity: listing.nft.rarity,
        imageUrl: listing.nft.imageUrl,
        tokenId: listing.nft.tokenId,
        contractAddress: listing.nft.contractAddress,
      },
      sellerId: listing.sellerId,
      seller: {
        id: listing.seller.id,
        username: listing.seller.username,
        displayName: listing.seller.displayName,
      },
      listingType: listing.listingType as ListingType,
      status: listing.status as ListingStatus,
      price: listing.price,
      currency: listing.currency as PaymentMethod,
      startingPrice: listing.startingPrice,
      currentBid: listing.currentBid,
      bidCount: listing.bidCount || 0,
      title: listing.title,
      description: listing.description,
      tags: listing.tags,
      acceptOffers: listing.acceptOffers,
      minOfferAmount: listing.minOfferAmount,
      createdAt: listing.createdAt.toISOString(),
      expiresAt: listing.expiresAt.toISOString(),
      soldAt: listing.soldAt?.toISOString(),
      viewCount: listing.viewCount || 0,
      favoriteCount: listing.favoriteCount || 0,
      metadata: listing.metadata,
    };
  }

  /**
   * Invalidate marketplace cache
   */
  async invalidateMarketplaceCache(listingId: string): Promise<void> {
    const cacheKey = `marketplace_listing:${listingId}`;
    await this.cacheService.del(cacheKey);
    this.logger.debug(`Invalidated cache for marketplace listing: ${listingId}`);
  }
}
