import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge Tailwind CSS classes
 * Combines clsx for conditional classes and tailwind-merge for deduplication
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format currency values
 */
export function formatCurrency(value: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(value)
}

/**
 * Format numbers with commas
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US').format(value)
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

/**
 * Format date to relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

/**
 * Generate random ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 15)
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Sleep function for delays
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 */
export function isEmpty(value: any): boolean {
  if (value == null) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * Safe JSON parse with fallback
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return fallback
  }
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    const success = document.execCommand('copy')
    document.body.removeChild(textArea)
    return success
  }
}

/**
 * Get initials from name
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Generate random color for avatars
 */
export function getRandomColor(): string {
  const colors = [
    'bg-red-500',
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-teal-500',
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if running in browser
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined'
}

/**
 * Local storage helpers with error handling
 */
export const storage = {
  get: <T>(key: string, fallback: T): T => {
    if (!isBrowser()) {
      console.log(`🔍 Storage Debug: Not in browser, returning fallback for key: ${key}`)
      return fallback
    }
    try {
      const item = localStorage.getItem(key)
      console.log(`🔍 Storage Debug: Getting key "${key}":`, item ? 'found' : 'not found')
      if (item) {
        // ✅ FIXED: Handle JWT tokens (auth_token) as plain strings
        if (key === 'auth_token') {
          console.log(`🔑 Storage Debug: JWT token retrieved: ${item.substring(0, 30)}...`)
          return item as T
        }

        // For other keys, try JSON parsing
        try {
          const parsed = JSON.parse(item)
          console.log(`🔍 Storage Debug: Parsed value for "${key}":`, typeof parsed === 'object' ? JSON.stringify(parsed).substring(0, 100) + '...' : parsed)
          return parsed
        } catch (parseError) {
          // If JSON parsing fails, return as string
          console.log(`🔍 Storage Debug: JSON parse failed for "${key}", returning as string`)
          return item as T
        }
      }
      return fallback
    } catch (error) {
      console.error(`❌ Storage Debug: Error getting key "${key}":`, error)
      return fallback
    }
  },

  set: (key: string, value: any): void => {
    if (!isBrowser()) {
      console.log(`🔍 Storage Debug: Not in browser, cannot set key: ${key}`)
      return
    }
    try {
      // ✅ FIXED: Handle JWT tokens (auth_token) as plain strings
      if (key === 'auth_token' && typeof value === 'string') {
        localStorage.setItem(key, value)
        console.log(`🔑 Storage Debug: JWT token stored: ${value.substring(0, 30)}...`)
        return
      }

      // For other keys, JSON stringify
      const stringValue = JSON.stringify(value)
      localStorage.setItem(key, stringValue)
      console.log(`✅ Storage Debug: Set key "${key}":`, typeof value === 'object' ? JSON.stringify(value).substring(0, 100) + '...' : value)
    } catch (error) {
      console.error(`❌ Storage Debug: Error setting key "${key}":`, error)
    }
  },

  remove: (key: string): void => {
    if (!isBrowser()) {
      console.log(`🔍 Storage Debug: Not in browser, cannot remove key: ${key}`)
      return
    }
    try {
      localStorage.removeItem(key)
      console.log(`🗑️ Storage Debug: Removed key "${key}"`)
    } catch (error) {
      console.error(`❌ Storage Debug: Error removing key "${key}":`, error)
    }
  }
}
