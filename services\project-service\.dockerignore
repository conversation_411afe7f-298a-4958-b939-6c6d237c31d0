# Enterprise Docker Ignore Template
# Optimize build context and image size

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
.next

# Development files
.env.local
.env.development
.env.test
*.log

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# Test files
test/
tests/
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
coverage/

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Temporary files
tmp/
temp/
