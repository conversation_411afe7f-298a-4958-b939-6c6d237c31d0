'use client'

import { useState, useEffect } from 'react'
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline'

interface ProgressStep {
  id: string
  label: string
  description: string
  status: 'pending' | 'active' | 'completed' | 'error'
  duration?: number
}

interface ProgressIndicatorProps {
  steps: ProgressStep[]
  currentStep?: string
  onComplete?: () => void
  onError?: (error: string) => void
  className?: string
}

export default function ProgressIndicator({
  steps,
  currentStep,
  onComplete,
  onError,
  className = ''
}: ProgressIndicatorProps) {
  const [localSteps, setLocalSteps] = useState<ProgressStep[]>(steps)
  const [startTime, setStartTime] = useState<number>(Date.now())

  useEffect(() => {
    setLocalSteps(steps)
  }, [steps])

  useEffect(() => {
    if (currentStep) {
      setLocalSteps(prevSteps => 
        prevSteps.map(step => {
          if (step.id === currentStep) {
            return { ...step, status: 'active' }
          } else if (step.status === 'active') {
            return { ...step, status: 'completed' }
          }
          return step
        })
      )
    }
  }, [currentStep])

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />
      case 'error':
        return <ExclamationCircleIcon className="h-6 w-6 text-red-500" />
      case 'active':
        return (
          <div className="h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        )
      default:
        return (
          <div className="h-6 w-6 border-2 border-gray-300 rounded-full" />
        )
    }
  }

  const getStepColor = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return 'text-green-700 bg-green-50 border-green-200'
      case 'error':
        return 'text-red-700 bg-red-50 border-red-200'
      case 'active':
        return 'text-blue-700 bg-blue-50 border-blue-200'
      default:
        return 'text-gray-500 bg-gray-50 border-gray-200'
    }
  }

  const completedSteps = localSteps.filter(step => step.status === 'completed').length
  const totalSteps = localSteps.length
  const progressPercentage = (completedSteps / totalSteps) * 100

  const elapsedTime = Math.floor((Date.now() - startTime) / 1000)

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900">Processing</h3>
          <span className="text-sm text-gray-500">{elapsedTime}s</span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        
        <div className="flex justify-between text-sm text-gray-600 mt-1">
          <span>{completedSteps} of {totalSteps} steps completed</span>
          <span>{Math.round(progressPercentage)}%</span>
        </div>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {localSteps.map((step, index) => (
          <div
            key={step.id}
            className={`flex items-start space-x-3 p-3 rounded-lg border transition-all duration-300 ${getStepColor(step)}`}
          >
            {/* Step Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getStepIcon(step)}
            </div>

            {/* Step Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">{step.label}</h4>
                {step.status === 'active' && step.duration && (
                  <span className="text-xs text-gray-500">~{step.duration}s</span>
                )}
              </div>
              <p className="text-sm opacity-75 mt-1">{step.description}</p>
              
              {/* Active Step Progress */}
              {step.status === 'active' && step.duration && (
                <div className="mt-2">
                  <div className="w-full bg-white bg-opacity-50 rounded-full h-1">
                    <div className="bg-current h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
                  </div>
                </div>
              )}
            </div>

            {/* Step Number */}
            <div className="flex-shrink-0">
              <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium rounded-full bg-white bg-opacity-50">
                {index + 1}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {localSteps.some(step => step.status === 'error') ? (
              <span className="text-red-600 font-medium">Process failed</span>
            ) : localSteps.every(step => step.status === 'completed') ? (
              <span className="text-green-600 font-medium">Process completed successfully</span>
            ) : (
              <span>Processing in progress...</span>
            )}
          </div>
          
          {localSteps.every(step => step.status === 'completed') && onComplete && (
            <button
              onClick={onComplete}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              Continue
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Predefined step configurations for common processes
export const ANALYSIS_STEPS: ProgressStep[] = [
  {
    id: 'fetch-profile',
    label: 'Fetching Twitter Profile',
    description: 'Retrieving profile data from Twitter API',
    status: 'pending',
    duration: 3
  },
  {
    id: 'analyze-metrics',
    label: 'Analyzing Metrics',
    description: 'Processing follower count, engagement, and activity data',
    status: 'pending',
    duration: 5
  },
  {
    id: 'calculate-score',
    label: 'Calculating Score',
    description: 'Computing overall influence score and rarity recommendation',
    status: 'pending',
    duration: 2
  },
  {
    id: 'save-results',
    label: 'Saving Results',
    description: 'Storing analysis results in database',
    status: 'pending',
    duration: 1
  }
]

export const NFT_GENERATION_STEPS: ProgressStep[] = [
  {
    id: 'prepare-data',
    label: 'Preparing NFT Data',
    description: 'Processing analysis results for NFT generation',
    status: 'pending',
    duration: 2
  },
  {
    id: 'generate-image',
    label: 'Generating NFT Image',
    description: 'Creating unique visual representation based on your profile',
    status: 'pending',
    duration: 8
  },
  {
    id: 'create-metadata',
    label: 'Creating Metadata',
    description: 'Generating NFT attributes and properties',
    status: 'pending',
    duration: 3
  },
  {
    id: 'mint-nft',
    label: 'Minting NFT',
    description: 'Creating your unique NFT token',
    status: 'pending',
    duration: 5
  },
  {
    id: 'finalize',
    label: 'Finalizing',
    description: 'Adding NFT to your collection',
    status: 'pending',
    duration: 2
  }
]
