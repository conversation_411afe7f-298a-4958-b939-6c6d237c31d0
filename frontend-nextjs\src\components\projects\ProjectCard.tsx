'use client'

import {
  <PERSON>,
  VStack,
  HStack,
  <PERSON>ing,
  Text,
  Button,
  Badge,
  Image
} from '@chakra-ui/react'
import Link from 'next/link'

interface ProjectCardProps {
  id: string
  name: string
  description: string
  status: string
  imageUrl?: string
  campaignCount?: number
}

export default function ProjectCard({
  id,
  name,
  description,
  status,
  imageUrl,
  campaignCount = 0
}: ProjectCardProps) {
  return (
    <Box
      bg="white"
      borderRadius="lg"
      boxShadow="md"
      overflow="hidden"
      transition="all 0.2s"
      _hover={{
        transform: "translateY(-4px)",
        boxShadow: "xl"
      }}
      border="1px solid"
      borderColor="gray.100"
    >
      {/* Project Image */}
      <Box h="200px" bg="gray.100" position="relative">
        {imageUrl ? (
          <Image src={imageUrl} alt={name} w="full" h="full" objectFit="cover" />
        ) : (
          <Box
            h="full"
            bgGradient="linear(to-r, blue.400, purple.500)"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text color="white" fontSize="lg" fontWeight="bold">
              {name.charAt(0)}
            </Text>
          </Box>
        )}
        <Badge
          position="absolute"
          top={3}
          right={3}
          colorScheme={status === 'active' ? 'green' : 'gray'}
        >
          {status.toUpperCase()}
        </Badge>
      </Box>

      {/* Project Content */}
      <VStack align="start" gap={4} p={6}>
        <VStack align="start" gap={2}>
          <Heading size="md" color="gray.800">{name}</Heading>
          <Text color="gray.600" fontSize="sm" noOfLines={3}>
            {description}
          </Text>
        </VStack>

        <HStack justify="space-between" w="full">
          <Text fontSize="sm" color="gray.500">
            {campaignCount} campaigns
          </Text>
          <Link href={`/projects/${id}`}>
            <Button colorScheme="blue" size="sm">
              View Project
            </Button>
          </Link>
        </HStack>
      </VStack>
    </Box>
  )
}
