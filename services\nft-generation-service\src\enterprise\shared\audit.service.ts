// Enterprise Audit Service
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateAuditLogDto } from '../models/audit-event.model';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(private readonly prisma: PrismaService) {}

  async logAction(auditData: CreateAuditLogDto & {
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      if (!process.env.ENABLE_AUDIT_LOGGING || process.env.ENABLE_AUDIT_LOGGING === 'false') {
        return;
      }

      const auditLog = await this.prisma.auditLog.create({
        data: {
          entityType: auditData.entityType,
          entityId: auditData.entityId,
          action: auditData.action,
          oldValues: auditData.oldValues,
          newValues: auditData.newValues,
          userId: auditData.userId,
          sessionId: auditData.sessionId,
          ipAddress: auditData.ipAddress,
          userAgent: auditData.userAgent,
          correlationId: auditData.correlationId
        }
      });

      this.logger.debug(`Audit log created: ${auditData.action} on ${auditData.entityType}:${auditData.entityId}`, auditData.correlationId);
      return auditLog;
    } catch (error) {
      this.logger.error(`Failed to create audit log: ${error.message}`, error.stack, auditData.correlationId);
      // Don't throw - audit logging should not break business operations
    }
  }

  async getAuditTrail(entityType: string, entityId: string, limit: number = 50) {
    try {
      const auditTrail = await this.prisma.auditLog.findMany({
        where: {
          entityType,
          entityId
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          action: true,
          oldValues: true,
          newValues: true,
          userId: true,
          correlationId: true,
          createdAt: true
        }
      });

      return auditTrail;
    } catch (error) {
      this.logger.error(`Failed to get audit trail: ${error.message}`, error.stack);
      return [];
    }
  }

  async getAuditStats() {
    try {
      const stats = await this.prisma.auditLog.groupBy({
        by: ['entityType', 'action'],
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } }
      });

      const totalLogs = await this.prisma.auditLog.count();
      const recentLogs = await this.prisma.auditLog.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });

      return {
        totalLogs,
        recentLogs,
        actionBreakdown: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Failed to get audit stats: ${error.message}`, error.stack);
      return {
        totalLogs: 0,
        recentLogs: 0,
        actionBreakdown: [],
        timestamp: new Date().toISOString()
      };
    }
  }
}
