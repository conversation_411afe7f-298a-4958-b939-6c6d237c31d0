// Debug API Gateway Integration
const axios = require('axios');

async function debugAPIGateway() {
  console.log('🔍 Debugging API Gateway Integration...\n');
  
  try {
    // Test 1: Direct Project Service
    console.log('1️⃣ Testing Direct Project Service...');
    const directResponse = await axios.get('http://localhost:3005/enterprise/projects');
    console.log('✅ Direct response:', {
      status: directResponse.status,
      contentType: directResponse.headers['content-type'],
      dataType: typeof directResponse.data,
      data: directResponse.data
    });
    console.log('');
    
    // Test 2: API Gateway Health
    console.log('2️⃣ Testing API Gateway Health...');
    const healthResponse = await axios.get('http://localhost:3010/api/health');
    console.log('✅ Health response:', {
      status: healthResponse.status,
      projectServiceStatus: healthResponse.data.services.find(s => s.name === 'project-service')?.status
    });
    console.log('');
    
    // Test 3: API Gateway Projects (with detailed error handling)
    console.log('3️⃣ Testing API Gateway Projects...');
    try {
      const gatewayResponse = await axios.get('http://localhost:3010/api/projects');
      console.log('✅ Gateway response:', {
        status: gatewayResponse.status,
        contentType: gatewayResponse.headers['content-type'],
        dataType: typeof gatewayResponse.data,
        data: gatewayResponse.data
      });
    } catch (error) {
      console.log('❌ Gateway error:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        contentType: error.response?.headers['content-type'],
        data: error.response?.data,
        message: error.message
      });
      
      // Let's try to understand what the proxy service is receiving
      console.log('\n🔍 Raw response data:', error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugAPIGateway().catch(console.error);
