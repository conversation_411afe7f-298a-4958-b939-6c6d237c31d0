'use client'

import {
  Box,
  Button,
  VStack,
  Text,
  Heading
} from '@chakra-ui/react'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'

export default function QuickLogin() {
  const { login } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleQuickLogin = async () => {
    try {
      setLoading(true)
      
      console.log('🔐 Quick login with test credentials')
      
      await login({
        email: '<EMAIL>',
        password: 'password123'
      })
      
      console.log('✅ Quick login successful')
      
      // Refresh the current page to update authentication state
      window.location.reload()
      
    } catch (error: any) {
      console.error('Quick login error:', error)
      alert(`Login failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box bg="blue.50" p={6} borderRadius="lg" border="2px solid" borderColor="blue.200">
      <VStack gap={4} align="stretch">
        <Box textAlign="center">
          <Heading as="h3" size="md" color="blue.700" mb={2}>
            🔐 Authentication Required
          </Heading>
          <Text color="blue.600" fontSize="sm">
            You need to be logged in to join campaigns and generate NFTs
          </Text>
        </Box>
        
        <VStack gap={3}>
          <Button
            colorScheme="blue"
            size="lg"
            onClick={handleQuickLogin}
            loading={loading}
            w="full"
          >
            {loading ? 'Logging in...' : 'Quick Login (Demo)'}
          </Button>
          
          <Text fontSize="xs" color="gray.500" textAlign="center">
            Uses test account: <EMAIL>
          </Text>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/auth/login')}
            w="full"
          >
            Go to Login Page
          </Button>
        </VStack>
      </VStack>
    </Box>
  )
}
