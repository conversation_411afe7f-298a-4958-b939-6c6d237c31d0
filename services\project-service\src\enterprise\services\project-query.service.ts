// Enterprise Project Query Service - Requirements-Driven Implementation
import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';

export interface ProjectListQuery {
  page?: number;
  limit?: number;
  category?: string;
  status?: string;
  ownerId?: string;
  search?: string;
  blockchainNetwork?: string;
  isPublic?: boolean;
}

export interface ProjectStatsQuery {
  ownerId?: string;
  timeRange?: '7d' | '30d' | '90d' | 'all';
}

@Injectable()
export class ProjectQueryService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get project by ID with complete details
   * Requirements: Return full project configuration for project owners
   */
  async getProjectById(id: string, includeConfig: boolean = false): Promise<any> {
    const project = await this.prisma.projectQuery.findUnique({
      where: { id },
    });

    if (!project) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }

    let projectDetails = {
      id: project.id,
      name: project.displayName,
      description: project.displayDescription,
      ownerId: project.ownerId,
      category: project.category,
      featuredImage: project.featuredImage,
      website: project.website,
      socialMediaLinks: project.socialMediaLinks,
      duration: project.duration,
      blockchainNetwork: project.blockchainNetwork,
      nftRarityTypes: project.nftRarityTypes,
      status: project.status,
      isPublic: project.isPublic,
      
      // Metrics
      participantCount: project.participantCount,
      campaignCount: project.campaignCount,
      totalNfts: project.totalNfts,
      totalMarketValue: project.totalMarketValue,
      averageNftPrice: project.averageNftPrice,
      tradingVolume24h: project.tradingVolume24h,
      priceChange7d: project.priceChange7d,
      priceChange30d: project.priceChange30d,
      popularityScore: project.popularityScore,
      
      createdAt: project.createdAt,
      lastUpdated: project.lastUpdated,
    };

    // Include full configuration for project owners
    if (includeConfig) {
      const fullProject = await this.prisma.projectCommand.findUnique({
        where: { id },
      });

      if (fullProject) {
        (projectDetails as any).analysisConfiguration = fullProject.analysisConfiguration;
        (projectDetails as any).nftConfiguration = fullProject.nftConfiguration;
        (projectDetails as any).networkConfig = fullProject.networkConfig;
        (projectDetails as any).participationConditions = fullProject.participationConditions;
        (projectDetails as any).maxParticipants = fullProject.maxParticipants;
        (projectDetails as any).allowParticipation = fullProject.allowParticipation;
      }
    }

    return {
      success: true,
      data: projectDetails,
    };
  }

  /**
   * Get projects list with filtering and pagination
   * Requirements: Support search, filtering, and pagination for frontend
   */
  async getProjects(query: ProjectListQuery): Promise<any> {
    const {
      page = 1,
      limit = 20,
      category,
      status = 'active',
      ownerId,
      search,
      blockchainNetwork,
      isPublic = true,
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      status,
      isPublic,
    };

    if (category) {
      where.category = category;
    }

    if (ownerId) {
      where.ownerId = ownerId;
    }

    if (blockchainNetwork) {
      where.blockchainNetwork = blockchainNetwork;
    }

    if (search) {
      where.OR = [
        { displayName: { contains: search, mode: 'insensitive' } },
        { displayDescription: { contains: search, mode: 'insensitive' } },
        { category: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get projects with pagination
    const [projects, total] = await Promise.all([
      this.prisma.projectQuery.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { popularityScore: 'desc' },
          { createdAt: 'desc' },
        ],
      }),
      this.prisma.projectQuery.count({ where }),
    ]);

    return {
      success: true,
      data: {
        projects: projects.map(project => ({
          id: project.id,
          name: project.displayName,
          description: project.displayDescription,
          category: project.category,
          featuredImage: project.featuredImage,
          website: project.website,
          blockchainNetwork: project.blockchainNetwork,
          nftRarityTypes: project.nftRarityTypes,
          
          // Metrics for display
          participantCount: project.participantCount,
          totalNfts: project.totalNfts,
          totalMarketValue: project.totalMarketValue,
          averageNftPrice: project.averageNftPrice,
          priceChange7d: project.priceChange7d,
          priceChange30d: project.priceChange30d,
          popularityScore: project.popularityScore,
          
          createdAt: project.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      },
    };
  }

  /**
   * Get project statistics
   * Requirements: Analytics data for dashboard and monitoring
   */
  async getProjectStats(query: ProjectStatsQuery): Promise<any> {
    const { ownerId, timeRange = '30d' } = query;

    // Calculate date range
    const now = new Date();
    const dateRanges = {
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      'all': new Date(0),
    };

    const startDate = dateRanges[timeRange];

    // Build where clause
    const where: any = {
      createdAt: { gte: startDate },
    };

    if (ownerId) {
      where.ownerId = ownerId;
    }

    // Get aggregated statistics
    const [
      totalProjects,
      activeProjects,
      totalParticipants,
      totalNfts,
      totalMarketValue,
      avgMarketValue,
    ] = await Promise.all([
      this.prisma.projectQuery.count({ where }),
      this.prisma.projectQuery.count({ 
        where: { ...where, status: 'active' } 
      }),
      this.prisma.projectQuery.aggregate({
        where,
        _sum: { participantCount: true },
      }),
      this.prisma.projectQuery.aggregate({
        where,
        _sum: { totalNfts: true },
      }),
      this.prisma.projectQuery.aggregate({
        where,
        _sum: { totalMarketValue: true },
      }),
      this.prisma.projectQuery.aggregate({
        where,
        _avg: { totalMarketValue: true },
      }),
    ]);

    // Get top performing projects
    const topProjects = await this.prisma.projectQuery.findMany({
      where,
      orderBy: { popularityScore: 'desc' },
      take: 5,
      select: {
        id: true,
        displayName: true,
        popularityScore: true,
        totalMarketValue: true,
        participantCount: true,
      },
    });

    // Get blockchain distribution
    const blockchainStats = await this.prisma.projectQuery.groupBy({
      by: ['blockchainNetwork'],
      where,
      _count: { blockchainNetwork: true },
      _sum: { totalMarketValue: true },
    });

    return {
      success: true,
      data: {
        overview: {
          totalProjects,
          activeProjects,
          totalParticipants: totalParticipants._sum.participantCount || 0,
          totalNfts: totalNfts._sum.totalNfts || 0,
          totalMarketValue: totalMarketValue._sum.totalMarketValue || 0,
          averageMarketValue: avgMarketValue._avg.totalMarketValue || 0,
        },
        topProjects,
        blockchainDistribution: blockchainStats.map(stat => ({
          network: stat.blockchainNetwork,
          projectCount: stat._count.blockchainNetwork,
          totalValue: stat._sum.totalMarketValue || 0,
        })),
        timeRange,
      },
    };
  }

  /**
   * Get projects by owner
   * Requirements: Project management dashboard for project owners
   */
  async getProjectsByOwner(ownerId: string, includeConfig: boolean = false): Promise<any> {
    const projects = await this.prisma.projectQuery.findMany({
      where: { ownerId },
      orderBy: { createdAt: 'desc' },
    });

    let projectsData = projects.map(project => ({
      id: project.id,
      name: project.displayName,
      description: project.displayDescription,
      category: project.category,
      status: project.status,
      blockchainNetwork: project.blockchainNetwork,
      
      // Metrics
      participantCount: project.participantCount,
      totalNfts: project.totalNfts,
      totalMarketValue: project.totalMarketValue,
      priceChange7d: project.priceChange7d,
      priceChange30d: project.priceChange30d,
      
      createdAt: project.createdAt,
      lastUpdated: project.lastUpdated,
    }));

    // Include full configuration if requested
    if (includeConfig && projects.length > 0) {
      const fullProjects = await this.prisma.projectCommand.findMany({
        where: { 
          id: { in: projects.map(p => p.id) },
          ownerId,
        },
      });

      projectsData = projectsData.map(project => {
        const fullProject = fullProjects.find(fp => fp.id === project.id);
        if (fullProject) {
          const extendedProject = project as any;
          extendedProject.analysisConfiguration = fullProject.analysisConfiguration;
          extendedProject.nftConfiguration = fullProject.nftConfiguration;
          extendedProject.participationConditions = fullProject.participationConditions;
          extendedProject.maxParticipants = fullProject.maxParticipants;
          return extendedProject;
        }
        return project;
      });
    }

    return {
      success: true,
      data: {
        projects: projectsData,
        total: projects.length,
      },
    };
  }

  /**
   * Search projects with advanced filtering
   * Requirements: Advanced search for marketplace and discovery
   */
  async searchProjects(searchTerm: string, filters: any = {}): Promise<any> {
    const where: any = {
      isPublic: true,
      status: 'active',
      OR: [
        { displayName: { contains: searchTerm, mode: 'insensitive' } },
        { displayDescription: { contains: searchTerm, mode: 'insensitive' } },
        { category: { contains: searchTerm, mode: 'insensitive' } },
      ],
    };

    // Apply additional filters
    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.blockchainNetwork) {
      where.blockchainNetwork = filters.blockchainNetwork;
    }

    if (filters.minMarketValue) {
      where.totalMarketValue = { gte: filters.minMarketValue };
    }

    if (filters.minParticipants) {
      where.participantCount = { gte: filters.minParticipants };
    }

    const projects = await this.prisma.projectQuery.findMany({
      where,
      orderBy: [
        { popularityScore: 'desc' },
        { totalMarketValue: 'desc' },
      ],
      take: 50, // Limit search results
    });

    return {
      success: true,
      data: {
        projects: projects.map(project => ({
          id: project.id,
          name: project.displayName,
          description: project.displayDescription,
          category: project.category,
          featuredImage: project.featuredImage,
          blockchainNetwork: project.blockchainNetwork,
          totalMarketValue: project.totalMarketValue,
          participantCount: project.participantCount,
          popularityScore: project.popularityScore,
        })),
        total: projects.length,
        searchTerm,
      },
    };
  }
}
