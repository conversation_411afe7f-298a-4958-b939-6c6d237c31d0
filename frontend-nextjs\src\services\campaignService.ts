import axios from 'axios';
import { API_CONFIG, SERVICES } from '@/config/api';

export interface Campaign {
  id: string;
  name: string;
  description: string;
  status: string;
  startDate: string;
  endDate: string;
  participantCount: number;
  maxParticipants: number;
  nftSettings: {
    style: string;
    theme: string;
    rarityThresholds: {
      common: number;
      rare: number;
      legendary: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface JoinCampaignData {
  userId: string;
  twitterUsername: string;
  twitterUserId?: string;
}

export class CampaignService {
  private baseURL = SERVICES.PROJECT_SERVICE;

  async getCampaigns(): Promise<Campaign[]> {
    try {
      const response = await axios.get(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.CAMPAIGNS.LIST}`
      );
      return response.data;
    } catch (error) {
      console.error('Get campaigns error:', error);
      throw error;
    }
  }

  async getCampaignById(id: string): Promise<Campaign> {
    try {
      const response = await axios.get(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.CAMPAIGNS.DETAILS}/${id}`
      );
      return response.data;
    } catch (error) {
      console.error('Get campaign by ID error:', error);
      throw error;
    }
  }

  // Note: Campaign join will be implemented when backend POST is fixed
  async joinCampaign(campaignId: string, userData: JoinCampaignData): Promise<any> {
    try {
      const response = await axios.post(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.CAMPAIGNS.JOIN}/${campaignId}/join`,
        userData
      );
      return response.data;
    } catch (error) {
      console.error('Join campaign error:', error);
      // For now, show a user-friendly message about backend optimization
      throw new Error('Campaign joining is temporarily unavailable while we optimize the backend. Please try again later.');
    }
  }

  async getActiveCampaigns(): Promise<Campaign[]> {
    try {
      const campaigns = await this.getCampaigns();
      return campaigns.filter(campaign => campaign.status === 'active');
    } catch (error) {
      console.error('Get active campaigns error:', error);
      throw error;
    }
  }
}

export const campaignService = new CampaignService();
