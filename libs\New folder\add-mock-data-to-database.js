// Add Mock Data to Database for Real User
// This script adds proper mock data to the database for user "persisttest20250528191812"

const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'nft_service_db',
  user: 'postgres',
  password: '1111'
});

async function addMockDataToDatabase() {
  const client = await pool.connect();

  try {
    console.log('🔧 Adding mock data to database for real user...');

    const userId = 'persisttest20250528191812';

    // Clear existing NFTs for this user
    await client.query('DELETE FROM nfts WHERE user_id = $1', [userId]);
    console.log('✅ Cleared existing NFTs for user');

    // Add 4 NFTs for 4 different campaigns (following business rule: 1 NFT per campaign)
    const mockNFTs = [
      {
        id: `nft_${userId}_1`,
        userId: userId,
        campaignId: 'campaign_1',
        name: 'Digital Warrior #001',
        description: 'A fierce digital warrior NFT',
        imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=warrior1',
        rarity: 'Rare',
        currentScore: 750,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      },
      {
        id: `nft_${userId}_2`,
        userId: userId,
        campaignId: 'campaign_2',
        name: 'Cyber Guardian #002',
        description: 'A protective cyber guardian NFT',
        imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guardian2',
        rarity: 'Rare',
        currentScore: 920,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      },
      {
        id: `nft_${userId}_3`,
        userId: userId,
        campaignId: 'campaign_3',
        name: 'Tech Mystic #003',
        description: 'A mystical tech-savvy NFT',
        imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=mystic3',
        rarity: 'Rare',
        currentScore: 1150,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      },
      {
        id: `nft_${userId}_4`,
        userId: userId,
        campaignId: 'campaign_4',
        name: 'Data Sage #004',
        description: 'A wise data manipulation NFT',
        imageUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sage4',
        rarity: 'Rare',
        currentScore: 680,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      }
    ];

    // Insert NFTs into database
    for (const nft of mockNFTs) {
      await client.query(`
        INSERT INTO nfts (id, user_id, campaign_id, name, description, image_url, rarity, current_score, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        nft.id,
        nft.userId,
        nft.campaignId,
        nft.name,
        nft.description,
        nft.imageUrl,
        nft.rarity,
        nft.currentScore,
        nft.createdAt
      ]);
    }

    console.log('✅ Added 4 NFTs to database');
    console.log('✅ Business rule compliant: 4 NFTs = 4 campaigns');
    console.log('✅ All 4 NFTs are Rare (as expected)');

    // Verify data
    const result = await client.query('SELECT * FROM nfts WHERE user_id = $1', [userId]);
    console.log(`✅ Verification: ${result.rows.length} NFTs found in database`);

  } catch (error) {
    console.error('❌ Error adding mock data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
addMockDataToDatabase();
