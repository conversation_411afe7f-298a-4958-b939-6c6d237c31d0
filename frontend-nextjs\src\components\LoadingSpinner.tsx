'use client'

import {
  Box,
  Flex,
  Spinner,
  Text,
  VStack
} from '@chakra-ui/react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  message?: string
  fullScreen?: boolean
  color?: string
}

export default function LoadingSpinner({ 
  size = 'lg', 
  message = 'Loading...', 
  fullScreen = false,
  color = 'blue.500'
}: LoadingSpinnerProps) {
  const content = (
    <VStack gap={4}>
      <Spinner size={size} color={color} thickness="4px" />
      {message && (
        <Text color="gray.600" fontSize="md" textAlign="center">
          {message}
        </Text>
      )}
    </VStack>
  )

  if (fullScreen) {
    return (
      <Flex
        position="fixed"
        top="0"
        left="0"
        right="0"
        bottom="0"
        bg="whiteAlpha.900"
        zIndex="modal"
        align="center"
        justify="center"
      >
        {content}
      </Flex>
    )
  }

  return (
    <Flex align="center" justify="center" py={8}>
      {content}
    </Flex>
  )
}

// Page Loading Component
export function PageLoading({ message = 'Loading page...' }: { message?: string }) {
  return (
    <Box minH="50vh">
      <LoadingSpinner size="xl" message={message} />
    </Box>
  )
}

// Button Loading Component
export function ButtonLoading({ size = 'sm' }: { size?: 'sm' | 'md' | 'lg' }) {
  return <Spinner size={size} />
}

// Card Loading Skeleton
export function CardSkeleton() {
  return (
    <Box bg="white" borderRadius="lg" boxShadow="md" p={6}>
      <VStack gap={4} align="stretch">
        <Box h="4" bg="gray.200" borderRadius="md" />
        <Box h="3" bg="gray.200" borderRadius="md" w="70%" />
        <Box h="3" bg="gray.200" borderRadius="md" w="50%" />
        <Box h="8" bg="gray.200" borderRadius="md" />
      </VStack>
    </Box>
  )
}
