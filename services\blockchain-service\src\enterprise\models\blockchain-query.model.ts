// Enterprise Blockchain Query Model (Read Side) - Template
import { ApiProperty } from '@nestjs/swagger';

export class BlockchainQueryDto {
  @ApiProperty({ description: 'Transaction ID' })
  id: string;

  @ApiProperty({ description: 'Display hash' })
  displayHash: string;

  @ApiProperty({ description: 'Display type' })
  displayType: string;

  @ApiProperty({ description: 'Display status' })
  displayStatus: string;

  @ApiProperty({ description: 'Contract address' })
  contractAddress: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;
}
