import { IsString, IsOptional, Is<PERSON><PERSON>, Is<PERSON><PERSON>ber, IsBoolean, IsArray, IsDateString, ValidateNested, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';

export enum SearchType {
  USERS = 'users',
  CAMPAIGNS = 'campaigns',
  NFTS = 'nfts',
  MARKETPLACE = 'marketplace',
  GLOBAL = 'global',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export enum UserSortBy {
  CREATED_AT = 'createdAt',
  USERNAME = 'username',
  DISPLAY_NAME = 'displayName',
  LAST_LOGIN = 'lastLoginAt',
  REPUTATION = 'reputation',
  NFT_COUNT = 'nftCount',
}

export enum CampaignSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  START_DATE = 'startDate',
  END_DATE = 'endDate',
  PARTICIPANTS = 'participantCount',
  REWARDS = 'totalRewards',
  STATUS = 'status',
}

export enum NFTSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  RARITY = 'rarity',
  VALUE = 'estimatedValue',
  MINTED_AT = 'mintedAt',
  CAMPAIGN = 'campaignName',
}

export enum MarketplaceSortBy {
  CREATED_AT = 'createdAt',
  PRICE = 'price',
  NAME = 'nftName',
  RARITY = 'nftRarity',
  VIEWS = 'viewCount',
  OFFERS = 'offerCount',
}

export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  CONTAINS = 'contains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  IN = 'in',
  NOT_IN = 'notIn',
  BETWEEN = 'between',
  IS_NULL = 'isNull',
  IS_NOT_NULL = 'isNotNull',
}

export class SearchFilterDto {
  @ApiProperty({
    description: 'Field to filter on',
    example: 'status',
  })
  @IsString()
  field: string;

  @ApiProperty({
    description: 'Filter operator',
    enum: FilterOperator,
    example: FilterOperator.EQUALS,
  })
  @IsEnum(FilterOperator)
  operator: FilterOperator;

  @ApiProperty({
    description: 'Filter value(s)',
    example: 'active',
  })
  value: any;

  @ApiPropertyOptional({
    description: 'Second value for BETWEEN operator',
    example: '2025-12-31',
  })
  @IsOptional()
  value2?: any;
}

export class SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Search query string',
    example: 'legendary NFT campaign',
  })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({
    description: 'Search type',
    enum: SearchType,
    example: SearchType.GLOBAL,
  })
  @IsEnum(SearchType)
  type: SearchType;

  @ApiPropertyOptional({
    description: 'Search filters',
    type: [SearchFilterDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SearchFilterDto)
  filters?: SearchFilterDto[];

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'createdAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    example: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Include faceted search results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeFacets?: boolean = false;

  @ApiPropertyOptional({
    description: 'Include search suggestions',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeSuggestions?: boolean = false;

  @ApiPropertyOptional({
    description: 'Include search analytics',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAnalytics?: boolean = false;

  @ApiPropertyOptional({
    description: 'Search within specific date range - start date',
    example: '2025-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @ApiPropertyOptional({
    description: 'Search within specific date range - end date',
    example: '2025-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @ApiPropertyOptional({
    description: 'Geographic location filter',
    example: 'US',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Language filter',
    example: 'en',
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({
    description: 'Search boost fields with weights',
    example: { name: 2.0, description: 1.5, tags: 1.0 },
  })
  @IsOptional()
  boostFields?: Record<string, number>;
}

export class AutocompleteQueryDto {
  @ApiProperty({
    description: 'Partial search query',
    example: 'leg',
  })
  @IsString()
  query: string;

  @ApiProperty({
    description: 'Search type for autocomplete',
    enum: SearchType,
    example: SearchType.GLOBAL,
  })
  @IsEnum(SearchType)
  type: SearchType;

  @ApiPropertyOptional({
    description: 'Maximum number of suggestions',
    example: 10,
    minimum: 1,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Include popular searches',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includePopular?: boolean = true;

  @ApiPropertyOptional({
    description: 'Include recent searches for user',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeRecent?: boolean = true;
}

export class SearchFacetDto {
  @ApiProperty({
    description: 'Facet field name',
    example: 'status',
  })
  field: string;

  @ApiProperty({
    description: 'Facet display name',
    example: 'Campaign Status',
  })
  name: string;

  @ApiProperty({
    description: 'Facet type',
    example: 'terms',
    enum: ['terms', 'range', 'date_range', 'histogram'],
  })
  type: string;

  @ApiProperty({
    description: 'Facet values with counts',
    example: [
      { value: 'active', count: 25, label: 'Active' },
      { value: 'completed', count: 15, label: 'Completed' },
    ],
  })
  values: Array<{
    value: any;
    count: number;
    label: string;
    selected?: boolean;
  }>;
}

export class SearchSuggestionDto {
  @ApiProperty({
    description: 'Suggestion text',
    example: 'legendary NFT',
  })
  text: string;

  @ApiProperty({
    description: 'Suggestion type',
    example: 'completion',
    enum: ['completion', 'correction', 'popular', 'recent'],
  })
  type: string;

  @ApiProperty({
    description: 'Suggestion score/relevance',
    example: 0.95,
  })
  score: number;

  @ApiPropertyOptional({
    description: 'Number of results for this suggestion',
    example: 42,
  })
  @IsOptional()
  resultCount?: number;

  @ApiPropertyOptional({
    description: 'Suggestion category',
    example: 'nft',
  })
  @IsOptional()
  category?: string;
}

export class SearchResultItemDto {
  @ApiProperty({
    description: 'Result item ID',
    example: 'item_123',
  })
  id: string;

  @ApiProperty({
    description: 'Result item type',
    example: 'campaign',
  })
  type: string;

  @ApiProperty({
    description: 'Result title/name',
    example: 'Summer NFT Campaign',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'Result description',
    example: 'Join our summer campaign to earn legendary NFTs',
  })
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Result image URL',
    example: 'https://platform.com/images/campaign_123.jpg',
  })
  @IsOptional()
  imageUrl?: string;

  @ApiPropertyOptional({
    description: 'Result URL/link',
    example: '/campaigns/campaign_123',
  })
  @IsOptional()
  url?: string;

  @ApiProperty({
    description: 'Search relevance score',
    example: 0.87,
  })
  score: number;

  @ApiProperty({
    description: 'Result creation date',
    example: '2025-06-01T10:00:00Z',
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Result metadata',
    example: {
      status: 'active',
      participants: 150,
      rarity: 'legendary',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Search highlights',
    example: {
      title: ['Summer <em>NFT</em> Campaign'],
      description: ['earn <em>legendary</em> NFTs'],
    },
  })
  @IsOptional()
  highlights?: Record<string, string[]>;
}

export class SearchResultsDto {
  @ApiProperty({
    description: 'Search query that was executed',
    example: 'legendary NFT campaign',
  })
  query: string;

  @ApiProperty({
    description: 'Search type',
    enum: SearchType,
    example: SearchType.GLOBAL,
  })
  type: SearchType;

  @ApiProperty({
    description: 'Search results',
    type: [SearchResultItemDto],
  })
  results: SearchResultItemDto[];

  @ApiProperty({
    description: 'Total number of results',
    example: 156,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Items per page',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 8,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether there are more results',
    example: true,
  })
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there are previous results',
    example: false,
  })
  hasPrev: boolean;

  @ApiProperty({
    description: 'Search execution time in milliseconds',
    example: 45,
  })
  executionTime: number;

  @ApiPropertyOptional({
    description: 'Search facets',
    type: [SearchFacetDto],
  })
  @IsOptional()
  facets?: SearchFacetDto[];

  @ApiPropertyOptional({
    description: 'Search suggestions',
    type: [SearchSuggestionDto],
  })
  @IsOptional()
  suggestions?: SearchSuggestionDto[];

  @ApiPropertyOptional({
    description: 'Search analytics data',
    example: {
      popularQueries: ['NFT', 'legendary', 'campaign'],
      relatedSearches: ['rare NFT', 'epic campaign'],
      searchTrends: { thisWeek: 245, lastWeek: 198 },
    },
  })
  @IsOptional()
  analytics?: Record<string, any>;

  @ApiProperty({
    description: 'Search timestamp',
    example: '2025-06-04T12:30:00Z',
  })
  timestamp: string;
}

export class SearchAnalyticsDto {
  @ApiProperty({
    description: 'Search query',
    example: 'legendary NFT',
  })
  query: string;

  @ApiProperty({
    description: 'Search type',
    enum: SearchType,
    example: SearchType.NFTS,
  })
  type: SearchType;

  @ApiProperty({
    description: 'Number of results returned',
    example: 42,
  })
  resultCount: number;

  @ApiProperty({
    description: 'Search execution time in milliseconds',
    example: 67,
  })
  executionTime: number;

  @ApiProperty({
    description: 'User ID who performed the search',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'Search timestamp',
    example: '2025-06-04T12:30:00Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Filters applied',
    type: [SearchFilterDto],
  })
  @IsOptional()
  filters?: SearchFilterDto[];

  @ApiPropertyOptional({
    description: 'Sort criteria used',
    example: { field: 'createdAt', order: 'desc' },
  })
  @IsOptional()
  sortCriteria?: { field: string; order: string };

  @ApiPropertyOptional({
    description: 'User clicked on result',
    example: true,
  })
  @IsOptional()
  hasClick?: boolean;

  @ApiPropertyOptional({
    description: 'Position of clicked result',
    example: 3,
  })
  @IsOptional()
  clickPosition?: number;

  @ApiPropertyOptional({
    description: 'Search session ID',
    example: 'session_456',
  })
  @IsOptional()
  sessionId?: string;
}
