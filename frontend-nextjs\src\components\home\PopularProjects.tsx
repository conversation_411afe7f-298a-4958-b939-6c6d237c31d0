'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  <PERSON>Stack,
  Button,
  SimpleGrid,
  Badge
} from '@chakra-ui/react'
import Link from 'next/link'

export default function PopularProjects() {
  // Mock data - will be replaced with real API data
  const featuredProjects = [
    {
      id: 1,
      name: "DeFi Protocol X",
      description: "Revolutionary DeFi platform with yield farming",
      participants: 1250,
      nftsMinted: 3400,
      category: "DeFi",
      status: "Active"
    },
    {
      id: 2,
      name: "GameFi Universe", 
      description: "Play-to-earn gaming ecosystem",
      participants: 890,
      nftsMinted: 2100,
      category: "Gaming",
      status: "Active"
    },
    {
      id: 3,
      name: "NFT Marketplace Pro",
      description: "Next-gen NFT trading platform", 
      participants: 2100,
      nftsMinted: 5600,
      category: "NFT",
      status: "Active"
    }
  ]

  return (
    <Box bg="gray.50" py={20}>
      <Container maxW="container.xl">
        <VStack gap={12}>
          <VStack gap={4} textAlign="center">
            <Heading as="h2" size="xl">Popular Projects</Heading>
            <Text fontSize="lg" color="gray.600">
              Join trending Web3 campaigns and earn exclusive NFTs
            </Text>
          </VStack>

          <SimpleGrid columns={{ base: 1, md: 3 }} gap={6} w="full">
            {featuredProjects.map((project) => (
              <Box 
                key={project.id}
                bg="white" 
                p={6} 
                borderRadius="lg" 
                boxShadow="md"
                _hover={{ transform: "translateY(-2px)" }}
                transition="all 0.2s"
              >
                <VStack gap={4} align="start">
                  <HStack justify="space-between" w="full">
                    <Badge colorScheme="green">{project.status}</Badge>
                    <Badge variant="outline">{project.category}</Badge>
                  </HStack>
                  
                  <Heading as="h3" size="md">{project.name}</Heading>
                  <Text color="gray.600" fontSize="sm">{project.description}</Text>
                  
                  <HStack gap={4} fontSize="sm">
                    <Text><strong>{project.participants}</strong> users</Text>
                    <Text><strong>{project.nftsMinted}</strong> NFTs</Text>
                  </HStack>
                  
                  <Button colorScheme="blue" size="sm" w="full">
                    Join Campaign
                  </Button>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>

          <Link href="/projects">
            <Button size="lg" variant="outline" colorScheme="blue">
              View All Projects
            </Button>
          </Link>
        </VStack>
      </Container>
    </Box>
  )
}
