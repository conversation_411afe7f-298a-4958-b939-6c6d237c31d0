import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface AuditLogData {
  entityType: string;
  entityId: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'READ' | 'PROFILE_COMPLETED' | 'SOCIAL_ACCOUNT_LINKED' | 'NOTIFICATION_PREFERENCES_UPDATED';
  oldValues?: any;
  newValues?: any;
  changedFields?: string[];
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  reason?: string;
  approvedBy?: string;
  complianceFlags?: string[];
  correlationId?: string;
  metadata?: any;
}

export interface AuditQueryOptions {
  entityType?: string;
  entityId?: string;
  action?: string;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(private readonly prisma: PrismaService) {}

  async logAction(auditData: AuditLogData): Promise<void> {
    try {
      this.logger.debug(`Creating audit log for ${auditData.action} on ${auditData.entityType}:${auditData.entityId}`, {
        correlationId: auditData.correlationId
      });

      await this.prisma.auditLog.create({
        data: {
          entityType: auditData.entityType,
          entityId: auditData.entityId,
          action: auditData.action,
          oldValues: auditData.oldValues || null,
          newValues: auditData.newValues || null,
          changedFields: auditData.changedFields || [],
          userId: auditData.userId,
          sessionId: auditData.sessionId,
          ipAddress: auditData.ipAddress,
          userAgent: auditData.userAgent,
          reason: auditData.reason,
          approvedBy: auditData.approvedBy,
          complianceFlags: auditData.complianceFlags || [],
          metadata: auditData.metadata || null,
        },
      });

      this.logger.debug(`Audit log created successfully`, {
        entityType: auditData.entityType,
        entityId: auditData.entityId,
        action: auditData.action,
        correlationId: auditData.correlationId
      });

    } catch (error) {
      this.logger.error(`Failed to create audit log: ${error.message}`, {
        auditData,
        error: error.stack
      });
      
      // Don't throw error to avoid breaking main operations
      // Audit logging should be resilient
    }
  }

  async getAuditLogs(options: AuditQueryOptions = {}): Promise<any[]> {
    try {
      const {
        entityType,
        entityId,
        action,
        userId,
        startDate,
        endDate,
        limit = 100,
        offset = 0
      } = options;

      // Build where conditions
      const where: any = {};
      
      if (entityType) where.entityType = entityType;
      if (entityId) where.entityId = entityId;
      if (action) where.action = action;
      if (userId) where.userId = userId;
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = startDate;
        if (endDate) where.createdAt.lte = endDate;
      }

      const auditLogs = await this.prisma.auditLog.findMany({
        where,
        take: limit,
        skip: offset,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
            }
          }
        }
      });

      this.logger.debug(`Retrieved ${auditLogs.length} audit logs`, { options });

      return auditLogs;

    } catch (error) {
      this.logger.error(`Failed to get audit logs: ${error.message}`, {
        options,
        error: error.stack
      });
      
      throw error;
    }
  }

  async getAuditTrail(entityType: string, entityId: string): Promise<any[]> {
    try {
      this.logger.debug(`Getting audit trail for ${entityType}:${entityId}`);

      const auditTrail = await this.prisma.auditLog.findMany({
        where: {
          entityType,
          entityId,
        },
        orderBy: { createdAt: 'asc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
            }
          }
        }
      });

      this.logger.debug(`Retrieved audit trail with ${auditTrail.length} entries`, {
        entityType,
        entityId
      });

      return auditTrail;

    } catch (error) {
      this.logger.error(`Failed to get audit trail: ${error.message}`, {
        entityType,
        entityId,
        error: error.stack
      });
      
      throw error;
    }
  }

  async getComplianceReport(startDate: Date, endDate: Date): Promise<any> {
    try {
      this.logger.debug(`Generating compliance report from ${startDate} to ${endDate}`);

      const [totalActions, actionsByType, userActions, complianceFlags] = await Promise.all([
        // Total actions count
        this.prisma.auditLog.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            }
          }
        }),

        // Actions by type
        this.prisma.auditLog.groupBy({
          by: ['action'],
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            }
          },
          _count: {
            action: true,
          }
        }),

        // Actions by user
        this.prisma.auditLog.groupBy({
          by: ['userId'],
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
            userId: {
              not: null,
            }
          },
          _count: {
            userId: true,
          }
        }),

        // Compliance flags
        this.prisma.auditLog.findMany({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
            complianceFlags: {
              isEmpty: false,
            }
          },
          select: {
            id: true,
            entityType: true,
            entityId: true,
            action: true,
            complianceFlags: true,
            createdAt: true,
          }
        })
      ]);

      const report = {
        period: {
          startDate,
          endDate,
        },
        summary: {
          totalActions,
          actionsByType: actionsByType.reduce((acc, item) => {
            acc[item.action] = item._count.action;
            return acc;
          }, {}),
          activeUsers: userActions.length,
          complianceIssues: complianceFlags.length,
        },
        details: {
          userActions,
          complianceFlags,
        }
      };

      this.logger.debug(`Compliance report generated successfully`, {
        totalActions,
        period: { startDate, endDate }
      });

      return report;

    } catch (error) {
      this.logger.error(`Failed to generate compliance report: ${error.message}`, {
        startDate,
        endDate,
        error: error.stack
      });
      
      throw error;
    }
  }
}
