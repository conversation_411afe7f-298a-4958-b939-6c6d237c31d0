<!DOCTYPE html>
<html>
<head>
    <title>User ID Mismatch Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 User ID Mismatch Debug Tool</h1>
    
    <div class="section">
        <h3>Debug User ID Issues</h3>
        <button onclick="debugUserIds()">Debug User ID Mismatch</button>
        <div id="debugResult"></div>
    </div>

    <script>
        function debugUserIds() {
            const result = document.getElementById('debugResult');
            
            // Check auth user
            const authData = localStorage.getItem('auth_user');
            const nftData = localStorage.getItem('user_nfts');
            
            let output = '<h4>🔍 User ID Analysis:</h4>';
            
            if (!authData) {
                output += '<div class="error">❌ No auth_user found</div>';
            } else {
                const user = JSON.parse(authData);
                output += `<div class="success">✅ Auth User Found</div>`;
                output += `<pre>Auth User ID: "${user.id}"
Auth User Type: ${typeof user.id}
Auth Username: "${user.username}"</pre>`;
            }
            
            if (!nftData) {
                output += '<div class="error">❌ No user_nfts found</div>';
            } else {
                const nfts = JSON.parse(nftData);
                output += `<div class="success">✅ NFT Data Found (${nfts.length} total)</div>`;
                
                // Show unique user IDs in NFT data
                const uniqueUserIds = [...new Set(nfts.map(nft => nft.userId))];
                output += `<pre>Unique User IDs in NFTs: ${uniqueUserIds.length}`;
                uniqueUserIds.forEach((id, index) => {
                    output += `
${index + 1}. "${id}" (${typeof id})`;
                });
                output += `</pre>`;
                
                // Show sample NFTs
                output += `<h4>Sample NFTs:</h4>`;
                nfts.slice(0, 3).forEach((nft, index) => {
                    output += `<pre>NFT ${index + 1}:
  userId: "${nft.userId}" (${typeof nft.userId})
  campaignId: "${nft.campaignId}"
  name: "${nft.name}"
  createdAt: "${nft.createdAt}"</pre>`;
                });
            }
            
            result.innerHTML = output;
        }
    </script>
</body>
</html>
