#!/usr/bin/env node

/**
 * Quick Service Status Checker
 * Checks if all required services are running
 */

const http = require('http');

const services = [
  { name: 'Frontend', url: 'http://localhost:3000', path: '/' },
  { name: 'API Gateway', url: 'http://localhost:3010', path: '/api' },
  { name: 'User Service', url: 'http://localhost:3011', path: '/api' },
  { name: 'Profile Analysis', url: 'http://localhost:3002', path: '/api' },
  { name: 'NFT Generation', url: 'http://localhost:3003', path: '/api' },
  { name: 'Mock Twitter', url: 'http://localhost:3020', path: '/api' }
];

async function checkService(service) {
  return new Promise((resolve) => {
    const url = new URL(service.path, service.url);
    
    const req = http.get(url, (res) => {
      resolve({
        name: service.name,
        status: 'RUNNING',
        code: res.statusCode,
        url: service.url
      });
    });
    
    req.on('error', () => {
      resolve({
        name: service.name,
        status: 'DOWN',
        code: 'ERROR',
        url: service.url
      });
    });
    
    req.setTimeout(2000, () => {
      req.destroy();
      resolve({
        name: service.name,
        status: 'TIMEOUT',
        code: 'TIMEOUT',
        url: service.url
      });
    });
  });
}

async function checkAllServices() {
  console.log('🔍 Checking Service Status...\n');
  
  const results = await Promise.all(services.map(checkService));
  
  console.log('📊 Service Status Report:');
  console.log('═'.repeat(50));
  
  results.forEach(result => {
    const status = result.status === 'RUNNING' ? '✅' : '❌';
    const code = result.code === 'ERROR' ? 'CONN_ERR' : 
                 result.code === 'TIMEOUT' ? 'TIMEOUT' : 
                 result.code;
    
    console.log(`${status} ${result.name.padEnd(20)} ${result.url.padEnd(25)} [${code}]`);
  });
  
  const runningCount = results.filter(r => r.status === 'RUNNING').length;
  const totalCount = results.length;
  
  console.log('═'.repeat(50));
  console.log(`📈 Services Running: ${runningCount}/${totalCount}`);
  
  if (runningCount === totalCount) {
    console.log('🎉 All services are running!');
  } else {
    console.log('⚠️ Some services are not responding');
  }
  
  console.log('\n🧪 Test Twitter Login:');
  console.log('1. Go to: http://localhost:3000/auth/login');
  console.log('2. Click the Twitter button');
  console.log('3. Should redirect through OAuth flow');
  
  console.log('\n🎨 Test NFT Images:');
  console.log('1. Go to: http://localhost:3000/dashboard');
  console.log('2. Analyze a profile and generate NFT');
  console.log('3. Should show actual SVG images');
}

checkAllServices().catch(console.error);
