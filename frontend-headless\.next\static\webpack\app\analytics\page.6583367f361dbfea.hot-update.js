"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/simple-analytics.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/simple-analytics.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SimpleAnalytics() {\n    // Mock data\n    const gainersData = [\n        {\n            id: '1',\n            name: '@alice_crypto',\n            score: 85,\n            change: '+12%',\n            rarity: 'Epic'\n        },\n        {\n            id: '2',\n            name: '@bob_nft',\n            score: 78,\n            change: '+8%',\n            rarity: 'Rare'\n        },\n        {\n            id: '3',\n            name: '@charlie_art',\n            score: 92,\n            change: '+15%',\n            rarity: 'Legendary'\n        }\n    ];\n    const collectionsData = [\n        {\n            id: '1',\n            name: 'CryptoArt',\n            value: 45.2,\n            change: '+5%',\n            color: '#3B82F6'\n        },\n        {\n            id: '2',\n            name: 'DigitalPunks',\n            value: 38.7,\n            change: '-2%',\n            color: '#8B5CF6'\n        },\n        {\n            id: '3',\n            name: 'MetaVerse',\n            value: 52.1,\n            change: '+8%',\n            color: '#10B981'\n        }\n    ];\n    const transactionsData = [\n        {\n            id: '1',\n            user: 'alice_crypto',\n            nft: 'Digital Art #123',\n            type: 'generation',\n            value: 0.5,\n            time: '5m ago'\n        },\n        {\n            id: '2',\n            user: 'bob_nft',\n            nft: 'Punk Avatar #456',\n            type: 'share',\n            value: 0.3,\n            time: '12m ago'\n        },\n        {\n            id: '3',\n            user: 'charlie_art',\n            nft: 'Meta World #789',\n            type: 'favorite',\n            value: 0.8,\n            time: '18m ago'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Yaps Kaito Analytics Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Platform insights with bubble maps and performance tracking\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Top Gainers & Losers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Users' final score status and performance tracking\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.TrendingUpIcon, {\n                                                className: \"h-5 w-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Top Gainers (\",\n                                            gainersData.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: gainersData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 65,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: item.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                                lineNumber: 69,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                children: item.rarity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                                lineNumber: 70,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                        lineNumber: 68,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Score: \",\n                                                                            item.score\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                        lineNumber: 74,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.TrendingUpIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 79,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: item.change\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 80,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Performance Bubble Map\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: gainersData.map((item, index)=>{\n                                                const size = 60 + item.score / 100 * 60; // 60-120px\n                                                const x = index % 2 * 40 + 20;\n                                                const y = Math.floor(index / 2) * 30 + 20;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute cursor-pointer transition-all duration-300 hover:scale-110\",\n                                                    style: {\n                                                        left: \"\".concat(x, \"%\"),\n                                                        top: \"\".concat(y, \"%\"),\n                                                        width: \"\".concat(size, \"px\"),\n                                                        height: \"\".concat(size, \"px\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: item.name.slice(0, 6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-bold\",\n                                                                    children: item.score\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 112,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, item.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Collection Market Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Project collections growth and decline tracking\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full\",\n                            children: collectionsData.map((collection, index)=>{\n                                const size = 80 + collection.value / 60 * 60; // 80-140px\n                                const x = index % 2 * 45 + 15;\n                                const y = Math.floor(index / 2) * 40 + 15;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute cursor-pointer transition-all duration-300 hover:scale-105\",\n                                    style: {\n                                        left: \"\".concat(x, \"%\"),\n                                        top: \"\".concat(y, \"%\"),\n                                        width: \"\".concat(size, \"px\"),\n                                        height: \"\".concat(size, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg\",\n                                        style: {\n                                            backgroundColor: collection.color\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mb-1\",\n                                                    children: collection.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold\",\n                                                    children: [\n                                                        collection.value,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: collection.change\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, this)\n                                }, collection.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Recent Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Latest NFT activities and interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Real-time updates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactionsData.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center bg-blue-100 text-blue-800\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold\",\n                                                    children: transaction.type === 'generation' ? 'GEN' : transaction.type === 'share' ? 'SHR' : 'FAV'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: transaction.nft\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"User: @\",\n                                                            transaction.user,\n                                                            \" • \",\n                                                            transaction.type,\n                                                            \" • \",\n                                                            transaction.time\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: [\n                                                        transaction.value,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = SimpleAnalytics;\nvar _c;\n$RefreshReg$(_c, \"SimpleAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/simple-analytics.tsx\n"));

/***/ })

});