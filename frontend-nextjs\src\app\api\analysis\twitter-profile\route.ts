import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Get authorization header from the request
    const authHeader = request.headers.get('authorization')
    
    // Prepare headers for the backend request
    const headers: any = {
      'Content-Type': 'application/json',
    }
    
    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    console.log('🔄 Proxying Twitter analysis request to API Gateway...')
    
    // Forward request to API Gateway
    const response = await fetch('http://localhost:3010/api/analysis/twitter-profile', {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ API Gateway error:', response.status, errorText)
      return NextResponse.json(
        { error: `API Gateway error: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('✅ Twitter analysis response received')
    
    return NextResponse.json(data)
  } catch (error: any) {
    console.error('❌ Twitter analysis proxy error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
