# Day 8: Advanced Search & Filtering - Testing Guide

## 🔍 **SEARCH FUNCTIONALITY TESTING**

### **📋 Test Overview**
Testing comprehensive search and filtering capabilities across all platform entities.

### **🚀 Available Search Endpoints**

#### **1. Global Search**
```bash
# Global search across all entities
curl -X GET "http://localhost:3011/api/search?query=test&type=global&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **2. User Search**
```bash
# Search users with filters
curl -X GET "http://localhost:3011/api/search/users?query=john&page=1&limit=10&sortBy=username&sortOrder=asc&verified=true&includeFacets=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **3. Campaign Search**
```bash
# Search campaigns with filters
curl -X GET "http://localhost:3011/api/search/campaigns?query=summer&status=active&type=social&sortBy=createdAt&sortOrder=desc&includeFacets=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **4. NFT Search**
```bash
# Search NFTs with rarity and value filters
curl -X GET "http://localhost:3011/api/search/nfts?query=legendary&rarity=legendary&minValue=100&maxValue=1000&sortBy=estimatedValue&sortOrder=desc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **5. Marketplace Search**
```bash
# Search marketplace listings with price filters
curl -X GET "http://localhost:3011/api/search/marketplace?query=rare&minPrice=50&maxPrice=500&currency=ETH&rarity=rare&sortBy=price&sortOrder=asc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **6. Autocomplete**
```bash
# Get autocomplete suggestions
curl -X GET "http://localhost:3011/api/search/autocomplete?query=leg&type=global&limit=10&includePopular=true&includeRecent=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **7. Search Suggestions (Public)**
```bash
# Get search suggestions (no auth required)
curl -X GET "http://localhost:3011/api/search/suggestions/nft?type=global&limit=5"
```

#### **8. Popular Searches (Public)**
```bash
# Get popular searches (no auth required)
curl -X GET "http://localhost:3011/api/search/popular?type=global&limit=10"
```

#### **9. Search Analytics**
```bash
# Get search analytics
curl -X GET "http://localhost:3011/api/search/analytics?timeframe=7d&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **🧪 Test Scenarios**

#### **Test 1: Basic Global Search**
```bash
# Test global search functionality
curl -X GET "http://localhost:3011/api/search?query=test&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "query": "test",
    "type": "global",
    "results": [],
    "totalCount": 0,
    "page": 1,
    "limit": 20,
    "totalPages": 0,
    "hasNext": false,
    "hasPrev": false,
    "executionTime": 45,
    "timestamp": "2025-06-04T12:30:00Z"
  }
}
```

#### **Test 2: User Search with Filters**
```bash
# Test user search with verification filter
curl -X GET "http://localhost:3011/api/search/users?query=admin&verified=true&sortBy=username&sortOrder=asc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 3: Campaign Search with Status Filter**
```bash
# Test campaign search with active status
curl -X GET "http://localhost:3011/api/search/campaigns?status=active&sortBy=createdAt&sortOrder=desc&includeFacets=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 4: NFT Search with Rarity Filter**
```bash
# Test NFT search with rarity filter
curl -X GET "http://localhost:3011/api/search/nfts?rarity=legendary&sortBy=engagementScore&sortOrder=desc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 5: Marketplace Search with Price Range**
```bash
# Test marketplace search with price range
curl -X GET "http://localhost:3011/api/search/marketplace?minPrice=10&maxPrice=100&currency=ETH&sortBy=price&sortOrder=asc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 6: Autocomplete Functionality**
```bash
# Test autocomplete for partial queries
curl -X GET "http://localhost:3011/api/search/autocomplete?query=leg&type=nfts&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 7: Public Search Suggestions**
```bash
# Test public search suggestions (no auth)
curl -X GET "http://localhost:3011/api/search/suggestions/campaign?type=campaigns&limit=5"
```

#### **Test 8: Popular Searches**
```bash
# Test popular searches endpoint
curl -X GET "http://localhost:3011/api/search/popular?type=global&limit=10"
```

#### **Test 9: Search Analytics**
```bash
# Test search analytics
curl -X GET "http://localhost:3011/api/search/analytics?timeframe=30d" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 10: Advanced Filtering**
```bash
# Test complex search with multiple filters
curl -X GET "http://localhost:3011/api/search?query=nft&type=global&page=1&limit=10&includeFacets=true&includeSuggestions=true&includeAnalytics=false&dateFrom=2025-01-01T00:00:00Z&dateTo=2025-12-31T23:59:59Z" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **📊 Performance Testing**

#### **Test 11: Large Result Set Pagination**
```bash
# Test pagination with large result sets
for page in {1..5}; do
  echo "Testing page $page..."
  curl -X GET "http://localhost:3011/api/search?type=global&page=$page&limit=50" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    -w "Time: %{time_total}s\n"
done
```

#### **Test 12: Search Performance Benchmarking**
```bash
# Benchmark search performance
echo "Benchmarking search performance..."
time curl -X GET "http://localhost:3011/api/search?query=test&type=global&limit=100" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -s -o /dev/null
```

### **🔒 Security Testing**

#### **Test 13: Authentication Required**
```bash
# Test that search requires authentication
curl -X GET "http://localhost:3011/api/search?query=test&type=global" \
  -H "Content-Type: application/json"
# Should return 401 Unauthorized
```

#### **Test 14: Public Endpoints Work Without Auth**
```bash
# Test public endpoints work without authentication
curl -X GET "http://localhost:3011/api/search/suggestions/test?type=global"
curl -X GET "http://localhost:3011/api/search/popular?type=global"
```

### **🧩 Integration Testing**

#### **Test 15: Search Analytics Logging**
```bash
# Perform searches and verify analytics are logged
curl -X GET "http://localhost:3011/api/search?query=analytics_test&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Check if analytics were logged
curl -X GET "http://localhost:3011/api/search/analytics?timeframe=1d" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 16: Cache Performance**
```bash
# Test search caching by running same query twice
echo "First search (cache miss):"
time curl -X GET "http://localhost:3011/api/search?query=cache_test&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -s -o /dev/null

echo "Second search (cache hit):"
time curl -X GET "http://localhost:3011/api/search?query=cache_test&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -s -o /dev/null
```

### **📈 Expected Results**

#### **✅ Success Criteria:**
1. **All search endpoints respond with 200 status**
2. **Search results are properly formatted**
3. **Pagination works correctly**
4. **Filters apply correctly**
5. **Sorting works as expected**
6. **Autocomplete provides relevant suggestions**
7. **Public endpoints work without authentication**
8. **Protected endpoints require authentication**
9. **Search analytics are logged**
10. **Cache improves performance on repeated queries**

#### **📊 Performance Benchmarks:**
- **Search Response Time:** < 100ms for simple queries
- **Complex Search:** < 500ms for filtered queries
- **Autocomplete:** < 50ms response time
- **Cache Hit:** < 10ms response time
- **Large Result Sets:** < 1s for 100+ results

#### **🔍 Search Features Verified:**
- ✅ **Global Search** - Search across all entities
- ✅ **Entity-Specific Search** - Users, Campaigns, NFTs, Marketplace
- ✅ **Advanced Filtering** - Multiple filter criteria
- ✅ **Sorting** - Multiple sort options
- ✅ **Pagination** - Efficient result pagination
- ✅ **Autocomplete** - Real-time search suggestions
- ✅ **Search Analytics** - Usage tracking and insights
- ✅ **Faceted Search** - Category-based filtering
- ✅ **Performance Optimization** - Caching and indexing

### **🚨 Error Scenarios to Test**

#### **Test 17: Invalid Parameters**
```bash
# Test invalid search type
curl -X GET "http://localhost:3011/api/search?query=test&type=invalid" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test invalid sort order
curl -X GET "http://localhost:3011/api/search?query=test&type=global&sortOrder=invalid" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test invalid page number
curl -X GET "http://localhost:3011/api/search?query=test&type=global&page=0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### **Test 18: Large Query Strings**
```bash
# Test very long search query
LONG_QUERY=$(printf 'a%.0s' {1..1000})
curl -X GET "http://localhost:3011/api/search?query=$LONG_QUERY&type=global" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **📝 Test Results Documentation**

Create a test results file to track outcomes:

```bash
# Create test results file
echo "# Day 8 Search Testing Results - $(date)" > search_test_results.md
echo "" >> search_test_results.md
echo "## Test Execution Summary" >> search_test_results.md
echo "- **Date:** $(date)" >> search_test_results.md
echo "- **Service:** User Service with Advanced Search" >> search_test_results.md
echo "- **Version:** Day 8 Implementation" >> search_test_results.md
echo "" >> search_test_results.md
```

This comprehensive testing guide covers all aspects of the advanced search and filtering functionality implemented in Day 8.
