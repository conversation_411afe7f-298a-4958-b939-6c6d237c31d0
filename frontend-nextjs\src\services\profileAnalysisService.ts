import axios from 'axios';
import { API_CONFIG } from '@/config/api';

export interface TwitterAnalysisData {
  id: string;
  twitterHandle: string;
  userId: string;
  status: 'pending' | 'completed' | 'failed';
  score: number;
  analysisData: {
    profile: {
      followerCount: number;
      followingCount: number;
      tweetCount: number;
      engagementRate: number;
      accountAge: number;
      isVerified: boolean;
      hasProfileImage: boolean;
      hasBio: boolean;
    };
    metrics: {
      contentQuality: number;
      activityLevel: number;
      influenceScore: number;
      authenticity: number;
      engagement: number;
    };
    breakdown: {
      followerScore: number;
      engagementScore: number;
      contentScore: number;
      activityScore: number;
      profileScore: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface AnalyzeTwitterProfileData {
  twitterHandle: string;
  userId?: string;
}

export interface AnalysisHistory {
  analyses: TwitterAnalysisData[];
  total: number;
  limit: number;
  offset: number;
}

export class ProfileAnalysisService {
  private baseURL = API_CONFIG.BASE_URL;

  /**
   * Analyze a Twitter profile
   */
  async analyzeTwitterProfile(data: AnalyzeTwitterProfileData): Promise<TwitterAnalysisData> {
    try {
      console.log('🔍 Starting Twitter profile analysis:', data.twitterHandle);

      const response = await axios.post(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.ANALYSIS.ANALYZE}`,
        data
      );

      console.log('✅ Twitter analysis completed:', response.data);

      // Handle the actual API response structure
      const analysisData = response.data.data || response.data;

      // Transform to match our interface if needed
      if (analysisData.results) {
        // Convert backend format to frontend format
        const transformedData = {
          ...analysisData,
          score: analysisData.results.nftRecommendation?.score || 50,
          analysisData: {
            profile: {
              followerCount: analysisData.results.profile?.followerCount || 0,
              followingCount: analysisData.results.profile?.followingCount || 0,
              tweetCount: analysisData.results.profile?.tweetCount || 0,
              engagementRate: analysisData.results.metrics?.engagementRate || 0,
              accountAge: analysisData.results.profile?.accountAge || 0,
              isVerified: false,
              hasProfileImage: true,
              hasBio: true,
            },
            metrics: {
              contentQuality: analysisData.results.nftRecommendation?.score || 50,
              activityLevel: Math.min(100, (analysisData.results.profile?.tweetCount || 0) / 100),
              influenceScore: Math.min(100, (analysisData.results.profile?.followerCount || 0) / 1000),
              authenticity: 75, // Default value
              engagement: analysisData.results.metrics?.engagementRate || 0,
            },
            breakdown: {
              followerScore: Math.min(100, (analysisData.results.profile?.followerCount || 0) / 1000),
              engagementScore: analysisData.results.metrics?.engagementRate || 0,
              contentScore: analysisData.results.nftRecommendation?.score || 50,
              activityScore: Math.min(100, (analysisData.results.profile?.tweetCount || 0) / 100),
              profileScore: 75, // Default value
            },
          },
        };
        return transformedData;
      }

      return analysisData;
    } catch (error: any) {
      console.error('❌ Twitter analysis error:', error);

      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || 'Invalid Twitter username';
        throw new Error(`Analysis Error: ${Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage}`);
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in.');
      } else if (error.response?.status === 500) {
        throw new Error('Analysis service is temporarily unavailable. Please try again later.');
      } else {
        throw new Error(error.message || 'Failed to analyze Twitter profile. Please try again.');
      }
    }
  }

  /**
   * Get analysis results by ID
   */
  async getAnalysisResults(analysisId: string): Promise<TwitterAnalysisData> {
    try {
      const response = await axios.get(
        `${this.baseURL}/api/analysis/results/${analysisId}`
      );
      return response.data;
    } catch (error) {
      console.error('Get analysis results error:', error);
      throw error;
    }
  }

  /**
   * Get user's analysis history
   */
  async getAnalysisHistory(limit: number = 10, offset: number = 0): Promise<AnalysisHistory> {
    try {
      const response = await axios.get(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.ANALYSIS.HISTORY}`,
        {
          params: { limit, offset }
        }
      );
      return response.data.data || response.data;
    } catch (error) {
      console.error('Get analysis history error:', error);
      throw error;
    }
  }

  /**
   * Get latest analysis for a user
   */
  async getLatestAnalysis(): Promise<TwitterAnalysisData | null> {
    try {
      const history = await this.getAnalysisHistory(1, 0);
      return history.analyses.length > 0 ? history.analyses[0] : null;
    } catch (error) {
      console.error('Get latest analysis error:', error);
      return null;
    }
  }

  /**
   * Calculate rarity based on score
   */
  calculateRarity(score: number): string {
    if (score >= 80) return 'Legendary';
    if (score >= 60) return 'Rare';
    if (score >= 40) return 'Uncommon';
    return 'Common';
  }

  /**
   * Get score color based on value
   */
  getScoreColor(score: number): string {
    if (score >= 80) return 'purple.500';
    if (score >= 60) return 'blue.500';
    if (score >= 40) return 'green.500';
    return 'gray.500';
  }

  /**
   * Format score for display
   */
  formatScore(score: number): string {
    return Math.round(score).toString();
  }
}

export const profileAnalysisService = new ProfileAnalysisService();
