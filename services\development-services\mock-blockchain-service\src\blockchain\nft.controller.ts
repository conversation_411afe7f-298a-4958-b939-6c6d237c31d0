import { <PERSON>, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BlockchainService } from './blockchain.service';
import { NFTMintRequest } from '../interfaces/blockchain.interfaces';

@ApiTags('nft')
@Controller('nft')
export class NFTController {
  constructor(private readonly blockchainService: BlockchainService) {}

  @Post('mint')
  @ApiOperation({ summary: 'Mint new NFT' })
  @ApiResponse({ status: 201, description: 'NFT minted successfully' })
  async mintNFT(@Body() request: NFTMintRequest) {
    const result = await this.blockchainService.mintNFT(request);
    return {
      status: result.success ? 'success' : 'error',
      data: result,
      message: result.success ? 'NFT minted successfully' : 'Failed to mint NFT'
    };
  }
}
