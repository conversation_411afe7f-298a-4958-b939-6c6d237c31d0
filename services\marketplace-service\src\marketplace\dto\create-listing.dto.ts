import { IsS<PERSON>, IsNotEmpty, IsOptional, IsNumber, IsEnum, IsArray, IsBoolean, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ListingType {
  FIXED = 'fixed',
  AUCTION = 'auction',
  OFFER = 'offer',
}

export enum SupportedCurrency {
  ETH = 'ETH',
  MATIC = 'MATIC',
  USDC = 'USDC',
  USDT = 'USDT',
}

export class CreateListingDto {
  @ApiProperty({ description: 'NFT ID from NFT Generation Service' })
  @IsString()
  @IsNotEmpty()
  nftId: string;

  @ApiProperty({ description: 'User ID of the seller' })
  @IsString()
  @IsNotEmpty()
  sellerId: string;

  @ApiProperty({ description: 'Campaign ID associated with the NFT' })
  @IsString()
  @IsNotEmpty()
  campaignId: string;

  @ApiProperty({ description: 'Unique token ID for the NFT' })
  @IsString()
  @IsNotEmpty()
  tokenId: string;

  @ApiProperty({ description: 'Smart contract address' })
  @IsString()
  @IsNotEmpty()
  contractAddress: string;

  @ApiProperty({ description: 'Blockchain network' })
  @IsString()
  @IsNotEmpty()
  blockchain: string;

  @ApiProperty({ description: 'Listing price', minimum: 0.001 })
  @IsNumber()
  @Min(0.001)
  @Max(1000)
  price: number;

  @ApiProperty({ description: 'Currency for the listing', enum: SupportedCurrency })
  @IsEnum(SupportedCurrency)
  currency: SupportedCurrency;

  @ApiProperty({ description: 'Type of listing', enum: ListingType })
  @IsEnum(ListingType)
  listingType: ListingType;

  @ApiProperty({ description: 'Title for the listing', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Description for the listing', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Image URL for the NFT', required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({ description: 'Expiration date for the listing', required: false })
  @IsOptional()
  expiresAt?: Date;

  @ApiProperty({ description: 'Whether to feature this listing', required: false })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiProperty({ description: 'Tags for the listing', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  metadata?: any;
}

export class UpdateListingDto {
  @ApiProperty({ description: 'Listing ID' })
  @IsString()
  @IsNotEmpty()
  listingId: string;

  @ApiProperty({ description: 'New price', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0.001)
  @Max(1000)
  price?: number;

  @ApiProperty({ description: 'New title', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: 'New description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'New expiration date', required: false })
  @IsOptional()
  expiresAt?: Date;

  @ApiProperty({ description: 'Update featured status', required: false })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiProperty({ description: 'Update tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class PurchaseNftDto {
  @ApiProperty({ description: 'Listing ID to purchase' })
  @IsString()
  @IsNotEmpty()
  listingId: string;

  @ApiProperty({ description: 'Buyer user ID' })
  @IsString()
  @IsNotEmpty()
  buyerId: string;

  @ApiProperty({ description: 'Buyer wallet address' })
  @IsString()
  @IsNotEmpty()
  buyerAddress: string;

  @ApiProperty({ description: 'Payment transaction hash', required: false })
  @IsOptional()
  @IsString()
  paymentTransactionHash?: string;
}
