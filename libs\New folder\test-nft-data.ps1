# NFT Data E2E Test
Write-Host "=== NFT DATA E2E TEST ===" -ForegroundColor Cyan
Write-Host "Testing NFT data retrieval for frontend" -ForegroundColor Green

# Test Configuration
$nftServiceUrl = "http://localhost:3003"
$testUserId = "test-user-123"

Write-Host "`nTesting NFT data API..." -ForegroundColor Yellow

# Test 1: Get User NFTs
try {
    $nfts = Invoke-RestMethod -Uri "$nftServiceUrl/api/nft-generation/user/$testUserId" -Method Get -TimeoutSec 10
    Write-Host "✅ NFTs retrieved successfully!" -ForegroundColor Green
    Write-Host "NFT count: $($nfts.Count)" -ForegroundColor Green
    
    if ($nfts.Count -gt 0) {
        Write-Host "First NFT: $($nfts[0].name)" -ForegroundColor Green
        Write-Host "NFT rarity: $($nfts[0].rarity)" -ForegroundColor Green
        Write-Host "NFT score: $($nfts[0].currentScore)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ NFT retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 NFT Data E2E Test COMPLETED!" -ForegroundColor Green
