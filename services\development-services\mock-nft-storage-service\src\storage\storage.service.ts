import { Injectable } from '@nestjs/common';
import { StorageUploadRequest, StorageUploadResult, IPFSFile, NFTMetadata } from '../interfaces/storage.interfaces';

@Injectable()
export class StorageService {
  private mockStorage: Map<string, any> = new Map();
  private mockIPFS: Map<string, IPFSFile> = new Map();

  constructor() {
    console.log('📦 Mock NFT Storage Service initialized');
    this.initializeMockData();
  }

  private initializeMockData() {
    // Initialize sample IPFS files
    const sampleFile: IPFSFile = {
      hash: 'QmSampleHash123',
      name: 'sample-nft.json',
      size: 1024,
      type: 'application/json',
      url: 'https://mock-ipfs.io/ipfs/QmSampleHash123',
      uploadedAt: new Date().toISOString()
    };
    this.mockIPFS.set('QmSampleHash123', sampleFile);
  }

  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };
  }

  // Upload metadata to mock IPFS
  async uploadMetadata(metadata: NFTMetadata): Promise<StorageUploadResult> {
    const hash = 'Qm' + Math.random().toString(36).substr(2, 44);
    const metadataUri = `https://mock-ipfs.io/ipfs/${hash}`;

    this.mockStorage.set(hash, {
      hash,
      metadata,
      uploadedAt: new Date().toISOString()
    });

    return {
      success: true,
      metadataUri,
      ipfsHash: hash
    };
  }

  // Get metadata by hash
  async getMetadata(hash: string): Promise<NFTMetadata | null> {
    const stored = this.mockStorage.get(hash);
    return stored ? stored.metadata : null;
  }
}
