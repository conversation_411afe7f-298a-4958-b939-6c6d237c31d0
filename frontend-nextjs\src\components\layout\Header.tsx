'use client'

import {
  Box,
  Container,
  Flex,
  Heading,
  HStack,
  Button
} from '@chakra-ui/react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'

export default function Header() {
  const { isAuthenticated, logout } = useAuth()

  return (
    <Box bg="white" boxShadow="sm" position="sticky" top={0} zIndex={1000}>
      <Container maxW="container.xl">
        <Flex h={16} alignItems="center" justifyContent="space-between">
          <Link href="/">
            <Heading as="h1" size="lg" color="blue.600" cursor="pointer">
              Social NFT
            </Heading>
          </Link>

          <HStack gap={8}>
            <HStack gap={6} display={{ base: 'none', md: 'flex' }}>
              <Link href="/">
                <Button variant="ghost">Home</Button>
              </Link>
              <Link href="/projects">
                <Button variant="ghost">Projects</Button>
              </Link>
              <Link href="/marketplace">
                <Button variant="ghost">Marketplace</Button>
              </Link>
            </HStack>

            {isAuthenticated ? (
              <HStack gap={2}>
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm">
                    Dashboard
                  </Button>
                </Link>
                <Button onClick={logout} variant="outline" size="sm">
                  Logout
                </Button>
              </HStack>
            ) : (
              <Link href="/auth/login">
                <Button colorScheme="blue">Login</Button>
              </Link>
            )}
          </HStack>
        </Flex>
      </Container>
    </Box>
  )
}
