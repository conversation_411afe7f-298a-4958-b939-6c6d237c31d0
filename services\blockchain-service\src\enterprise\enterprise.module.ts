// Enterprise Blockchain Module - Template
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BlockchainCommandController } from './controllers/blockchain-command.controller';
import { BlockchainQueryController } from './controllers/blockchain-query.controller';
import { PrismaService } from './shared/prisma.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    })
  ],
  controllers: [
    BlockchainCommandController,
    BlockchainQueryController
  ],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
