# Database Schema & Migration Issues & Solutions Documentation

## Overview
This document details database schema and migration issues encountered during the social commerce platform development, including TypeORM configuration problems, entity relationship challenges, and schema synchronization concerns.

## Critical Issues & Solutions

### 1. **Auto-Synchronization Enabled in Production** ⭐ **CRITICAL RISK**

**Problem:** Database auto-synchronization enabled in production environment causing uncontrolled schema changes.

**Symptoms:**
- Schema changes applied automatically without migration control
- Risk of data loss during schema modifications
- No rollback capability for schema changes

**Root Cause:** 
TypeORM synchronize option set to true in production configuration.

**Code Location (Problematic):**
```yaml
# File: docker-compose.yml (Lines 86, 128)
environment:
  DB_SYNCHRONIZE: "true"  # DANGEROUS in production
```

```typescript
// File: services/user-service/src/app.module.ts (Line 41)
synchronize: configService.get<boolean>('DB_SYNCHRONIZE', true),
```

**Solution:**
```yaml
# Recommended fix - Environment-based synchronization
environment:
  DB_SYNCHRONIZE: "false"  # Disable in production
  DB_RUN_MIGRATIONS: "true"  # Use migrations instead
```

```typescript
// Add migration configuration
useFactory: (configService: ConfigService) => ({
  type: 'postgres',
  // ... other config
  synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
  migrationsRun: configService.get<boolean>('DB_RUN_MIGRATIONS', true),
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
}),
```

**Impact:** 
- ✅ Prevents accidental data loss in production
- ✅ Enables controlled schema versioning
- ✅ Allows rollback capabilities

**Status:** ⚠️ **PRODUCTION RISK** - Needs immediate attention

### 2. **Missing Migration System**

**Problem:** No migration files or migration system implemented, relying only on auto-synchronization.

**Symptoms:**
- No version control for database schema changes
- Cannot rollback schema changes
- No deployment history tracking

**Root Cause:** 
Migration system not set up, only using TypeORM auto-sync.

**Code Location:**
```bash
# Missing directory
services/user-service/migrations/  # Does not exist
```

**Solution:**
```bash
# 1. Create migrations directory
mkdir -p services/user-service/src/migrations

# 2. Generate initial migration
npm run typeorm:migration:generate -- -n InitialSchema

# 3. Add migration scripts to package.json
```

```json
{
  "scripts": {
    "typeorm": "typeorm-ts-node-commonjs",
    "typeorm:migration:generate": "npm run typeorm -- migration:generate",
    "typeorm:migration:run": "npm run typeorm -- migration:run",
    "typeorm:migration:revert": "npm run typeorm -- migration:revert"
  }
}
```

**Impact:** 
- ✅ Enables proper schema version control
- ✅ Allows safe production deployments
- ✅ Provides rollback capabilities

**Status:** ⚠️ **MISSING IMPLEMENTATION**

### 3. **Circular Reference Disabled (User ↔ Profile)**

**Problem:** User-Profile bidirectional relationship commented out to avoid circular reference issues.

**Code Location:**
```typescript
// File: services/user-service/src/authentication/entities/user.entity.ts (Lines 51-52)
// @OneToOne(() => Profile, profile => profile.user, { cascade: true })
// profile: Profile; // Temporarily disabled to avoid circular reference
```

**Root Cause:** 
TypeORM circular dependency resolution needed between User and Profile entities.

**Solution Applied:** ✅ **RESOLVED** 
- Documented in `circular-reference-issues-solutions.md`
- Proper import order and lazy loading implemented

**Status:** ✅ **RESOLVED** - See circular reference documentation

### 4. **Missing Store Service Entities**

**Problem:** Store service has no data model entities defined.

**Symptoms:**
- Empty entities directory in store service
- No database schema for store management
- Store service cannot persist data

**Code Location:**
```bash
# Empty directory
services/store-service/src/store-management/entities/  # No files
```

**Solution:**
```typescript
// Recommended Store entity structure
@Entity('stores')
export class Store {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  ownerId: string;  // Reference to User

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

**Impact:** 
- ⚠️ Store service cannot function without data model
- ⚠️ Blocks store management feature implementation

**Status:** ⚠️ **MISSING IMPLEMENTATION**

### 5. **Messaging Module Disabled**

**Problem:** Messaging module commented out in TypeORM configuration affecting microservices communication.

**Code Location:**
```typescript
// File: services/user-service/src/app.module.ts (Lines 5-6, 58-62)
// import { MessagingModule } from '@app/messaging'; // Temporarily disabled

// Messaging - Temporarily disabled for initial deployment
// MessagingModule.register({
//   rabbitmqUrl: process.env.RABBITMQ_URL || 'amqp://admin:admin@localhost:5672',
//   serviceName: 'user-service',
// }),
```

**Root Cause:** 
Circular dependency or build issues with messaging library.

**Impact:** 
- ⚠️ Incomplete microservices communication
- ⚠️ Event-driven architecture not fully functional

**Status:** ⚠️ **TEMPORARILY DISABLED**

## Working Database Features

### ✅ **Database Connection Setup**
- **Multiple Database Creation:** Automatic creation of separate databases per service
- **Health Checks:** Robust health monitoring with retry logic
- **Service Dependencies:** Proper startup sequence with health conditions
- **Network Configuration:** All services properly networked

### ✅ **Entity Relationships Working**
- **VerificationToken → User:** Proper ManyToOne relationship implemented
- **Profile → User:** One-way relationship functioning correctly
- **TypeORM Configuration:** Basic setup working for existing entities

### ✅ **Environment Configuration**
- **Consistent Variables:** Database credentials consistent across services
- **Docker Integration:** Proper containerized database setup
- **Volume Persistence:** Data persistence with postgres_data volume

## Files Modified

### TypeORM Configuration:
1. **`services/user-service/src/app.module.ts`**
   - Lines 5-6: Messaging module disabled
   - Lines 41-42: Synchronize and logging configuration
   - Lines 58-62: Messaging configuration commented out

### Docker Configuration:
1. **`docker-compose.yml`**
   - Lines 86, 128: DB_SYNCHRONIZE set to "true" (needs change)
   - Lines 11, 14: Multiple database configuration

### Database Scripts:
1. **`tools/scripts/create-multiple-postgresql-databases.sh`**
   - Automatic database creation script (working correctly)

## Recommended Migration Strategy

### Phase 1: Immediate (High Priority)
1. **Disable auto-sync in production** - Change DB_SYNCHRONIZE to false
2. **Implement migration system** - Set up TypeORM migrations
3. **Generate initial migration** - Capture current schema state

### Phase 2: Short Term (Medium Priority)
1. **Implement Store entities** - Create store data model
2. **Re-enable messaging module** - Resolve circular dependencies
3. **Add migration scripts** - Automate migration management

### Phase 3: Long Term (Low Priority)
1. **Database optimization** - Add indexes and performance tuning
2. **Advanced relationships** - Implement complex entity relationships
3. **Data seeding** - Add development data seeding system

## Testing Database Changes

### Migration Testing:
```bash
# Test migration generation
npm run typeorm:migration:generate -- -n TestMigration

# Test migration execution
npm run typeorm:migration:run

# Test rollback
npm run typeorm:migration:revert
```

### Schema Validation:
```bash
# Verify schema matches entities
npm run typeorm:schema:sync --check
```

---

**Status:** ⚠️ **NEEDS ATTENTION** - Critical production risks identified
**Date:** 2025-05-26
**Impact:** High - Database schema management and production safety
