'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  AreaChart,
  Area
} from 'recharts';
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  UsersIcon, 
  CurrencyDollarIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

export default function AdvancedChartsAnalytics() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [loading, setLoading] = useState(false);

  // Mock data for charts
  const platformGrowthData = [
    { date: '2024-01-01', users: 1200, nfts: 450, volume: 12.5 },
    { date: '2024-01-02', users: 1350, nfts: 520, volume: 15.2 },
    { date: '2024-01-03', users: 1480, nfts: 610, volume: 18.7 },
    { date: '2024-01-04', users: 1620, nfts: 720, volume: 22.1 },
    { date: '2024-01-05', users: 1750, nfts: 840, volume: 26.8 },
    { date: '2024-01-06', users: 1890, nfts: 950, volume: 31.2 },
    { date: '2024-01-07', users: 2020, nfts: 1080, volume: 35.9 }
  ];

  const rarityDistributionData = [
    { name: 'Common', value: 45, color: '#6B7280' },
    { name: 'Rare', value: 30, color: '#3B82F6' },
    { name: 'Epic', value: 20, color: '#8B5CF6' },
    { name: 'Legendary', value: 5, color: '#F59E0B' }
  ];

  const gainersLosersData = [
    { name: '@alice_crypto', change: 12.5, type: 'gainer' },
    { name: '@bob_nft', change: 8.2, type: 'gainer' },
    { name: '@charlie_art', change: 5.7, type: 'gainer' },
    { name: '@diana_meta', change: -2.1, type: 'loser' },
    { name: '@eve_crypto', change: -4.8, type: 'loser' }
  ];

  const marketValueData = [
    { date: '2024-01-01', value: 125.5 },
    { date: '2024-01-02', value: 132.8 },
    { date: '2024-01-03', value: 128.9 },
    { date: '2024-01-04', value: 145.2 },
    { date: '2024-01-05', value: 152.7 },
    { date: '2024-01-06', value: 148.3 },
    { date: '2024-01-07', value: 165.9 }
  ];

  const engagementData = [
    { date: '2024-01-01', activeUsers: 450, generations: 120, shares: 80 },
    { date: '2024-01-02', activeUsers: 520, generations: 145, shares: 95 },
    { date: '2024-01-03', activeUsers: 610, generations: 180, shares: 110 },
    { date: '2024-01-04', activeUsers: 720, generations: 210, shares: 125 },
    { date: '2024-01-05', activeUsers: 840, generations: 250, shares: 140 },
    { date: '2024-01-06', activeUsers: 950, generations: 290, shares: 160 },
    { date: '2024-01-07', activeUsers: 1080, generations: 320, shares: 180 }
  ];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'gainers', name: 'Gainers/Losers', icon: TrendingUpIcon },
    { id: 'market', name: 'Market Value', icon: CurrencyDollarIcon },
    { id: 'performance', name: 'Performance', icon: UsersIcon }
  ];

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Advanced Analytics Dashboard</h1>
          <p className="text-gray-600">Interactive data visualization with professional charts</p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Platform Growth Chart */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Growth</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={platformGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="users" stroke="#3B82F6" strokeWidth={2} />
                <Line type="monotone" dataKey="nfts" stroke="#10B981" strokeWidth={2} />
                <Line type="monotone" dataKey="volume" stroke="#F59E0B" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* NFT Rarity Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">NFT Rarity Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={rarityDistributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {rarityDistributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Engagement Metrics */}
          <div className="bg-white p-6 rounded-lg shadow lg:col-span-2">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Engagement Metrics</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area type="monotone" dataKey="activeUsers" stackId="1" stroke="#3B82F6" fill="#3B82F6" />
                <Area type="monotone" dataKey="generations" stackId="1" stroke="#10B981" fill="#10B981" />
                <Area type="monotone" dataKey="shares" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {activeTab === 'gainers' && (
        <div className="space-y-6">
          {/* Period Selector */}
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Period:</span>
            {['24h', '7d', '30d'].map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  selectedPeriod === period
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {period}
              </button>
            ))}
          </div>

          {/* Gainers/Losers Chart */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Gainers & Losers</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={gainersLosersData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip />
                <Bar 
                  dataKey="change" 
                  fill={(entry) => entry.type === 'gainer' ? '#10B981' : '#EF4444'}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {activeTab === 'market' && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Market Value Trends</h3>
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={marketValueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="value" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.6} />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      )}

      {activeTab === 'performance' && (
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <UsersIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
          <p className="text-gray-500">Advanced performance metrics coming soon...</p>
        </div>
      )}
    </div>
  );
}
