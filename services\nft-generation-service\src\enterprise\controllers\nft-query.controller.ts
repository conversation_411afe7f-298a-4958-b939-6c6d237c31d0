// Enterprise NFT Query Controller (Read Side)
import { <PERSON>, Get, Query, Param, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { NftQueryFiltersDto, NftQueryResponseDto } from '../models/nft-query.model';
import { NftQueryService } from '../services/nft-query.service';

@ApiTags('NFT Queries (Read Operations)')
@Controller('nfts')
export class NftQueryController {
  constructor(private readonly nftQueryService: NftQueryService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get NFT by ID (Enterprise CQRS Query)' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT retrieved successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  @ApiResponse({ status: 503, description: 'Service temporarily unavailable' })
  async getNftById(
    @Param('id') id: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-get-${Date.now()}`;
      
      const result = await this.nftQueryService.getNftById(id, {
        correlationId,
        userId: headers['x-user-id']
      });

      if (!result) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: {
            code: 'NftNotFoundException',
            message: 'NFT not found',
            timestamp: new Date().toISOString(),
            correlationId
          }
        });
      }

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'NFT service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get NFTs with pagination (Enterprise CQRS Query)' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'isListed', required: false, description: 'Filter by listing status' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of items to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of items to skip' })
  @ApiResponse({ status: 200, description: 'NFTs retrieved successfully', type: NftQueryResponseDto })
  async getNfts(
    @Query() filters: NftQueryFiltersDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-list-${Date.now()}`;
      
      const result = await this.nftQueryService.getNfts(filters, {
        correlationId,
        userId: headers['x-user-id']
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'NFT service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Get('user/:userId/stats')
  @ApiOperation({ summary: 'Get user NFT statistics (Enterprise Query)' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User stats retrieved successfully' })
  async getUserNftStats(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-stats-${Date.now()}`;
      
      const result = await this.nftQueryService.getUserNftStats(userId, {
        correlationId
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'NFT service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }
}
