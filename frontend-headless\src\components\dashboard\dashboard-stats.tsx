'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { nftApi, analysisApi } from '@/lib/api'
import { 
  CubeIcon, 
  ChartBarIcon, 
  TrophyIcon, 
  SparklesIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'

interface DashboardStats {
  totalNFTs: number
  totalAnalyses: number
  averageScore: number
  highestScore: number
  rarityDistribution: {
    legendary: number
    epic: number
    rare: number
    common: number
  }
  recentActivity: {
    nftsThisWeek: number
    analysesThisWeek: number
  }
}

export default function DashboardStats() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      fetchUserStats()
    }
  }, [user?.id])

  const fetchUserStats = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)

      // Fetch NFTs and analyses in parallel
      const [nftResponse, analysisResponse] = await Promise.all([
        nftApi.getUserNFTs(user.id),
        analysisApi.getUserAnalysisHistory(user.id, 50)
      ])

      // Process NFT data
      const nfts = nftResponse.success ? nftResponse.data?.nfts || [] : []
      const analyses = analysisResponse.success ? analysisResponse.data?.analyses || [] : []

      // Calculate stats
      const totalNFTs = nfts.length
      const totalAnalyses = analyses.length
      
      // Calculate average score
      const scores = analyses.map((a: any) => a.score).filter((s: number) => s > 0)
      const averageScore = scores.length > 0 ? Math.round(scores.reduce((a: number, b: number) => a + b, 0) / scores.length) : 0
      const highestScore = scores.length > 0 ? Math.max(...scores) : 0

      // Calculate rarity distribution
      const rarityDistribution = {
        legendary: nfts.filter((nft: any) => nft.rarity?.toLowerCase() === 'legendary').length,
        epic: nfts.filter((nft: any) => nft.rarity?.toLowerCase() === 'epic').length,
        rare: nfts.filter((nft: any) => nft.rarity?.toLowerCase() === 'rare').length,
        common: nfts.filter((nft: any) => nft.rarity?.toLowerCase() === 'common').length,
      }

      // Calculate recent activity (last 7 days)
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      
      const nftsThisWeek = nfts.filter((nft: any) => 
        new Date(nft.createdAt) > oneWeekAgo
      ).length
      
      const analysesThisWeek = analyses.filter((analysis: any) => 
        new Date(analysis.createdAt) > oneWeekAgo
      ).length

      setStats({
        totalNFTs,
        totalAnalyses,
        averageScore,
        highestScore,
        rarityDistribution,
        recentActivity: {
          nftsThisWeek,
          analysesThisWeek
        }
      })

    } catch (error) {
      console.error('Error fetching user stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    change, 
    changeType,
    subtitle 
  }: {
    title: string
    value: string | number
    icon: any
    change?: number
    changeType?: 'increase' | 'decrease'
    subtitle?: string
  }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {change !== undefined && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                    changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {changeType === 'increase' ? (
                      <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                    ) : (
                      <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                    )}
                    <span className="sr-only">{changeType === 'increase' ? 'Increased' : 'Decreased'} by</span>
                    {change}
                  </div>
                )}
              </dd>
              {subtitle && (
                <dd className="text-xs text-gray-500 mt-1">{subtitle}</dd>
              )}
            </dl>
          </div>
        </div>
      </div>
    </div>
  )

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white overflow-hidden shadow rounded-lg animate-pulse">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-6 w-6 bg-gray-300 rounded"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-300 rounded w-16 mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!stats) return null

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatCard
        title="Total NFTs"
        value={stats.totalNFTs}
        icon={CubeIcon}
        change={stats.recentActivity.nftsThisWeek}
        changeType="increase"
        subtitle={`${stats.recentActivity.nftsThisWeek} this week`}
      />
      
      <StatCard
        title="Analyses"
        value={stats.totalAnalyses}
        icon={ChartBarIcon}
        change={stats.recentActivity.analysesThisWeek}
        changeType="increase"
        subtitle={`${stats.recentActivity.analysesThisWeek} this week`}
      />
      
      <StatCard
        title="Average Score"
        value={stats.averageScore}
        icon={SparklesIcon}
        subtitle="Across all analyses"
      />
      
      <StatCard
        title="Best Score"
        value={stats.highestScore}
        icon={TrophyIcon}
        subtitle="Personal best"
      />
    </div>
  )
}
