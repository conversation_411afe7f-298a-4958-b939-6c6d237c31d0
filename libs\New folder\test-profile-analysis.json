{"twitterHandle": "testuser_crypto", "userId": "86dd5e58-1616-4cd0-9ecc-fc2cbff072cc", "projectId": "0a3cfd03-52a6-4110-9811-64319e2334a6", "parameters": {"campaignId": "0a3cfd03-52a6-4110-9811-64319e2334a6", "analysisParameters": {"hasAvatar": {"enabled": true, "weight": 10}, "hasBanner": {"enabled": true, "weight": 5}, "hasBio": {"enabled": true, "weight": 15}, "followerCount": {"enabled": true, "weight": 25, "ranges": [{"min": 0, "max": 100, "score": 5}, {"min": 101, "max": 500, "score": 10}, {"min": 501, "max": 1000, "score": 15}, {"min": 1001, "max": 5000, "score": 20}, {"min": 5001, "max": 999999, "score": 25}]}, "followingCount": {"enabled": true, "weight": 5}, "tweetCount": {"enabled": true, "weight": 10}, "accountAge": {"enabled": true, "weight": 15}, "campaignEngagement": {"enabled": true, "weight": 20}, "contentQuality": {"enabled": true, "weight": 15}, "referrals": {"enabled": true, "weight": 10}}}}