'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { 
  ChartBarIcon, 
  TrophyIcon, 
  SparklesIcon, 
  UsersIcon,
  TrendingUpIcon,
  CalendarIcon,
  EyeIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

interface AnalyticsData {
  totalNFTs: number
  totalAnalyses: number
  averageScore: number
  rarityDistribution: {
    common: number
    rare: number
    epic: number
    legendary: number
    mythic: number
  }
  recentActivity: Array<{
    id: string
    type: 'analysis' | 'nft_generated'
    twitterHandle: string
    score?: number
    rarity?: string
    timestamp: string
  }>
  topPerformers: Array<{
    twitterHandle: string
    score: number
    rarity: string
  }>
  monthlyStats: Array<{
    month: string
    analyses: number
    nfts: number
  }>
}

interface EnhancedAnalyticsProps {
  refreshTrigger?: number
}

export default function EnhancedAnalytics({ refreshTrigger }: EnhancedAnalyticsProps) {
  const { user } = useAuth()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d'>('30d')

  useEffect(() => {
    if (user?.id) {
      fetchAnalytics()
    }
  }, [user?.id, refreshTrigger, selectedTimeframe])

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true)
      
      // Simulate analytics data (replace with real API call)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockAnalytics: AnalyticsData = {
        totalNFTs: 12,
        totalAnalyses: 18,
        averageScore: 73.5,
        rarityDistribution: {
          common: 4,
          rare: 5,
          epic: 2,
          legendary: 1,
          mythic: 0
        },
        recentActivity: [
          {
            id: '1',
            type: 'nft_generated',
            twitterHandle: 'elonmusk',
            score: 95,
            rarity: 'legendary',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            type: 'analysis',
            twitterHandle: 'tim_cook',
            score: 87,
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '3',
            type: 'nft_generated',
            twitterHandle: 'sundarpichai',
            score: 82,
            rarity: 'epic',
            timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        topPerformers: [
          { twitterHandle: 'elonmusk', score: 95, rarity: 'legendary' },
          { twitterHandle: 'tim_cook', score: 87, rarity: 'epic' },
          { twitterHandle: 'sundarpichai', score: 82, rarity: 'epic' }
        ],
        monthlyStats: [
          { month: 'Jan', analyses: 5, nfts: 4 },
          { month: 'Feb', analyses: 8, nfts: 6 },
          { month: 'Mar', analyses: 5, nfts: 2 }
        ]
      }
      
      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'text-yellow-600 bg-yellow-50'
      case 'epic':
        return 'text-purple-600 bg-purple-50'
      case 'rare':
        return 'text-blue-600 bg-blue-50'
      case 'common':
        return 'text-gray-600 bg-gray-50'
      default:
        return 'text-green-600 bg-green-50'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data</h3>
        <p className="mt-1 text-sm text-gray-500">Start analyzing profiles to see your analytics.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Analytics Overview</h2>
        <div className="flex space-x-2">
          {(['7d', '30d', '90d'] as const).map((timeframe) => (
            <button
              key={timeframe}
              onClick={() => setSelectedTimeframe(timeframe)}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                selectedTimeframe === timeframe
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {timeframe}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <SparklesIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total NFTs</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalNFTs}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Analyses</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalAnalyses}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrophyIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Score</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.averageScore}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round((analytics.totalNFTs / analytics.totalAnalyses) * 100)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Rarity Distribution & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Rarity Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Rarity Distribution</h3>
          <div className="space-y-3">
            {Object.entries(analytics.rarityDistribution).map(([rarity, count]) => (
              <div key={rarity} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${getRarityColor(rarity)}`}></div>
                  <span className="text-sm font-medium text-gray-900 capitalize">{rarity}</span>
                </div>
                <span className="text-sm text-gray-600">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {analytics.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between py-2">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {activity.type === 'nft_generated' ? (
                      <SparklesIcon className="h-5 w-5 text-purple-500" />
                    ) : (
                      <ChartBarIcon className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.type === 'nft_generated' ? 'NFT Generated' : 'Profile Analyzed'}
                    </p>
                    <p className="text-sm text-gray-600">@{activity.twitterHandle}</p>
                  </div>
                </div>
                <div className="text-right">
                  {activity.score && (
                    <p className="text-sm font-medium text-gray-900">{activity.score}</p>
                  )}
                  <p className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Profiles</h3>
        <div className="space-y-3">
          {analytics.topPerformers.map((performer, index) => (
            <div key={performer.twitterHandle} className="flex items-center justify-between py-2">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">@{performer.twitterHandle}</p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(performer.rarity)}`}>
                    {performer.rarity}
                  </span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-gray-900">{performer.score}</p>
                <p className="text-xs text-gray-500">Score</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
