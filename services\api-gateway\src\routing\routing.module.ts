import { <PERSON>du<PERSON> } from '@nestjs/common';
import { UsersController } from './controllers/users.controller';
import { AuthController } from './controllers/auth.controller';
import { AnalysisController } from './controllers/analysis.controller';
import { ProjectsController } from './controllers/projects.controller';
import { NftGenerationController } from './controllers/nft-generation.controller';
import { NftsController } from './controllers/nfts.controller';
import { BlockchainController } from './controllers/blockchain.controller';
import { EnvironmentController } from './controllers/environment.controller';
import { MarketplaceController } from './controllers/marketplace.controller';
import { SearchController } from './controllers/search.controller';
import { ProxyService } from './services/proxy.service';

@Module({
  controllers: [
    UsersController,
    AuthController,
    AnalysisController,
    ProjectsController,
    NftGenerationController,
    NftsController,
    BlockchainController,
    EnvironmentController,
    MarketplaceController,
    SearchController,
  ],
  providers: [
    ProxyService,
  ],
  exports: [
    ProxyService,
  ],
})
export class RoutingModule {}
