// Enterprise Marketplace Module - Template
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ListingCommandController } from './controllers/listing-command.controller';
import { PrismaService } from './shared/prisma.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    })
  ],
  controllers: [ListingCommandController],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
