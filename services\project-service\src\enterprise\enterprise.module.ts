// Enterprise Project Module - Requirements-Driven Implementation
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ProjectCommandController } from './controllers/project-command.controller';
import { ProjectQueryController } from './controllers/project-query.controller';
import { CampaignCommandController } from './controllers/campaign-command.controller';
import { CampaignQueryController } from './controllers/campaign-query.controller';
import { ProjectCommandService } from './services/project-command.service';
import { ProjectQueryService } from './services/project-query.service';
import { CampaignCommandService } from './services/campaign-command.service';
import { CampaignQueryService } from './services/campaign-query.service';
import { PrismaService } from './shared/prisma.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    EventEmitterModule.forRoot({
      // Event emitter configuration for domain events
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    })
  ],
  controllers: [
    ProjectCommandController,
    ProjectQueryController,
    CampaignCommandController,
    CampaignQueryController,
  ],
  providers: [
    PrismaService,
    ProjectCommandService,
    ProjectQueryService,
    CampaignCommandService,
    CampaignQueryService,
  ],
  exports: [
    PrismaService,
    ProjectCommandService,
    ProjectQueryService,
    CampaignCommandService,
    CampaignQueryService,
  ]
})
export class EnterpriseModule {}
