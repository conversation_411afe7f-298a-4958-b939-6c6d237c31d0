# User Service

User management and authentication service for the Social NFT Platform.

## Features

- User registration and login
- JWT-based authentication
- Password hashing with bcrypt
- User profile management
- PostgreSQL database integration
- Swagger API documentation

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. Start the service:
```bash
# Development
npm run dev

# Production
npm run build
npm run start:prod
```

### API Documentation

Once running, visit: http://localhost:3001/api/docs

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `GET /auth/profile` - Get user profile (requires auth)

### Health
- `GET /` - Service status
- `GET /health` - Health check

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| NODE_ENV | Environment | development |
| PORT | Service port | 3001 |
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 5432 |
| DB_USERNAME | Database username | postgres |
| DB_PASSWORD | Database password | postgres |
| DB_DATABASE | Database name | user_service_db |
| JWT_SECRET | JWT secret key | your-secret-key |
| JWT_EXPIRES_IN | JWT expiration | 7d |

## Database Schema

### Users Table
- id (UUID, Primary Key)
- username (String, Unique)
- email (String, Unique)
- password (String, Hashed)
- twitterUsername (String, Optional)
- twitterId (String, Optional)
- role (String, Default: 'user')
- isActive (Boolean, Default: true)
- isEmailVerified (Boolean, Default: false)
- createdAt (Timestamp)
- updatedAt (Timestamp)

## Testing

```bash
# Unit tests
npm run test

# Test coverage
npm run test:cov

# Watch mode
npm run test:watch
```

## Docker

```bash
# Build image
docker build -t user-service .

# Run container
docker run -p 3001:3001 user-service
```
