#!/usr/bin/env node

/**
 * NFT Auto-Refresh Test
 * 
 * This test verifies that the frontend NFT gallery automatically refreshes
 * after generating a new NFT, without requiring a manual page refresh.
 * 
 * Test Flow:
 * 1. Register a new user
 * 2. Check initial NFT count (should be 0)
 * 3. Analyze a profile and generate NFT
 * 4. Verify NFT count increased (auto-refresh working)
 */

const axios = require('axios');

// API Gateway URL
const API_BASE = 'http://localhost:3010/api';

// Test data
const testUser = {
  username: `autorefreshtest${Date.now()}`,
  email: `autorefreshtest${Date.now()}@example.com`,
  password: 'TestPassword123',
  confirmPassword: 'TestPassword123',
  displayName: 'Auto Refresh Test User'
};

async function testNFTAutoRefresh() {
  console.log('🔄 Testing NFT Auto-Refresh Functionality');
  console.log('=' .repeat(60));

  try {
    // Step 1: Register new user
    console.log('\n📝 Step 1: Register new user');
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
    console.log(`   ✅ User registered: ${testUser.username}`);
    
    const userId = registerResponse.data.data.user.id;
    const authToken = registerResponse.data.data.accessToken;

    // Step 2: Check initial NFT count
    console.log('\n📊 Step 2: Check initial NFT count');
    const initialNFTResponse = await axios.get(`${API_BASE}/nft-generation/user/${userId}/history?limit=50`);
    const initialCount = initialNFTResponse.data.data.total;
    console.log(`   📋 Initial NFT count: ${initialCount}`);

    // Step 3: Analyze profile
    console.log('\n🔍 Step 3: Analyze Twitter profile');
    const analysisResponse = await axios.post(`${API_BASE}/analysis/twitter-profile`, {
      twitterHandle: 'testuser_autorefresh',
      userId: userId,
      analysisType: 'comprehensive'
    });
    console.log(`   ✅ Analysis completed - Score: ${analysisResponse.data.data.score}/100`);
    
    const analysisId = analysisResponse.data.data.id;

    // Step 4: Generate NFT
    console.log('\n🎨 Step 4: Generate NFT');
    const nftResponse = await axios.post(`${API_BASE}/nft-generation/generate-from-analysis`, {
      userId: userId,
      analysisId: analysisId,
      customization: {
        style: 'artistic',
        theme: 'tech'
      }
    });
    console.log(`   ✅ NFT generated - ${nftResponse.data.data.rarity} (Score: ${nftResponse.data.data.score})`);

    // Step 5: Check updated NFT count
    console.log('\n📈 Step 5: Verify NFT count increased');
    const updatedNFTResponse = await axios.get(`${API_BASE}/nft-generation/user/${userId}/history?limit=50`);
    const updatedCount = updatedNFTResponse.data.data.total;
    console.log(`   📋 Updated NFT count: ${updatedCount}`);

    // Verify auto-refresh would work
    const countIncreased = updatedCount > initialCount;
    console.log(`   ${countIncreased ? '✅' : '❌'} Count increased: ${initialCount} → ${updatedCount}`);

    // Summary
    console.log('\n' + '=' .repeat(60));
    if (countIncreased) {
      console.log('🎉 NFT AUTO-REFRESH TEST SUCCESSFUL!');
      console.log('✅ Backend properly updates NFT count');
      console.log('✅ Frontend should auto-refresh with new implementation');
      console.log('✅ User will see new NFTs without manual refresh');
    } else {
      console.log('❌ NFT AUTO-REFRESH TEST FAILED!');
      console.log('❌ NFT count did not increase as expected');
    }
    
    console.log('\n📋 Test Results:');
    console.log(`   👤 User: ${testUser.username}`);
    console.log(`   🔍 Analysis Score: ${analysisResponse.data.data.score}`);
    console.log(`   🎨 NFT Rarity: ${nftResponse.data.data.rarity}`);
    console.log(`   📊 NFT Count: ${initialCount} → ${updatedCount}`);
    
    return countIncreased;

  } catch (error) {
    console.error('\n❌ Auto-refresh test failed:', error.response?.data || error.message);
    console.error('📋 Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method
    });
    return false;
  }
}

// Run the test
if (require.main === module) {
  testNFTAutoRefresh()
    .then(success => {
      console.log('\n🔄 Next: Test the frontend auto-refresh in browser!');
      console.log('   1. Register a new user in browser');
      console.log('   2. Generate an NFT');
      console.log('   3. Watch NFT Gallery update automatically');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testNFTAutoRefresh };
