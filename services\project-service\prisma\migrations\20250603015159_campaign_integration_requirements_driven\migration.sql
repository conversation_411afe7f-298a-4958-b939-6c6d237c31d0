-- AlterTable
ALTER TABLE "campaign_commands" ADD COLUMN     "approvedAt" TIMESTAMP(3),
ADD COLUMN     "approvedBy" TEXT,
ADD COLUMN     "campaignType" TEXT NOT NULL DEFAULT 'user_acquisition',
ADD COLUMN     "currentMetrics" JSONB,
ADD COLUMN     "dataClassification" TEXT NOT NULL DEFAULT 'internal',
ADD COLUMN     "launchApproval" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "minParticipants" INTEGER DEFAULT 1,
ADD COLUMN     "nftGenerationRules" JSONB,
ADD COLUMN     "retentionPolicy" TEXT NOT NULL DEFAULT '7years',
ADD COLUMN     "scoreCalculation" JSONB,
ADD COLUMN     "socialPlatforms" JSONB,
ADD COLUMN     "targetAudience" JSONB,
ADD COLUMN     "targetMetrics" JSONB,
ADD COLUMN     "timezone" TEXT NOT NULL DEFAULT 'UTC',
ADD COLUMN     "trackingParameters" JSONB,
ADD COLUMN     "updatedBy" TEXT;

-- CreateTable
CREATE TABLE "campaign_queries" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "projectName" TEXT NOT NULL,
    "projectOwner" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "displayDescription" TEXT,
    "campaignType" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "timezone" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "participantCount" INTEGER NOT NULL DEFAULT 0,
    "maxParticipants" INTEGER,
    "minParticipants" INTEGER NOT NULL DEFAULT 1,
    "participationRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "totalNftsMinted" INTEGER NOT NULL DEFAULT 0,
    "averageScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "engagementRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "conversionRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "totalTweets" INTEGER NOT NULL DEFAULT 0,
    "totalLikes" INTEGER NOT NULL DEFAULT 0,
    "totalRetweets" INTEGER NOT NULL DEFAULT 0,
    "totalMentions" INTEGER NOT NULL DEFAULT 0,
    "targetMetrics" JSONB,
    "currentMetrics" JSONB,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "featuredImage" TEXT,
    "socialPlatforms" JSONB,
    "rewards" JSONB,
    "launchApproval" BOOLEAN NOT NULL DEFAULT false,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "campaign_queries_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "campaign_commands" ADD CONSTRAINT "campaign_commands_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "project_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;
