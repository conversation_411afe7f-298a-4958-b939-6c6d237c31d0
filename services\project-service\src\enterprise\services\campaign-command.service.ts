// Enterprise Campaign Command Service - Requirements-Driven Implementation
import { Injectable, NotFoundException, BadRequestException, ConflictException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';
import { CreateCampaignCommandDto, UpdateCampaignCommandDto, CampaignStatus } from '../models/campaign-command.model';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class CampaignCommandService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create new campaign linked to project
   * Requirements: Complete campaign configuration with project relationship
   */
  async createCampaign(dto: CreateCampaignCommandDto, correlationId: string): Promise<any> {
    try {
      // Validate project exists and user has permission
      const project = await this.prisma.projectCommand.findUnique({
        where: { id: dto.projectId },
      });

      if (!project) {
        throw new NotFoundException(`Project with ID ${dto.projectId} not found`);
      }

      if (project.status === 'archived') {
        throw new BadRequestException('Cannot create campaign for archived project');
      }

      // Validate campaign dates
      this.validateCampaignDates(dto.startDate, dto.endDate);

      // Validate reward structure
      this.validateRewardStructure(dto.rewards);

      // Validate target metrics
      if (dto.targetMetrics) {
        this.validateTargetMetrics(dto.targetMetrics);
      }

      // Create campaign command
      const campaignCommand = await this.prisma.campaignCommand.create({
        data: {
          projectId: dto.projectId,
          name: dto.name,
          description: dto.description,
          campaignType: dto.campaignType,
          
          // Timeline
          startDate: new Date(dto.startDate),
          endDate: new Date(dto.endDate),
          timezone: dto.timezone || 'UTC',
          
          // Configuration (Requirements-driven)
          targetAudience: dto.targetAudience ? JSON.stringify(dto.targetAudience) : null,
          requirements: dto.requirements || [],
          rewards: JSON.stringify(dto.rewards),
          socialPlatforms: dto.socialPlatforms ? JSON.stringify(dto.socialPlatforms) : null,
          nftGenerationRules: dto.nftGenerationRules ? JSON.stringify(dto.nftGenerationRules) : null,
          targetMetrics: dto.targetMetrics ? JSON.stringify(dto.targetMetrics) : null,
          
          // Participation limits
          maxParticipants: dto.maxParticipants,
          minParticipants: dto.minParticipants || 1,
          
          // Approval workflow
          launchApproval: dto.launchApproval || false,
          
          // Enterprise audit
          createdBy: project.ownerId,
          updatedBy: project.ownerId,
        },
      });

      // Create optimized query model
      await this.createCampaignQuery(campaignCommand, project);

      // Update project campaign count
      await this.updateProjectCampaignCount(dto.projectId);

      // Emit domain event
      await this.eventEmitter.emitAsync('campaign.created', {
        campaignId: campaignCommand.id,
        projectId: dto.projectId,
        ownerId: project.ownerId,
        campaignType: dto.campaignType,
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        data: {
          id: campaignCommand.id,
          name: campaignCommand.name,
          projectId: campaignCommand.projectId,
          status: campaignCommand.status,
          campaignType: campaignCommand.campaignType,
        },
        message: 'Campaign created successfully',
        correlationId,
      };

    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException('Campaign with this name already exists for this project');
      }
      throw error;
    }
  }

  /**
   * Update existing campaign
   * Requirements: Support partial updates with validation
   */
  async updateCampaign(id: string, dto: UpdateCampaignCommandDto, correlationId: string): Promise<any> {
    try {
      // Check if campaign exists
      const existingCampaign = await this.prisma.campaignCommand.findUnique({
        where: { id },
        include: { project: true },
      });

      if (!existingCampaign) {
        throw new NotFoundException(`Campaign with ID ${id} not found`);
      }

      // Validate campaign can be updated
      if (existingCampaign.status === 'completed' || existingCampaign.status === 'cancelled') {
        throw new BadRequestException('Cannot update completed or cancelled campaign');
      }

      // Validate dates if provided
      if (dto.startDate || dto.endDate) {
        const startDate = dto.startDate || existingCampaign.startDate.toISOString();
        const endDate = dto.endDate || existingCampaign.endDate.toISOString();
        this.validateCampaignDates(startDate, endDate);
      }

      // Validate reward structure if provided
      if (dto.rewards) {
        this.validateRewardStructure(dto.rewards);
      }

      // Prepare update data with proper JSON conversion
      const updateData: any = {
        ...dto,
        updatedBy: existingCampaign.project.ownerId,
        version: { increment: 1 },
      };

      // Convert complex objects to JSON
      if (dto.targetAudience) {
        updateData.targetAudience = JSON.stringify(dto.targetAudience);
      }
      if (dto.rewards) {
        updateData.rewards = JSON.stringify(dto.rewards);
      }
      if (dto.socialPlatforms) {
        updateData.socialPlatforms = JSON.stringify(dto.socialPlatforms);
      }
      if (dto.nftGenerationRules) {
        updateData.nftGenerationRules = JSON.stringify(dto.nftGenerationRules);
      }
      if (dto.targetMetrics) {
        updateData.targetMetrics = JSON.stringify(dto.targetMetrics);
      }
      if (dto.startDate) {
        updateData.startDate = new Date(dto.startDate);
      }
      if (dto.endDate) {
        updateData.endDate = new Date(dto.endDate);
      }

      // Update campaign command
      const updatedCampaign = await this.prisma.campaignCommand.update({
        where: { id },
        data: updateData,
      });

      // Update query model
      await this.updateCampaignQuery(updatedCampaign, existingCampaign.project);

      // Emit domain event
      await this.eventEmitter.emitAsync('campaign.updated', {
        campaignId: id,
        projectId: existingCampaign.projectId,
        ownerId: existingCampaign.project.ownerId,
        changes: Object.keys(dto),
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        data: {
          id: updatedCampaign.id,
          name: updatedCampaign.name,
          status: updatedCampaign.status,
          version: updatedCampaign.version,
        },
        message: 'Campaign updated successfully',
        correlationId,
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete campaign (soft delete with cascade)
   * Requirements: Maintain data integrity and audit trail
   */
  async deleteCampaign(id: string, correlationId: string): Promise<any> {
    try {
      const campaign = await this.prisma.campaignCommand.findUnique({
        where: { id },
        include: { project: true },
      });

      if (!campaign) {
        throw new NotFoundException(`Campaign with ID ${id} not found`);
      }

      // Check if campaign can be deleted
      if (campaign.status === 'active' && campaign.participantCount > 0) {
        throw new BadRequestException('Cannot delete active campaign with participants');
      }

      // Soft delete by updating status
      await this.prisma.campaignCommand.update({
        where: { id },
        data: {
          status: CampaignStatus.CANCELLED,
          updatedBy: campaign.project.ownerId,
          version: { increment: 1 },
        },
      });

      // Update query model
      await this.prisma.campaignQuery.update({
        where: { id },
        data: {
          status: CampaignStatus.CANCELLED,
          isActive: false,
        },
      });

      // Update project campaign count
      await this.updateProjectCampaignCount(campaign.projectId);

      // Emit domain event
      await this.eventEmitter.emitAsync('campaign.deleted', {
        campaignId: id,
        projectId: campaign.projectId,
        ownerId: campaign.project.ownerId,
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        message: 'Campaign deleted successfully',
        correlationId,
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Approve campaign for launch
   * Requirements: Approval workflow for campaign activation
   */
  async approveCampaign(id: string, approvedBy: string, correlationId: string): Promise<any> {
    try {
      const campaign = await this.prisma.campaignCommand.findUnique({
        where: { id },
        include: { project: true },
      });

      if (!campaign) {
        throw new NotFoundException(`Campaign with ID ${id} not found`);
      }

      if (campaign.launchApproval && campaign.approvedBy) {
        throw new ConflictException('Campaign is already approved');
      }

      // Update approval status
      const updatedCampaign = await this.prisma.campaignCommand.update({
        where: { id },
        data: {
          launchApproval: true,
          approvedBy,
          approvedAt: new Date(),
          updatedBy: approvedBy,
          version: { increment: 1 },
        },
      });

      // Update query model
      await this.prisma.campaignQuery.update({
        where: { id },
        data: {
          launchApproval: true,
          approvedBy,
          approvedAt: new Date(),
        },
      });

      // Emit domain event
      await this.eventEmitter.emitAsync('campaign.approved', {
        campaignId: id,
        projectId: campaign.projectId,
        ownerId: campaign.project.ownerId,
        approvedBy,
        correlationId,
        timestamp: new Date(),
      });

      return {
        success: true,
        data: {
          id: updatedCampaign.id,
          approved: true,
          approvedBy,
          approvedAt: updatedCampaign.approvedAt,
        },
        message: 'Campaign approved successfully',
        correlationId,
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate campaign dates
   */
  private validateCampaignDates(startDate: string, endDate: string): void {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    if (start >= end) {
      throw new BadRequestException('Campaign end date must be after start date');
    }

    if (end <= now) {
      throw new BadRequestException('Campaign end date must be in the future');
    }

    // Campaign must be at least 1 day long
    const minDuration = 24 * 60 * 60 * 1000; // 1 day in milliseconds
    if (end.getTime() - start.getTime() < minDuration) {
      throw new BadRequestException('Campaign must be at least 1 day long');
    }

    // Campaign cannot be longer than 1 year
    const maxDuration = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if (end.getTime() - start.getTime() > maxDuration) {
      throw new BadRequestException('Campaign cannot be longer than 1 year');
    }
  }

  /**
   * Validate reward structure
   */
  private validateRewardStructure(rewards: any): void {
    if (!rewards.nftRewards) {
      throw new BadRequestException('NFT rewards configuration is required');
    }

    const { nftRewards } = rewards;
    const totalProbability = (nftRewards.common?.probability || 0) + 
                           (nftRewards.rare?.probability || 0) + 
                           (nftRewards.legendary?.probability || 0);

    if (totalProbability > 100) {
      throw new BadRequestException('Total NFT reward probabilities cannot exceed 100%');
    }

    if (totalProbability < 50) {
      throw new BadRequestException('Total NFT reward probabilities should be at least 50%');
    }
  }

  /**
   * Validate target metrics
   */
  private validateTargetMetrics(metrics: any): void {
    if (metrics.targetParticipants && metrics.targetParticipants < 1) {
      throw new BadRequestException('Target participants must be at least 1');
    }

    if (metrics.targetEngagementRate && (metrics.targetEngagementRate < 0 || metrics.targetEngagementRate > 100)) {
      throw new BadRequestException('Target engagement rate must be between 0 and 100');
    }
  }

  /**
   * Create optimized query model
   */
  private async createCampaignQuery(campaignCommand: any, project: any): Promise<void> {
    await this.prisma.campaignQuery.create({
      data: {
        id: campaignCommand.id,
        projectId: campaignCommand.projectId,
        projectName: project.name,
        projectOwner: project.ownerId,
        displayName: campaignCommand.name,
        displayDescription: campaignCommand.description,
        campaignType: campaignCommand.campaignType,
        status: campaignCommand.status,
        startDate: campaignCommand.startDate,
        endDate: campaignCommand.endDate,
        timezone: campaignCommand.timezone,
        isActive: this.isCampaignActive(campaignCommand),
        maxParticipants: campaignCommand.maxParticipants,
        minParticipants: campaignCommand.minParticipants,
        socialPlatforms: campaignCommand.socialPlatforms,
        rewards: campaignCommand.rewards,
        targetMetrics: campaignCommand.targetMetrics,
        launchApproval: campaignCommand.launchApproval,
        approvedBy: campaignCommand.approvedBy,
        approvedAt: campaignCommand.approvedAt,
        createdAt: campaignCommand.createdAt,
      },
    });
  }

  /**
   * Update optimized query model
   */
  private async updateCampaignQuery(campaignCommand: any, project: any): Promise<void> {
    await this.prisma.campaignQuery.update({
      where: { id: campaignCommand.id },
      data: {
        displayName: campaignCommand.name,
        displayDescription: campaignCommand.description,
        campaignType: campaignCommand.campaignType,
        status: campaignCommand.status,
        startDate: campaignCommand.startDate,
        endDate: campaignCommand.endDate,
        timezone: campaignCommand.timezone,
        isActive: this.isCampaignActive(campaignCommand),
        maxParticipants: campaignCommand.maxParticipants,
        minParticipants: campaignCommand.minParticipants,
        socialPlatforms: campaignCommand.socialPlatforms,
        rewards: campaignCommand.rewards,
        targetMetrics: campaignCommand.targetMetrics,
        launchApproval: campaignCommand.launchApproval,
        lastUpdated: new Date(),
      },
    });
  }

  /**
   * Check if campaign is currently active
   */
  private isCampaignActive(campaign: any): boolean {
    const now = new Date();
    const start = new Date(campaign.startDate);
    const end = new Date(campaign.endDate);
    
    return campaign.status === 'active' && 
           now >= start && 
           now <= end && 
           campaign.launchApproval;
  }

  /**
   * Update project campaign count
   */
  private async updateProjectCampaignCount(projectId: string): Promise<void> {
    const campaignCount = await this.prisma.campaignCommand.count({
      where: {
        projectId,
        status: { not: 'cancelled' }
      },
    });

    await this.prisma.projectQuery.update({
      where: { id: projectId },
      data: { campaignCount },
    });
  }
}
