# Dependencies Stage - CACHED (rarely changes)
FROM node:18-alpine AS dependencies

WORKDIR /app

# Copy only package files for dependency caching
COPY services/user-service/package*.json ./

# Install dependencies - THIS LAYER WILL BE CACHED
RUN npm install --legacy-peer-deps

# Development Stage - NEVER CACHED (changes frequently)
FROM node:18-alpine AS development

WORKDIR /app

# Copy cached dependencies from previous stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/package*.json ./

# Copy source code - THIS LAYER IS NEVER CACHED
COPY services/user-service/src ./src
COPY services/user-service/tsconfig.json ./
COPY services/user-service/nest-cli.json ./

# Copy Prisma files (will be created during migration)
COPY services/user-service/prisma ./prisma

# Build the application - REBUILDS ONLY WHEN SOURCE CHANGES
RUN npm run build

# Generate Prisma client (will be added during migration)
RUN npx prisma generate || echo "Prisma not yet configured"

# Set environment variables
ENV NODE_ENV=development
ENV NODE_OPTIONS="--max-old-space-size=640"

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port
EXPOSE 3011

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3011/api/health || exit 1

# Start the application
CMD ["node", "dist/main.js"]
