-- CreateTable
CREATE TABLE "campaigns" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'draft',
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "banner_image" VARCHAR(255),
    "max_participants" INTEGER,
    "min_age" INTEGER,
    "geo_restrictions" TEXT[],
    "tags" TEXT[],
    "priority" INTEGER NOT NULL DEFAULT 5,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "campaigns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "campaign_requirements" (
    "id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "target" VARCHAR(255),
    "min_value" DOUBLE PRECISION,
    "max_value" DOUBLE PRECISION,
    "mandatory" BOOLEAN NOT NULL DEFAULT true,
    "points" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "campaign_requirements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "campaign_rewards" (
    "id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "value" DOUBLE PRECISION,
    "rarity" VARCHAR(20),
    "max_quantity" INTEGER,
    "min_points_required" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "current_quantity" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "campaign_rewards_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "campaign_participations" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'in_progress',
    "total_points" INTEGER NOT NULL DEFAULT 0,
    "referral_code" VARCHAR(50),
    "accepted_terms" BOOLEAN NOT NULL DEFAULT true,
    "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "campaign_participations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "requirement_submissions" (
    "id" TEXT NOT NULL,
    "participation_id" TEXT NOT NULL,
    "requirement_type" VARCHAR(50) NOT NULL,
    "submission_value" TEXT,
    "proof_urls" TEXT[],
    "notes" TEXT,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending_review',
    "points_awarded" INTEGER,
    "reviewer_comments" TEXT,
    "feedback" TEXT,
    "reviewed_by" TEXT,
    "submitted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewed_at" TIMESTAMP(3),

    CONSTRAINT "requirement_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "campaign_rewards_earned" (
    "id" TEXT NOT NULL,
    "participation_id" TEXT NOT NULL,
    "reward_id" TEXT NOT NULL,
    "claimed" BOOLEAN NOT NULL DEFAULT false,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "earned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "claimed_at" TIMESTAMP(3),

    CONSTRAINT "campaign_rewards_earned_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "campaigns_status_idx" ON "campaigns"("status");

-- CreateIndex
CREATE INDEX "campaigns_type_idx" ON "campaigns"("type");

-- CreateIndex
CREATE INDEX "campaigns_featured_idx" ON "campaigns"("featured");

-- CreateIndex
CREATE INDEX "campaigns_start_date_end_date_idx" ON "campaigns"("start_date", "end_date");

-- CreateIndex
CREATE INDEX "campaigns_created_at_idx" ON "campaigns"("created_at");

-- CreateIndex
CREATE INDEX "campaign_requirements_campaign_id_idx" ON "campaign_requirements"("campaign_id");

-- CreateIndex
CREATE INDEX "campaign_requirements_type_idx" ON "campaign_requirements"("type");

-- CreateIndex
CREATE INDEX "campaign_rewards_campaign_id_idx" ON "campaign_rewards"("campaign_id");

-- CreateIndex
CREATE INDEX "campaign_rewards_type_idx" ON "campaign_rewards"("type");

-- CreateIndex
CREATE INDEX "campaign_rewards_rarity_idx" ON "campaign_rewards"("rarity");

-- CreateIndex
CREATE INDEX "campaign_participations_user_id_idx" ON "campaign_participations"("user_id");

-- CreateIndex
CREATE INDEX "campaign_participations_campaign_id_idx" ON "campaign_participations"("campaign_id");

-- CreateIndex
CREATE INDEX "campaign_participations_status_idx" ON "campaign_participations"("status");

-- CreateIndex
CREATE INDEX "campaign_participations_joined_at_idx" ON "campaign_participations"("joined_at");

-- CreateIndex
CREATE UNIQUE INDEX "campaign_participations_user_id_campaign_id_key" ON "campaign_participations"("user_id", "campaign_id");

-- CreateIndex
CREATE INDEX "requirement_submissions_participation_id_idx" ON "requirement_submissions"("participation_id");

-- CreateIndex
CREATE INDEX "requirement_submissions_requirement_type_idx" ON "requirement_submissions"("requirement_type");

-- CreateIndex
CREATE INDEX "requirement_submissions_status_idx" ON "requirement_submissions"("status");

-- CreateIndex
CREATE INDEX "requirement_submissions_submitted_at_idx" ON "requirement_submissions"("submitted_at");

-- CreateIndex
CREATE INDEX "campaign_rewards_earned_participation_id_idx" ON "campaign_rewards_earned"("participation_id");

-- CreateIndex
CREATE INDEX "campaign_rewards_earned_reward_id_idx" ON "campaign_rewards_earned"("reward_id");

-- CreateIndex
CREATE INDEX "campaign_rewards_earned_claimed_idx" ON "campaign_rewards_earned"("claimed");

-- CreateIndex
CREATE INDEX "campaign_rewards_earned_earned_at_idx" ON "campaign_rewards_earned"("earned_at");

-- AddForeignKey
ALTER TABLE "campaign_requirements" ADD CONSTRAINT "campaign_requirements_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_rewards" ADD CONSTRAINT "campaign_rewards_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_participations" ADD CONSTRAINT "campaign_participations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_commands"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_participations" ADD CONSTRAINT "campaign_participations_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "requirement_submissions" ADD CONSTRAINT "requirement_submissions_participation_id_fkey" FOREIGN KEY ("participation_id") REFERENCES "campaign_participations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_rewards_earned" ADD CONSTRAINT "campaign_rewards_earned_participation_id_fkey" FOREIGN KEY ("participation_id") REFERENCES "campaign_participations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_rewards_earned" ADD CONSTRAINT "campaign_rewards_earned_reward_id_fkey" FOREIGN KEY ("reward_id") REFERENCES "campaign_rewards"("id") ON DELETE CASCADE ON UPDATE CASCADE;
