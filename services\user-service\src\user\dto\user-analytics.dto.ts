import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UserEngagementMetrics {
  @ApiProperty({
    description: 'Total number of campaigns participated in',
    example: 5,
  })
  totalCampaigns: number;

  @ApiProperty({
    description: 'Total number of NFTs generated',
    example: 12,
  })
  totalNftsGenerated: number;

  @ApiProperty({
    description: 'Total number of NFTs owned',
    example: 8,
  })
  totalNftsOwned: number;

  @ApiProperty({
    description: 'Total number of marketplace transactions',
    example: 3,
  })
  totalTransactions: number;

  @ApiProperty({
    description: 'Total value of NFTs owned (in ETH)',
    example: 2.5,
  })
  totalPortfolioValue: number;

  @ApiProperty({
    description: 'Average engagement score across campaigns',
    example: 85.5,
  })
  averageEngagementScore: number;

  @ApiProperty({
    description: 'Current streak of active days',
    example: 7,
  })
  currentStreak: number;

  @ApiProperty({
    description: 'Longest streak of active days',
    example: 21,
  })
  longestStreak: number;
}

export class UserSocialMetrics {
  @ApiProperty({
    description: 'Twitter follower count',
    example: 1500,
  })
  twitterFollowers: number;

  @ApiProperty({
    description: 'Twitter following count',
    example: 800,
  })
  twitterFollowing: number;

  @ApiProperty({
    description: 'Twitter engagement rate',
    example: 3.2,
  })
  twitterEngagementRate: number;

  @ApiProperty({
    description: 'Total social media interactions',
    example: 450,
  })
  totalInteractions: number;

  @ApiProperty({
    description: 'Social influence score',
    example: 72.8,
  })
  influenceScore: number;
}

export class UserActivityHistory {
  @ApiProperty({
    description: 'Date of activity',
    example: '2025-06-03',
  })
  date: string;

  @ApiProperty({
    description: 'Activity type',
    example: 'campaign_participation',
    enum: ['campaign_participation', 'nft_generation', 'marketplace_transaction', 'profile_update'],
  })
  activityType: string;

  @ApiProperty({
    description: 'Activity description',
    example: 'Participated in CryptoPunks campaign',
  })
  description: string;

  @ApiProperty({
    description: 'Points earned from activity',
    example: 50,
  })
  pointsEarned: number;
}

export class UserRankingInfo {
  @ApiProperty({
    description: 'Global rank among all users',
    example: 142,
  })
  globalRank: number;

  @ApiProperty({
    description: 'Rank change in last 7 days',
    example: 5,
  })
  rankChange7d: number;

  @ApiProperty({
    description: 'Rank change in last 30 days',
    example: -12,
  })
  rankChange30d: number;

  @ApiProperty({
    description: 'Percentile ranking',
    example: 85.5,
  })
  percentile: number;
}

export class UserAnalyticsDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user_123',
  })
  userId: string;

  @ApiProperty({
    description: 'User engagement metrics',
    type: UserEngagementMetrics,
  })
  engagement: UserEngagementMetrics;

  @ApiProperty({
    description: 'User social media metrics',
    type: UserSocialMetrics,
  })
  social: UserSocialMetrics;

  @ApiProperty({
    description: 'User ranking information',
    type: UserRankingInfo,
  })
  ranking: UserRankingInfo;

  @ApiProperty({
    description: 'Recent activity history',
    type: [UserActivityHistory],
  })
  recentActivity: UserActivityHistory[];

  @ApiProperty({
    description: 'Total points earned',
    example: 2450,
  })
  totalPoints: number;

  @ApiProperty({
    description: 'Current level',
    example: 'Gold',
    enum: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'],
  })
  currentLevel: string;

  @ApiProperty({
    description: 'Progress to next level (percentage)',
    example: 65.5,
  })
  progressToNextLevel: number;

  @ApiPropertyOptional({
    description: 'Achievements earned',
    example: ['First NFT', 'Campaign Master', 'Social Butterfly'],
  })
  achievements?: string[];

  @ApiProperty({
    description: 'Analytics generation timestamp',
    example: '2025-06-03T10:30:00Z',
  })
  generatedAt: string;
}
