"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/layout */ \"(app-pages-browser)/./src/components/layout/layout.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/real-nft-gallery */ \"(app-pages-browser)/./src/components/dashboard/real-nft-gallery.tsx\");\n/* harmony import */ var _components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/profile-analyzer */ \"(app-pages-browser)/./src/components/dashboard/profile-analyzer.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/user/enhanced-user-profile */ \"(app-pages-browser)/./src/components/user/enhanced-user-profile.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst stats = [\n    {\n        name: 'Total NFTs',\n        value: '12',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        change: '+2',\n        changeType: 'positive'\n    },\n    {\n        name: 'Active Campaigns',\n        value: '3',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        change: '+1',\n        changeType: 'positive'\n    },\n    {\n        name: 'Rewards Earned',\n        value: '$1,234',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        change: '+$234',\n        changeType: 'positive'\n    },\n    {\n        name: 'Engagement Score',\n        value: '85%',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        change: '+5%',\n        changeType: 'positive'\n    }\n];\nconst recentNFTs = [\n    {\n        id: 1,\n        name: 'Cosmic Explorer #1234',\n        campaign: 'Space Campaign',\n        rarity: 'Rare',\n        image: 'https://via.placeholder.com/100x100/6366f1/ffffff?text=NFT',\n        value: '$45.00'\n    },\n    {\n        id: 2,\n        name: 'Digital Warrior #5678',\n        campaign: 'Gaming Campaign',\n        rarity: 'Epic',\n        image: 'https://via.placeholder.com/100x100/8b5cf6/ffffff?text=NFT',\n        value: '$78.00'\n    },\n    {\n        id: 3,\n        name: 'Cyber Punk #9012',\n        campaign: 'Tech Campaign',\n        rarity: 'Legendary',\n        image: 'https://via.placeholder.com/100x100/ec4899/ffffff?text=NFT',\n        value: '$156.00'\n    }\n];\nconst activeCampaigns = [\n    {\n        id: 1,\n        name: 'DeFi Revolution',\n        description: 'Promote the future of decentralized finance',\n        progress: 75,\n        reward: '$50 + NFT',\n        deadline: '3 days left',\n        participants: 1234\n    },\n    {\n        id: 2,\n        name: 'Green Blockchain',\n        description: 'Spread awareness about eco-friendly crypto',\n        progress: 45,\n        reward: '$30 + NFT',\n        deadline: '1 week left',\n        participants: 856\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [nftRefreshTrigger, setNftRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showAdvancedCollection, setShowAdvancedCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Function to trigger NFT gallery refresh\n    const handleNFTGenerated = ()=>{\n        console.log('🔄 Triggering NFT gallery refresh...');\n        setNftRefreshTrigger((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username),\n                                                        \"! Here's what's happening with your NFTs and campaigns.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Explore\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-8 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_7__.EnhancedUserProfile, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2 space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    limit: 6,\n                                                    refreshTrigger: nftRefreshTrigger\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    onNFTGenerated: handleNFTGenerated\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-white shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Create NFT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"View Analytics\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"Um2q32fQGT/wCsWSA1rQ2uTy0+E=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});