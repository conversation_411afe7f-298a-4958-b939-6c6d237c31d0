-- CreateTable
CREATE TABLE "nft_commands" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "projectId" TEXT,
    "campaignId" TEXT,
    "templateId" TEXT,
    "tokenId" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "imageUrl" TEXT,
    "metadataUrl" TEXT,
    "attributes" JSONB,
    "metadata" JSONB,
    "rarity" TEXT,
    "score" INTEGER,
    "generationParams" JSONB NOT NULL,
    "generationStatus" TEXT NOT NULL DEFAULT 'pending',
    "generationError" TEXT,
    "contractAddress" TEXT,
    "blockchainTxHash" TEXT,
    "blockchainStatus" TEXT NOT NULL DEFAULT 'not_minted',
    "ipfsHash" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "dataClassification" TEXT NOT NULL DEFAULT 'internal',
    "retentionPolicy" TEXT NOT NULL DEFAULT '7years',

    CONSTRAINT "nft_commands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nft_queries" (
    "id" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "displayDescription" TEXT,
    "thumbnailUrl" TEXT,
    "fullImageUrl" TEXT,
    "rarity" TEXT,
    "score" INTEGER,
    "userId" TEXT NOT NULL,
    "username" TEXT,
    "projectId" TEXT,
    "projectName" TEXT,
    "campaignId" TEXT,
    "campaignName" TEXT,
    "status" TEXT NOT NULL,
    "generationTime" INTEGER,
    "mintingTime" INTEGER,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "likeCount" INTEGER NOT NULL DEFAULT 0,
    "shareCount" INTEGER NOT NULL DEFAULT 0,
    "isListed" BOOLEAN NOT NULL DEFAULT false,
    "currentPrice" TEXT,
    "lastSalePrice" TEXT,
    "totalSales" INTEGER NOT NULL DEFAULT 0,
    "avgResponseTime" DOUBLE PRECISION,
    "lastAccessedAt" TIMESTAMP(3),
    "popularityScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "nft_queries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "oldValues" JSONB,
    "newValues" JSONB,
    "userId" TEXT,
    "sessionId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "correlationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nft_events" (
    "id" TEXT NOT NULL,
    "eventType" TEXT NOT NULL,
    "eventVersion" TEXT NOT NULL DEFAULT '1.0',
    "aggregateId" TEXT NOT NULL,
    "eventData" JSONB NOT NULL,
    "correlationId" TEXT,
    "causationId" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "nft_events_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "nft_commands_userId_idx" ON "nft_commands"("userId");

-- CreateIndex
CREATE INDEX "nft_commands_projectId_idx" ON "nft_commands"("projectId");

-- CreateIndex
CREATE INDEX "nft_commands_campaignId_idx" ON "nft_commands"("campaignId");

-- CreateIndex
CREATE INDEX "nft_commands_generationStatus_idx" ON "nft_commands"("generationStatus");

-- CreateIndex
CREATE INDEX "nft_commands_blockchainStatus_idx" ON "nft_commands"("blockchainStatus");

-- CreateIndex
CREATE INDEX "nft_commands_createdAt_idx" ON "nft_commands"("createdAt");

-- CreateIndex
CREATE INDEX "nft_queries_userId_idx" ON "nft_queries"("userId");

-- CreateIndex
CREATE INDEX "nft_queries_status_idx" ON "nft_queries"("status");

-- CreateIndex
CREATE INDEX "nft_queries_rarity_idx" ON "nft_queries"("rarity");

-- CreateIndex
CREATE INDEX "nft_queries_isListed_idx" ON "nft_queries"("isListed");

-- CreateIndex
CREATE INDEX "nft_queries_popularityScore_idx" ON "nft_queries"("popularityScore");

-- CreateIndex
CREATE INDEX "nft_queries_createdAt_idx" ON "nft_queries"("createdAt");

-- CreateIndex
CREATE INDEX "audit_logs_entityType_entityId_idx" ON "audit_logs"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "audit_logs_userId_idx" ON "audit_logs"("userId");

-- CreateIndex
CREATE INDEX "audit_logs_correlationId_idx" ON "audit_logs"("correlationId");

-- CreateIndex
CREATE INDEX "audit_logs_createdAt_idx" ON "audit_logs"("createdAt");

-- CreateIndex
CREATE INDEX "nft_events_aggregateId_idx" ON "nft_events"("aggregateId");

-- CreateIndex
CREATE INDEX "nft_events_eventType_idx" ON "nft_events"("eventType");

-- CreateIndex
CREATE INDEX "nft_events_correlationId_idx" ON "nft_events"("correlationId");

-- CreateIndex
CREATE INDEX "nft_events_createdAt_idx" ON "nft_events"("createdAt");
