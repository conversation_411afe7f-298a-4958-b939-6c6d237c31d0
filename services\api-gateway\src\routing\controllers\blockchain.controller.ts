import { <PERSON>, Get, Post, Patch, Body, Param, Query, Headers, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('Blockchain')
@Controller('blockchain')
export class BlockchainController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('mint')
  @ApiOperation({ summary: 'Mint a single NFT on blockchain' })
  @ApiResponse({ status: 201, description: 'NFT minting initiated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data or NFT already minted' })
  @ApiResponse({ status: 500, description: 'Minting failed' })
  async mintNft(@Body() mintNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        '/api/blockchain/mint',
        'POST',
        mintNftDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Post('batch-mint')
  @ApiOperation({ summary: 'Mint multiple NFTs in batch' })
  @ApiResponse({ status: 201, description: 'Batch minting initiated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async batchMint(@Body() batchMintDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        '/api/blockchain/batch-mint',
        'POST',
        batchMintDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Patch('update-status')
  @ApiOperation({ summary: 'Update mint transaction status' })
  @ApiResponse({ status: 200, description: 'Mint status updated successfully' })
  @ApiResponse({ status: 404, description: 'Mint record not found' })
  async updateMintStatus(@Body() updateDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        '/api/blockchain/update-status',
        'PATCH',
        updateDto,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Get('mints/user/:userId')
  @ApiOperation({ summary: 'Get all mints for a specific user' })
  @ApiResponse({ status: 200, description: 'User mints retrieved successfully' })
  async getMintsByUser(@Param('userId') userId: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        `/api/blockchain/mints/user/${userId}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Get('mints/campaign/:campaignId')
  @ApiOperation({ summary: 'Get all mints for a specific campaign' })
  @ApiResponse({ status: 200, description: 'Campaign mints retrieved successfully' })
  async getMintsByCampaign(@Param('campaignId') campaignId: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        `/api/blockchain/mints/campaign/${campaignId}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Get('mints/blockchain/:blockchain')
  @ApiOperation({ summary: 'Get all mints for a specific blockchain' })
  @ApiResponse({ status: 200, description: 'Blockchain mints retrieved successfully' })
  async getMintsByBlockchain(@Param('blockchain') blockchain: string, @Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        `/api/blockchain/mints/blockchain/${blockchain}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Get('blockchain-status')
  @ApiOperation({ summary: 'Check blockchain connectivity status' })
  @ApiResponse({ status: 200, description: 'Blockchain connectivity status' })
  async getBlockchainStatus(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        '/api/blockchain-status',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Check Blockchain Service health' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(@Req() req: Request, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'blockchain-service',
        '/api/health',
        'GET',
        null,
        req.headers,
        req.query
      );

      res.status(response.status).json(response.data);
    } catch (error) {
      res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: 'blockchain-service'
      });
    }
  }
}
