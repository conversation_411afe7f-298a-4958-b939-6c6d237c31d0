import { 
  UserPlusIcon, 
  RocketLaunchIcon, 
  GiftIcon,
  ArrowRightIcon 
} from '@heroicons/react/24/outline'

const steps = [
  {
    id: 1,
    name: 'Connect Your Social Media',
    description: 'Link your Twitter account to start participating in Web3 campaigns and social engagement activities.',
    icon: UserPlusIcon,
    color: 'from-blue-500 to-blue-600',
  },
  {
    id: 2,
    name: 'Join Campaigns',
    description: 'Browse and join exciting campaigns from Web3 projects. Complete social tasks and engage with the community.',
    icon: RocketLaunchIcon,
    color: 'from-purple-500 to-purple-600',
  },
  {
    id: 3,
    name: 'Earn Evolving NFTs',
    description: 'Receive unique NFTs that evolve and upgrade based on your engagement level and campaign participation.',
    icon: GiftIcon,
    color: 'from-pink-500 to-pink-600',
  },
]

export default function HowItWorks() {
  return (
    <div className="py-16 bg-gray-50 overflow-hidden lg:py-24">
      <div className="relative max-w-xl mx-auto px-4 sm:px-6 lg:px-8 lg:max-w-7xl">
        <div className="relative">
          <h2 className="text-center text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            How It Works
          </h2>
          <p className="mt-4 max-w-3xl mx-auto text-center text-xl text-gray-500">
            Get started in three simple steps and begin earning evolving NFTs through social engagement
          </p>
        </div>

        <div className="relative mt-12 lg:mt-24 lg:grid lg:grid-cols-3 lg:gap-8 lg:items-center">
          <div className="relative lg:col-span-3">
            <dl className="space-y-10 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-x-8 sm:gap-y-10">
              {steps.map((step, stepIdx) => (
                <div key={step.name} className="relative">
                  <dt>
                    <div className={`absolute flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-r ${step.color} text-white`}>
                      <step.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                    <p className="ml-16 text-lg leading-6 font-medium text-gray-900">
                      Step {step.id}: {step.name}
                    </p>
                  </dt>
                  <dd className="mt-2 ml-16 text-base text-gray-500">
                    {step.description}
                  </dd>
                  
                  {/* Arrow for desktop */}
                  {stepIdx < steps.length - 1 && (
                    <div className="hidden sm:block absolute top-6 left-full transform translate-x-4 -translate-y-1/2">
                      <ArrowRightIcon className="h-5 w-5 text-gray-400" />
                    </div>
                  )}
                </div>
              ))}
            </dl>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="inline-flex rounded-md shadow">
            <a
              href="/auth/register"
              className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
            >
              Start Your Journey
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </a>
          </div>
          <p className="mt-3 text-sm text-gray-500">
            No credit card required • Free to start • Instant setup
          </p>
        </div>
      </div>
    </div>
  )
}
