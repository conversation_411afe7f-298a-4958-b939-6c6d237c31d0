#!/bin/bash

# =============================================================================
# MARKETPLACE ERROR HANDLING TEST SCRIPT
# =============================================================================
# Tests error scenarios and edge cases for marketplace service
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# API endpoints
MARKETPLACE_API="http://localhost:3006/api/marketplace"
API_GATEWAY="http://localhost:3010/api/marketplace"

echo ""
log_info "🚨 MARKETPLACE ERROR HANDLING TESTING"
echo "====================================="

# Function to test error scenarios
test_error_scenario() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    log_info "Testing: $description"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "x-user-id: test-error-user" \
            -d "$data" \
            "$endpoint")
    elif [ "$method" = "GET" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X GET \
            "$endpoint")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X "$method" \
            -H "Content-Type: application/json" \
            -H "x-user-id: test-error-user" \
            -d "$data" \
            "$endpoint")
    fi
    
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "  Expected Status: $expected_status"
    echo "  Actual Status: $http_status"
    echo "  Response: $(echo "$response_body" | head -c 150)..."
    
    if [ "$http_status" = "$expected_status" ]; then
        log_success "$description - CORRECT ERROR HANDLING"
        return 0
    else
        log_error "$description - UNEXPECTED STATUS (Expected: $expected_status, Got: $http_status)"
        return 1
    fi
}

echo ""
log_info "🧪 PHASE 4: ERROR HANDLING TESTING"
echo "=================================="

# Test 1: Invalid JSON Data
echo ""
log_info "Test 1: Invalid JSON Data"
echo "------------------------"

invalid_json='{"nftId": "invalid-uuid", "price": "not-a-number"'
test_error_scenario "POST" "$MARKETPLACE_API/listings" "$invalid_json" "400" "Invalid JSON Format"

# Test 2: Missing Required Fields
echo ""
log_info "Test 2: Missing Required Fields"
echo "-----------------------------"

missing_fields='{"nftId": "550e8400-e29b-41d4-a716-************"}'
test_error_scenario "POST" "$MARKETPLACE_API/listings" "$missing_fields" "400" "Missing Required Fields"

# Test 3: Invalid UUID Format
echo ""
log_info "Test 3: Invalid UUID Format"
echo "-------------------------"

invalid_uuid='{
    "nftId": "not-a-valid-uuid",
    "tokenId": "token123",
    "contractAddress": "******************************************",
    "price": "1.5",
    "currency": "ETH",
    "listingType": "fixed_price"
}'
test_error_scenario "POST" "$MARKETPLACE_API/listings" "$invalid_uuid" "400" "Invalid UUID Format"

# Test 4: Invalid Listing Type
echo ""
log_info "Test 4: Invalid Listing Type"
echo "--------------------------"

invalid_type='{
    "nftId": "550e8400-e29b-41d4-a716-************",
    "tokenId": "token123",
    "contractAddress": "******************************************",
    "price": "1.5",
    "currency": "ETH",
    "listingType": "invalid_type"
}'
test_error_scenario "POST" "$MARKETPLACE_API/listings" "$invalid_type" "400" "Invalid Listing Type"

# Test 5: Missing User ID Header
echo ""
log_info "Test 5: Missing User ID Header"
echo "----------------------------"

valid_data='{
    "nftId": "550e8400-e29b-41d4-a716-************",
    "tokenId": "token123",
    "contractAddress": "******************************************",
    "price": "1.5",
    "currency": "ETH",
    "listingType": "fixed_price"
}'

# Test without x-user-id header
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -d "$valid_data" \
    "$MARKETPLACE_API/listings")

http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

log_info "Testing: Missing User ID Header"
echo "  Expected Status: 400 or 401"
echo "  Actual Status: $http_status"
echo "  Response: $(echo "$response_body" | head -c 150)..."

if [[ "$http_status" =~ ^(400|401|500)$ ]]; then
    log_success "Missing User ID Header - CORRECT ERROR HANDLING"
else
    log_error "Missing User ID Header - UNEXPECTED STATUS (Expected: 400/401, Got: $http_status)"
fi

# Test 6: Non-existent Listing ID
echo ""
log_info "Test 6: Non-existent Listing ID"
echo "-----------------------------"

test_error_scenario "GET" "$MARKETPLACE_API/listings/00000000-0000-0000-0000-000000000000" "" "404" "Non-existent Listing ID"

# Test 7: Invalid Offer Amount
echo ""
log_info "Test 7: Invalid Offer Amount"
echo "--------------------------"

invalid_offer='{
    "listingId": "550e8400-e29b-41d4-a716-************",
    "amount": "-1.0",
    "currency": "ETH"
}'
test_error_scenario "POST" "$MARKETPLACE_API/offers" "$invalid_offer" "400" "Invalid Offer Amount"

# Test 8: Service Unavailable Simulation
echo ""
log_info "Test 8: Service Unavailable (API Gateway)"
echo "---------------------------------------"

# Test API Gateway with non-existent service endpoint
test_error_scenario "GET" "$API_GATEWAY/nonexistent" "" "404" "Non-existent Endpoint"

echo ""
log_success "🎉 Error Handling Testing Complete!"
echo ""
log_info "📊 Error Handling Summary:"
log_info "- Invalid data validation: Tested"
log_info "- Missing field validation: Tested"
log_info "- Authentication errors: Tested"
log_info "- Resource not found errors: Tested"
log_info "- Service unavailable scenarios: Tested"
echo ""
