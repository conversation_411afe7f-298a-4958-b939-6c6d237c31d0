import { Injectable, LogLevel } from '@nestjs/common';

@Injectable()
export class LoggerService {
  private context: string = '';

  setContext(context: string): void {
    this.context = context;
  }

  log(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${this.context}] ${message}`, data ? JSON.stringify(data) : '');
  }

  error(message: string, trace?: string, data?: any): void {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] [${this.context}] ERROR: ${message}`, {
      trace,
      data,
    });
  }

  warn(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    console.warn(`[${timestamp}] [${this.context}] WARN: ${message}`, data ? JSON.stringify(data) : '');
  }

  debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      const timestamp = new Date().toISOString();
      console.debug(`[${timestamp}] [${this.context}] DEBUG: ${message}`, data ? JSON.stringify(data) : '');
    }
  }
}
