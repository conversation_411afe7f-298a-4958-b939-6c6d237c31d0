// Enterprise Project Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model ProjectCommand {
  id                  String   @id @default(cuid())

  // Core Project Data
  name                String
  description         String?
  ownerId             String
  category            String?
  tags                Json?

  // Project Media & Links
  images              Json?    // Array of image URLs
  website             String?
  socialMediaLinks    Json?    // Twitter, Discord, Telegram, etc.

  // Campaign Configuration
  duration            Json?    // { startDate, endDate, timezone }
  participationConditions Json? // Array of conditions/requirements

  // Analysis Configuration (Requirements-driven)
  analysisConfiguration Json?  // Complete analysis parameter configuration

  // NFT Configuration (Requirements-driven)
  nftConfiguration    Json?    // NFT design, thresholds, evolution rules

  // Blockchain Configuration (Requirements-driven)
  blockchainNetwork   String?  // ethereum, polygon, bsc, base
  contractAddress     String?
  networkConfig       Json?    // Network-specific settings

  // Project Settings
  isPublic            Boolean  @default(true)
  allowParticipation  Boolean  @default(true)
  maxParticipants     Int?

  // Status
  status              String   @default("draft") // draft, active, paused, completed, archived

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  updatedBy           String?
  version             Int      @default(1)

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  // Relationships
  campaigns           CampaignCommand[]

  @@map("project_commands")
}

model CampaignCommand {
  id                  String   @id @default(cuid())

  // Project Relationship (Requirements-driven)
  projectId           String
  project             ProjectCommand @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // Core Campaign Data
  name                String
  description         String?

  // Campaign Configuration (Requirements-driven)
  campaignType        String   @default("user_acquisition") // user_acquisition, engagement, retention
  targetAudience      Json?    // Target audience criteria
  requirements        Json?    // Participation requirements
  rewards             Json?    // Reward structure and NFT allocation

  // Social Media Integration (Requirements-driven)
  socialPlatforms     Json?    // Twitter, Farcaster, Lens configurations
  trackingParameters  Json?    // Hashtags, mentions, engagement metrics

  // NFT Generation Rules (Requirements-driven)
  nftGenerationRules  Json?    // Rules for NFT creation and evolution
  scoreCalculation    Json?    // Campaign-specific scoring overrides

  // Campaign Timeline
  startDate           DateTime
  endDate             DateTime
  timezone            String   @default("UTC")

  // Campaign Limits
  maxParticipants     Int?
  minParticipants     Int?     @default(1)
  participantCount    Int      @default(0)

  // Campaign Metrics (Requirements-driven)
  targetMetrics       Json?    // Success criteria and KPIs
  currentMetrics      Json?    // Real-time performance data

  // Status & Lifecycle
  status              String   @default("draft") // draft, active, paused, completed, cancelled
  launchApproval      Boolean  @default(false)
  approvedBy          String?
  approvedAt          DateTime?

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  updatedBy           String?
  version             Int      @default(1)

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  @@map("campaign_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model ProjectQuery {
  id                  String   @id

  // Optimized Display Data
  displayName         String
  displayDescription  String?
  ownerId             String
  ownerUsername       String?
  category            String?

  // Display Media
  featuredImage       String?
  website             String?
  socialMediaLinks    Json?

  // Campaign Display Info
  duration            Json?
  blockchainNetwork   String?
  nftRarityTypes      Json?    // Available NFT types for display

  // Status & Metrics
  status              String
  isPublic            Boolean
  participantCount    Int      @default(0)
  campaignCount       Int      @default(0)
  totalNfts           Int      @default(0)

  // Market Metrics (Requirements-driven)
  totalMarketValue    Float    @default(0.0)
  averageNftPrice     Float    @default(0.0)
  tradingVolume24h    Float    @default(0.0)
  priceChange7d       Float    @default(0.0)
  priceChange30d      Float    @default(0.0)

  // Performance Metrics
  viewCount           Int      @default(0)
  popularityScore     Float    @default(0.0)
  avgResponseTime     Float?

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("project_queries")
}

model CampaignQuery {
  id                  String   @id

  // Project Relationship
  projectId           String
  projectName         String
  projectOwner        String

  // Optimized Display Data
  displayName         String
  displayDescription  String?
  campaignType        String

  // Campaign Status & Timeline
  status              String
  startDate           DateTime
  endDate             DateTime
  timezone            String
  isActive            Boolean  @default(false)

  // Participation Metrics
  participantCount    Int      @default(0)
  maxParticipants     Int?
  minParticipants     Int      @default(1)
  participationRate   Float    @default(0.0)

  // Performance Metrics (Requirements-driven)
  totalNftsMinted     Int      @default(0)
  averageScore        Float    @default(0.0)
  engagementRate      Float    @default(0.0)
  conversionRate      Float    @default(0.0)

  // Social Media Metrics
  totalTweets         Int      @default(0)
  totalLikes          Int      @default(0)
  totalRetweets       Int      @default(0)
  totalMentions       Int      @default(0)

  // Campaign Success Metrics
  targetMetrics       Json?
  currentMetrics      Json?
  successRate         Float    @default(0.0)

  // Display Configuration
  featuredImage       String?
  socialPlatforms     Json?
  rewards             Json?

  // Approval & Launch
  launchApproval      Boolean  @default(false)
  approvedBy          String?
  approvedAt          DateTime?

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("campaign_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "project", "campaign", "participation"
  entityId        String
  action          String   // "create", "update", "delete", "join", "leave"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model ProjectEvent {
  id              String   @id @default(cuid())
  eventType       String   // "project_created", "campaign_started", "user_joined"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Project/Campaign ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("project_events")
}
