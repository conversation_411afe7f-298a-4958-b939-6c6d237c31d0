import { Controller, Get, Param } from '@nestjs/common';
import { TwitterService } from './twitter.service';
import { TwitterUser } from '../interfaces/twitter.interfaces';

@Controller('users')
export class UsersController {
  constructor(private readonly twitterService: TwitterService) {}

  @Get(':username')
  async getUserByUsername(@Param('username') username: string) {
    const user = await this.twitterService.getUserByUsername(username);
    return user ? { status: 'success', data: user } : { status: 'error', message: 'User not found' };
  }
}
