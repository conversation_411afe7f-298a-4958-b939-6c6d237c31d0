// Test Project Service Implementation
const { PrismaClient } = require('@prisma/client');

async function testProjectService() {
  console.log('🧪 Testing Project Service Implementation...\n');
  
  const prisma = new PrismaClient();
  
  try {
    // Test 1: Database Connection
    console.log('1️⃣ Testing Database Connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');
    
    // Test 2: Create Sample Project
    console.log('2️⃣ Testing Project Creation...');
    
    const sampleProject = {
      name: 'Test DeFi Project',
      description: 'A sample DeFi project for testing',
      ownerId: 'test-owner-123',
      category: 'DeFi',
      images: ['https://example.com/image1.jpg'],
      website: 'https://testdefi.com',
      socialMediaLinks: {
        twitter: 'https://twitter.com/testdefi',
        discord: 'https://discord.gg/testdefi'
      },
      duration: {
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        timezone: 'UTC'
      },
      participationConditions: ['Follow on Twitter', 'Join Discord'],
      analysisConfiguration: {
        fixedParameters: {
          hasBio: { weight: 10, enabled: true },
          hasAvatar: { weight: 5, enabled: true },
          hasBanner: { weight: 5, enabled: true },
          followerCategories: [
            { name: 'Small', min: 0, max: 1000, weight: 5 },
            { name: 'Medium', min: 1001, max: 10000, weight: 10 },
            { name: 'Large', min: 10001, max: 100000, weight: 15 }
          ],
          engagementRate: { weight: 20, enabled: true },
          accountAge: { weight: 10, enabled: true }
        },
        variableParameters: {
          activityLevel: { weight: 15, enabled: true },
          contentQuality: { weight: 10, enabled: true },
          projectInteractions: { weight: 15, enabled: true },
          referrals: { weight: 5, enabled: true },
          campaignLoyalty: { weight: 10, enabled: true }
        },
        updateFrequencyHours: 24
      },
      nftConfiguration: {
        scoreThresholds: {
          common: 0,
          rare: 50,
          legendary: 80
        },
        design: {
          theme: 'futuristic',
          style: 'modern',
          mainColor: '#00ff88',
          fixedElements: ['logo', 'project-name']
        },
        evolutionTriggers: [
          { type: 'score_increase', condition: 'score >= 50', targetRarity: 'rare' },
          { type: 'score_increase', condition: 'score >= 80', targetRarity: 'legendary' }
        ],
        blockchainUpdatePolicy: 'batched'
      },
      blockchainNetwork: 'ethereum',
      isPublic: true,
      allowParticipation: true,
      maxParticipants: 1000
    };
    
    const createdProject = await prisma.projectCommand.create({
      data: {
        ...sampleProject,
        socialMediaLinks: JSON.stringify(sampleProject.socialMediaLinks),
        duration: JSON.stringify(sampleProject.duration),
        analysisConfiguration: JSON.stringify(sampleProject.analysisConfiguration),
        nftConfiguration: JSON.stringify(sampleProject.nftConfiguration),
        networkConfig: JSON.stringify({ chainId: 1, rpcUrl: 'https://mainnet.infura.io' }),
        createdBy: sampleProject.ownerId,
        updatedBy: sampleProject.ownerId
      }
    });
    
    console.log('✅ Project created successfully:', {
      id: createdProject.id,
      name: createdProject.name,
      status: createdProject.status,
      blockchainNetwork: createdProject.blockchainNetwork
    });
    console.log('');
    
    // Test 3: Create Query Model
    console.log('3️⃣ Testing Query Model Creation...');
    
    const queryProject = await prisma.projectQuery.create({
      data: {
        id: createdProject.id,
        displayName: createdProject.name,
        displayDescription: createdProject.description,
        ownerId: createdProject.ownerId,
        category: createdProject.category,
        featuredImage: sampleProject.images[0],
        website: createdProject.website,
        socialMediaLinks: createdProject.socialMediaLinks,
        duration: createdProject.duration,
        blockchainNetwork: createdProject.blockchainNetwork,
        nftRarityTypes: JSON.stringify({
          common: { threshold: 0, available: true },
          rare: { threshold: 50, available: true },
          legendary: { threshold: 80, available: true }
        }),
        status: createdProject.status,
        isPublic: createdProject.isPublic,
        createdAt: createdProject.createdAt
      }
    });
    
    console.log('✅ Query model created successfully:', {
      id: queryProject.id,
      displayName: queryProject.displayName,
      blockchainNetwork: queryProject.blockchainNetwork
    });
    console.log('');
    
    // Test 4: Query Operations
    console.log('4️⃣ Testing Query Operations...');
    
    const projects = await prisma.projectQuery.findMany({
      where: { isPublic: true },
      take: 5
    });
    
    console.log(`✅ Found ${projects.length} public projects`);
    console.log('');
    
    // Test 5: Update Operations
    console.log('5️⃣ Testing Update Operations...');
    
    const updatedProject = await prisma.projectCommand.update({
      where: { id: createdProject.id },
      data: {
        description: 'Updated description for testing',
        version: { increment: 1 }
      }
    });
    
    console.log('✅ Project updated successfully:', {
      id: updatedProject.id,
      version: updatedProject.version,
      description: updatedProject.description
    });
    console.log('');
    
    // Test 6: Complex Queries
    console.log('6️⃣ Testing Complex Queries...');
    
    const stats = await prisma.projectQuery.aggregate({
      _count: { id: true },
      _avg: { popularityScore: true },
      _sum: { participantCount: true }
    });
    
    console.log('✅ Statistics calculated:', {
      totalProjects: stats._count.id,
      avgPopularity: stats._avg.popularityScore,
      totalParticipants: stats._sum.participantCount
    });
    console.log('');
    
    // Test 7: Campaign Integration
    console.log('7️⃣ Testing Campaign Integration...');

    const sampleCampaign = {
      projectId: createdProject.id,
      name: 'Test User Acquisition Campaign',
      description: 'A sample campaign for testing project-campaign integration',
      campaignType: 'user_acquisition',
      startDate: '2024-01-15T00:00:00Z',
      endDate: '2024-02-15T23:59:59Z',
      timezone: 'UTC',
      targetAudience: {
        minFollowers: 100,
        maxFollowers: 10000,
        minAccountAge: 30,
        minEngagementRate: 2.0
      },
      requirements: ['Follow on Twitter', 'Retweet announcement', 'Join Discord'],
      rewards: {
        nftRewards: {
          common: { probability: 60, bonusPoints: 10 },
          rare: { probability: 30, bonusPoints: 25 },
          legendary: { probability: 10, bonusPoints: 50 }
        }
      },
      socialPlatforms: [
        {
          platform: 'twitter',
          enabled: true,
          requiredHashtags: ['#TestDeFi', '#NFT'],
          requiredMentions: ['@testdefi'],
          minDailyInteractions: 3
        }
      ],
      nftGenerationRules: {
        autoMintOnJoin: true,
        evolutionFrequencyHours: 24,
        scoreMultipliers: { 'twitter_engagement': 1.5 }
      },
      targetMetrics: {
        targetParticipants: 500,
        targetEngagementRate: 5.0,
        targetNftMints: 300
      },
      maxParticipants: 1000,
      minParticipants: 10
    };

    const createdCampaign = await prisma.campaignCommand.create({
      data: {
        ...sampleCampaign,
        targetAudience: JSON.stringify(sampleCampaign.targetAudience),
        rewards: JSON.stringify(sampleCampaign.rewards),
        socialPlatforms: JSON.stringify(sampleCampaign.socialPlatforms),
        nftGenerationRules: JSON.stringify(sampleCampaign.nftGenerationRules),
        targetMetrics: JSON.stringify(sampleCampaign.targetMetrics),
        startDate: new Date(sampleCampaign.startDate),
        endDate: new Date(sampleCampaign.endDate),
        createdBy: createdProject.ownerId,
        updatedBy: createdProject.ownerId
      }
    });

    console.log('✅ Campaign created successfully:', {
      id: createdCampaign.id,
      name: createdCampaign.name,
      projectId: createdCampaign.projectId,
      campaignType: createdCampaign.campaignType
    });
    console.log('');

    // Test 8: Campaign Query Model
    console.log('8️⃣ Testing Campaign Query Model...');

    const queryCampaign = await prisma.campaignQuery.create({
      data: {
        id: createdCampaign.id,
        projectId: createdCampaign.projectId,
        projectName: createdProject.name,
        projectOwner: createdProject.ownerId,
        displayName: createdCampaign.name,
        displayDescription: createdCampaign.description,
        campaignType: createdCampaign.campaignType,
        status: createdCampaign.status,
        startDate: createdCampaign.startDate,
        endDate: createdCampaign.endDate,
        timezone: createdCampaign.timezone,
        isActive: false,
        maxParticipants: createdCampaign.maxParticipants,
        minParticipants: createdCampaign.minParticipants,
        socialPlatforms: createdCampaign.socialPlatforms,
        rewards: createdCampaign.rewards,
        targetMetrics: createdCampaign.targetMetrics,
        launchApproval: createdCampaign.launchApproval,
        createdAt: createdCampaign.createdAt
      }
    });

    console.log('✅ Campaign query model created successfully:', {
      id: queryCampaign.id,
      displayName: queryCampaign.displayName,
      projectName: queryCampaign.projectName
    });
    console.log('');

    // Test 9: Project-Campaign Relationship
    console.log('9️⃣ Testing Project-Campaign Relationship...');

    const projectWithCampaigns = await prisma.projectCommand.findUnique({
      where: { id: createdProject.id },
      include: { campaigns: true }
    });

    console.log('✅ Project-Campaign relationship verified:', {
      projectId: projectWithCampaigns.id,
      campaignCount: projectWithCampaigns.campaigns.length,
      campaignNames: projectWithCampaigns.campaigns.map(c => c.name)
    });
    console.log('');

    console.log('🎉 All tests passed! Project & Campaign Service integration is working correctly.\n');

    // Cleanup
    console.log('🧹 Cleaning up test data...');
    await prisma.campaignQuery.delete({ where: { id: createdCampaign.id } });
    await prisma.campaignCommand.delete({ where: { id: createdCampaign.id } });
    await prisma.projectQuery.delete({ where: { id: createdProject.id } });
    await prisma.projectCommand.delete({ where: { id: createdProject.id } });
    console.log('✅ Cleanup completed\n');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testProjectService().catch(console.error);
