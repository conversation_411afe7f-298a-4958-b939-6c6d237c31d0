// Enterprise Health Controller
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { HealthCheck, HealthCheckService, HealthCheckResult } from '@nestjs/terminus';
import { PrismaService } from '../shared/prisma.service';
import { AuditService } from '../shared/audit.service';
import { EventService } from '../shared/event.service';

@ApiTags('Health Monitoring')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
    private readonly eventService: EventService
  ) {}

  @Get()
  @ApiOperation({ summary: 'Enterprise health check with detailed monitoring' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  @ApiResponse({ status: 503, description: 'Service is unhealthy' })
  @HealthCheck()
  async check(@Headers() headers: any, @Res() res: Response) {
    try {
      const correlationId = headers['x-correlation-id'] || `health-${Date.now()}`;
      const startTime = Date.now();

      // Basic health checks
      const isDbHealthy = await this.prisma.isHealthy();
      
      // Get enterprise metrics
      const [dbStats, auditStats, eventStats] = await Promise.all([
        this.prisma.getDatabaseStats().catch(() => null),
        this.auditService.getAuditStats().catch(() => null),
        this.eventService.getEventStats().catch(() => null)
      ]);

      const responseTime = Date.now() - startTime;
      const status = isDbHealthy ? 'ok' : 'error';

      const healthData = {
        status,
        service: 'nft-generation-service',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime(),
        port: process.env.PORT || '3003',
        environment: process.env.NODE_ENV || 'development',
        correlationId,
        responseTime,
        database: {
          healthy: isDbHealthy,
          stats: dbStats
        },
        enterprise: {
          auditLogging: process.env.ENABLE_AUDIT_LOGGING === 'true',
          eventSourcing: process.env.ENABLE_EVENT_SOURCING === 'true',
          performanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
          auditStats,
          eventStats
        },
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        }
      };

      const httpStatus = isDbHealthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      return res.status(httpStatus).json(healthData);
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: 'error',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString(),
        error: {
          message: 'Health check failed',
          details: error.message
        },
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Get('simple')
  @ApiOperation({ summary: 'Simple health check' })
  @ApiResponse({ status: 200, description: 'Service is running' })
  async simple(@Headers() headers: any, @Res() res: Response) {
    return res.status(HttpStatus.OK).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'nft-generation-service'
    });
  }

  @Get('database')
  @ApiOperation({ summary: 'Database health check' })
  @ApiResponse({ status: 200, description: 'Database is healthy' })
  @ApiResponse({ status: 503, description: 'Database is unhealthy' })
  async database(@Headers() headers: any, @Res() res: Response) {
    try {
      const isHealthy = await this.prisma.isHealthy();
      const stats = await this.prisma.getDatabaseStats();

      const httpStatus = isHealthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      return res.status(httpStatus).json({
        status: isHealthy ? 'ok' : 'error',
        database: {
          healthy: isHealthy,
          stats
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        status: 'error',
        database: {
          healthy: false,
          error: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  }
}
