'use client'

import {
  Container,
  Heading,
  Text,
  VStack,
  SimpleGrid,
  Box,
  Badge,
  Icon
} from '@chakra-ui/react'
import { FaTwitter, FaRocket, FaTrophy } from 'react-icons/fa'

export default function HowItWorks() {
  const steps = [
    {
      step: "1",
      title: "Connect Twitter",
      description: "Link your Twitter account to join campaigns",
      icon: FaTwitter
    },
    {
      step: "2", 
      title: "Join Campaigns",
      description: "Browse Web3 projects and join their campaigns",
      icon: FaRocket
    },
    {
      step: "3",
      title: "Earn Evolving NFTs",
      description: "Get NFTs that upgrade with your engagement",
      icon: FaTrophy
    }
  ]

  return (
    <Container maxW="container.xl" py={20}>
      <VStack gap={12}>
        <VStack gap={4} textAlign="center">
          <Heading as="h2" size="xl">How It Works</Heading>
          <Text fontSize="lg" color="gray.600">
            Three simple steps to start earning evolving NFTs
          </Text>
        </VStack>

        <SimpleGrid columns={{ base: 1, md: 3 }} gap={8} w="full">
          {steps.map((step) => (
            <VStack key={step.step} gap={4} textAlign="center" p={6}>
              <Box 
                bg="blue.500" 
                color="white" 
                borderRadius="full" 
                w={16} 
                h={16} 
                display="flex" 
                alignItems="center" 
                justifyContent="center"
              >
                <Icon as={step.icon} boxSize={8} />
              </Box>
              <Badge colorScheme="blue" fontSize="lg" px={3} py={1}>
                Step {step.step}
              </Badge>
              <Heading as="h3" size="md">{step.title}</Heading>
              <Text color="gray.600">{step.description}</Text>
            </VStack>
          ))}
        </SimpleGrid>
      </VStack>
    </Container>
  )
}
