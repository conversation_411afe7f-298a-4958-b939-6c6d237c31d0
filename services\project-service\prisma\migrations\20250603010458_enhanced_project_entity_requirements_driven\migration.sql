-- CreateTable
CREATE TABLE "project_commands" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "ownerId" TEXT NOT NULL,
    "category" TEXT,
    "tags" JSONB,
    "images" JSONB,
    "website" TEXT,
    "socialMediaLinks" JSONB,
    "duration" JSONB,
    "participationConditions" JSONB,
    "analysisConfiguration" JSONB,
    "nftConfiguration" JSONB,
    "blockchainNetwork" TEXT,
    "contractAddress" TEXT,
    "networkConfig" JSONB,
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "allowParticipation" BOOLEAN NOT NULL DEFAULT true,
    "maxParticipants" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "dataClassification" TEXT NOT NULL DEFAULT 'internal',
    "retentionPolicy" TEXT NOT NULL DEFAULT '7years',

    CONSTRAINT "project_commands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "campaign_commands" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "requirements" JSONB,
    "rewards" JSONB,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "participantCount" INTEGER NOT NULL DEFAULT 0,
    "maxParticipants" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "campaign_commands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_queries" (
    "id" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "displayDescription" TEXT,
    "ownerId" TEXT NOT NULL,
    "ownerUsername" TEXT,
    "category" TEXT,
    "featuredImage" TEXT,
    "website" TEXT,
    "socialMediaLinks" JSONB,
    "duration" JSONB,
    "blockchainNetwork" TEXT,
    "nftRarityTypes" JSONB,
    "status" TEXT NOT NULL,
    "isPublic" BOOLEAN NOT NULL,
    "participantCount" INTEGER NOT NULL DEFAULT 0,
    "campaignCount" INTEGER NOT NULL DEFAULT 0,
    "totalNfts" INTEGER NOT NULL DEFAULT 0,
    "totalMarketValue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "averageNftPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "tradingVolume24h" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "priceChange7d" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "priceChange30d" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "popularityScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "avgResponseTime" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_queries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "oldValues" JSONB,
    "newValues" JSONB,
    "userId" TEXT,
    "sessionId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "correlationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_events" (
    "id" TEXT NOT NULL,
    "eventType" TEXT NOT NULL,
    "eventVersion" TEXT NOT NULL DEFAULT '1.0',
    "aggregateId" TEXT NOT NULL,
    "eventData" JSONB NOT NULL,
    "correlationId" TEXT,
    "causationId" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "project_events_pkey" PRIMARY KEY ("id")
);
