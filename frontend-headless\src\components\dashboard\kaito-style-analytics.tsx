'use client';

import React from 'react';
import { 
  ChartBarIcon, 
  UsersIcon, 
  CurrencyDollarIcon, 
  ClockIcon 
} from '@heroicons/react/24/outline';
import { ArrowTrendingUpIcon } from '@heroicons/react/24/outline';

export default function KaitoStyleAnalytics() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-white">NFT Platform Leaderboard</h1>
            <div className="flex items-center space-x-2 bg-teal-600 px-3 py-1 rounded-full">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">Certified Platform</span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm">
              <option>7d</option>
              <option>30d</option>
              <option>90d</option>
            </select>
            <button className="bg-teal-600 hover:bg-teal-700 px-4 py-2 rounded text-sm font-medium">
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Section 1: NFT Platform Leaderboard - Treemap Style */}
      <div className="px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Side: Top Performers List */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white">Yapper Leaderboard</h3>
                <span className="text-sm text-gray-400">Top 10</span>
              </div>
              
              <div className="space-y-2">
                {[
                  { rank: 1, name: '@alice_crypto', score: 2.5, change: '+12.5%', trend: 'up' },
                  { rank: 2, name: '@bob_nft', score: 2.3, change: '+8.2%', trend: 'up' },
                  { rank: 3, name: '@charlie_art', score: 2.1, change: '+5.7%', trend: 'up' },
                  { rank: 4, name: '@diana_meta', score: 1.9, change: '-2.1%', trend: 'down' },
                  { rank: 5, name: '@eve_crypto', score: 1.8, change: '+3.4%', trend: 'up' }
                ].map((user) => (
                  <div key={user.rank} className="flex items-center justify-between p-2 hover:bg-gray-700 rounded">
                    <div className="flex items-center space-x-3">
                      <span className="text-gray-400 text-sm w-6">{user.rank}</span>
                      <div className="w-8 h-8 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{user.name.slice(1, 3).toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="text-white text-sm font-medium">{user.name}</div>
                        <div className="text-gray-400 text-xs">{user.score}k</div>
                      </div>
                    </div>
                    <div className={`text-xs font-bold ${user.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                      {user.change}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side: Treemap Visualization */}
          <div className="lg:col-span-3">
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white">Performance Treemap</h3>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <span>Size: Score</span>
                  <span>•</span>
                  <span>Color: Growth</span>
                </div>
              </div>
              
              <div className="relative h-96 bg-gray-900 rounded-lg p-4 overflow-hidden">
                {/* Large performers */}
                <div className="absolute bg-gradient-to-br from-green-600 to-green-700 rounded" 
                     style={{left: '2%', top: '5%', width: '28%', height: '40%'}}>
                  <div className="p-3 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-sm">@alice_crypto</div>
                      <div className="text-green-200 text-xs">Epic NFT Creator</div>
                    </div>
                    <div>
                      <div className="text-white text-lg font-bold">2.5k</div>
                      <div className="text-green-200 text-xs">+12.5% ↗</div>
                    </div>
                  </div>
                </div>

                <div className="absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded" 
                     style={{left: '32%', top: '5%', width: '25%', height: '35%'}}>
                  <div className="p-3 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-sm">@bob_nft</div>
                      <div className="text-blue-200 text-xs">Rare Collector</div>
                    </div>
                    <div>
                      <div className="text-white text-lg font-bold">2.3k</div>
                      <div className="text-blue-200 text-xs">+8.2% ↗</div>
                    </div>
                  </div>
                </div>

                <div className="absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded" 
                     style={{left: '59%', top: '5%', width: '20%', height: '30%'}}>
                  <div className="p-2 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-xs">@charlie_art</div>
                      <div className="text-purple-200 text-xs">Artist</div>
                    </div>
                    <div>
                      <div className="text-white text-sm font-bold">2.1k</div>
                      <div className="text-purple-200 text-xs">+5.7%</div>
                    </div>
                  </div>
                </div>

                {/* Medium performers */}
                <div className="absolute bg-gradient-to-br from-red-600 to-red-700 rounded" 
                     style={{left: '81%', top: '5%', width: '17%', height: '25%'}}>
                  <div className="p-2 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-xs">@diana_meta</div>
                    </div>
                    <div>
                      <div className="text-white text-sm font-bold">1.9k</div>
                      <div className="text-red-200 text-xs">-2.1% ↘</div>
                    </div>
                  </div>
                </div>

                {/* Smaller performers */}
                <div className="absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded" 
                     style={{left: '2%', top: '47%', width: '18%', height: '25%'}}>
                  <div className="p-2 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-xs">@eve_crypto</div>
                    </div>
                    <div>
                      <div className="text-white text-sm font-bold">1.8k</div>
                      <div className="text-teal-200 text-xs">+3.4%</div>
                    </div>
                  </div>
                </div>

                <div className="absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded" 
                     style={{left: '22%', top: '47%', width: '15%', height: '20%'}}>
                  <div className="p-2 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-white font-bold text-xs">@frank_nft</div>
                    </div>
                    <div>
                      <div className="text-white text-sm font-bold">1.6k</div>
                      <div className="text-orange-200 text-xs">+1.2%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: NFT Platform Social Graph */}
      <div className="px-6 py-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">NFT Platform Social Graph</h2>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>Connections: 1,247</span>
              <span>•</span>
              <span>Active Users: 89</span>
            </div>
          </div>

          <div className="relative h-96 bg-gray-900 rounded-lg overflow-hidden">
            {/* Central hub */}
            <div className="absolute w-16 h-16 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center border-4 border-teal-300"
                 style={{left: '50%', top: '50%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">HUB</span>
            </div>

            {/* Connection lines */}
            <svg className="absolute inset-0 w-full h-full">
              {/* Lines from center to various nodes */}
              <line x1="50%" y1="50%" x2="20%" y2="20%" stroke="#10B981" strokeWidth="2" opacity="0.6" />
              <line x1="50%" y1="50%" x2="80%" y2="25%" stroke="#3B82F6" strokeWidth="2" opacity="0.6" />
              <line x1="50%" y1="50%" x2="15%" y2="70%" stroke="#8B5CF6" strokeWidth="2" opacity="0.6" />
              <line x1="50%" y1="50%" x2="85%" y2="75%" stroke="#F59E0B" strokeWidth="2" opacity="0.6" />
              <line x1="50%" y1="50%" x2="30%" y2="85%" stroke="#EF4444" strokeWidth="2" opacity="0.6" />
              <line x1="50%" y1="50%" x2="70%" y2="15%" stroke="#06B6D4" strokeWidth="2" opacity="0.6" />

              {/* Inter-node connections */}
              <line x1="20%" y1="20%" x2="80%" y2="25%" stroke="#10B981" strokeWidth="1" opacity="0.3" />
              <line x1="15%" y1="70%" x2="30%" y2="85%" stroke="#8B5CF6" strokeWidth="1" opacity="0.3" />
              <line x1="80%" y1="25%" x2="85%" y2="75%" stroke="#3B82F6" strokeWidth="1" opacity="0.3" />
            </svg>

            {/* Network nodes */}
            <div className="absolute w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center border-2 border-green-300"
                 style={{left: '20%', top: '20%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">AC</span>
            </div>

            <div className="absolute w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center border-2 border-blue-300"
                 style={{left: '80%', top: '25%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">BN</span>
            </div>

            <div className="absolute w-11 h-11 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center border-2 border-purple-300"
                 style={{left: '15%', top: '70%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">CA</span>
            </div>

            <div className="absolute w-9 h-9 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center border-2 border-yellow-300"
                 style={{left: '85%', top: '75%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">DM</span>
            </div>

            <div className="absolute w-8 h-8 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center border-2 border-red-300"
                 style={{left: '30%', top: '85%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">EC</span>
            </div>

            <div className="absolute w-10 h-10 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center border-2 border-cyan-300"
                 style={{left: '70%', top: '15%', transform: 'translate(-50%, -50%)'}}>
              <span className="text-white text-xs font-bold">FN</span>
            </div>

            {/* Smaller peripheral nodes */}
            {[
              {x: '10%', y: '40%', color: 'from-pink-400 to-pink-600', border: 'border-pink-300'},
              {x: '90%', y: '50%', color: 'from-indigo-400 to-indigo-600', border: 'border-indigo-300'},
              {x: '40%', y: '10%', color: 'from-emerald-400 to-emerald-600', border: 'border-emerald-300'},
              {x: '60%', y: '90%', color: 'from-violet-400 to-violet-600', border: 'border-violet-300'},
            ].map((node, index) => (
              <div key={index} className={`absolute w-6 h-6 bg-gradient-to-r ${node.color} rounded-full border ${node.border}`}
                   style={{left: node.x, top: node.y, transform: 'translate(-50%, -50%)'}}></div>
            ))}

            {/* Legend */}
            <div className="absolute bottom-4 right-4 bg-gray-800 bg-opacity-90 p-3 rounded">
              <div className="text-xs text-gray-300 space-y-1">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-teal-500 rounded-full"></div>
                  <span>Platform Hub</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Top Creators</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Active Traders</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 3: Top 100 NFT Platform Users */}
      <div className="px-6 py-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Top 100 NFT Platform Users</h2>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <span>7d</span>
                <span>30d</span>
                <span>90d</span>
                <span>1y</span>
              </div>
            </div>
          </div>

          {/* Header */}
          <div className="grid grid-cols-12 gap-4 mb-4 text-xs text-gray-400 font-medium">
            <div className="col-span-1">Rank</div>
            <div className="col-span-3">User</div>
            <div className="col-span-2">Score</div>
            <div className="col-span-2">NFTs</div>
            <div className="col-span-2">Volume</div>
            <div className="col-span-2">Growth</div>
          </div>

          {/* User rows */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {[
              { rank: 1, name: '@alice_crypto', avatar: 'AC', score: 2547, nfts: 89, volume: 45.2, growth: 12.5, scoreBar: 95, nftBar: 78, volumeBar: 85, growthBar: 92 },
              { rank: 2, name: '@bob_nft_master', avatar: 'BN', score: 2341, nfts: 76, volume: 38.7, growth: 8.2, scoreBar: 88, nftBar: 65, volumeBar: 72, growthBar: 78 },
              { rank: 3, name: '@charlie_artist', avatar: 'CA', score: 2156, nfts: 92, volume: 52.1, growth: 5.7, scoreBar: 82, nftBar: 85, volumeBar: 95, growthBar: 65 },
              { rank: 4, name: '@diana_collector', avatar: 'DC', score: 1987, nfts: 45, volume: 28.9, growth: -2.1, scoreBar: 75, nftBar: 42, volumeBar: 55, growthBar: 25 },
              { rank: 5, name: '@eve_crypto_queen', avatar: 'EC', score: 1834, nfts: 67, volume: 34.5, growth: 3.4, scoreBar: 70, nftBar: 58, volumeBar: 65, growthBar: 58 }
            ].map((user) => (
              <div key={user.rank} className="grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-700 rounded-lg transition-colors">
                {/* Rank */}
                <div className="col-span-1 text-gray-300 text-sm font-medium">
                  {user.rank}
                </div>

                {/* User */}
                <div className="col-span-3 flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">{user.avatar}</span>
                  </div>
                  <div>
                    <div className="text-white text-sm font-medium">{user.name}</div>
                    <div className="text-gray-400 text-xs">Verified Creator</div>
                  </div>
                </div>

                {/* Score with bar */}
                <div className="col-span-2">
                  <div className="text-white text-sm font-bold mb-1">{user.score.toLocaleString()}</div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                         style={{width: `${user.scoreBar}%`}}></div>
                  </div>
                </div>

                {/* NFTs with bar */}
                <div className="col-span-2">
                  <div className="text-white text-sm font-bold mb-1">{user.nfts}</div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full"
                         style={{width: `${user.nftBar}%`}}></div>
                  </div>
                </div>

                {/* Volume with bar */}
                <div className="col-span-2">
                  <div className="text-white text-sm font-bold mb-1">{user.volume} ETH</div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full"
                         style={{width: `${user.volumeBar}%`}}></div>
                  </div>
                </div>

                {/* Growth with bar */}
                <div className="col-span-2">
                  <div className={`text-sm font-bold mb-1 ${user.growth >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {user.growth >= 0 ? '+' : ''}{user.growth}%
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className={`h-2 rounded-full ${user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'}`}
                         style={{width: `${user.growthBar}%`}}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
