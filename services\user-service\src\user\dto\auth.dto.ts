import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'Email address or username',
    example: '<EMAIL>',
  })
  @IsString()
  @MaxLength(100, { message: 'Email/username must not exceed 100 characters' })
  emailOrUsername: string;

  @ApiProperty({
    description: 'User password',
    example: 'SecurePassword123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @ApiPropertyOptional({
    description: 'Remember me for extended session',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean = false;

  @ApiPropertyOptional({
    description: 'Device information for tracking',
    example: 'Chrome on Windows',
  })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class RegisterDto {
  @ApiProperty({
    description: 'Username for the user',
    example: 'johndoe',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(50, { message: 'Username must not exceed 50 characters' })
  username: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @MaxLength(100, { message: 'Email must not exceed 100 characters' })
  email: string;

  @ApiProperty({
    description: 'Password for the user account',
    example: 'SecurePassword123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @ApiProperty({
    description: 'Confirm password',
    example: 'SecurePassword123!',
  })
  @IsString()
  confirmPassword: string;

  @ApiPropertyOptional({
    description: 'Display name for the user',
    example: 'John Doe',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Display name must not exceed 100 characters' })
  displayName?: string;

  @ApiPropertyOptional({
    description: 'Device information for tracking',
    example: 'Chrome on Windows',
  })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  refreshToken: string;

  @ApiPropertyOptional({
    description: 'Device information for validation',
    example: 'Chrome on Windows',
  })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class LogoutDto {
  @ApiPropertyOptional({
    description: 'Logout from all devices',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  logoutFromAllDevices?: boolean = false;

  @ApiPropertyOptional({
    description: 'Refresh token to invalidate',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsOptional()
  @IsString()
  refreshToken?: string;
}

export class ChangePasswordDto {
  @ApiProperty({
    description: 'Current password',
    example: 'OldPassword123!',
  })
  @IsString()
  currentPassword: string;

  @ApiProperty({
    description: 'New password',
    example: 'NewSecurePassword123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: 'New password must be at least 8 characters long' })
  newPassword: string;

  @ApiProperty({
    description: 'Confirm new password',
    example: 'NewSecurePassword123!',
  })
  @IsString()
  confirmNewPassword: string;
}

export class ForgotPasswordDto {
  @ApiProperty({
    description: 'Email address for password reset',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;
}

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Password reset token',
    example: 'reset_token_here',
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'New password',
    example: 'NewSecurePassword123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  newPassword: string;

  @ApiProperty({
    description: 'Confirm new password',
    example: 'NewSecurePassword123!',
  })
  @IsString()
  confirmNewPassword: string;
}

export enum SocialProvider {
  TWITTER = 'twitter',
  FARCASTER = 'farcaster',
  LENS = 'lens',
  GOOGLE = 'google',
  GITHUB = 'github',
}

export class SocialAuthDto {
  @ApiProperty({
    description: 'Social provider',
    enum: SocialProvider,
    example: SocialProvider.TWITTER,
  })
  @IsEnum(SocialProvider, { message: 'Provider must be one of: twitter, farcaster, lens, google, github' })
  provider: SocialProvider;

  @ApiProperty({
    description: 'Authorization code from social provider',
    example: 'auth_code_from_provider',
  })
  @IsString()
  code: string;

  @ApiPropertyOptional({
    description: 'State parameter for CSRF protection',
    example: 'random_state_string',
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: 'Redirect URI used in OAuth flow',
    example: 'https://app.example.com/auth/callback',
  })
  @IsOptional()
  @IsString()
  redirectUri?: string;

  @ApiPropertyOptional({
    description: 'Device information for tracking',
    example: 'Chrome on Windows',
  })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class AuthResponseDto {
  @ApiProperty({
    description: 'Access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: 'Access token expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'User information',
  })
  user: {
    id: string;
    username: string;
    email: string;
    displayName?: string;
    role: string;
    isEmailVerified: boolean;
    isProfileComplete: boolean;
  };

  @ApiPropertyOptional({
    description: 'Session information',
  })
  session?: {
    id: string;
    deviceInfo?: string;
    createdAt: string;
    expiresAt: string;
  };
}
