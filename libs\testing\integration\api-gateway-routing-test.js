// Targeted API Gateway Routing Test
// Tests specific routing issues and endpoint accessibility

const axios = require('axios');

const PROJECT_SERVICE_URL = 'http://localhost:3005';
const API_GATEWAY_URL = 'http://localhost:3010';

async function testAPIGatewayRouting() {
  console.log('🔍 TARGETED API GATEWAY ROUTING TEST\n');
  
  const results = [];
  
  try {
    // Test 1: API Gateway root (should fail - no root endpoint)
    console.log('1️⃣ Testing API Gateway Root Endpoint...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/`);
      results.push({ test: 'Root Endpoint', status: 'UNEXPECTED_PASS', details: response.status });
    } catch (error) {
      if (error.response?.status === 404) {
        results.push({ test: 'Root Endpoint', status: 'EXPECTED_FAIL', details: '404 - No root endpoint (correct)' });
        console.log('✅ Expected 404 - API Gateway has no root endpoint');
      } else {
        results.push({ test: 'Root Endpoint', status: 'UNEXPECTED_ERROR', details: error.message });
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
    // Test 2: API Gateway /api prefix
    console.log('\n2️⃣ Testing API Gateway /api Prefix...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/api`);
      results.push({ test: 'API Prefix', status: 'PASS', details: response.status });
      console.log('✅ /api endpoint accessible:', response.status);
    } catch (error) {
      results.push({ test: 'API Prefix', status: 'FAIL', details: error.message });
      console.log('❌ /api endpoint failed:', error.message);
    }
    
    // Test 3: API Gateway Health
    console.log('\n3️⃣ Testing API Gateway Health...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/api/health`);
      results.push({ test: 'Health Endpoint', status: 'PASS', details: response.data.status });
      console.log('✅ Health endpoint working:', response.data.status);
    } catch (error) {
      results.push({ test: 'Health Endpoint', status: 'FAIL', details: error.message });
      console.log('❌ Health endpoint failed:', error.message);
    }
    
    // Test 4: API Gateway Projects
    console.log('\n4️⃣ Testing API Gateway Projects...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/api/projects`);
      results.push({ test: 'Projects Endpoint', status: 'PASS', details: { 
        status: response.status, 
        hasData: !!response.data,
        projectCount: response.data?.data?.projects?.length || 0
      }});
      console.log('✅ Projects endpoint working:', {
        status: response.status,
        projectCount: response.data?.data?.projects?.length || 0
      });
    } catch (error) {
      results.push({ test: 'Projects Endpoint', status: 'FAIL', details: error.message });
      console.log('❌ Projects endpoint failed:', error.message);
    }
    
    // Test 5: API Gateway Campaigns
    console.log('\n5️⃣ Testing API Gateway Campaigns...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/api/campaigns`);
      results.push({ test: 'Campaigns Endpoint', status: 'PASS', details: {
        status: response.status,
        hasData: !!response.data,
        campaignCount: response.data?.data?.campaigns?.length || 0
      }});
      console.log('✅ Campaigns endpoint working:', {
        status: response.status,
        campaignCount: response.data?.data?.campaigns?.length || 0
      });
    } catch (error) {
      results.push({ test: 'Campaigns Endpoint', status: 'FAIL', details: error.message });
      console.log('❌ Campaigns endpoint failed:', error.message);
    }
    
    // Test 6: Compare Direct vs Gateway
    console.log('\n6️⃣ Comparing Direct Service vs API Gateway...');
    try {
      const [directResponse, gatewayResponse] = await Promise.all([
        axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects`),
        axios.get(`${API_GATEWAY_URL}/api/projects`)
      ]);
      
      const comparison = {
        direct: {
          status: directResponse.status,
          projectCount: directResponse.data?.data?.projects?.length || 0,
          responseTime: directResponse.headers['x-response-time'] || 'N/A'
        },
        gateway: {
          status: gatewayResponse.status,
          projectCount: gatewayResponse.data?.data?.projects?.length || 0,
          responseTime: gatewayResponse.headers['x-response-time'] || 'N/A'
        }
      };
      
      const isMatching = comparison.direct.projectCount === comparison.gateway.projectCount;
      
      results.push({ test: 'Direct vs Gateway', status: isMatching ? 'PASS' : 'MISMATCH', details: comparison });
      console.log(isMatching ? '✅' : '⚠️', 'Direct vs Gateway comparison:', comparison);
      
    } catch (error) {
      results.push({ test: 'Direct vs Gateway', status: 'FAIL', details: error.message });
      console.log('❌ Comparison failed:', error.message);
    }
    
    // Test 7: API Gateway Documentation
    console.log('\n7️⃣ Testing API Gateway Documentation...');
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/api/docs`);
      results.push({ test: 'API Documentation', status: 'PASS', details: 'Swagger docs accessible' });
      console.log('✅ API Documentation accessible');
    } catch (error) {
      results.push({ test: 'API Documentation', status: 'FAIL', details: error.message });
      console.log('❌ API Documentation failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
  
  // Summary
  console.log('\n📊 ROUTING TEST SUMMARY:');
  console.log('========================');
  
  const passed = results.filter(r => r.status === 'PASS').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  const expectedFails = results.filter(r => r.status === 'EXPECTED_FAIL').length;
  const mismatches = results.filter(r => r.status === 'MISMATCH').length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⚠️ Mismatches: ${mismatches}`);
  console.log(`📝 Expected Fails: ${expectedFails}`);
  
  const totalTests = results.length;
  const successfulTests = passed + expectedFails; // Expected fails are actually correct behavior
  const successRate = ((successfulTests / totalTests) * 100).toFixed(1);
  
  console.log(`📈 Success Rate: ${successRate}%`);
  
  if (failed === 0 && mismatches === 0) {
    console.log('\n🎉 API Gateway routing is working correctly!');
    console.log('✅ All endpoints accessible through /api prefix');
    console.log('✅ Proxy service forwarding requests properly');
    console.log('✅ Direct service and gateway responses match');
  } else {
    console.log('\n⚠️ Some routing issues detected:');
    results.filter(r => r.status === 'FAIL' || r.status === 'MISMATCH').forEach(result => {
      console.log(`   - ${result.test}: ${result.details}`);
    });
  }
}

testAPIGatewayRouting().catch(console.error);
