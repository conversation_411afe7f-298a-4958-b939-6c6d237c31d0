'use client'

import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Badge
} from '@chakra-ui/react'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface SocialConnection {
  platform: string
  handle: string
  isConnected: boolean
  isVerified: boolean
  lastSync?: string
  icon: string
  color: string
}

interface SocialConnectionsProps {
  onConnectionChange: (platform: string, connected: boolean) => void
}

export default function SocialConnections({ onConnectionChange }: SocialConnectionsProps) {
  const { user } = useAuth()
  const [connections, setConnections] = useState<SocialConnection[]>([
    {
      platform: 'Twitter',
      handle: user?.twitterHandle || '',
      isConnected: !!user?.twitterHandle,
      isVerified: true,
      lastSync: '2 hours ago',
      icon: '🐦',
      color: 'blue'
    },
    {
      platform: 'Discord',
      handle: user?.discordHandle || '',
      isConnected: !!user?.discordHandle,
      isVerified: false,
      icon: '💬',
      color: 'purple'
    },
    {
      platform: 'Instagram',
      handle: '',
      isConnected: false,
      isVerified: false,
      icon: '📷',
      color: 'pink'
    },
    {
      platform: 'LinkedIn',
      handle: '',
      isConnected: false,
      isVerified: false,
      icon: '💼',
      color: 'blue'
    },
    {
      platform: 'GitHub',
      handle: '',
      isConnected: false,
      isVerified: false,
      icon: '⚡',
      color: 'gray'
    }
  ])

  const [isConnecting, setIsConnecting] = useState<string | null>(null)

  // Simple toast replacement
  const showMessage = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    alert(`${type.toUpperCase()}: ${message}`)
  }

  const handleConnect = async (platform: string) => {
    setIsConnecting(platform)

    try {
      // Simulate OAuth connection flow
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Update connection status
      setConnections(prev => prev.map(conn =>
        conn.platform === platform
          ? {
              ...conn,
              isConnected: true,
              handle: `@${user?.username || 'user'}`,
              lastSync: 'Just now'
            }
          : conn
      ))

      onConnectionChange(platform, true)

      showMessage(`Successfully connected your ${platform} account`, 'success')
    } catch (error) {
      showMessage(`Failed to connect ${platform}. Please try again.`, 'error')
    } finally {
      setIsConnecting(null)
    }
  }

  const handleDisconnect = async (platform: string) => {
    setIsConnecting(platform)

    try {
      // Simulate disconnection
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update connection status
      setConnections(prev => prev.map(conn =>
        conn.platform === platform
          ? {
              ...conn,
              isConnected: false,
              handle: '',
              isVerified: false,
              lastSync: undefined
            }
          : conn
      ))

      onConnectionChange(platform, false)

      showMessage(`Successfully disconnected your ${platform} account`, 'info')
    } catch (error) {
      showMessage(`Failed to disconnect ${platform}. Please try again.`, 'error')
    } finally {
      setIsConnecting(null)
    }
  }

  const handleSync = async (platform: string) => {
    setIsConnecting(platform)

    try {
      // Simulate data sync
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Update last sync time
      setConnections(prev => prev.map(conn =>
        conn.platform === platform
          ? { ...conn, lastSync: 'Just now' }
          : conn
      ))

      showMessage(`Successfully synced your ${platform} data`, 'success')
    } catch (error) {
      showMessage(`Failed to sync ${platform} data. Please try again.`, 'error')
    } finally {
      setIsConnecting(null)
    }
  }

  return (
    <Box>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        🔗 Social Media Connections
      </Text>

      <VStack gap={4} align="stretch">
        {connections.map((connection) => (
          <Box
            key={connection.platform}
            p={4}
            bg="white"
            borderRadius="lg"
            boxShadow="sm"
            border="1px solid"
            borderColor="gray.200"
          >
            <HStack justify="space-between" align="center">
              <HStack gap={3}>
                <Box fontSize="2xl">
                  {connection.icon}
                </Box>

                <VStack align="start" gap={1}>
                  <HStack gap={2}>
                    <Text fontWeight="medium">
                      {connection.platform}
                    </Text>
                    {connection.isVerified && (
                      <Badge colorScheme="green" size="sm">
                        Verified
                      </Badge>
                    )}
                  </HStack>

                  {connection.isConnected ? (
                    <VStack align="start" gap={0}>
                      <Text fontSize="sm" color="gray.600">
                        {connection.handle}
                      </Text>
                      {connection.lastSync && (
                        <Text fontSize="xs" color="gray.500">
                          Last sync: {connection.lastSync}
                        </Text>
                      )}
                    </VStack>
                  ) : (
                    <Text fontSize="sm" color="gray.500">
                      Not connected
                    </Text>
                  )}
                </VStack>
              </HStack>

              <HStack gap={2}>
                {connection.isConnected ? (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSync(connection.platform)}
                      disabled={isConnecting === connection.platform}
                    >
                      {isConnecting === connection.platform ? 'Syncing...' : 'Sync'}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      colorScheme="red"
                      onClick={() => handleDisconnect(connection.platform)}
                      disabled={isConnecting === connection.platform}
                    >
                      {isConnecting === connection.platform ? 'Disconnecting...' : 'Disconnect'}
                    </Button>
                  </>
                ) : (
                  <Button
                    size="sm"
                    colorScheme={connection.color}
                    onClick={() => handleConnect(connection.platform)}
                    disabled={isConnecting === connection.platform}
                  >
                    {isConnecting === connection.platform ? 'Connecting...' : 'Connect'}
                  </Button>
                )}
              </HStack>
            </HStack>
          </Box>
        ))}
      </VStack>

      <Box mt={6} p={4} bg="blue.50" borderRadius="md">
        <Text fontSize="sm" color="blue.700">
          💡 <strong>Tip:</strong> Connecting your social media accounts helps generate more accurate NFTs based on your online presence and engagement.
        </Text>
      </Box>
    </Box>
  )
}
