'use client'

import { Box, Heading, Text, Button, VStack, Container } from '@chakra-ui/react'
import { useAuth } from '@/contexts/AuthContext'

export default function HomePage() {
  const { user, isAuthenticated, logout } = useAuth()

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="center">
        <Box textAlign="center">
          <Heading as="h1" size="2xl" mb={4}>
            Social NFT Platform
          </Heading>
          <Text fontSize="xl" color="gray.600">
            Connect your Twitter, join campaigns, and mint evolving NFTs
          </Text>
        </Box>

        {isAuthenticated ? (
          <VStack spacing={4}>
            <Text fontSize="lg">
              Welcome back, <strong>{user?.username}</strong>!
            </Text>
            <Button colorScheme="red" onClick={logout}>
              Logout
            </Button>
          </VStack>
        ) : (
          <VStack spacing={4}>
            <Text fontSize="lg">
              Get started by creating an account or logging in
            </Text>
            <Button colorScheme="blue" size="lg">
              Get Started
            </Button>
          </VStack>
        )}

        <Box textAlign="center" mt={8}>
          <Heading as="h2" size="lg" mb={4}>
            Features
          </Heading>
          <VStack spacing={2}>
            <Text>🐦 Connect your Twitter account</Text>
            <Text>🎯 Join exciting NFT campaigns</Text>
            <Text>🎨 Mint unique, evolving NFTs</Text>
            <Text>📈 Watch your NFTs evolve over time</Text>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
