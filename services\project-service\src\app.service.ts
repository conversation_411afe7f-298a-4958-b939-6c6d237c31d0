import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo(): Record<string, any> {
    return {
      name: 'Project Service',
      description: 'Campaign management service for Social NFT Platform',
      version: '1.0.0',
      status: 'active',
      features: [
        'Campaign creation and management',
        'Campaign parameter configuration',
        'User campaign participation',
        'Campaign analytics and monitoring'
      ],
      endpoints: {
        health: '/health',
        docs: '/api/docs',
        campaigns: '/api/campaigns'
      }
    };
  }
}
