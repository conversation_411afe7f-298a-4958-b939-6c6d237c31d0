#!/usr/bin/env node

/**
 * Frontend-Backend Integration Test
 * 
 * This test verifies that the frontend-headless can successfully connect
 * to and interact with our real backend services.
 * 
 * Test Flow:
 * 1. Test User Registration API
 * 2. Test User Login API  
 * 3. Test Profile Analysis API
 * 4. Test NFT Generation API
 * 5. Test Data Retrieval APIs
 */

const axios = require('axios');

// Service URLs - All through API Gateway (matching new frontend configuration)
const API_GATEWAY = 'http://localhost:3010/api';
const USER_SERVICE = API_GATEWAY; // Auth routes
const ANALYSIS_SERVICE = API_GATEWAY; // Analysis routes
const NFT_SERVICE = API_GATEWAY; // NFT routes

// Test data
const testUser = {
  username: `frontendtest${Date.now()}`,
  email: `frontendtest${Date.now()}@example.com`,
  password: 'TestPassword123',
  confirmPassword: 'TestPassword123',
  displayName: 'Frontend Test User'
};

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testFrontendBackendIntegration() {
  console.log('🚀 Starting Frontend-Backend Integration Test');
  console.log('=' .repeat(80));

  try {
    // Test 1: User Registration (Frontend → User Service)
    console.log('\n📝 Test 1: User Registration API');
    console.log(`   Testing: POST ${USER_SERVICE}/auth/register`);
    
    const registerResponse = await axios.post(`${USER_SERVICE}/auth/register`, testUser);
    console.log(`   ✅ Registration successful: ${testUser.username}`);
    console.log(`   📋 Response: ${registerResponse.status} - ${JSON.stringify(registerResponse.data).substring(0, 100)}...`);
    
    const userId = registerResponse.data.data.user.id;
    const authToken = registerResponse.data.data.accessToken;

    // Test 2: User Login (Frontend → User Service)
    console.log('\n🔐 Test 2: User Login API');
    console.log(`   Testing: POST ${USER_SERVICE}/auth/login`);
    
    const loginResponse = await axios.post(`${USER_SERVICE}/auth/login`, {
      emailOrUsername: testUser.email,
      password: testUser.password
    });
    console.log(`   ✅ Login successful`);
    console.log(`   📋 Response: ${loginResponse.status} - Token received: ${!!loginResponse.data.data.accessToken}`);

    // Test 3: Profile Analysis (Frontend → API Gateway → Analysis Service)
    console.log('\n🔍 Test 3: Profile Analysis API via API Gateway');
    console.log(`   Testing: POST ${ANALYSIS_SERVICE}/analysis/twitter-profile`);

    const analysisResponse = await axios.post(`${ANALYSIS_SERVICE}/analysis/twitter-profile`, {
      twitterHandle: 'testuser_frontend_integration',
      userId: userId,
      analysisType: 'comprehensive'
    });
    console.log(`   ✅ Analysis successful - Score: ${analysisResponse.data.data.score}/100`);
    console.log(`   📋 Analysis ID: ${analysisResponse.data.data.id}`);

    const analysisId = analysisResponse.data.data.id;

    // Test 4: NFT Generation (Frontend → API Gateway → NFT Service)
    console.log('\n🎨 Test 4: NFT Generation API via API Gateway');
    console.log(`   Testing: POST ${NFT_SERVICE}/nft-generation/generate-from-analysis`);

    const nftResponse = await axios.post(`${NFT_SERVICE}/nft-generation/generate-from-analysis`, {
      userId: userId,
      analysisId: analysisId,
      customization: {
        style: 'artistic',
        theme: 'tech'
      }
    });
    console.log(`   ✅ NFT generated - Rarity: ${nftResponse.data.data.rarity}, Score: ${nftResponse.data.data.score}`);
    console.log(`   📋 NFT ID: ${nftResponse.data.data.id}`);

    // Test 5: Data Retrieval - NFT History (Frontend → API Gateway → NFT Service)
    console.log('\n📚 Test 5: NFT History Retrieval API via API Gateway');
    console.log(`   Testing: GET ${NFT_SERVICE}/nft-generation/user/${userId}/history`);

    const nftHistoryResponse = await axios.get(`${NFT_SERVICE}/nft-generation/user/${userId}/history?limit=10`);
    console.log(`   ✅ NFT History retrieved: ${nftHistoryResponse.data.data.total} NFTs found`);
    console.log(`   📋 Latest NFT: ${nftHistoryResponse.data.data.nfts[0]?.name || 'None'}`);

    // Test 6: Data Retrieval - Analysis History (Frontend → API Gateway → Analysis Service)
    console.log('\n📊 Test 6: Analysis History Retrieval API via API Gateway');
    console.log(`   Testing: GET ${ANALYSIS_SERVICE}/analysis/history`);

    const analysisHistoryResponse = await axios.get(`${ANALYSIS_SERVICE}/analysis/history?userId=${userId}&limit=10`);
    console.log(`   ✅ Analysis History retrieved: ${analysisHistoryResponse.data.data.total} analyses found`);
    console.log(`   📋 Latest Analysis: @${analysisHistoryResponse.data.data.analyses[0]?.twitterHandle || 'None'}`);

    // Test 7: User Profile (Frontend → API Gateway → User Service)
    console.log('\n👤 Test 7: User Profile API via API Gateway');
    console.log(`   Testing: GET ${USER_SERVICE}/auth/profile`);

    const profileResponse = await axios.get(`${USER_SERVICE}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    console.log(`   ✅ Profile retrieved: ${profileResponse.data.data.username}`);
    console.log(`   📋 User ID: ${profileResponse.data.data.id}`);

    // Summary
    console.log('\n' + '=' .repeat(80));
    console.log('🎉 FRONTEND-BACKEND INTEGRATION TEST SUCCESSFUL!');
    console.log('=' .repeat(80));
    console.log(`👤 User: ${testUser.username} (${userId})`);
    console.log(`🔐 Authentication: Working ✅`);
    console.log(`🔍 Profile Analysis: Working ✅`);
    console.log(`🎨 NFT Generation: Working ✅`);
    console.log(`📚 Data Retrieval: Working ✅`);
    console.log(`📊 API Endpoints: All 7 tests passed ✅`);
    
    console.log('\n✨ Frontend is ready to connect to backend services!');
    console.log('✨ All API endpoints are working correctly!');
    console.log('✨ Complete user journey is functional!');
    
    return true;

  } catch (error) {
    console.error('\n❌ Integration test failed:', error.response?.data || error.message);
    console.error('📋 Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method
    });
    return false;
  }
}

// Run the test
if (require.main === module) {
  testFrontendBackendIntegration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testFrontendBackendIntegration };
