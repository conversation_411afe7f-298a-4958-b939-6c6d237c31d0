import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class DatabaseHealthService extends HealthIndicator {
  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      
      // Test database connection
      await this.prisma.$queryRaw`SELECT 1`;
      
      const responseTime = Date.now() - startTime;
      
      const result = this.getStatus(key, true, {
        database: 'postgresql',
        status: 'up',
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        database: 'postgresql',
        status: 'down',
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw new HealthCheckError('Database health check failed', result);
    }
  }
}
