"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/analytics-dashboard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/charts/analytics-charts */ \"(app-pages-browser)/./src/components/charts/analytics-charts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [platformData, setPlatformData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gainersLosers, setGainersLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [marketValue, setMarketValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTransactions, setRecentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('7d');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('yaps-kaito');\n    // Mock chart data - in production this would come from the analytics API\n    const mockGrowthData = [\n        {\n            date: '2025-06-01',\n            users: 1200,\n            nfts: 3200,\n            volume: 220\n        },\n        {\n            date: '2025-06-02',\n            users: 1210,\n            nfts: 3350,\n            volume: 225\n        },\n        {\n            date: '2025-06-03',\n            users: 1225,\n            nfts: 3400,\n            volume: 230\n        },\n        {\n            date: '2025-06-04',\n            users: 1240,\n            nfts: 3420,\n            volume: 232\n        },\n        {\n            date: '2025-06-05',\n            users: 1245,\n            nfts: 3450,\n            volume: 234\n        },\n        {\n            date: '2025-06-06',\n            users: 1247,\n            nfts: 3456,\n            volume: 235\n        }\n    ];\n    const mockEngagementData = [\n        {\n            date: '2025-06-01',\n            active_users: 89,\n            nft_generations: 45,\n            shares: 23\n        },\n        {\n            date: '2025-06-02',\n            active_users: 94,\n            nft_generations: 52,\n            shares: 28\n        },\n        {\n            date: '2025-06-03',\n            active_users: 76,\n            nft_generations: 38,\n            shares: 19\n        },\n        {\n            date: '2025-06-04',\n            active_users: 112,\n            nft_generations: 67,\n            shares: 34\n        },\n        {\n            date: '2025-06-05',\n            active_users: 98,\n            nft_generations: 43,\n            shares: 25\n        },\n        {\n            date: '2025-06-06',\n            active_users: 105,\n            nft_generations: 58,\n            shares: 31\n        }\n    ];\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch platform overview using proper API client\n            const platformResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getPlatformOverview();\n            if (platformResult.success) {\n                setPlatformData(platformResult.data);\n            }\n            // Fetch top gainers/losers using proper API client\n            const gainersResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getTopGainersLosers(selectedPeriod, 10);\n            if (gainersResult.success) {\n                setGainersLosers(gainersResult.data);\n            }\n            // Fetch collection market value using proper API client\n            const marketResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getCollectionMarketValue();\n            if (marketResult.success) {\n                setMarketValue(marketResult.data);\n            }\n            // Fetch recent transactions using proper API client\n            const transactionsResult = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getRecentTransactions(15);\n            if (transactionsResult.success) {\n                setRecentTransactions(transactionsResult.data.transactions);\n            }\n        } catch (err) {\n            setError('Failed to fetch analytics data');\n            console.error('Analytics fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsDashboard.useEffect\"], [\n        selectedPeriod\n    ]);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n        return num.toString();\n    };\n    const formatEth = (eth)=>\"\".concat(eth.toFixed(2), \" ETH\");\n    const getRarityColor = (rarity)=>{\n        switch(rarity.toLowerCase()){\n            case 'legendary':\n                return 'bg-yellow-500';\n            case 'epic':\n                return 'bg-purple-500';\n            case 'rare':\n                return 'bg-blue-500';\n            case 'common':\n                return 'bg-gray-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading analytics data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchAnalyticsData,\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            \"Retry\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Comprehensive platform insights and performance metrics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.user_growth_rate,\n                                                \"% from last month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"NFTs Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(platformData.overview.total_nfts_generated)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.nft_generation_growth_rate,\n                                                \"% growth rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatEth(platformData.overview.total_volume_eth)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"+\",\n                                                platformData.growth_metrics.marketplace_volume_growth_rate,\n                                                \"% volume growth\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Active Campaigns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: platformData.overview.active_campaigns\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                platformData.growth_metrics.campaign_participation_rate,\n                                                \"% participation rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: 'yaps-kaito',\n                                    name: '🎯 Yaps Kaito Style'\n                                },\n                                {\n                                    id: 'overview',\n                                    name: 'Overview'\n                                },\n                                {\n                                    id: 'gainers-losers',\n                                    name: 'Top Gainers/Losers'\n                                },\n                                {\n                                    id: 'market-value',\n                                    name: 'Market Value'\n                                },\n                                {\n                                    id: 'performance',\n                                    name: 'Performance'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab.name\n                                }, tab.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'yaps-kaito' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            gainersLosers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Top Gainers & Losers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Users' final score status and performance tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Period:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    [\n                                                        '7d',\n                                                        '30d'\n                                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedPeriod(period),\n                                                            className: \"px-3 py-1 text-sm font-medium rounded-md \".concat(selectedPeriod === period ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'),\n                                                            children: period\n                                                        }, period, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon, {\n                                                                className: \"h-5 w-5 text-green-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Top Gainers (\",\n                                                            gainersLosers.top_gainers.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: gainersLosers.top_gainers.map((item)=>{\n                                                            var _item_name_match;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                    className: \"h-6 w-6 text-gray-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 382,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 381,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-gray-900\",\n                                                                                                children: ((_item_name_match = item.name.match(/@(\\w+)/)) === null || _item_name_match === void 0 ? void 0 : _item_name_match[0]) || '@user'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                                lineNumber: 386,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getRarityColor(item.rarity), \" text-white\"),\n                                                                                                children: item.rarity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                                lineNumber: 387,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 385,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: [\n                                                                                            \"Score: \",\n                                                                                            item.current_score,\n                                                                                            \" | Volume: \",\n                                                                                            item.volume_24h,\n                                                                                            \" ETH\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1 text-green-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon, {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 398,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-bold\",\n                                                                                        children: item.change_percentage\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 399,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    selectedPeriod,\n                                                                                    \" change\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                        children: \"Performance Bubble Map\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-full h-full\",\n                                                                children: gainersLosers.top_gainers.map((item, index)=>{\n                                                                    var _item_name_match, _item_name_match1;\n                                                                    const size = 40 + item.current_score / 100 * 80; // 40-120px\n                                                                    const x = index % 3 * 30 + 10;\n                                                                    const y = Math.floor(index / 3) * 25 + 10;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute cursor-pointer transition-all duration-300 hover:scale-110\",\n                                                                        style: {\n                                                                            left: \"\".concat(x, \"%\"),\n                                                                            top: \"\".concat(y, \"%\"),\n                                                                            width: \"\".concat(size, \"px\"),\n                                                                            height: \"\".concat(size, \"px\")\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500\",\n                                                                            title: \"\".concat(((_item_name_match = item.name.match(/@(\\w+)/)) === null || _item_name_match === void 0 ? void 0 : _item_name_match[0]) || '@user', \" - Score: \").concat(item.current_score, \" (\").concat(item.change_percentage, \")\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs\",\n                                                                                        children: (((_item_name_match1 = item.name.match(/@(\\w+)/)) === null || _item_name_match1 === void 0 ? void 0 : _item_name_match1[0]) || '@user').slice(0, 6)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 434,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs font-bold\",\n                                                                                        children: item.current_score\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                        lineNumber: 435,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, item.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 29\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 right-2 bg-white p-2 rounded shadow\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Gainers\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs mt-1\",\n                                                                            children: \"Size = Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            marketValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Collection Market Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Project collections growth and decline tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: [\n                                                            marketValue.market_overview.total_market_value.toFixed(2),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium \".concat(marketValue.market_overview.market_cap_change_24h.startsWith('+') ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            marketValue.market_overview.market_cap_change_24h,\n                                                            \" (24h)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: marketValue.bubble_map_data.map((collection, index)=>{\n                                                    const maxValue = Math.max(...marketValue.bubble_map_data.map((c)=>c.value));\n                                                    const size = 60 + collection.value / maxValue * 90; // 60-150px\n                                                    const x = index % 2 * 40 + 10;\n                                                    const y = Math.floor(index / 2) * 35 + 10;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl\",\n                                                        style: {\n                                                            left: \"\".concat(x, \"%\"),\n                                                            top: \"\".concat(y, \"%\"),\n                                                            width: \"\".concat(size, \"px\"),\n                                                            height: \"\".concat(size, \"px\")\n                                                        },\n                                                        onClick: ()=>console.log('Navigate to collection:', collection.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg border-2 border-white\",\n                                                            style: {\n                                                                backgroundColor: collection.color\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs mb-1\",\n                                                                        children: collection.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            collection.value,\n                                                                            \" ETH\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            collection.size,\n                                                                            \" NFTs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-bold mt-1 \".concat(collection.change_24h.startsWith('+') ? 'text-green-200' : 'text-red-200'),\n                                                                        children: collection.change_24h\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, collection.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-4 right-4 bg-white p-3 rounded shadow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Collection Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: marketValue.bubble_map_data.map((collection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded\",\n                                                                            style: {\n                                                                                backgroundColor: collection.color\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: collection.rarity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, collection.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-2\",\n                                                            children: \"Click to view collection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 15\n                            }, this),\n                            recentTransactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Recent Transactions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Latest NFT activities, purchases and interactions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClockIcon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Real-time updates\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTransactions.slice(0, 10).map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(transaction.type === 'generation' ? 'bg-blue-100 text-blue-800' : transaction.type === 'share' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: transaction.type === 'generation' ? '🎨' : transaction.type === 'share' ? '📤' : '❤️'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: transaction.nft_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 568,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium \".concat(getRarityColor(transaction.rarity), \" text-white\"),\n                                                                                children: transaction.rarity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"User: @\",\n                                                                                    transaction.username\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"capitalize\",\n                                                                                children: transaction.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    Math.floor((new Date().getTime() - new Date(transaction.timestamp).getTime()) / (1000 * 60)),\n                                                                                    \"m ago\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-gray-900\",\n                                                                        children: [\n                                                                            transaction.value_eth.toFixed(3),\n                                                                            \" ETH\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    (transaction.value_eth * 2000).toFixed(0),\n                                                                    \" USD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'overview' && platformData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.PlatformGrowthChart, {\n                                        data: mockGrowthData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.RarityDistributionChart, {\n                                        data: platformData.top_performing.nft_rarities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Top Performing Campaigns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Campaigns with highest engagement\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: platformData.top_performing.campaigns.map((campaign, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: campaign.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                formatNumber(campaign.participants),\n                                                                                \" participants\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        formatNumber(campaign.nfts_generated),\n                                                                        \" NFTs\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, campaign.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.EngagementMetricsChart, {\n                                        data: mockEngagementData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'gainers-losers' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Period:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 15\n                                    }, this),\n                                    [\n                                        '24h',\n                                        '7d',\n                                        '30d'\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPeriod(period),\n                                            className: \"px-3 py-1 text-sm font-medium rounded-md \".concat(selectedPeriod === period ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'),\n                                            children: period\n                                        }, period, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 13\n                            }, this),\n                            gainersLosers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.GainersLosersChart, {\n                                        gainers: gainersLosers.top_gainers,\n                                        losers: gainersLosers.top_losers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white shadow rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Market Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Overall market performance for \",\n                                                            selectedPeriod\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: formatEth(gainersLosers.market_summary.total_volume_24h)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Total Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 \".concat(gainersLosers.market_summary.volume_change_24h.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                children: gainersLosers.market_summary.volume_change_24h\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: gainersLosers.market_summary.average_score_change\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Avg Score Change\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: gainersLosers.market_summary.most_active_rarity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Most Active Rarity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap justify-center gap-1\",\n                                                                children: gainersLosers.market_summary.trending_themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: theme\n                                                                    }, theme, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: \"Trending Themes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow rounded-lg p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-8 w-8 mx-auto text-gray-400 animate-spin mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading gainers and losers data...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'market-value' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: marketValue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_analytics_charts__WEBPACK_IMPORTED_MODULE_3__.MarketValueChart, {\n                                    data: marketValue.bubble_map_data\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Market Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Collection market value and statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatEth(marketValue.market_overview.total_market_value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Total Market Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 \".concat(marketValue.market_overview.market_cap_change_24h.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: marketValue.market_overview.market_cap_change_24h\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: marketValue.market_overview.total_collections\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Collections\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatNumber(marketValue.market_overview.total_nfts)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Total NFTs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatEth(marketValue.market_overview.average_nft_value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Avg NFT Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-400 animate-spin mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Loading market value data...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'performance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CalendarIcon_ChartBarIcon_CurrencyDollarIcon_PresentationChartLineIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Advanced Performance Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Coming soon: Real-time performance tracking, predictive analytics, and advanced insights.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                        lineNumber: 775,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\analytics-dashboard.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"iXdvtH/Ekfjub2RYzHmwB92fHMs=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/analytics-dashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowPathIcon: () => (/* reexport safe */ _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   PresentationChartLineIcon: () => (/* reexport safe */ _PresentationChartLineIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowPathIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _PresentationChartLineIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PresentationChartLineIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PresentationChartLineIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UsersIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFycm93UGF0aEljb24sQ2FsZW5kYXJJY29uLENoYXJ0QmFySWNvbixDdXJyZW5jeURvbGxhckljb24sUHJlc2VudGF0aW9uQ2hhcnRMaW5lSWNvbixUcmVuZGluZ1VwSWNvbixVc2Vyc0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNGO0FBQ0E7QUFDWTtBQUNjO0FBQ2hDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJ6XFxEb2N1bWVudHNcXEF1Z21lbnRcXHNvY2lhbC1uZnQtcGxhdGZvcm0tdjJcXGZyb250ZW5kLWhlYWRsZXNzXFxub2RlX21vZHVsZXNcXEBoZXJvaWNvbnNcXHJlYWN0XFwyNFxcb3V0bGluZVxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dQYXRoSWNvbiB9IGZyb20gXCIuL0Fycm93UGF0aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYWxlbmRhckljb24gfSBmcm9tIFwiLi9DYWxlbmRhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdXJyZW5jeURvbGxhckljb24gfSBmcm9tIFwiLi9DdXJyZW5jeURvbGxhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQcmVzZW50YXRpb25DaGFydExpbmVJY29uIH0gZnJvbSBcIi4vUHJlc2VudGF0aW9uQ2hhcnRMaW5lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJBcnJvd1BhdGhJY29uIiwiQ2FsZW5kYXJJY29uIiwiQ2hhcnRCYXJJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiUHJlc2VudGF0aW9uQ2hhcnRMaW5lSWNvbiIsIlVzZXJzSWNvbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=ArrowPathIcon,CalendarIcon,ChartBarIcon,CurrencyDollarIcon,PresentationChartLineIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ })

});