// Enterprise NFT Query Service (Read Side)
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';
import { NftQueryFiltersDto } from '../models/nft-query.model';

export interface QueryContext {
  correlationId?: string;
  userId?: string;
}

@Injectable()
export class NftQueryService {
  private readonly logger = new Logger(NftQueryService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getNftById(id: string, context: QueryContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Querying NFT ${id}`, context.correlationId);

      const nft = await this.prisma.nftQuery.findUnique({
        where: { id }
      });

      if (nft) {
        // Update view count and last accessed
        await this.prisma.nftQuery.update({
          where: { id },
          data: {
            viewCount: { increment: 1 },
            lastAccessedAt: new Date(),
            avgResponseTime: this.calculateAvgResponseTime(nft.avgResponseTime, Date.now() - startTime)
          }
        }).catch(() => {}); // Silent fail for performance metrics
      }

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFT query completed in ${responseTime}ms`, context.correlationId);

      return nft;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`NFT query failed after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      
      // Graceful fallback - try command model
      try {
        const commandNft = await this.prisma.nftCommand.findUnique({ where: { id } });
        if (commandNft) {
          this.logger.warn(`Fallback to command model for NFT ${id}`, context.correlationId);
          return this.mapCommandToQuery(commandNft);
        }
      } catch (fallbackError) {
        this.logger.error(`Fallback query also failed: ${fallbackError.message}`, fallbackError.stack, context.correlationId);
      }
      
      throw { status: 503, code: 'ServiceUnavailableException', message: 'NFT service temporarily unavailable' };
    }
  }

  async getNfts(filters: NftQueryFiltersDto, context: QueryContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Querying NFTs with filters`, context.correlationId);

      const where: any = {};
      
      if (filters.userId) where.userId = filters.userId;
      if (filters.projectId) where.projectId = filters.projectId;
      if (filters.status) where.status = filters.status;
      if (filters.isListed !== undefined) where.isListed = filters.isListed;

      const [nfts, total] = await Promise.all([
        this.prisma.nftQuery.findMany({
          where,
          take: filters.limit || 10,
          skip: filters.offset || 0,
          orderBy: { [filters.sortBy || 'createdAt']: filters.sortOrder || 'desc' }
        }),
        this.prisma.nftQuery.count({ where })
      ]);

      const responseTime = Date.now() - startTime;
      this.logger.log(`NFTs query completed in ${responseTime}ms, found ${nfts.length}/${total}`, context.correlationId);

      return {
        nfts,
        pagination: {
          total,
          limit: filters.limit || 10,
          offset: filters.offset || 0,
          hasMore: (filters.offset || 0) + (filters.limit || 10) < total
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`NFTs query failed after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw { status: 503, code: 'ServiceUnavailableException', message: 'NFT service temporarily unavailable' };
    }
  }

  async getUserNftStats(userId: string, context: QueryContext) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Querying NFT stats for user ${userId}`, context.correlationId);

      const stats = await this.prisma.nftQuery.aggregate({
        where: { userId },
        _count: { id: true },
        _sum: { 
          viewCount: true, 
          likeCount: true, 
          shareCount: true,
          totalSales: true 
        },
        _avg: { popularityScore: true }
      });

      const listedCount = await this.prisma.nftQuery.count({
        where: { userId, isListed: true }
      });

      const result = {
        totalNfts: stats._count.id || 0,
        totalViews: stats._sum.viewCount || 0,
        totalLikes: stats._sum.likeCount || 0,
        totalShares: stats._sum.shareCount || 0,
        totalSales: stats._sum.totalSales || 0,
        listedNfts: listedCount,
        avgPopularityScore: stats._avg.popularityScore || 0,
        lastUpdated: new Date().toISOString()
      };

      const responseTime = Date.now() - startTime;
      this.logger.log(`User stats query completed in ${responseTime}ms`, context.correlationId);

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`User stats query failed after ${responseTime}ms: ${error.message}`, error.stack, context.correlationId);
      throw { status: 503, code: 'ServiceUnavailableException', message: 'NFT service temporarily unavailable' };
    }
  }

  private calculateAvgResponseTime(currentAvg: number | null, newTime: number): number {
    if (!currentAvg) return newTime;
    return Math.round((currentAvg * 0.9) + (newTime * 0.1)); // Weighted average
  }

  private mapCommandToQuery(commandNft: any) {
    return {
      id: commandNft.id,
      displayName: commandNft.name,
      displayDescription: commandNft.description,
      thumbnailUrl: commandNft.imageUrl,
      fullImageUrl: commandNft.imageUrl,
      userId: commandNft.userId,
      projectId: commandNft.projectId,
      campaignId: commandNft.campaignId,
      status: this.mapGenerationStatusToNftStatus(commandNft.generationStatus),
      viewCount: 0,
      likeCount: 0,
      shareCount: 0,
      isListed: false,
      totalSales: 0,
      popularityScore: 0,
      createdAt: commandNft.createdAt,
      lastUpdated: new Date()
    };
  }

  private mapGenerationStatusToNftStatus(generationStatus: string): string {
    switch (generationStatus) {
      case 'pending': return 'draft';
      case 'generating': return 'generated';
      case 'completed': return 'generated';
      case 'failed': return 'draft';
      default: return 'draft';
    }
  }
}
