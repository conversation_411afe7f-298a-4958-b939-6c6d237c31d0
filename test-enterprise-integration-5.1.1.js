// Step 5.1.1 - Gateway Health Check Test
const axios = require('axios');

async function testGatewayHealthCheck() {
  console.log('🏥 Step 5.1.1: Gateway Health Check Test');
  
  try {
    // Test 1: Basic Gateway Response
    console.log('\n📡 Test 1: Basic Gateway Response...');
    const basicResponse = await axios.get('http://localhost:3010/api');
    console.log('✅ Gateway basic response:', basicResponse.data);
    
    // Test 2: Enterprise Health Check through Gateway
    console.log('\n🏥 Test 2: Enterprise Health Check...');
    const healthResponse = await axios.get('http://localhost:3010/api/users/health/enterprise', {
      headers: { 'X-Correlation-ID': 'test-health-5.1.1' }
    });
    console.log('✅ Enterprise health check:', healthResponse.data);
    
    // Test 3: Verify Correlation ID Forwarding
    console.log('\n🔗 Test 3: Correlation ID Forwarding...');
    const correlationId = `test-correlation-${Date.now()}`;
    const correlationResponse = await axios.get('http://localhost:3010/api/users/health/enterprise', {
      headers: { 'X-Correlation-ID': correlationId }
    });
    console.log('✅ Correlation ID forwarded successfully');
    console.log('   Response headers:', correlationResponse.headers['x-correlation-id']);
    
    console.log('\n🎉 Step 5.1.1 PASSED: Gateway Health Check Working');
    return true;
    
  } catch (error) {
    console.error('❌ Step 5.1.1 FAILED:', error.message);
    return false;
  }
}

testGatewayHealthCheck();
