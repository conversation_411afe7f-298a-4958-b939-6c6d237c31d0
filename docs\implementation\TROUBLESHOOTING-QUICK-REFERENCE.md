# Troubleshooting Quick Reference Guide

## 🚨 **Emergency Service Recovery**

### **Project Service Won't Start**

#### **Error: Prisma client not initialized**
```bash
# Solution:
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/project-service
rm -rf node_modules/.prisma
npx prisma generate
npm run build
npx nest start --watch
```

#### **Error: Cannot find module 'dist/main'**
```bash
# Solution:
npm run build
npx nest start --watch
```

#### **Error: Database connection failed**
```bash
# Check DATABASE_URL in .env:
DATABASE_URL="postgresql://postgres:1111@localhost:5432/project_service"

# Verify database exists:
npx prisma db push
```

### **API Gateway Won't Start**

#### **Error: npm script not found**
```bash
# Use direct nest command:
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/api-gateway
npx nest start --watch
```

#### **Error: Port already in use**
```bash
# Find and kill process:
netstat -ano | findstr :3010
taskkill /PID <PID> /F
```

---

## 🔍 **Quick Diagnostic Commands**

### **Service Health Checks**
```bash
# Project Service
curl http://localhost:3005/health

# API Gateway  
curl http://localhost:3010/api/health

# Database Connection
cd services/project-service && npx prisma db push
```

### **Integration Verification**
```bash
# Quick test
cd libs/testing
node integration/api-gateway-routing-test.js

# Full test
node integration/comprehensive-integration-test.js
```

### **Service Status Check**
```bash
# Check running services
netstat -ano | findstr :3005
netstat -ano | findstr :3010
netstat -ano | findstr :5432
```

---

## ⚠️ **Common Mistakes to Avoid**

### **❌ DON'T:**
- Fix TypeScript warnings when service is working
- Make multiple changes simultaneously
- Use relative paths in terminal commands
- Change database names without verification
- Remove node_modules without backup plan

### **✅ DO:**
- Test functionality before fixing warnings
- Make one change at a time
- Use full absolute paths
- Follow established naming conventions
- Document working state before changes

---

## 🎯 **Decision Tree**

### **When You See TypeScript Errors:**
```
Are integration tests passing? 
├─ YES → Ignore errors, continue development
└─ NO → Are services running?
   ├─ YES → Fix business logic, not TypeScript
   └─ NO → Fix service startup issues first
```

### **When Services Won't Start:**
```
Check error message:
├─ "Prisma client not initialized" → Regenerate Prisma client
├─ "Cannot find module dist/main" → Run npm run build  
├─ "Database connection failed" → Check DATABASE_URL
├─ "Port already in use" → Kill existing process
└─ "Script not found" → Use npx nest start --watch
```

### **When Integration Tests Fail:**
```
Check service health first:
├─ Services down → Fix service startup
├─ Services up → Check routing configuration
└─ Routing OK → Check business logic
```

---

## 📋 **Standard Recovery Procedure**

### **Step 1: Assess Current State**
```bash
# Check what's running
netstat -ano | findstr ":3005\|:3010\|:5432"

# Test basic connectivity
curl http://localhost:3005/health
curl http://localhost:3010/api/health
```

### **Step 2: Service Recovery (if needed)**
```bash
# Project Service
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/project-service
npx prisma generate
npm run build
npx nest start --watch

# API Gateway
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/api-gateway  
npm run build
npx nest start --watch
```

### **Step 3: Verification**
```bash
# Run targeted test
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/libs/testing
node integration/api-gateway-routing-test.js
```

### **Step 4: Document Issues**
- What was the error?
- What steps were taken?
- What worked/didn't work?
- How to prevent in future?

---

## 🔧 **Environment Standards**

### **Database Naming:**
- **Format:** `{service_name}` (no suffix)
- **Example:** `project_service`, `user_service`

### **Port Assignments:**
- **Project Service:** 3005
- **API Gateway:** 3010  
- **PostgreSQL:** 5432

### **Command Patterns:**
```bash
# Always use full paths
cd /c/Users/<USER>/Documents/Augment/social-nft-platform-v2/services/{service-name}

# Always use npx for local commands
npx prisma generate
npx nest start --watch

# Always verify after changes
curl http://localhost:{port}/health
```

---

## 📞 **Emergency Contacts & Resources**

### **Key Files:**
- **Service Health:** `GET /{service}/health`
- **API Documentation:** `http://localhost:3010/api/docs`
- **Database Schema:** `services/project-service/prisma/schema.prisma`
- **Environment Config:** `services/{service}/.env`

### **Testing Resources:**
- **Quick Test:** `libs/testing/integration/api-gateway-routing-test.js`
- **Full Test:** `libs/testing/integration/comprehensive-integration-test.js`
- **Diagnostic:** `libs/testing/integration/api-gateway-diagnostic.js`

### **Documentation:**
- **Master Reference:** `docs/MASTER_REFERENCE.md`
- **Implementation Docs:** `docs/implementation/`
- **This Issue:** `docs/implementation/API-GATEWAY-ROUTING-ISSUE-RESOLUTION.md`

**Remember: When in doubt, test functionality first, fix cosmetics later!** 🎯
