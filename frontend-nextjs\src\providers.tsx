'use client'

import { ChakraProvider, createSystem, defaultConfig, Box } from '@chakra-ui/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import { WalletDetection } from '@/utils/walletDetection'

const system = createSystem(defaultConfig, {
  theme: {
    tokens: {
      colors: {
        brand: {
          50: { value: '#e3f2fd' },
          100: { value: '#bbdefb' },
          500: { value: '#2196f3' },
          900: { value: '#0d47a1' },
        },
      },
    },
  },
})

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        retry: 1,
      },
    },
  }))

  // Initialize wallet detection and suppress MetaMask errors
  useEffect(() => {
    WalletDetection.suppressMetaMaskErrors();

    // Log wallet status for debugging (optional)
    const walletInfo = WalletDetection.getWalletInfo();
    if (walletInfo.isInstalled) {
      console.log('✅ Wallet detected:', walletInfo);
    } else {
      console.log('ℹ️ No wallet detected (this is normal)');
    }
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <ChakraProvider value={system}>
        <AuthProvider>
          <Box bg="gray.50">
            {children}
          </Box>
        </AuthProvider>
      </ChakraProvider>
    </QueryClientProvider>
  )
}
