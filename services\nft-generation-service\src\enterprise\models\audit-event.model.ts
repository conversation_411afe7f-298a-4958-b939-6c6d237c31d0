// Enterprise Audit and Event Models
import { IsString, IsOptional, IsObject, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum EntityType {
  NFT = 'nft',
  GENERATION = 'generation',
  MINTING = 'minting',
  TEMPLATE = 'template'
}

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  GENERATE = 'generate',
  MINT = 'mint',
  LIST = 'list',
  UNLIST = 'unlist'
}

export enum EventType {
  NFT_CREATED = 'nft_created',
  NFT_UPDATED = 'nft_updated',
  GENERATION_STARTED = 'generation_started',
  GENERATION_COMPLETED = 'generation_completed',
  GENERATION_FAILED = 'generation_failed',
  MINTING_STARTED = 'minting_started',
  MINTING_COMPLETED = 'minting_completed',
  MINTING_FAILED = 'minting_failed',
  NFT_LISTED = 'nft_listed',
  NFT_UNLISTED = 'nft_unlisted'
}

export class AuditLogDto {
  @ApiProperty({ description: 'Audit log ID' })
  id: string;

  @ApiProperty({ description: 'Entity type', enum: EntityType })
  entityType: EntityType;

  @ApiProperty({ description: 'Entity ID' })
  entityId: string;

  @ApiProperty({ description: 'Action performed', enum: AuditAction })
  action: AuditAction;

  @ApiPropertyOptional({ description: 'Old values before change' })
  oldValues?: any;

  @ApiPropertyOptional({ description: 'New values after change' })
  newValues?: any;

  @ApiPropertyOptional({ description: 'User who performed the action' })
  userId?: string;

  @ApiPropertyOptional({ description: 'Session ID' })
  sessionId?: string;

  @ApiPropertyOptional({ description: 'IP address' })
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent' })
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Correlation ID for request tracing' })
  correlationId?: string;

  @ApiProperty({ description: 'Timestamp of the action' })
  createdAt: Date;
}

export class NftEventDto {
  @ApiProperty({ description: 'Event ID' })
  id: string;

  @ApiProperty({ description: 'Event type', enum: EventType })
  eventType: EventType;

  @ApiProperty({ description: 'Event version' })
  eventVersion: string;

  @ApiProperty({ description: 'Aggregate ID (NFT ID)' })
  aggregateId: string;

  @ApiProperty({ description: 'Event data' })
  eventData: any;

  @ApiPropertyOptional({ description: 'Correlation ID' })
  correlationId?: string;

  @ApiPropertyOptional({ description: 'Causation ID' })
  causationId?: string;

  @ApiPropertyOptional({ description: 'User ID' })
  userId?: string;

  @ApiProperty({ description: 'Event timestamp' })
  createdAt: Date;
}

export class CreateAuditLogDto {
  @ApiProperty({ description: 'Entity type', enum: EntityType })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({ description: 'Entity ID' })
  @IsString()
  entityId: string;

  @ApiProperty({ description: 'Action performed', enum: AuditAction })
  @IsEnum(AuditAction)
  action: AuditAction;

  @ApiPropertyOptional({ description: 'Old values' })
  @IsOptional()
  @IsObject()
  oldValues?: any;

  @ApiPropertyOptional({ description: 'New values' })
  @IsOptional()
  @IsObject()
  newValues?: any;

  @ApiPropertyOptional({ description: 'User ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: 'Correlation ID' })
  @IsOptional()
  @IsString()
  correlationId?: string;
}

export class CreateNftEventDto {
  @ApiProperty({ description: 'Event type', enum: EventType })
  @IsEnum(EventType)
  eventType: EventType;

  @ApiProperty({ description: 'Aggregate ID' })
  @IsString()
  aggregateId: string;

  @ApiProperty({ description: 'Event data' })
  @IsObject()
  eventData: any;

  @ApiPropertyOptional({ description: 'Correlation ID' })
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiPropertyOptional({ description: 'User ID' })
  @IsOptional()
  @IsString()
  userId?: string;
}
