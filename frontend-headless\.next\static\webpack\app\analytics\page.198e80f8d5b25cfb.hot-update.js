"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/simple-analytics.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/simple-analytics.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SimpleAnalytics() {\n    // Mock data\n    const gainersData = [\n        {\n            id: '1',\n            name: '@alice_crypto',\n            score: 85,\n            change: '+12%',\n            rarity: 'Epic'\n        },\n        {\n            id: '2',\n            name: '@bob_nft',\n            score: 78,\n            change: '+8%',\n            rarity: 'Rare'\n        },\n        {\n            id: '3',\n            name: '@charlie_art',\n            score: 92,\n            change: '+15%',\n            rarity: 'Legendary'\n        }\n    ];\n    const collectionsData = [\n        {\n            id: '1',\n            name: 'CryptoArt',\n            value: 45.2,\n            change: '+5%',\n            color: '#3B82F6'\n        },\n        {\n            id: '2',\n            name: 'DigitalPunks',\n            value: 38.7,\n            change: '-2%',\n            color: '#8B5CF6'\n        },\n        {\n            id: '3',\n            name: 'MetaVerse',\n            value: 52.1,\n            change: '+8%',\n            color: '#10B981'\n        }\n    ];\n    const transactionsData = [\n        {\n            id: '1',\n            user: 'alice_crypto',\n            nft: 'Digital Art #123',\n            type: 'generation',\n            value: 0.5,\n            time: '5m ago'\n        },\n        {\n            id: '2',\n            user: 'bob_nft',\n            nft: 'Punk Avatar #456',\n            type: 'share',\n            value: 0.3,\n            time: '12m ago'\n        },\n        {\n            id: '3',\n            user: 'charlie_art',\n            nft: 'Meta World #789',\n            type: 'favorite',\n            value: 0.8,\n            time: '18m ago'\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowPathIcon, {\n                        className: \"h-6 w-6 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading analytics data...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Yaps Kaito Analytics Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Platform insights with bubble maps and performance tracking\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Top Gainers & Losers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Users' final score status and performance tracking\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.TrendingUpIcon, {\n                                                className: \"h-5 w-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Top Gainers (\",\n                                            gainersData.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: gainersData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: item.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                                lineNumber: 78,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                                children: item.rarity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                                lineNumber: 79,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                        lineNumber: 77,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Score: \",\n                                                                            item.score\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                lineNumber: 76,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.TrendingUpIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: item.change\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 89,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Performance Bubble Map\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: gainersData.map((item, index)=>{\n                                                const size = 60 + item.score / 100 * 60; // 60-120px\n                                                const x = index % 2 * 40 + 20;\n                                                const y = Math.floor(index / 2) * 30 + 20;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute cursor-pointer transition-all duration-300 hover:scale-110\",\n                                                    style: {\n                                                        left: \"\".concat(x, \"%\"),\n                                                        top: \"\".concat(y, \"%\"),\n                                                        width: \"\".concat(size, \"px\"),\n                                                        height: \"\".concat(size, \"px\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg bg-green-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: item.name.slice(0, 6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 120,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-bold\",\n                                                                    children: item.score\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, item.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Collection Market Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Project collections growth and decline tracking\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full\",\n                            children: collectionsData.map((collection, index)=>{\n                                const size = 80 + collection.value / 60 * 60; // 80-140px\n                                const x = index % 2 * 45 + 15;\n                                const y = Math.floor(index / 2) * 40 + 15;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute cursor-pointer transition-all duration-300 hover:scale-105\",\n                                    style: {\n                                        left: \"\".concat(x, \"%\"),\n                                        top: \"\".concat(y, \"%\"),\n                                        width: \"\".concat(size, \"px\"),\n                                        height: \"\".concat(size, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg\",\n                                        style: {\n                                            backgroundColor: collection.color\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mb-1\",\n                                                    children: collection.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold\",\n                                                    children: [\n                                                        collection.value,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: collection.change\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, this)\n                                }, collection.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Recent Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Latest NFT activities and interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Real-time updates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactionsData.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center bg-blue-100 text-blue-800\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: transaction.type === 'generation' ? '🎨' : transaction.type === 'share' ? '📤' : '❤️'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: transaction.nft\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"User: @\",\n                                                            transaction.user,\n                                                            \" • \",\n                                                            transaction.type,\n                                                            \" • \",\n                                                            transaction.time\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CurrencyDollarIcon_TrendingUpIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: [\n                                                        transaction.value,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\simple-analytics.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c = SimpleAnalytics;\nvar _c;\n$RefreshReg$(_c, \"SimpleAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/simple-analytics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClockIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UsersIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNsb2NrSWNvbixDdXJyZW5jeURvbGxhckljb24sVHJlbmRpbmdVcEljb24sVXNlcnNJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDcUQ7QUFDa0I7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccnpcXERvY3VtZW50c1xcQXVnbWVudFxcc29jaWFsLW5mdC1wbGF0Zm9ybS12MlxcZnJvbnRlbmQtaGVhZGxlc3NcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdXJyZW5jeURvbGxhckljb24gfSBmcm9tIFwiLi9DdXJyZW5jeURvbGxhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2Vyc0ljb24gfSBmcm9tIFwiLi9Vc2Vyc0ljb24uanNcIiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiQ2xvY2tJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiVXNlcnNJY29uIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=ClockIcon,CurrencyDollarIcon,TrendingUpIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ })

});