{"name": "mock-blockchain-service", "version": "1.0.0", "description": "Mock Blockchain Service for Social NFT Platform Development", "main": "dist/main.js", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/node": "^20.3.1", "typescript": "^5.1.3"}}