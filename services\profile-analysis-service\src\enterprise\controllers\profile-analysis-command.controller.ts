import { Controller, Post, Body, Headers, <PERSON>s, HttpStatus, <PERSON><PERSON>, BadRequestException, Param, Put } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { Response } from 'express';
import { TwitterAnalysisService, AnalyzeTwitterProfileDto } from '../services/twitter-analysis.service';

@ApiTags('Profile Analysis Commands (Write Operations)')
@Controller('analysis')
export class ProfileAnalysisCommandController {
  private readonly logger = new Logger(ProfileAnalysisCommandController.name);

  constructor(private readonly twitterAnalysisService: TwitterAnalysisService) {}

  @Post('twitter-profile')
  @ApiOperation({ summary: 'Analyze Twitter profile and generate comprehensive analysis' })
  @ApiBody({
    description: 'Twitter profile analysis request',
    schema: {
      type: 'object',
      properties: {
        twitterHandle: {
          type: 'string',
          description: 'Twitter username (with or without @)',
          example: 'elonmusk'
        },
        userId: {
          type: 'string',
          description: 'User ID requesting the analysis (optional)',
          example: 'user_123'
        }
      },
      required: ['twitterHandle']
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Analysis completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            twitterHandle: { type: 'string' },
            status: { type: 'string', enum: ['pending', 'completed', 'failed'] },
            score: { type: 'number' },
            analysisData: { type: 'object' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid input or analysis failed' })
  async analyzeTwitterProfile(
    @Body() analyzeDto: AnalyzeTwitterProfileDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Starting Twitter profile analysis for: ${analyzeDto.twitterHandle}`, {
        correlationId: headers['x-correlation-id']
      });

      if (!analyzeDto.twitterHandle) {
        throw new BadRequestException('Twitter handle is required');
      }

      const result = await this.twitterAnalysisService.analyzeTwitterProfile(analyzeDto);

      this.logger.log(`Analysis completed successfully for: ${analyzeDto.twitterHandle}`, {
        analysisId: result.id,
        score: result.score,
        rarity: result.analysisData.nftRecommendation.rarity,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: result,
        message: 'Twitter profile analysis completed successfully',
        correlationId: headers['x-correlation-id']
      });

    } catch (error) {
      this.logger.error(`Analysis failed for: ${analyzeDto.twitterHandle}`, {
        error: error.message,
        stack: error.stack,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        error: {
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        correlationId: headers['x-correlation-id']
      });
    }
  }

  @Put(':id/reanalyze')
  @ApiOperation({ summary: 'Re-analyze existing Twitter profile' })
  @ApiParam({ name: 'id', description: 'Analysis ID' })
  @ApiResponse({ status: 200, description: 'Re-analysis completed successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async reanalyzeProfile(
    @Param('id') id: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Re-analyzing profile with ID: ${id}`, {
        correlationId: headers['x-correlation-id']
      });

      // Get existing analysis
      const existingAnalysis = await this.twitterAnalysisService.getAnalysisById(id);
      if (!existingAnalysis) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          error: {
            message: `Analysis with ID ${id} not found`,
            timestamp: new Date().toISOString(),
          },
          correlationId: headers['x-correlation-id']
        });
      }

      // Perform new analysis
      const result = await this.twitterAnalysisService.analyzeTwitterProfile({
        twitterHandle: existingAnalysis.twitterHandle,
        userId: existingAnalysis.userId
      });

      this.logger.log(`Re-analysis completed for: ${existingAnalysis.twitterHandle}`, {
        oldScore: existingAnalysis.score,
        newScore: result.score,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        message: 'Profile re-analysis completed successfully',
        comparison: {
          previousScore: existingAnalysis.score,
          newScore: result.score,
          scoreDifference: result.score - existingAnalysis.score
        },
        correlationId: headers['x-correlation-id']
      });

    } catch (error) {
      this.logger.error(`Re-analysis failed for ID: ${id}`, {
        error: error.message,
        stack: error.stack,
        correlationId: headers['x-correlation-id']
      });

      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        error: {
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        correlationId: headers['x-correlation-id']
      });
    }
  }
}
