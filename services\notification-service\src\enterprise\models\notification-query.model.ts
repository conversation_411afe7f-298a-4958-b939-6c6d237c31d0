// Enterprise Notification Query Model (Read Side) - Template
import { ApiProperty } from '@nestjs/swagger';

export class NotificationQueryDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ description: 'Display type' })
  displayType: string;

  @ApiProperty({ description: 'Display channel' })
  displayChannel: string;

  @ApiProperty({ description: 'Display message' })
  displayMessage: string;

  @ApiProperty({ description: 'Recipient ID' })
  recipientId: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;
}
