#!/usr/bin/env node

/**
 * Social NFT Platform - Service Manager
 * Systematic approach to prevent duplicate services and port conflicts
 */

const { spawn, exec } = require('child_process');
const path = require('path');

// Service Configuration
const SERVICES = {
  'api-gateway': { port: 3010, dir: 'services/api-gateway', command: 'npm start' },
  'user-service': { port: 3011, dir: 'services/user-service', command: 'npm start' },
  'profile-analysis': { port: 3002, dir: 'services/profile-analysis-service', command: 'npm run start:dev' },
  'nft-generation': { port: 3003, dir: 'services/nft-generation-service', command: 'npm run start:dev' },
  'blockchain': { port: 3004, dir: 'services/blockchain-service', command: 'npm run start:dev' },
  'project': { port: 3005, dir: 'services/project-service', command: 'npm run start:dev' },
  'frontend': { port: 3000, dir: 'frontend-nextjs', command: 'npm run dev' }
};

class ServiceManager {
  constructor() {
    this.runningServices = new Map();
  }

  /**
   * Check if port is in use
   */
  async isPortInUse(port) {
    return new Promise((resolve) => {
      exec(`netstat -an | grep ":${port}"`, (error, stdout) => {
        resolve(stdout.includes('LISTENING'));
      });
    });
  }

  /**
   * Start a service with conflict detection
   */
  async startService(serviceName) {
    const service = SERVICES[serviceName];
    if (!service) {
      console.error(`❌ Unknown service: ${serviceName}`);
      return false;
    }

    // Check for port conflicts
    const portInUse = await this.isPortInUse(service.port);
    if (portInUse) {
      console.log(`⚠️  Port ${service.port} already in use for ${serviceName}`);
      console.log(`✅ Service ${serviceName} appears to be running`);
      return true;
    }

    console.log(`🚀 Starting ${serviceName} on port ${service.port}...`);
    
    try {
      const process = spawn('npm', ['start'], {
        cwd: path.join(process.cwd(), service.dir),
        stdio: 'inherit',
        shell: true
      });

      this.runningServices.set(serviceName, {
        process,
        port: service.port,
        startTime: new Date()
      });

      process.on('error', (error) => {
        console.error(`❌ Error starting ${serviceName}:`, error.message);
        this.runningServices.delete(serviceName);
      });

      process.on('exit', (code) => {
        console.log(`🔄 Service ${serviceName} exited with code ${code}`);
        this.runningServices.delete(serviceName);
      });

      return true;
    } catch (error) {
      console.error(`❌ Failed to start ${serviceName}:`, error.message);
      return false;
    }
  }

  /**
   * Check status of all services
   */
  async checkStatus() {
    console.log('\n📊 Service Status Check:');
    console.log('========================');
    
    for (const [name, config] of Object.entries(SERVICES)) {
      const portInUse = await this.isPortInUse(config.port);
      const status = portInUse ? '✅ RUNNING' : '❌ STOPPED';
      console.log(`${name.padEnd(15)} | Port ${config.port} | ${status}`);
    }
    console.log('========================\n');
  }

  /**
   * Start core backend services
   */
  async startBackend() {
    console.log('🔧 Starting Core Backend Services...\n');
    
    const coreServices = ['user-service', 'profile-analysis', 'nft-generation', 'project', 'api-gateway'];
    
    for (const service of coreServices) {
      await this.startService(service);
      // Wait a bit between services
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  /**
   * Start all services
   */
  async startAll() {
    console.log('🚀 Starting All Services...\n');
    
    const allServices = Object.keys(SERVICES);
    
    for (const service of allServices) {
      await this.startService(service);
      // Wait a bit between services
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

// CLI Interface
const manager = new ServiceManager();
const command = process.argv[2];
const serviceName = process.argv[3];

switch (command) {
  case 'start':
    if (serviceName) {
      manager.startService(serviceName);
    } else {
      manager.startBackend();
    }
    break;
  
  case 'start-all':
    manager.startAll();
    break;
  
  case 'status':
    manager.checkStatus();
    break;
  
  default:
    console.log(`
🔧 Social NFT Platform - Service Manager

Usage:
  node tools/service-manager.js status              # Check all service status
  node tools/service-manager.js start               # Start core backend services
  node tools/service-manager.js start <service>     # Start specific service
  node tools/service-manager.js start-all           # Start all services

Available services: ${Object.keys(SERVICES).join(', ')}
`);
}
