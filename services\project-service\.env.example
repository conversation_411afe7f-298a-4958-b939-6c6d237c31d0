# Project Service Configuration
PORT=3006
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=social_nft_platform

# Service Information
SERVICE_NAME=project-service
SERVICE_VERSION=1.0.0

# API Configuration
API_PREFIX=api

# JWT Configuration (for authentication)
JWT_SECRET=your-jwt-secret-key

# Logging
LOG_LEVEL=debug
