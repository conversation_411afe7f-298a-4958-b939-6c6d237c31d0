# Manual JWT Persistence Testing Guide

## 🧪 **Step-by-Step JWT Testing Instructions**

**Enhanced debugging is now active! Follow these steps to test JWT persistence:**

### **🔐 Test Credentials Ready:**
- **Email:** `<EMAIL>`
- **Password:** `password123`

### **📋 Testing Steps:**

#### **Step 1: Test Login Flow**
1. **Go to:** http://localhost:3000/auth/login
2. **Open browser console** (F12 → Console tab)
3. **Enter test credentials** above
4. **Click "Sign In"**
5. **Watch console logs** for JWT debugging messages

**Expected Console Output:**
```
🔐 Setting JWT token in localStorage: eyJhbGciOiJIUzI1NiIs...
✅ JWT token stored successfully
🔍 Getting JWT token from localStorage: eyJhbGciOiJIUzI1NiIs...
Initializing auth, token exists: true
Token found, fetching profile...
Profile fetched successfully: persisttest20250528191812
```

#### **Step 2: Test Session Persistence**
1. **After successful login** → Should redirect to dashboard
2. **Refresh the page** (F5 or Ctrl+R)
3. **Watch console logs** during page reload
4. **Expected:** Should stay on dashboard (no redirect to login)

#### **Step 3: Test Logout Flow**
1. **Click "Logout" button** on dashboard
2. **Watch console logs** for logout debugging
3. **Expected:** Redirect to login page

**Expected Console Output:**
```
🚪 Logging out: Removing JWT token from localStorage
✅ JWT token removed successfully
```

#### **Step 4: Test Post-Logout State**
1. **After logout** → Should be on login page
2. **Refresh the page** (F5)
3. **Expected:** Should stay on login page (no auto-login)

### **🔍 What to Look For:**
- ✅ JWT token being stored during login
- ✅ JWT token being retrieved during page refresh
- ✅ Profile API calls working with Authorization header
- ✅ Token being removed during logout

## 🎯 **Ready for Testing!**
Please test this flow and report what you see in the console!
