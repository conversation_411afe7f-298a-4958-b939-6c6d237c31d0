/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/twitter/callback/page";
exports.ids = ["app/auth/twitter/callback/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Ftwitter%2Fcallback%2Fpage&page=%2Fauth%2Ftwitter%2Fcallback%2Fpage&appPaths=%2Fauth%2Ftwitter%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Ftwitter%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Ftwitter%2Fcallback%2Fpage&page=%2Fauth%2Ftwitter%2Fcallback%2Fpage&appPaths=%2Fauth%2Ftwitter%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Ftwitter%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/twitter/callback/page.tsx */ \"(rsc)/./src/app/auth/twitter/callback/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'twitter',\n        {\n        children: [\n        'callback',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/twitter/callback/page\",\n        pathname: \"/auth/twitter/callback\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Ftwitter%2Fcallback%2Fpage&page=%2Fauth%2Ftwitter%2Fcallback%2Fpage&appPaths=%2Fauth%2Ftwitter%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Ftwitter%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(rsc)/./src/contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/toast-context.tsx */ \"(rsc)/./src/contexts/toast-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/query-provider.tsx */ \"(rsc)/./src/providers/query-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/twitter/callback/page.tsx */ \"(rsc)/./src/app/auth/twitter/callback/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3J6JTVDJTVDRG9jdW1lbnRzJTVDJTVDQXVnbWVudCU1QyU1Q3NvY2lhbC1uZnQtcGxhdGZvcm0tdjIlNUMlNUNmcm9udGVuZC1oZWFkbGVzcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUN0d2l0dGVyJTVDJTVDY2FsbGJhY2slNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNExBQThKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxyelxcXFxEb2N1bWVudHNcXFxcQXVnbWVudFxcXFxzb2NpYWwtbmZ0LXBsYXRmb3JtLXYyXFxcXGZyb250ZW5kLWhlYWRsZXNzXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFx0d2l0dGVyXFxcXGNhbGxiYWNrXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccnpcXERvY3VtZW50c1xcQXVnbWVudFxcc29jaWFsLW5mdC1wbGF0Zm9ybS12MlxcZnJvbnRlbmQtaGVhZGxlc3NcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/twitter/callback/page.tsx":
/*!************************************************!*\
  !*** ./src/app/auth/twitter/callback/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\app\\auth\\twitter\\callback\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"eaaf0730052a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJ6XFxEb2N1bWVudHNcXEF1Z21lbnRcXHNvY2lhbC1uZnQtcGxhdGZvcm0tdjJcXGZyb250ZW5kLWhlYWRsZXNzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYWFmMDczMDA1MmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_query_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/query-provider */ \"(rsc)/./src/providers/query-provider.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_toast_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/toast-context */ \"(rsc)/./src/contexts/toast-context.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Social NFT Platform - Connect, Engage, Evolve\",\n    description: \"Join Web3 campaigns through social media engagement and earn evolving NFTs. Help blockchain projects grow while building your digital collection.\",\n    keywords: \"NFT, Web3, Social Media, Blockchain, Campaigns, Digital Assets\",\n    authors: [\n        {\n            name: \"Social NFT Platform\"\n        }\n    ],\n    openGraph: {\n        title: \"Social NFT Platform\",\n        description: \"Connect, Engage, Evolve - Earn NFTs through social engagement\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_query_provider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_toast_context__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   withAuth: () => (/* binding */ withAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\auth-context.tsx",
"useAuth",
);const withAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\auth-context.tsx",
"withAuth",
);const ProtectedRoute = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\auth-context.tsx",
"ProtectedRoute",
);

/***/ }),

/***/ "(rsc)/./src/contexts/toast-context.tsx":
/*!****************************************!*\
  !*** ./src/contexts/toast-context.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),
/* harmony export */   useToast: () => (/* binding */ useToast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useToast = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\toast-context.tsx",
"useToast",
);const ToastProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\contexts\\toast-context.tsx",
"ToastProvider",
);

/***/ }),

/***/ "(rsc)/./src/providers/query-provider.tsx":
/*!******************************************!*\
  !*** ./src/providers/query-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\providers\\\\query-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\Augment\\social-nft-platform-v2\\frontend-headless\\src\\providers\\query-provider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/toast-context.tsx */ \"(ssr)/./src/contexts/toast-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/query-provider.tsx */ \"(ssr)/./src/providers/query-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Ccontexts%5C%5Ctoast-context.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Cproviders%5C%5Cquery-provider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/twitter/callback/page.tsx */ \"(ssr)/./src/app/auth/twitter/callback/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3J6JTVDJTVDRG9jdW1lbnRzJTVDJTVDQXVnbWVudCU1QyU1Q3NvY2lhbC1uZnQtcGxhdGZvcm0tdjIlNUMlNUNmcm9udGVuZC1oZWFkbGVzcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUN0d2l0dGVyJTVDJTVDY2FsbGJhY2slNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNExBQThKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxyelxcXFxEb2N1bWVudHNcXFxcQXVnbWVudFxcXFxzb2NpYWwtbmZ0LXBsYXRmb3JtLXYyXFxcXGZyb250ZW5kLWhlYWRsZXNzXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFx0d2l0dGVyXFxcXGNhbGxiYWNrXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Crz%5C%5CDocuments%5C%5CAugment%5C%5Csocial-nft-platform-v2%5C%5Cfrontend-headless%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Ctwitter%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/twitter/callback/page.tsx":
/*!************************************************!*\
  !*** ./src/app/auth/twitter/callback/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TwitterCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TwitterCallbackPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { updateUser } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addDebug = (msg)=>{\n        console.log(msg);\n        setDebugInfo((prev)=>[\n                ...prev,\n                `${new Date().toLocaleTimeString()}: ${msg}`\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TwitterCallbackPage.useEffect\": ()=>{\n            addDebug('🔄 Twitter Callback: useEffect triggered');\n            const handleTwitterCallback = {\n                \"TwitterCallbackPage.useEffect.handleTwitterCallback\": async ()=>{\n                    try {\n                        addDebug('🐦 Twitter Callback: Starting authentication process');\n                        // Get URL parameters directly from window.location\n                        const urlParams = new URLSearchParams(window.location.search);\n                        const token = urlParams.get('token') // JWT token from backend\n                        ;\n                        const error = urlParams.get('error');\n                        addDebug(`🐦 URL search params: ${window.location.search}`);\n                        addDebug(`🔐 Token: ${token ? 'present' : 'missing'}, Error: ${error || 'none'}`);\n                        if (error) {\n                            addDebug(`❌ Authentication error: ${error}`);\n                            setStatus('error');\n                            setMessage(`Authentication failed: ${error}`);\n                            return;\n                        }\n                        if (!token) {\n                            addDebug(`❌ Missing JWT token`);\n                            setStatus('error');\n                            setMessage('No authentication token received');\n                            return;\n                        }\n                        // ✅ ENHANCEMENT: Handle JWT token from backend\n                        try {\n                            addDebug('🔐 Processing JWT token from backend');\n                            // Validate token format (basic check)\n                            if (!token.startsWith('eyJ')) {\n                                addDebug('❌ Invalid JWT token format');\n                                setStatus('error');\n                                setMessage('Invalid authentication token format');\n                                return;\n                            }\n                            // ✅ ENHANCED: Validate JWT token with enhanced backend\n                            addDebug('🔍 Validating JWT token with enhanced backend');\n                            const validationResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.validateToken(token);\n                            if (!validationResponse.success) {\n                                addDebug(`❌ Enhanced token validation failed: ${validationResponse.error}`);\n                                setStatus('error');\n                                setMessage(`Enhanced token validation failed: ${validationResponse.error}`);\n                                return;\n                            }\n                            addDebug('✅ Enhanced JWT token validation successful');\n                            const validatedUser = validationResponse.data.user;\n                            addDebug(`👤 Enhanced authenticated user: ${validatedUser.username}`);\n                            addDebug(`✅ User details: ${validatedUser.displayName} (@${validatedUser.username})`);\n                            // ✅ ENHANCED: Create complete user object with enhanced data\n                            const userData = {\n                                id: validatedUser.id,\n                                username: validatedUser.username,\n                                email: validatedUser.email,\n                                displayName: validatedUser.displayName,\n                                profileImage: validatedUser.profileImage || 'https://via.placeholder.com/150',\n                                role: validatedUser.role,\n                                isEmailVerified: false,\n                                createdAt: new Date().toISOString(),\n                                lastUpdated: new Date().toISOString(),\n                                totalNfts: 0 // Will be updated as user creates NFTs\n                            };\n                            addDebug(`💾 Saving enhanced user data: ${userData.username}`);\n                            // Save to storage\n                            localStorage.setItem('auth_token', token);\n                            localStorage.setItem('user', JSON.stringify(userData));\n                            addDebug('🔄 Updating auth context...');\n                            // Update auth context\n                            updateUser(userData);\n                            addDebug('✅ JWT authentication completed successfully');\n                            setStatus('success');\n                            setMessage('Successfully logged in with Twitter!');\n                            // Redirect to dashboard after a short delay\n                            addDebug('🔄 Redirecting to dashboard in 2 seconds...');\n                            setTimeout({\n                                \"TwitterCallbackPage.useEffect.handleTwitterCallback\": ()=>{\n                                    addDebug('🚀 Redirecting to dashboard now');\n                                    router.push('/dashboard');\n                                }\n                            }[\"TwitterCallbackPage.useEffect.handleTwitterCallback\"], 2000);\n                        } catch (tokenError) {\n                            console.error('❌ JWT token processing error:', tokenError);\n                            addDebug(`❌ Token processing failed: ${tokenError.message}`);\n                            setStatus('error');\n                            setMessage('Failed to process authentication token');\n                        }\n                    } catch (error) {\n                        console.error('Twitter callback error:', error);\n                        setStatus('error');\n                        setMessage('An unexpected error occurred during authentication');\n                    }\n                }\n            }[\"TwitterCallbackPage.useEffect.handleTwitterCallback\"];\n            // Add a small delay to ensure the component is fully mounted\n            const timer = setTimeout({\n                \"TwitterCallbackPage.useEffect.timer\": ()=>{\n                    handleTwitterCallback();\n                }\n            }[\"TwitterCallbackPage.useEffect.timer\"], 100);\n            return ({\n                \"TwitterCallbackPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"TwitterCallbackPage.useEffect\"];\n        }\n    }[\"TwitterCallbackPage.useEffect\"], [\n        router,\n        updateUser\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto h-12 w-12 text-blue-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-12 w-12\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                                children: \"Completing Twitter Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-center text-sm text-gray-600\",\n                                children: \"Please wait while we complete your authentication...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this),\n                            debugInfo.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-gray-100 rounded text-xs text-left max-h-32 overflow-y-auto\",\n                                children: debugInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-700\",\n                                        children: info\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto h-12 w-12 text-green-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                                children: \"Login Successful!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-center text-sm text-gray-600\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-center text-sm text-gray-500\",\n                                children: \"Redirecting to dashboard...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto h-12 w-12 text-red-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                                children: \"Authentication Failed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-center text-sm text-gray-600\",\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/auth/login'),\n                                    className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                    children: \"Back to Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvdHdpdHRlci9jYWxsYmFjay9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMkM7QUFDaUI7QUFDWDtBQUNkO0FBRXBCLFNBQVNNO0lBQ3RCLE1BQU1DLFNBQVNMLDBEQUFTQTtJQUN4QixNQUFNTSxlQUFlTCxnRUFBZUE7SUFDcEMsTUFBTSxFQUFFTSxVQUFVLEVBQUUsR0FBR0wsK0RBQU9BO0lBQzlCLE1BQU0sQ0FBQ00sUUFBUUMsVUFBVSxHQUFHViwrQ0FBUUEsQ0FBa0M7SUFDdEUsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2EsV0FBV0MsYUFBYSxHQUFHZCwrQ0FBUUEsQ0FBVyxFQUFFO0lBRXZELE1BQU1lLFdBQVcsQ0FBQ0M7UUFDaEJDLFFBQVFDLEdBQUcsQ0FBQ0Y7UUFDWkYsYUFBYUssQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU0sR0FBRyxJQUFJQyxPQUFPQyxrQkFBa0IsR0FBRyxFQUFFLEVBQUVMLEtBQUs7YUFBQztJQUM5RTtJQUVBakIsZ0RBQVNBO3lDQUFDO1lBQ1JnQixTQUFTO1lBRVQsTUFBTU87dUVBQXdCO29CQUM1QixJQUFJO3dCQUNGUCxTQUFTO3dCQUVULG1EQUFtRDt3QkFDbkQsTUFBTVEsWUFBWSxJQUFJQyxnQkFBZ0JDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTt3QkFDNUQsTUFBTUMsUUFBUUwsVUFBVU0sR0FBRyxDQUFDLFNBQVMseUJBQXlCOzt3QkFDOUQsTUFBTUMsUUFBUVAsVUFBVU0sR0FBRyxDQUFDO3dCQUU1QmQsU0FBUyxDQUFDLHNCQUFzQixFQUFFVSxPQUFPQyxRQUFRLENBQUNDLE1BQU0sRUFBRTt3QkFDMURaLFNBQVMsQ0FBQyxVQUFVLEVBQUVhLFFBQVEsWUFBWSxVQUFVLFNBQVMsRUFBRUUsU0FBUyxRQUFRO3dCQUVoRixJQUFJQSxPQUFPOzRCQUNUZixTQUFTLENBQUMsd0JBQXdCLEVBQUVlLE9BQU87NEJBQzNDcEIsVUFBVTs0QkFDVkUsV0FBVyxDQUFDLHVCQUF1QixFQUFFa0IsT0FBTzs0QkFDNUM7d0JBQ0Y7d0JBRUEsSUFBSSxDQUFDRixPQUFPOzRCQUNWYixTQUFTLENBQUMsbUJBQW1CLENBQUM7NEJBQzlCTCxVQUFVOzRCQUNWRSxXQUFXOzRCQUNYO3dCQUNGO3dCQUVBLCtDQUErQzt3QkFDL0MsSUFBSTs0QkFDRkcsU0FBUzs0QkFFVCxzQ0FBc0M7NEJBQ3RDLElBQUksQ0FBQ2EsTUFBTUcsVUFBVSxDQUFDLFFBQVE7Z0NBQzVCaEIsU0FBUztnQ0FDVEwsVUFBVTtnQ0FDVkUsV0FBVztnQ0FDWDs0QkFDRjs0QkFFQSx1REFBdUQ7NEJBQ3ZERyxTQUFTOzRCQUNULE1BQU1pQixxQkFBcUIsTUFBTTVCLDZDQUFPQSxDQUFDNkIsYUFBYSxDQUFDTDs0QkFFdkQsSUFBSSxDQUFDSSxtQkFBbUJFLE9BQU8sRUFBRTtnQ0FDL0JuQixTQUFTLENBQUMsb0NBQW9DLEVBQUVpQixtQkFBbUJGLEtBQUssRUFBRTtnQ0FDMUVwQixVQUFVO2dDQUNWRSxXQUFXLENBQUMsa0NBQWtDLEVBQUVvQixtQkFBbUJGLEtBQUssRUFBRTtnQ0FDMUU7NEJBQ0Y7NEJBRUFmLFNBQVM7NEJBQ1QsTUFBTW9CLGdCQUFnQkgsbUJBQW1CSSxJQUFJLENBQUNDLElBQUk7NEJBQ2xEdEIsU0FBUyxDQUFDLGdDQUFnQyxFQUFFb0IsY0FBY0csUUFBUSxFQUFFOzRCQUNwRXZCLFNBQVMsQ0FBQyxnQkFBZ0IsRUFBRW9CLGNBQWNJLFdBQVcsQ0FBQyxHQUFHLEVBQUVKLGNBQWNHLFFBQVEsQ0FBQyxDQUFDLENBQUM7NEJBRXBGLDZEQUE2RDs0QkFDN0QsTUFBTUUsV0FBVztnQ0FDZkMsSUFBSU4sY0FBY00sRUFBRTtnQ0FDcEJILFVBQVVILGNBQWNHLFFBQVE7Z0NBQ2hDSSxPQUFPUCxjQUFjTyxLQUFLO2dDQUMxQkgsYUFBYUosY0FBY0ksV0FBVztnQ0FDdENJLGNBQWNSLGNBQWNRLFlBQVksSUFBSTtnQ0FDNUNDLE1BQU1ULGNBQWNTLElBQUk7Z0NBQ3hCQyxpQkFBaUI7Z0NBQ2pCQyxXQUFXLElBQUkxQixPQUFPMkIsV0FBVztnQ0FDakNDLGFBQWEsSUFBSTVCLE9BQU8yQixXQUFXO2dDQUNuQ0UsV0FBVyxFQUFFLHVDQUF1Qzs0QkFDdEQ7NEJBRUFsQyxTQUFTLENBQUMsOEJBQThCLEVBQUV5QixTQUFTRixRQUFRLEVBQUU7NEJBRTdELGtCQUFrQjs0QkFDbEJZLGFBQWFDLE9BQU8sQ0FBQyxjQUFjdkI7NEJBQ25Dc0IsYUFBYUMsT0FBTyxDQUFDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQ2I7NEJBRTVDekIsU0FBUzs0QkFFVCxzQkFBc0I7NEJBQ3RCUCxXQUFXZ0M7NEJBRVh6QixTQUFTOzRCQUVUTCxVQUFVOzRCQUNWRSxXQUFXOzRCQUVYLDRDQUE0Qzs0QkFDNUNHLFNBQVM7NEJBQ1R1Qzt1RkFBVztvQ0FDVHZDLFNBQVM7b0NBQ1RULE9BQU9pRCxJQUFJLENBQUM7Z0NBQ2Q7c0ZBQUc7d0JBRUwsRUFBRSxPQUFPQyxZQUFZOzRCQUNuQnZDLFFBQVFhLEtBQUssQ0FBQyxpQ0FBaUMwQjs0QkFDL0N6QyxTQUFTLENBQUMsMkJBQTJCLEVBQUV5QyxXQUFXN0MsT0FBTyxFQUFFOzRCQUMzREQsVUFBVTs0QkFDVkUsV0FBVzt3QkFDYjtvQkFDRixFQUFFLE9BQU9rQixPQUFZO3dCQUNuQmIsUUFBUWEsS0FBSyxDQUFDLDJCQUEyQkE7d0JBQ3pDcEIsVUFBVTt3QkFDVkUsV0FBVztvQkFDYjtnQkFDRjs7WUFFQSw2REFBNkQ7WUFDN0QsTUFBTTZDLFFBQVFIO3VEQUFXO29CQUN2QmhDO2dCQUNGO3NEQUFHO1lBRUg7aURBQU8sSUFBTW9DLGFBQWFEOztRQUM1Qjt3Q0FBRztRQUFDbkQ7UUFBUUU7S0FBVztJQUV2QixxQkFDRSw4REFBQ21EO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztvQkFDWm5ELFdBQVcsMkJBQ1Y7OzBDQUNFLDhEQUFDa0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVO29DQUF5QkUsTUFBSztvQ0FBT0MsU0FBUTs7c0RBQzFELDhEQUFDQzs0Q0FBT0osV0FBVTs0Q0FBYUssSUFBRzs0Q0FBS0MsSUFBRzs0Q0FBS0MsR0FBRTs0Q0FBS0MsUUFBTzs0Q0FBZUMsYUFBWTs7Ozs7O3NEQUN4Riw4REFBQ0M7NENBQUtWLFdBQVU7NENBQWFFLE1BQUs7NENBQWVTLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUd2RCw4REFBQ0M7Z0NBQUdaLFdBQVU7MENBQXlEOzs7Ozs7MENBR3ZFLDhEQUFDYTtnQ0FBRWIsV0FBVTswQ0FBeUM7Ozs7Ozs0QkFJckQvQyxVQUFVNkQsTUFBTSxHQUFHLG1CQUNsQiw4REFBQ2Y7Z0NBQUlDLFdBQVU7MENBQ1ovQyxVQUFVOEQsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNwQiw4REFBQ2xCO3dDQUFnQkMsV0FBVTtrREFBaUJnQjt1Q0FBbENDOzs7Ozs7Ozs7Ozs7b0JBT25CcEUsV0FBVywyQkFDVjs7MENBQ0UsOERBQUNrRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlDLE1BQUs7b0NBQU9NLFFBQU87b0NBQWVMLFNBQVE7OENBQzdDLDRFQUFDTzt3Q0FBS1EsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFWLGFBQVk7d0NBQUlFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3pFLDhEQUFDQztnQ0FBR1osV0FBVTswQ0FBeUQ7Ozs7OzswQ0FHdkUsOERBQUNhO2dDQUFFYixXQUFVOzBDQUNWakQ7Ozs7OzswQ0FFSCw4REFBQzhEO2dDQUFFYixXQUFVOzBDQUF5Qzs7Ozs7Ozs7b0JBTXpEbkQsV0FBVyx5QkFDVjs7MENBQ0UsOERBQUNrRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlDLE1BQUs7b0NBQU9NLFFBQU87b0NBQWVMLFNBQVE7OENBQzdDLDRFQUFDTzt3Q0FBS1EsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFWLGFBQVk7d0NBQUlFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3pFLDhEQUFDQztnQ0FBR1osV0FBVTswQ0FBeUQ7Ozs7OzswQ0FHdkUsOERBQUNhO2dDQUFFYixXQUFVOzBDQUNWakQ7Ozs7OzswQ0FFSCw4REFBQ2dEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDb0I7b0NBQ0NDLFNBQVMsSUFBTTNFLE9BQU9pRCxJQUFJLENBQUM7b0NBQzNCSyxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyelxcRG9jdW1lbnRzXFxBdWdtZW50XFxzb2NpYWwtbmZ0LXBsYXRmb3JtLXYyXFxmcm9udGVuZC1oZWFkbGVzc1xcc3JjXFxhcHBcXGF1dGhcXHR3aXR0ZXJcXGNhbGxiYWNrXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9hdXRoLWNvbnRleHQnXG5pbXBvcnQgeyBhdXRoQXBpIH0gZnJvbSAnQC9saWIvYXBpJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUd2l0dGVyQ2FsbGJhY2tQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKVxuICBjb25zdCB7IHVwZGF0ZVVzZXIgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbc3RhdHVzLCBzZXRTdGF0dXNdID0gdXNlU3RhdGU8J2xvYWRpbmcnIHwgJ3N1Y2Nlc3MnIHwgJ2Vycm9yJz4oJ2xvYWRpbmcnKVxuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2RlYnVnSW5mbywgc2V0RGVidWdJbmZvXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSlcblxuICBjb25zdCBhZGREZWJ1ZyA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKG1zZylcbiAgICBzZXREZWJ1Z0luZm8ocHJldiA9PiBbLi4ucHJldiwgYCR7bmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKX06ICR7bXNnfWBdKVxuICB9XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBhZGREZWJ1Zygn8J+UhCBUd2l0dGVyIENhbGxiYWNrOiB1c2VFZmZlY3QgdHJpZ2dlcmVkJylcblxuICAgIGNvbnN0IGhhbmRsZVR3aXR0ZXJDYWxsYmFjayA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGFkZERlYnVnKCfwn5CmIFR3aXR0ZXIgQ2FsbGJhY2s6IFN0YXJ0aW5nIGF1dGhlbnRpY2F0aW9uIHByb2Nlc3MnKVxuXG4gICAgICAgIC8vIEdldCBVUkwgcGFyYW1ldGVycyBkaXJlY3RseSBmcm9tIHdpbmRvdy5sb2NhdGlvblxuICAgICAgICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpXG4gICAgICAgIGNvbnN0IHRva2VuID0gdXJsUGFyYW1zLmdldCgndG9rZW4nKSAvLyBKV1QgdG9rZW4gZnJvbSBiYWNrZW5kXG4gICAgICAgIGNvbnN0IGVycm9yID0gdXJsUGFyYW1zLmdldCgnZXJyb3InKVxuXG4gICAgICAgIGFkZERlYnVnKGDwn5CmIFVSTCBzZWFyY2ggcGFyYW1zOiAke3dpbmRvdy5sb2NhdGlvbi5zZWFyY2h9YClcbiAgICAgICAgYWRkRGVidWcoYPCflJAgVG9rZW46ICR7dG9rZW4gPyAncHJlc2VudCcgOiAnbWlzc2luZyd9LCBFcnJvcjogJHtlcnJvciB8fCAnbm9uZSd9YClcblxuICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICBhZGREZWJ1Zyhg4p2MIEF1dGhlbnRpY2F0aW9uIGVycm9yOiAke2Vycm9yfWApXG4gICAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpXG4gICAgICAgICAgc2V0TWVzc2FnZShgQXV0aGVudGljYXRpb24gZmFpbGVkOiAke2Vycm9yfWApXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgICAgYWRkRGVidWcoYOKdjCBNaXNzaW5nIEpXVCB0b2tlbmApXG4gICAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpXG4gICAgICAgICAgc2V0TWVzc2FnZSgnTm8gYXV0aGVudGljYXRpb24gdG9rZW4gcmVjZWl2ZWQnKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgLy8g4pyFIEVOSEFOQ0VNRU5UOiBIYW5kbGUgSldUIHRva2VuIGZyb20gYmFja2VuZFxuICAgICAgICB0cnkge1xuICAgICAgICAgIGFkZERlYnVnKCfwn5SQIFByb2Nlc3NpbmcgSldUIHRva2VuIGZyb20gYmFja2VuZCcpXG5cbiAgICAgICAgICAvLyBWYWxpZGF0ZSB0b2tlbiBmb3JtYXQgKGJhc2ljIGNoZWNrKVxuICAgICAgICAgIGlmICghdG9rZW4uc3RhcnRzV2l0aCgnZXlKJykpIHtcbiAgICAgICAgICAgIGFkZERlYnVnKCfinYwgSW52YWxpZCBKV1QgdG9rZW4gZm9ybWF0JylcbiAgICAgICAgICAgIHNldFN0YXR1cygnZXJyb3InKVxuICAgICAgICAgICAgc2V0TWVzc2FnZSgnSW52YWxpZCBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3JtYXQnKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8g4pyFIEVOSEFOQ0VEOiBWYWxpZGF0ZSBKV1QgdG9rZW4gd2l0aCBlbmhhbmNlZCBiYWNrZW5kXG4gICAgICAgICAgYWRkRGVidWcoJ/CflI0gVmFsaWRhdGluZyBKV1QgdG9rZW4gd2l0aCBlbmhhbmNlZCBiYWNrZW5kJylcbiAgICAgICAgICBjb25zdCB2YWxpZGF0aW9uUmVzcG9uc2UgPSBhd2FpdCBhdXRoQXBpLnZhbGlkYXRlVG9rZW4odG9rZW4pXG5cbiAgICAgICAgICBpZiAoIXZhbGlkYXRpb25SZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgICAgICBhZGREZWJ1Zyhg4p2MIEVuaGFuY2VkIHRva2VuIHZhbGlkYXRpb24gZmFpbGVkOiAke3ZhbGlkYXRpb25SZXNwb25zZS5lcnJvcn1gKVxuICAgICAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpXG4gICAgICAgICAgICBzZXRNZXNzYWdlKGBFbmhhbmNlZCB0b2tlbiB2YWxpZGF0aW9uIGZhaWxlZDogJHt2YWxpZGF0aW9uUmVzcG9uc2UuZXJyb3J9YClcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cblxuICAgICAgICAgIGFkZERlYnVnKCfinIUgRW5oYW5jZWQgSldUIHRva2VuIHZhbGlkYXRpb24gc3VjY2Vzc2Z1bCcpXG4gICAgICAgICAgY29uc3QgdmFsaWRhdGVkVXNlciA9IHZhbGlkYXRpb25SZXNwb25zZS5kYXRhLnVzZXJcbiAgICAgICAgICBhZGREZWJ1Zyhg8J+RpCBFbmhhbmNlZCBhdXRoZW50aWNhdGVkIHVzZXI6ICR7dmFsaWRhdGVkVXNlci51c2VybmFtZX1gKVxuICAgICAgICAgIGFkZERlYnVnKGDinIUgVXNlciBkZXRhaWxzOiAke3ZhbGlkYXRlZFVzZXIuZGlzcGxheU5hbWV9IChAJHt2YWxpZGF0ZWRVc2VyLnVzZXJuYW1lfSlgKVxuXG4gICAgICAgICAgLy8g4pyFIEVOSEFOQ0VEOiBDcmVhdGUgY29tcGxldGUgdXNlciBvYmplY3Qgd2l0aCBlbmhhbmNlZCBkYXRhXG4gICAgICAgICAgY29uc3QgdXNlckRhdGEgPSB7XG4gICAgICAgICAgICBpZDogdmFsaWRhdGVkVXNlci5pZCxcbiAgICAgICAgICAgIHVzZXJuYW1lOiB2YWxpZGF0ZWRVc2VyLnVzZXJuYW1lLFxuICAgICAgICAgICAgZW1haWw6IHZhbGlkYXRlZFVzZXIuZW1haWwsXG4gICAgICAgICAgICBkaXNwbGF5TmFtZTogdmFsaWRhdGVkVXNlci5kaXNwbGF5TmFtZSxcbiAgICAgICAgICAgIHByb2ZpbGVJbWFnZTogdmFsaWRhdGVkVXNlci5wcm9maWxlSW1hZ2UgfHwgJ2h0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS8xNTAnLFxuICAgICAgICAgICAgcm9sZTogdmFsaWRhdGVkVXNlci5yb2xlLFxuICAgICAgICAgICAgaXNFbWFpbFZlcmlmaWVkOiBmYWxzZSwgLy8gV2lsbCBiZSB1cGRhdGVkIGJ5IGJhY2tlbmQgaWYgbmVlZGVkXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB0b3RhbE5mdHM6IDAgLy8gV2lsbCBiZSB1cGRhdGVkIGFzIHVzZXIgY3JlYXRlcyBORlRzXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgYWRkRGVidWcoYPCfkr4gU2F2aW5nIGVuaGFuY2VkIHVzZXIgZGF0YTogJHt1c2VyRGF0YS51c2VybmFtZX1gKVxuXG4gICAgICAgICAgLy8gU2F2ZSB0byBzdG9yYWdlXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhfdG9rZW4nLCB0b2tlbilcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSlcblxuICAgICAgICAgIGFkZERlYnVnKCfwn5SEIFVwZGF0aW5nIGF1dGggY29udGV4dC4uLicpXG5cbiAgICAgICAgICAvLyBVcGRhdGUgYXV0aCBjb250ZXh0XG4gICAgICAgICAgdXBkYXRlVXNlcih1c2VyRGF0YSlcblxuICAgICAgICAgIGFkZERlYnVnKCfinIUgSldUIGF1dGhlbnRpY2F0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKVxuXG4gICAgICAgICAgc2V0U3RhdHVzKCdzdWNjZXNzJylcbiAgICAgICAgICBzZXRNZXNzYWdlKCdTdWNjZXNzZnVsbHkgbG9nZ2VkIGluIHdpdGggVHdpdHRlciEnKVxuXG4gICAgICAgICAgLy8gUmVkaXJlY3QgdG8gZGFzaGJvYXJkIGFmdGVyIGEgc2hvcnQgZGVsYXlcbiAgICAgICAgICBhZGREZWJ1Zygn8J+UhCBSZWRpcmVjdGluZyB0byBkYXNoYm9hcmQgaW4gMiBzZWNvbmRzLi4uJylcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGFkZERlYnVnKCfwn5qAIFJlZGlyZWN0aW5nIHRvIGRhc2hib2FyZCBub3cnKVxuICAgICAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKVxuICAgICAgICAgIH0sIDIwMDApXG5cbiAgICAgICAgfSBjYXRjaCAodG9rZW5FcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBKV1QgdG9rZW4gcHJvY2Vzc2luZyBlcnJvcjonLCB0b2tlbkVycm9yKVxuICAgICAgICAgIGFkZERlYnVnKGDinYwgVG9rZW4gcHJvY2Vzc2luZyBmYWlsZWQ6ICR7dG9rZW5FcnJvci5tZXNzYWdlfWApXG4gICAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpXG4gICAgICAgICAgc2V0TWVzc2FnZSgnRmFpbGVkIHRvIHByb2Nlc3MgYXV0aGVudGljYXRpb24gdG9rZW4nKVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1R3aXR0ZXIgY2FsbGJhY2sgZXJyb3I6JywgZXJyb3IpXG4gICAgICAgIHNldFN0YXR1cygnZXJyb3InKVxuICAgICAgICBzZXRNZXNzYWdlKCdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkIGR1cmluZyBhdXRoZW50aWNhdGlvbicpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWRkIGEgc21hbGwgZGVsYXkgdG8gZW5zdXJlIHRoZSBjb21wb25lbnQgaXMgZnVsbHkgbW91bnRlZFxuICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBoYW5kbGVUd2l0dGVyQ2FsbGJhY2soKVxuICAgIH0sIDEwMClcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpXG4gIH0sIFtyb3V0ZXIsIHVwZGF0ZVVzZXJdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS01MCBweS0xMiBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCB3LWZ1bGwgc3BhY2UteS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICB7c3RhdHVzID09PSAnbG9hZGluZycgJiYgKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gaC0xMiB3LTEyXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgICAgICAgPHBhdGggY2xhc3NOYW1lPVwib3BhY2l0eS03NVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCI+PC9wYXRoPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cIm10LTYgdGV4dC1jZW50ZXIgdGV4dC0zeGwgZm9udC1leHRyYWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIENvbXBsZXRpbmcgVHdpdHRlciBMb2dpblxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIFBsZWFzZSB3YWl0IHdoaWxlIHdlIGNvbXBsZXRlIHlvdXIgYXV0aGVudGljYXRpb24uLi5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7LyogRGVidWcgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgIHtkZWJ1Z0luZm8ubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHAtMyBiZy1ncmF5LTEwMCByb3VuZGVkIHRleHQteHMgdGV4dC1sZWZ0IG1heC1oLTMyIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgICAge2RlYnVnSW5mby5tYXAoKGluZm8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiPntpbmZvfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAge3N0YXR1cyA9PT0gJ3N1Y2Nlc3MnICYmIChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPVwiMlwiIGQ9XCJNNSAxM2w0IDRMMTkgN1wiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJtdC02IHRleHQtY2VudGVyIHRleHQtM3hsIGZvbnQtZXh0cmFib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICBMb2dpbiBTdWNjZXNzZnVsIVxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIHttZXNzYWdlfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgUmVkaXJlY3RpbmcgdG8gZGFzaGJvYXJkLi4uXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7c3RhdHVzID09PSAnZXJyb3InICYmIChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIj48L3BhdGg+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwibXQtNiB0ZXh0LWNlbnRlciB0ZXh0LTN4bCBmb250LWV4dHJhYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgQXV0aGVudGljYXRpb24gRmFpbGVkXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAge21lc3NhZ2V9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02XCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9hdXRoL2xvZ2luJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBqdXN0aWZ5LWNlbnRlciBweS0yIHB4LTQgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEJhY2sgdG8gTG9naW5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJ1c2VBdXRoIiwiYXV0aEFwaSIsIlR3aXR0ZXJDYWxsYmFja1BhZ2UiLCJyb3V0ZXIiLCJzZWFyY2hQYXJhbXMiLCJ1cGRhdGVVc2VyIiwic3RhdHVzIiwic2V0U3RhdHVzIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJkZWJ1Z0luZm8iLCJzZXREZWJ1Z0luZm8iLCJhZGREZWJ1ZyIsIm1zZyIsImNvbnNvbGUiLCJsb2ciLCJwcmV2IiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhhbmRsZVR3aXR0ZXJDYWxsYmFjayIsInVybFBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsIndpbmRvdyIsImxvY2F0aW9uIiwic2VhcmNoIiwidG9rZW4iLCJnZXQiLCJlcnJvciIsInN0YXJ0c1dpdGgiLCJ2YWxpZGF0aW9uUmVzcG9uc2UiLCJ2YWxpZGF0ZVRva2VuIiwic3VjY2VzcyIsInZhbGlkYXRlZFVzZXIiLCJkYXRhIiwidXNlciIsInVzZXJuYW1lIiwiZGlzcGxheU5hbWUiLCJ1c2VyRGF0YSIsImlkIiwiZW1haWwiLCJwcm9maWxlSW1hZ2UiLCJyb2xlIiwiaXNFbWFpbFZlcmlmaWVkIiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJsYXN0VXBkYXRlZCIsInRvdGFsTmZ0cyIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5Iiwic2V0VGltZW91dCIsInB1c2giLCJ0b2tlbkVycm9yIiwidGltZXIiLCJjbGVhclRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJmaWxsIiwidmlld0JveCIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJwYXRoIiwiZCIsImgyIiwicCIsImxlbmd0aCIsIm1hcCIsImluZm8iLCJpbmRleCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImJ1dHRvbiIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/twitter/callback/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastComponent: () => (/* binding */ ToastComponent),\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transition/transition.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ToastComponent,ToastContainer auto */ \n\n\n\nconst toastIcons = {\n    success: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    error: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    warning: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    info: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n};\nconst toastColors = {\n    success: 'bg-green-50 border-green-200 text-green-800',\n    error: 'bg-red-50 border-red-200 text-red-800',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    info: 'bg-blue-50 border-blue-200 text-blue-800'\n};\nconst iconColors = {\n    success: 'text-green-400',\n    error: 'text-red-400',\n    warning: 'text-yellow-400',\n    info: 'text-blue-400'\n};\nfunction ToastComponent({ toast, onClose }) {\n    const [show, setShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const Icon = toastIcons[toast.type];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToastComponent.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ToastComponent.useEffect.timer\": ()=>{\n                    setShow(false);\n                    setTimeout({\n                        \"ToastComponent.useEffect.timer\": ()=>onClose(toast.id)\n                    }[\"ToastComponent.useEffect.timer\"], 300) // Wait for animation\n                    ;\n                }\n            }[\"ToastComponent.useEffect.timer\"], toast.duration || 5000);\n            return ({\n                \"ToastComponent.useEffect\": ()=>clearTimeout(timer)\n            })[\"ToastComponent.useEffect\"];\n        }\n    }[\"ToastComponent.useEffect\"], [\n        toast.id,\n        toast.duration,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Transition, {\n        show: show,\n        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n        enter: \"transform ease-out duration-300 transition\",\n        enterFrom: \"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2\",\n        enterTo: \"translate-y-0 opacity-100 sm:translate-x-0\",\n        leave: \"transition ease-in duration-100\",\n        leaveFrom: \"opacity-100\",\n        leaveTo: \"opacity-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border ${toastColors[toast.type]}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: `h-6 w-6 ${iconColors[toast.type]}`,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3 w-0 flex-1 pt-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium\",\n                                    children: toast.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                toast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm opacity-90\",\n                                    children: toast.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-4 flex-shrink-0 flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                onClick: ()=>{\n                                    setShow(false);\n                                    setTimeout(()=>onClose(toast.id), 300);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastContainer({ toasts, onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"aria-live\": \"assertive\",\n        className: \"fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full flex flex-col items-center space-y-4 sm:items-end\",\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastComponent, {\n                    toast: toast,\n                    onClose: onClose\n                }, toast.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,ProtectedRoute auto */ \n\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth provider component\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isAuthenticated = !!user;\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const initializeAuth = async ()=>{\n        try {\n            console.log('🔄 Auth Context: Initializing enhanced authentication state');\n            const token = _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.get('auth_token', null);\n            const savedUser = _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.get('user', null);\n            console.log('🔍 Debug - Token exists:', !!token);\n            console.log('🔍 Debug - User exists:', !!savedUser);\n            if (token) {\n                console.log(`🔑 Debug - Token: ${token.substring(0, 30)}...`);\n                console.log(`🔑 Debug - Token length: ${token.length}`);\n                console.log(`🔑 Debug - Token type: ${typeof token}`);\n                // Check if it's a JWT token\n                const isJWT = token.startsWith('eyJ');\n                console.log(`🔑 Debug - Is JWT format: ${isJWT}`);\n                if (isJWT) {\n                    try {\n                        const parts = token.split('.');\n                        console.log(`🔑 Debug - JWT parts count: ${parts.length}`);\n                        if (parts.length === 3) {\n                            const payload = JSON.parse(atob(parts[1]));\n                            console.log(`🔑 Debug - JWT payload:`, payload);\n                            console.log(`🔑 Debug - JWT provider: ${payload.provider || 'unknown'}`);\n                        }\n                    } catch (e) {\n                        console.log(`🔑 Debug - JWT decode error: ${e.message}`);\n                    }\n                }\n            }\n            if (savedUser) {\n                console.log(`👤 Debug - Saved user: ${JSON.stringify(savedUser, null, 2)}`);\n                console.log(`👤 Debug - User provider: ${savedUser.provider || 'unknown'}`);\n            }\n            if (token && savedUser) {\n                console.log('🔍 Auth Context: Found stored token and user, validating with enhanced backend...');\n                console.log(`👤 Stored user: ${savedUser.username}`);\n                console.log(`🔑 Token: ${token.substring(0, 20)}...`);\n                // ✅ ENHANCED: Use enhanced token validation endpoint with Twitter-specific handling\n                try {\n                    console.log('📡 Making token validation request...');\n                    // Check if this is a Twitter OAuth token\n                    const isTwitterToken = token.startsWith('eyJ') && savedUser?.provider === 'twitter';\n                    console.log(`🐦 Debug - Is Twitter token: ${isTwitterToken}`);\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.validateToken(token);\n                    console.log('📡 Token validation response:', response);\n                    if (response.success && response.data) {\n                        console.log('✅ Auth Context: Enhanced token validation successful');\n                        console.log(`👤 Validated user: ${response.data.user.username}`);\n                        // ✅ ENHANCED: Use validated user data from backend\n                        const validatedUser = {\n                            id: response.data.user.id,\n                            username: response.data.user.username,\n                            email: response.data.user.email,\n                            displayName: response.data.user.displayName,\n                            profileImage: response.data.user.profileImage || savedUser.profileImage,\n                            role: response.data.user.role,\n                            isEmailVerified: savedUser.isEmailVerified || false,\n                            createdAt: savedUser.createdAt || new Date().toISOString(),\n                            lastUpdated: new Date().toISOString(),\n                            totalNfts: savedUser.totalNfts || 0\n                        };\n                        console.log('🔄 Setting user in context:', validatedUser);\n                        setUser(validatedUser);\n                        // ✅ ENHANCED: Update stored user data with validated data\n                        _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', validatedUser);\n                        console.log('✅ Auth Context: User session restored with enhanced data');\n                    } else {\n                        console.log(`❌ Auth Context: Enhanced token validation failed: ${response.error}`);\n                        console.log('❌ Full response:', response);\n                        // ✅ TWITTER FIX: For Twitter tokens, try alternative validation\n                        if (isTwitterToken) {\n                            console.log('🐦 Twitter token validation failed, trying alternative approach...');\n                            // If token validation fails but we have saved user data,\n                            // check if token is still structurally valid (not expired)\n                            try {\n                                const parts = token.split('.');\n                                if (parts.length === 3) {\n                                    const payload = JSON.parse(atob(parts[1]));\n                                    const now = Math.floor(Date.now() / 1000);\n                                    if (payload.exp && payload.exp > now) {\n                                        console.log('🐦 Twitter token is not expired, restoring session');\n                                        setUser(savedUser);\n                                        console.log('✅ Auth Context: Twitter session restored from saved data');\n                                        return;\n                                    } else {\n                                        console.log('🐦 Twitter token is expired');\n                                    }\n                                }\n                            } catch (e) {\n                                console.log('🐦 Twitter token structure invalid');\n                            }\n                        }\n                        clearAuth();\n                    }\n                } catch (validationError) {\n                    console.error('❌ Auth Context: Enhanced token validation error:', validationError);\n                    console.error('❌ Full error:', validationError);\n                    // ✅ TWITTER FIX: For Twitter tokens, try fallback\n                    const isTwitterToken = token.startsWith('eyJ') && savedUser?.provider === 'twitter';\n                    if (isTwitterToken) {\n                        console.log('🐦 Twitter token validation error, trying fallback...');\n                        try {\n                            const parts = token.split('.');\n                            if (parts.length === 3) {\n                                const payload = JSON.parse(atob(parts[1]));\n                                const now = Math.floor(Date.now() / 1000);\n                                if (payload.exp && payload.exp > now) {\n                                    console.log('🐦 Twitter token fallback: token not expired, restoring session');\n                                    setUser(savedUser);\n                                    console.log('✅ Auth Context: Twitter session restored via fallback');\n                                    return;\n                                }\n                            }\n                        } catch (e) {\n                            console.log('🐦 Twitter token fallback failed');\n                        }\n                    }\n                    clearAuth();\n                }\n            } else {\n                console.log('🔍 Auth Context: No stored authentication found');\n                if (!token) console.log('❌ Missing token');\n                if (!savedUser) console.log('❌ Missing saved user');\n            }\n        } catch (error) {\n            console.error('❌ Auth Context: Enhanced auth initialization error:', error);\n            clearAuth();\n        } finally{\n            setIsLoading(false);\n            console.log('✅ Auth Context: Enhanced authentication initialization complete');\n        }\n    };\n    const login = async (emailOrUsername, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                emailOrUsername,\n                password\n            });\n            if (response.success && response.data) {\n                const { accessToken, user: userData } = response.data;\n                // Save to storage\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('auth_token', accessToken);\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', userData);\n                // Update state\n                setUser(userData);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || 'Login failed'\n                };\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                error: error.message || 'Login failed'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loginWithTwitter = async ()=>{\n        try {\n            setIsLoading(true);\n            // Redirect to Twitter OAuth through API Gateway\n            const twitterAuthUrl = `${process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'}/api/auth/twitter`;\n            console.log('🐦 Frontend: Redirecting to Twitter OAuth:', twitterAuthUrl);\n            window.location.href = twitterAuthUrl;\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Twitter login error:', error);\n            return {\n                success: false,\n                error: error.message || 'Twitter login failed'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (data)=>{\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.register(data);\n            if (response.success && response.data) {\n                const { accessToken, user: userData } = response.data;\n                // Save to storage\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('auth_token', accessToken);\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', userData);\n                // Update state\n                setUser(userData);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || 'Registration failed'\n                };\n            }\n        } catch (error) {\n            console.error('Registration error:', error);\n            return {\n                success: false,\n                error: error.message || 'Registration failed'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            // Call logout API\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        } catch (error) {\n            console.error('Logout API error:', error);\n        } finally{\n            // Clear state and storage regardless of API response\n            clearAuth();\n        }\n    };\n    const updateUser = (data)=>{\n        console.log('🔄 Auth Context: Updating user data', data);\n        if (data && Object.keys(data).length > 0) {\n            // If we have user data, update or create user\n            const updatedUser = user ? {\n                ...user,\n                ...data\n            } : data;\n            setUser(updatedUser);\n            _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', updatedUser);\n            console.log('✅ Auth Context: User updated successfully', updatedUser);\n        } else if (user) {\n            // If no data provided but user exists, just update existing\n            const updatedUser = {\n                ...user,\n                ...data\n            };\n            setUser(updatedUser);\n            _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', updatedUser);\n        }\n    };\n    const refreshAuth = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n            if (response.success && response.data) {\n                setUser(response.data);\n                _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.set('user', response.data);\n            }\n        } catch (error) {\n            console.error('Auth refresh error:', error);\n        }\n    };\n    const clearAuth = ()=>{\n        setUser(null);\n        _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.remove('auth_token');\n        _lib_utils__WEBPACK_IMPORTED_MODULE_3__.storage.remove('user');\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        isLoading,\n        login,\n        loginWithTwitter,\n        register,\n        logout,\n        updateUser,\n        refreshAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 341,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use auth context\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n// HOC for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, isLoading } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            // Redirect to login\n            if (false) {}\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n            lineNumber: 377,\n            columnNumber: 12\n        }, this);\n    };\n}\n// Component for protected routes\nfunction ProtectedRoute({ children }) {\n    const { isAuthenticated, isLoading } = useAuth();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\auth-context.tsx\",\n            lineNumber: 387,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        // Redirect to login\n        if (false) {}\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/toast-context.tsx":
/*!****************************************!*\
  !*** ./src/contexts/toast-context.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ useToast,ToastProvider auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return context;\n}\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (type, title, message, duration)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            id,\n            type,\n            title,\n            message,\n            duration: duration || 5000\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n    };\n    const showSuccess = (title, message)=>{\n        showToast('success', title, message);\n    };\n    const showError = (title, message)=>{\n        showToast('error', title, message);\n    };\n    const showWarning = (title, message)=>{\n        showToast('warning', title, message);\n    };\n    const showInfo = (title, message)=>{\n        showToast('info', title, message);\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            showToast,\n            showSuccess,\n            showError,\n            showWarning,\n            showInfo\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {\n                toasts: toasts,\n                onClose: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\toast-context.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\contexts\\\\toast-context.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/toast-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analysisApi: () => (/* binding */ analysisApi),\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   campaignApi: () => (/* binding */ campaignApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   marketplaceApi: () => (/* binding */ marketplaceApi),\n/* harmony export */   nftApi: () => (/* binding */ nftApi),\n/* harmony export */   searchApi: () => (/* binding */ searchApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n// API Configuration - All requests go through API Gateway\nconst API_GATEWAY_URL = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010';\n// All requests route through API Gateway - Single Entry Point\nconst API_BASE_URL = API_GATEWAY_URL + '/api';\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    const token = _utils__WEBPACK_IMPORTED_MODULE_0__.storage.get('auth_token', null);\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Clear auth token on unauthorized\n        _utils__WEBPACK_IMPORTED_MODULE_0__.storage.remove('auth_token');\n        _utils__WEBPACK_IMPORTED_MODULE_0__.storage.remove('user');\n        // Redirect to login if in browser\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// Generic API methods\nconst api = {\n    // GET request\n    get: async (url, config)=>{\n        try {\n            const response = await apiClient.get(url, config);\n            return response.data;\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.error || error.message || 'Request failed'\n            };\n        }\n    },\n    // POST request\n    post: async (url, data, config)=>{\n        try {\n            const response = await apiClient.post(url, data, config);\n            return response.data;\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.error || error.message || 'Request failed'\n            };\n        }\n    },\n    // PUT request\n    put: async (url, data, config)=>{\n        try {\n            const response = await apiClient.put(url, data, config);\n            return response.data;\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.error || error.message || 'Request failed'\n            };\n        }\n    },\n    // PATCH request\n    patch: async (url, data, config)=>{\n        try {\n            const response = await apiClient.patch(url, data, config);\n            return response.data;\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.error || error.message || 'Request failed'\n            };\n        }\n    },\n    // DELETE request\n    delete: async (url, config)=>{\n        try {\n            const response = await apiClient.delete(url, config);\n            return response.data;\n        } catch (error) {\n            return {\n                success: false,\n                error: error.response?.data?.error || error.message || 'Request failed'\n            };\n        }\n    }\n};\n// Auth API\nconst authApi = {\n    register: (data)=>api.post('/auth/register', data),\n    login: (data)=>api.post('/auth/login', data),\n    // Twitter OAuth endpoints\n    twitterAuth: ()=>api.get('/auth/twitter'),\n    twitterCallback: (code, state)=>api.post('/auth/twitter/callback', {\n            code,\n            state\n        }),\n    // Token validation\n    validateToken: (token)=>api.post('/auth/validate-token', {\n            token\n        }),\n    logout: ()=>api.post('/auth/logout'),\n    refreshToken: ()=>api.post('/auth/refresh'),\n    getProfile: ()=>api.get('/auth/profile'),\n    updateProfile: (data)=>api.patch('/auth/profile', data)\n};\n// User API\nconst userApi = {\n    getUsers: (params)=>api.get('/users', {\n            params\n        }),\n    getUser: (id)=>api.get(`/users/${id}`),\n    updateUser: (id, data)=>api.patch(`/users/${id}`, data),\n    deleteUser: (id)=>api.delete(`/users/${id}`),\n    getUserStats: (id)=>api.get(`/users/${id}/stats`)\n};\n// Campaign API\nconst campaignApi = {\n    getCampaigns: (params)=>api.get('/campaigns', {\n            params\n        }),\n    getCampaign: (id)=>api.get(`/campaigns/${id}`),\n    createCampaign: (data)=>api.post('/campaigns', data),\n    updateCampaign: (id, data)=>api.patch(`/campaigns/${id}`, data),\n    deleteCampaign: (id)=>api.delete(`/campaigns/${id}`),\n    joinCampaign: (id)=>api.post(`/campaigns/${id}/join`),\n    leaveCampaign: (id)=>api.post(`/campaigns/${id}/leave`),\n    getCampaignParticipants: (id)=>api.get(`/campaigns/${id}/participants`)\n};\n// NFT API - All requests through API Gateway\nconst nftApi = {\n    // Get user's NFT history via API Gateway\n    getUserNFTs: (userId)=>api.get(`/nfts/user/${userId}/history?limit=50`),\n    // Generate NFT from analysis via API Gateway\n    generateNFTFromAnalysis: (data)=>api.post('/nfts/generate-from-analysis', data),\n    // Standard NFT operations via API Gateway\n    getNFTs: (params)=>api.get('/nfts', {\n            params\n        }),\n    getNFT: (id)=>api.get(`/nfts/${id}`),\n    generateNFT: (data)=>api.post('/nfts/generate', data),\n    updateNFT: (id, data)=>api.patch(`/nfts/${id}`, data),\n    deleteNFT: (id)=>api.delete(`/nfts/${id}`),\n    getCampaignNFTs: (campaignId)=>api.get(`/nfts/campaign/${campaignId}`)\n};\n// Marketplace API\nconst marketplaceApi = {\n    getListings: (params)=>api.get('/marketplace', {\n            params\n        }),\n    getListing: (id)=>api.get(`/marketplace/${id}`),\n    createListing: (data)=>api.post('/marketplace', data),\n    updateListing: (id, data)=>api.patch(`/marketplace/${id}`, data),\n    deleteListing: (id)=>api.delete(`/marketplace/${id}`),\n    purchaseListing: (id)=>api.post(`/marketplace/${id}/purchase`)\n};\n// Search API\nconst searchApi = {\n    search: (params)=>api.get('/search', {\n            params\n        }),\n    autocomplete: (params)=>api.get('/search/autocomplete', {\n            params\n        }),\n    getPopularSearches: (params)=>api.get('/search/popular', {\n            params\n        }),\n    getSearchAnalytics: (params)=>api.get('/search/analytics', {\n            params\n        })\n};\n// Profile Analysis API - All requests through API Gateway\nconst analysisApi = {\n    // Analyze Twitter profile via API Gateway\n    analyzeTwitterProfile: (data)=>api.post('/analysis/twitter-profile', data),\n    // Get user's analysis history via API Gateway\n    getUserAnalysisHistory: (userId, limit = 10)=>api.get(`/analysis/history?userId=${userId}&limit=${limit}`)\n};\n// Analytics API\nconst analyticsApi = {\n    getDashboardStats: ()=>api.get('/analytics/dashboard'),\n    getUserAnalytics: (userId, timeframe)=>api.get(`/analytics/users/${userId}`, {\n            params: {\n                timeframe\n            }\n        }),\n    getCampaignAnalytics: (campaignId, timeframe)=>api.get(`/analytics/campaigns/${campaignId}`, {\n            params: {\n                timeframe\n            }\n        }),\n    getPlatformAnalytics: (timeframe)=>api.get('/analytics/platform', {\n            params: {\n                timeframe\n            }\n        }),\n    getRealtimeMetrics: ()=>api.get('/analytics/realtime'),\n    // New comprehensive analytics endpoints\n    getPlatformOverview: ()=>api.get('/analytics/platform-overview'),\n    getUserInsights: (userId)=>api.get(`/analytics/user/${userId}/insights`),\n    getNFTPerformance: (period = '30d', rarity)=>api.get('/analytics/nft-performance', {\n            params: {\n                period,\n                rarity\n            }\n        }),\n    getEngagementMetrics: (period = '30d')=>api.get('/analytics/engagement-metrics', {\n            params: {\n                period\n            }\n        }),\n    getTopGainersLosers: (period = '24h', limit = 5)=>api.get('/analytics/top-gainers-losers', {\n            params: {\n                period,\n                limit\n            }\n        }),\n    getCollectionMarketValue: ()=>api.get('/analytics/collection-market-value'),\n    getRecentTransactions: (limit = 20)=>api.get('/analytics/recent-transactions', {\n            params: {\n                limit\n            }\n        }),\n    trackEvent: (eventData)=>api.post('/analytics/track-event', eventData)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getRandomColor: () => (/* binding */ getRandomColor),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   storage: () => (/* binding */ storage),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx for conditional classes and tailwind-merge for deduplication\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency values\n */ function formatCurrency(value, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency\n    }).format(value);\n}\n/**\n * Format numbers with commas\n */ function formatNumber(value) {\n    return new Intl.NumberFormat('en-US').format(value);\n}\n/**\n * Truncate text with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n/**\n * Format date to relative time (e.g., \"2 hours ago\")\n */ function formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return date.toLocaleDateString();\n}\n/**\n * Generate random ID\n */ function generateId() {\n    return Math.random().toString(36).substring(2, 15);\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Sleep function for delays\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Check if value is empty (null, undefined, empty string, empty array, empty object)\n */ function isEmpty(value) {\n    if (value == null) return true;\n    if (typeof value === 'string') return value.trim() === '';\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === 'object') return Object.keys(value).length === 0;\n    return false;\n}\n/**\n * Safe JSON parse with fallback\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch  {\n        // Fallback for older browsers\n        const textArea = document.createElement('textarea');\n        textArea.value = text;\n        document.body.appendChild(textArea);\n        textArea.select();\n        const success = document.execCommand('copy');\n        document.body.removeChild(textArea);\n        return success;\n    }\n}\n/**\n * Get initials from name\n */ function getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0).toUpperCase()).slice(0, 2).join('');\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate random color for avatars\n */ function getRandomColor() {\n    const colors = [\n        'bg-red-500',\n        'bg-blue-500',\n        'bg-green-500',\n        'bg-yellow-500',\n        'bg-purple-500',\n        'bg-pink-500',\n        'bg-indigo-500',\n        'bg-teal-500'\n    ];\n    return colors[Math.floor(Math.random() * colors.length)];\n}\n/**\n * Format file size\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n/**\n * Check if running in browser\n */ function isBrowser() {\n    return \"undefined\" !== 'undefined';\n}\n/**\n * Local storage helpers with error handling\n */ const storage = {\n    get: (key, fallback)=>{\n        if (!isBrowser()) {\n            console.log(`🔍 Storage Debug: Not in browser, returning fallback for key: ${key}`);\n            return fallback;\n        }\n        try {\n            const item = localStorage.getItem(key);\n            console.log(`🔍 Storage Debug: Getting key \"${key}\":`, item ? 'found' : 'not found');\n            if (item) {\n                // ✅ FIXED: Handle JWT tokens (auth_token) as plain strings\n                if (key === 'auth_token') {\n                    console.log(`🔑 Storage Debug: JWT token retrieved: ${item.substring(0, 30)}...`);\n                    return item;\n                }\n                // For other keys, try JSON parsing\n                try {\n                    const parsed = JSON.parse(item);\n                    console.log(`🔍 Storage Debug: Parsed value for \"${key}\":`, typeof parsed === 'object' ? JSON.stringify(parsed).substring(0, 100) + '...' : parsed);\n                    return parsed;\n                } catch (parseError) {\n                    // If JSON parsing fails, return as string\n                    console.log(`🔍 Storage Debug: JSON parse failed for \"${key}\", returning as string`);\n                    return item;\n                }\n            }\n            return fallback;\n        } catch (error) {\n            console.error(`❌ Storage Debug: Error getting key \"${key}\":`, error);\n            return fallback;\n        }\n    },\n    set: (key, value)=>{\n        if (!isBrowser()) {\n            console.log(`🔍 Storage Debug: Not in browser, cannot set key: ${key}`);\n            return;\n        }\n        try {\n            // ✅ FIXED: Handle JWT tokens (auth_token) as plain strings\n            if (key === 'auth_token' && typeof value === 'string') {\n                localStorage.setItem(key, value);\n                console.log(`🔑 Storage Debug: JWT token stored: ${value.substring(0, 30)}...`);\n                return;\n            }\n            // For other keys, JSON stringify\n            const stringValue = JSON.stringify(value);\n            localStorage.setItem(key, stringValue);\n            console.log(`✅ Storage Debug: Set key \"${key}\":`, typeof value === 'object' ? JSON.stringify(value).substring(0, 100) + '...' : value);\n        } catch (error) {\n            console.error(`❌ Storage Debug: Error setting key \"${key}\":`, error);\n        }\n    },\n    remove: (key)=>{\n        if (!isBrowser()) {\n            console.log(`🔍 Storage Debug: Not in browser, cannot remove key: ${key}`);\n            return;\n        }\n        try {\n            localStorage.removeItem(key);\n            console.log(`🗑️ Storage Debug: Removed key \"${key}\"`);\n        } catch (error) {\n            console.error(`❌ Storage Debug: Error removing key \"${key}\":`, error);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/query-provider.tsx":
/*!******************************************!*\
  !*** ./src/providers/query-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        // With SSR, we usually want to set some default staleTime\n                        // above 0 to avoid refetching immediately on the client\n                        staleTime: 60 * 1000,\n                        retry: {\n                            \"QueryProvider.useState\": (failureCount, error)=>{\n                                // Don't retry on 4xx errors\n                                if (error?.response?.status >= 400 && error?.response?.status < 500) {\n                                    return false;\n                                }\n                                // Retry up to 3 times for other errors\n                                return failureCount < 3;\n                            }\n                        }[\"QueryProvider.useState\"]\n                    },\n                    mutations: {\n                        retry: false\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\providers\\\\query-provider.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\providers\\\\query-provider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/query-provider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@tanstack","vendor-chunks/mime-db","vendor-chunks/@headlessui","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/@heroicons","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Ftwitter%2Fcallback%2Fpage&page=%2Fauth%2Ftwitter%2Fcallback%2Fpage&appPaths=%2Fauth%2Ftwitter%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Ftwitter%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crz%5CDocuments%5CAugment%5Csocial-nft-platform-v2%5Cfrontend-headless&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();