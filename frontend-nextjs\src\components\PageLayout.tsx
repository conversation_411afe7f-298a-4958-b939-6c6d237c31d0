'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack
} from '@chakra-ui/react'
import Link from 'next/link'
import { ReactNode } from 'react'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface PageLayoutProps {
  title: string
  description?: string
  breadcrumbs?: BreadcrumbItem[]
  children: ReactNode
  maxWidth?: string
  headerActions?: ReactNode
}

export default function PageLayout({
  title,
  description,
  breadcrumbs,
  children,
  maxWidth = 'container.xl',
  headerActions
}: PageLayoutProps) {
  return (
    <Box py={8}>
      <Container maxW={maxWidth}>
        <VStack gap={8} align="stretch">
          {/* Simple Breadcrumbs */}
          {breadcrumbs && breadcrumbs.length > 0 && (
            <HStack fontSize="sm" color="gray.600" gap={2}>
              {breadcrumbs.map((crumb, index) => (
                <HStack key={index} gap={2}>
                  {crumb.href ? (
                    <Link href={crumb.href} style={{ color: 'blue', textDecoration: 'underline' }}>
                      {crumb.label}
                    </Link>
                  ) : (
                    <Text color="gray.800" fontWeight="medium">{crumb.label}</Text>
                  )}
                  {index < breadcrumbs.length - 1 && <Text color="gray.400">→</Text>}
                </HStack>
              ))}
            </HStack>
          )}

          {/* Page Header */}
          <Box>
            <HStack justify="space-between" align="start" mb={description ? 4 : 0}>
              <VStack align="start" gap={2}>
                <Heading as="h1" size="xl">
                  {title}
                </Heading>
                {description && (
                  <Text color="gray.600" fontSize="lg">
                    {description}
                  </Text>
                )}
              </VStack>

              {headerActions && (
                <Box>{headerActions}</Box>
              )}
            </HStack>
          </Box>

          {/* Page Content */}
          <Box>
            {children}
          </Box>
        </VStack>
      </Container>
    </Box>
  )
}

// Specialized Layout Components
export function DashboardLayout({ children }: { children: ReactNode }) {
  return (
    <PageLayout
      title="Dashboard"
      description="Welcome to your Social NFT Platform dashboard"
      breadcrumbs={[
        { label: 'Home', href: '/' },
        { label: 'Dashboard' }
      ]}
    >
      {children}
    </PageLayout>
  )
}

export function CampaignsLayout({ children }: { children: ReactNode }) {
  return (
    <PageLayout
      title="Campaigns"
      description="Browse and join campaigns to generate unique NFTs"
      breadcrumbs={[
        { label: 'Home', href: '/' },
        { label: 'Campaigns' }
      ]}
    >
      {children}
    </PageLayout>
  )
}

export function NFTsLayout({ children }: { children: ReactNode }) {
  return (
    <PageLayout
      title="My NFT Collection"
      description="View and manage your generated NFTs"
      breadcrumbs={[
        { label: 'Home', href: '/' },
        { label: 'My NFTs' }
      ]}
    >
      {children}
    </PageLayout>
  )
}

export function ProfileLayout({ children }: { children: ReactNode }) {
  return (
    <PageLayout
      title="Profile"
      description="Manage your account and social connections"
      breadcrumbs={[
        { label: 'Home', href: '/' },
        { label: 'Profile' }
      ]}
    >
      {children}
    </PageLayout>
  )
}
