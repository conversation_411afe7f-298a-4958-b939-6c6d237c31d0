const axios = require('axios');

const API_BASE = 'http://localhost:3010/api';

async function testProfileAnalysisIntegration() {
  console.log('🔍 Testing Complete Profile Analysis Integration...\n');

  try {
    let accessToken = '';
    let userId = '';

    // Test 1: User Registration & Authentication
    console.log('1. Testing User Registration & Authentication...');
    const timestamp = Date.now();
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, {
      username: `analysisuser${timestamp}`,
      email: `analysisuser${timestamp}@example.com`,
      password: 'TestPassword123',
      confirmPassword: 'TestPassword123',
      displayName: 'Analysis Test User'
    });

    console.log('✅ Registration Success:', {
      success: registerResponse.data.success,
      user: registerResponse.data.data?.user?.username
    });

    accessToken = registerResponse.data.data.accessToken;
    userId = registerResponse.data.data.user.id;
    console.log('');

    // Test 2: Profile Analysis Health Check
    console.log('2. Testing Profile Analysis Health Check...');
    const healthResponse = await axios.get(`${API_BASE}/analysis/health`);
    console.log('✅ Analysis Service Health:', {
      success: healthResponse.data.success,
      service: healthResponse.data.data?.service,
      gateway: healthResponse.data.data?.gateway
    });
    console.log('');

    // Test 3: Twitter Profile Analysis via API Gateway
    console.log('3. Testing Twitter Profile Analysis via API Gateway...');
    const analysisRequest = {
      twitterHandle: 'testuser_gateway',
      userId: userId
    };

    const analysisResponse = await axios.post(
      `${API_BASE}/analysis/twitter-profile`,
      analysisRequest,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Twitter Analysis Success:', {
      success: analysisResponse.data.success,
      analysisId: analysisResponse.data.data?.id,
      twitterHandle: analysisResponse.data.data?.twitterHandle,
      score: analysisResponse.data.data?.score,
      status: analysisResponse.data.data?.status,
      rarity: analysisResponse.data.data?.analysisData?.nftRecommendation?.rarity
    });

    const analysisId = analysisResponse.data.data.id;
    console.log('');

    // Test 4: Get Analysis Results via API Gateway
    console.log('4. Testing Get Analysis Results via API Gateway...');
    const resultsResponse = await axios.get(
      `${API_BASE}/analysis/results/${analysisId}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );

    console.log('✅ Analysis Results Retrieved:', {
      success: resultsResponse.data.success,
      analysisId: resultsResponse.data.data?.id,
      score: resultsResponse.data.data?.score,
      status: resultsResponse.data.data?.status
    });

    // Display detailed analysis data
    if (resultsResponse.data.data?.analysisData) {
      const analysisData = resultsResponse.data.data.analysisData;
      console.log('📊 Detailed Analysis Results:');
      console.log('  Profile Metrics:', {
        followers: analysisData.profile?.followerCount,
        following: analysisData.profile?.followingCount,
        tweets: analysisData.profile?.tweetCount,
        engagementRate: analysisData.profile?.engagementRate,
        verified: analysisData.profile?.isVerified
      });
      console.log('  Analysis Scores:', {
        contentQuality: analysisData.metrics?.contentQuality,
        activityLevel: analysisData.metrics?.activityLevel,
        influenceScore: analysisData.metrics?.influenceScore,
        authenticity: analysisData.metrics?.authenticity
      });
      console.log('  NFT Recommendation:', {
        score: analysisData.nftRecommendation?.score,
        rarity: analysisData.nftRecommendation?.rarity,
        reasoning: analysisData.nftRecommendation?.reasoning?.slice(0, 2) // Show first 2 reasons
      });
    }
    console.log('');

    // Test 5: Get User Analysis History via API Gateway
    console.log('5. Testing User Analysis History via API Gateway...');
    const historyResponse = await axios.get(
      `${API_BASE}/analysis/history?userId=${userId}&limit=5`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );

    console.log('✅ Analysis History Retrieved:', {
      success: historyResponse.data.success,
      totalAnalyses: historyResponse.data.data?.total,
      analysesReturned: historyResponse.data.data?.analyses?.length
    });
    console.log('');

    // Test 6: Re-analyze Profile via API Gateway
    console.log('6. Testing Profile Re-analysis via API Gateway...');
    const reanalysisResponse = await axios.put(
      `${API_BASE}/analysis/${analysisId}/reanalyze`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );

    console.log('✅ Re-analysis Success:', {
      success: reanalysisResponse.data.success,
      previousScore: reanalysisResponse.data.comparison?.previousScore,
      newScore: reanalysisResponse.data.comparison?.newScore,
      scoreDifference: reanalysisResponse.data.comparison?.scoreDifference
    });
    console.log('');

    // Test 7: Multiple Twitter Handles Analysis
    console.log('7. Testing Multiple Twitter Handles Analysis...');
    const handles = ['@elonmusk', 'billgates', 'tim_cook'];
    const analysisResults = [];

    for (const handle of handles) {
      try {
        const multiAnalysisResponse = await axios.post(
          `${API_BASE}/analysis/twitter-profile`,
          {
            twitterHandle: handle,
            userId: userId
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        analysisResults.push({
          handle: handle,
          score: multiAnalysisResponse.data.data?.score,
          rarity: multiAnalysisResponse.data.data?.analysisData?.nftRecommendation?.rarity,
          success: true
        });
      } catch (error) {
        analysisResults.push({
          handle: handle,
          error: error.message,
          success: false
        });
      }
    }

    console.log('✅ Multiple Handles Analysis Results:');
    analysisResults.forEach((result, index) => {
      if (result.success) {
        console.log(`  ${index + 1}. ${result.handle} - Score: ${result.score} - Rarity: ${result.rarity}`);
      } else {
        console.log(`  ${index + 1}. ${result.handle} - Error: ${result.error}`);
      }
    });
    console.log('');

    // Test 8: Updated Analysis History
    console.log('8. Testing Updated Analysis History...');
    const updatedHistoryResponse = await axios.get(
      `${API_BASE}/analysis/history?userId=${userId}&limit=10`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );

    console.log('✅ Updated History Retrieved:', {
      success: updatedHistoryResponse.data.success,
      totalAnalyses: updatedHistoryResponse.data.data?.total,
      analysesReturned: updatedHistoryResponse.data.data?.analyses?.length
    });

    if (updatedHistoryResponse.data.data?.analyses) {
      console.log('📋 Complete Analysis History:');
      updatedHistoryResponse.data.data.analyses.forEach((analysis, index) => {
        console.log(`  ${index + 1}. ${analysis.twitterHandle} - Score: ${analysis.score} - Rarity: ${analysis.analysisData?.nftRecommendation?.rarity || 'N/A'}`);
      });
    }
    console.log('');

    // Test 9: Error Handling - Invalid Analysis ID
    console.log('9. Testing Error Handling - Invalid Analysis ID...');
    try {
      await axios.get(
        `${API_BASE}/analysis/results/invalid_analysis_id`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      console.log('❌ Error handling test failed: Should have returned error');
    } catch (error) {
      console.log('✅ Error Handling Success:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message
      });
    }
    console.log('');

    // Summary
    console.log('🎉 All Profile Analysis Integration Tests Passed!');
    console.log('');
    console.log('📊 Integration Test Summary:');
    console.log('- ✅ User Registration & Authentication: Working');
    console.log('- ✅ Profile Analysis Health Check: Working');
    console.log('- ✅ Twitter Profile Analysis via API Gateway: Working');
    console.log('- ✅ Analysis Results Retrieval via API Gateway: Working');
    console.log('- ✅ User Analysis History via API Gateway: Working');
    console.log('- ✅ Profile Re-analysis via API Gateway: Working');
    console.log('- ✅ Multiple Handles Analysis: Working');
    console.log('- ✅ Error Handling: Working');
    console.log('');
    console.log('🚀 COMPLETE END-TO-END PROFILE ANALYSIS INTEGRATION: FULLY FUNCTIONAL!');
    console.log('');
    console.log('🎯 Ready for Frontend Integration:');
    console.log('- Authentication system working');
    console.log('- Profile analysis business logic implemented');
    console.log('- API Gateway routing working');
    console.log('- Comprehensive error handling');
    console.log('- Real-time analysis with scoring and NFT recommendations');

  } catch (error) {
    console.error('❌ Profile Analysis Integration Test Failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure API Gateway is running on port 3010');
    console.log('2. Make sure Profile Analysis Service is running on port 3002');
    console.log('3. Make sure User Service is running on port 3011');
    console.log('4. Verify all database connections');
    console.log('5. Check service logs for detailed error information');
  }
}

testProfileAnalysisIntegration();
