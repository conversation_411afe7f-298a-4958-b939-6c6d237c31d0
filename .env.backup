# Development Environment with Mock Services
# Use this configuration for fast development without external API dependencies

NODE_ENV=development
USE_MOCK_SERVICES=true

# API Gateway Configuration
API_GATEWAY_PORT=3010

# Mock Service URLs (Development Services)
MOCK_TWITTER_SERVICE_URL=http://localhost:3020
MOCK_BLOCKCHAIN_SERVICE_URL=http://localhost:3021
MOCK_NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Real Service URLs (Production Services - still available in development)
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
ANALYTICS_SERVICE_URL=http://localhost:3007
NOTIFICATION_SERVICE_URL=http://localhost:3008

# Database Configuration (Real database for all environments)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-specific Database Names
USER_DB_NAME=user_service_db
PROFILE_ANALYSIS_DB_NAME=profile_analysis_db
NFT_GENERATION_DB_NAME=nft_generation_db
BLOCKCHAIN_DB_NAME=blockchain_service_db
PROJECT_DB_NAME=project_service_db
MARKETPLACE_DB_NAME=marketplace_service_db
ANALYTICS_DB_NAME=analytics_service_db
NOTIFICATION_DB_NAME=notification_service_db

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true

# Real Service API Keys (for production integrations)
# Twitter API Configuration
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=1WzbsQXOrskAx4tP231AtUSKm6QOnDklbvXMHozDwP8nOpnK3c
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAKEC1AEAAAAAfnWqV8LvjCmxyIUfLf1cslix%2FTI%3DmgfXwKx3Mm5warOLTUyqcbUzbiN7lrQ1UmE5GoMTOOY051IkPZ

# Blockchain Configuration
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
ETHEREUM_TESTNET_RPC_URL=https://sepolia.infura.io/v3/your-project-id
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id
POLYGON_TESTNET_RPC_URL=https://polygon-mumbai.infura.io/v3/your-project-id
ETHEREUM_CHAIN_ID=1
POLYGON_CHAIN_ID=137
NFT_CONTRACT_ADDRESS_ETHEREUM=******************************************
NFT_CONTRACT_ADDRESS_POLYGON=******************************************

# NFT Storage Configuration
NFT_STORAGE_API_KEY=your-nft-storage-api-key
PINATA_API_KEY=your-pinata-api-key
PINATA_SECRET_API_KEY=your-pinata-secret-api-key
IPFS_PROVIDER=nft.storage
PINNING_SERVICE=pinata
