# Manual Testing Instructions

## 🧪 **Complete Frontend Testing Guide**

**All services are running and ready for testing!**

### **✅ Backend Status Verified:**
- User Service (3011): JWT working ✓
- Project Service (3005): Campaigns loading ✓  
- NFT Service (3003): Ready ✓
- Profile Service (3002): Ready ✓

### **🎯 Testing Steps:**

#### **Step 1: Test User Registration**
1. **Go to:** http://localhost:3000/auth/register
2. **Fill form with:**
   - Username: `manualtest2024`
   - Email: `<EMAIL>`
   - Password: `password123`
3. **Submit and observe:** Should redirect to dashboard

#### **Step 2: Test JWT Persistence**
1. **After successful login:** Refresh the page (F5)
2. **Check console logs:** Should see JWT debugging messages
3. **Expected:** Should stay logged in (no redirect to login)

#### **Step 3: Test Campaign Navigation**
1. **On dashboard:** Click "View Campaign" button
2. **Expected:** Navigate to campaign detail page
3. **Test back button:** Should return to dashboard

#### **Step 4: Test Logout**
1. **Click logout button** on dashboard
2. **Expected:** Redirect to login page
3. **Test refresh:** Should stay on login (not auto-login)

### **🔍 What to Look For:**
- ✅ No CORS errors in console
- ✅ JWT debugging logs showing token status
- ✅ Campaign data loading correctly
- ✅ Navigation working smoothly

## 🎯 **Ready for Complete Testing!**
