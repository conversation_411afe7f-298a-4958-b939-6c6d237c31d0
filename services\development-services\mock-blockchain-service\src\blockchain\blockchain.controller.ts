import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BlockchainService } from './blockchain.service';

@ApiTags('blockchain')
@Controller('blockchain')
export class BlockchainController {
  constructor(private readonly blockchainService: BlockchainService) {}

  @Get('health')
  @ApiOperation({ summary: 'Blockchain service health check' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck() {
    return this.blockchainService.healthCheck();
  }

  @Get('status')
  @ApiOperation({ summary: 'Get blockchain network status' })
  @ApiResponse({ status: 200, description: 'Network status retrieved successfully' })
  async getNetworkStatus() {
    return {
      status: 'success',
      data: {
        network: 'mock-ethereum',
        blockHeight: 18500000 + Math.floor(Math.random() * 1000),
        gasPrice: '20 gwei',
        connected: true,
        timestamp: new Date().toISOString()
      }
    };
  }
}
