"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/basic-analytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/basic-analytics.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BasicAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CurrencyDollarIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BasicAnalytics() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for different data sections\n    const [topGainers, setTopGainers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topLosers, setTopLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collectionGainers, setCollectionGainers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collectionLosers, setCollectionLosers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topUsers, setTopUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentTransactions, setRecentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch data from backend\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BasicAnalytics.useEffect\": ()=>{\n            const fetchAnalyticsData = {\n                \"BasicAnalytics.useEffect.fetchAnalyticsData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        // Fetch all analytics data in parallel\n                        const [gainersLosersResponse, collectionMarketResponse, platformOverviewResponse, recentTransactionsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getTopGainersLosers('7d', 10),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getCollectionMarketValue(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getPlatformOverview(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.analyticsApi.getRecentTransactions(10)\n                        ]);\n                        // Process gainers/losers data\n                        if (gainersLosersResponse.success && gainersLosersResponse.data) {\n                            const { gainers, losers } = gainersLosersResponse.data;\n                            setTopGainers(gainers || []);\n                            setTopLosers(losers || []);\n                        }\n                        // Process collection market data\n                        if (collectionMarketResponse.success && collectionMarketResponse.data) {\n                            const { gainers, losers } = collectionMarketResponse.data;\n                            setCollectionGainers(gainers || []);\n                            setCollectionLosers(losers || []);\n                        }\n                        // Process platform overview for top users\n                        if (platformOverviewResponse.success && platformOverviewResponse.data) {\n                            const { topUsers: users } = platformOverviewResponse.data;\n                            setTopUsers(users || []);\n                        }\n                        // Process recent transactions\n                        if (recentTransactionsResponse.success && recentTransactionsResponse.data) {\n                            setRecentTransactions(recentTransactionsResponse.data.transactions || []);\n                        }\n                    } catch (err) {\n                        console.error('Error fetching analytics data:', err);\n                        setError('Failed to load analytics data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"BasicAnalytics.useEffect.fetchAnalyticsData\"];\n            fetchAnalyticsData();\n        }\n    }[\"BasicAnalytics.useEffect\"], []);\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error Loading Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Analytics Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Platform insights and performance tracking\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Users Score Board\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700\",\n                                children: \"See Top 100 ↗\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Gainer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Absolute (5m)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Relative (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ30D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ3M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'Robert Le...',\n                                                            handle: '@robert',\n                                                            current: '0.48%',\n                                                            d7: '+43pps',\n                                                            d30: '+30pps',\n                                                            d3m: '+36pps',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Laura S...',\n                                                            handle: '@laura',\n                                                            current: '0.58%',\n                                                            d7: '+35pps',\n                                                            d30: '+30pps',\n                                                            d3m: '+32pps',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Yann...',\n                                                            handle: '@yann',\n                                                            current: '0.50%',\n                                                            d7: '+22pps',\n                                                            d30: '+25pps',\n                                                            d3m: '+26pps',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Yu Hu...',\n                                                            handle: '@yuhu',\n                                                            current: '0.61%',\n                                                            d7: '+43pps',\n                                                            d30: '+25pps',\n                                                            d3m: '+36pps',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'alext...',\n                                                            handle: '@alext',\n                                                            current: '0.51%',\n                                                            d7: '+30pps',\n                                                            d30: '+20pps',\n                                                            d3m: '+28pps',\n                                                            trend: 'up'\n                                                        }\n                                                    ].map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold\",\n                                                                                children: user.name.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 209,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900\",\n                                                                    children: user.current\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d7\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d30\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: user.d3m\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Loser\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Absolute (5m)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Relative (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ30D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ3M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'Tomass K...',\n                                                            handle: '@tomass',\n                                                            current: '0.06%',\n                                                            d7: '-7pps',\n                                                            d30: '-37pps',\n                                                            d3m: '-44pps',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'BREAD...',\n                                                            handle: '@bread',\n                                                            current: '0.27%',\n                                                            d7: '-57pps',\n                                                            d30: '-25pps',\n                                                            d3m: '-50pps',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: '_gabrielS...',\n                                                            handle: '@gabriel',\n                                                            current: '0.39%',\n                                                            d7: '-6pps',\n                                                            d30: '-30pps',\n                                                            d3m: '-44pps',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'alyson...',\n                                                            handle: '@alyson',\n                                                            current: '0.30%',\n                                                            d7: '-6pps',\n                                                            d30: '-24pps',\n                                                            d3m: '-37pps',\n                                                            trend: 'down'\n                                                        }\n                                                    ].map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold\",\n                                                                                children: user.name.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900\",\n                                                                    children: user.current\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d7\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d30\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: user.d3m\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Top20\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"7D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-medium\",\n                                                        children: \"30D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"3M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"6M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"12M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '5%',\n                                                    width: '22%',\n                                                    height: '35%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"\\uD83E\\uDDCA IcoBe...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-yellow-200 text-xs\",\n                                                                    children: \"\\uD83D\\uDC51\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"0.73%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-yellow-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-yellow-300 rounded\",\n                                                                        style: {\n                                                                            width: '60%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-red-600 to-red-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '26%',\n                                                    top: '5%',\n                                                    width: '20%',\n                                                    height: '32%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"\\uD83C\\uDF5E BREAD | Σ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"0.56%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-red-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-red-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '48%',\n                                                    top: '5%',\n                                                    width: '18%',\n                                                    height: '30%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDFE2 mert | heliu...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.55%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-green-300 rounded\",\n                                                                        style: {\n                                                                            width: '55%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '68%',\n                                                    top: '5%',\n                                                    width: '16%',\n                                                    height: '28%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDF19 Yueya.eth...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.51%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-blue-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-blue-300 rounded\",\n                                                                        style: {\n                                                                            width: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '42%',\n                                                    width: '16%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC0B wale.m...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.45%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-teal-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '20%',\n                                                    top: '42%',\n                                                    width: '15%',\n                                                    height: '23%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC64 david p...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.40%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-purple-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-purple-300 rounded\",\n                                                                        style: {\n                                                                            width: '40%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '37%',\n                                                    top: '42%',\n                                                    width: '13%',\n                                                    height: '20%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDFAF nairolf\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"0.38%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '52%',\n                                                    top: '42%',\n                                                    width: '12%',\n                                                    height: '18%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"⚡ Tomas...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"0.38%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Collection Market Value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700\",\n                                children: \"See All Collections ↗\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Gainer Collections\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Market Cap (24h)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Volume (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Floor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ24h\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'CryptoArt Pro',\n                                                            symbol: 'CAP',\n                                                            floor: '2.5 ETH',\n                                                            d24h: '+15.2%',\n                                                            d7d: '+45.8%',\n                                                            volume: '125 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Digital Punks',\n                                                            symbol: 'DP',\n                                                            floor: '1.8 ETH',\n                                                            d24h: '+12.7%',\n                                                            d7d: '+38.4%',\n                                                            volume: '98 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Meta Worlds',\n                                                            symbol: 'MW',\n                                                            floor: '3.2 ETH',\n                                                            d24h: '+9.8%',\n                                                            d7d: '+28.9%',\n                                                            volume: '156 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Pixel Heroes',\n                                                            symbol: 'PH',\n                                                            floor: '0.9 ETH',\n                                                            d24h: '+8.5%',\n                                                            d7d: '+22.1%',\n                                                            volume: '67 ETH',\n                                                            trend: 'up'\n                                                        },\n                                                        {\n                                                            name: 'Cyber Cats',\n                                                            symbol: 'CC',\n                                                            floor: '1.4 ETH',\n                                                            d24h: '+7.2%',\n                                                            d7d: '+19.6%',\n                                                            volume: '89 ETH',\n                                                            trend: 'up'\n                                                        }\n                                                    ].map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-white\",\n                                                                                children: collection.symbol.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 440,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: collection.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900 font-medium\",\n                                                                    children: collection.floor\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: collection.d24h\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-green-600 font-medium\",\n                                                                    children: collection.d7d\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-700 font-medium\",\n                                                                    children: collection.volume\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Top Loser Collections\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Market Cap (24h)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: \"Δ Volume (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 text-xs text-gray-500 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-4\",\n                                                                children: \"Collection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Floor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ24h\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Δ7D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: \"Volume\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    [\n                                                        {\n                                                            name: 'Old School NFT',\n                                                            symbol: 'OSN',\n                                                            floor: '0.3 ETH',\n                                                            d24h: '-18.5%',\n                                                            d7d: '-42.3%',\n                                                            volume: '12 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Retro Pixels',\n                                                            symbol: 'RP',\n                                                            floor: '0.5 ETH',\n                                                            d24h: '-15.2%',\n                                                            d7d: '-35.7%',\n                                                            volume: '23 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Classic Art',\n                                                            symbol: 'CA',\n                                                            floor: '0.8 ETH',\n                                                            d24h: '-12.8%',\n                                                            d7d: '-28.4%',\n                                                            volume: '34 ETH',\n                                                            trend: 'down'\n                                                        },\n                                                        {\n                                                            name: 'Vintage Cards',\n                                                            symbol: 'VC',\n                                                            floor: '0.2 ETH',\n                                                            d24h: '-10.9%',\n                                                            d7d: '-25.1%',\n                                                            volume: '18 ETH',\n                                                            trend: 'down'\n                                                        }\n                                                    ].map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-4 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 bg-gradient-to-r from-red-400 to-orange-500 rounded flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-white\",\n                                                                                children: collection.symbol.slice(0, 2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: collection.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-900 font-medium\",\n                                                                    children: collection.floor\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: collection.d24h\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-red-600 font-medium\",\n                                                                    children: collection.d7d\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"col-span-2 text-gray-700 font-medium\",\n                                                                    children: collection.volume\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Top20 Collections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 font-medium\",\n                                                        children: \"24h\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"7D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"30D\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"3M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-50 rounded-lg p-4 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '5%',\n                                                    width: '22%',\n                                                    height: '35%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"\\uD83C\\uDFA8 CryptoArt Pro\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"\\uD83D\\uDC51\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"2.5 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"+15.2%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-blue-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-blue-300 rounded\",\n                                                                        style: {\n                                                                            width: '75%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '26%',\n                                                    top: '5%',\n                                                    width: '20%',\n                                                    height: '32%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"\\uD83D\\uDC7E Digital Punks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"1.8 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-purple-200 text-xs\",\n                                                                    children: \"+12.7%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-8 bg-purple-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-purple-300 rounded\",\n                                                                        style: {\n                                                                            width: '65%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-green-600 to-green-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '48%',\n                                                    top: '5%',\n                                                    width: '18%',\n                                                    height: '30%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDF0D Meta Worlds\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"3.2 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-200 text-xs\",\n                                                                    children: \"+9.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-green-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-green-300 rounded\",\n                                                                        style: {\n                                                                            width: '60%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-yellow-600 to-yellow-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '68%',\n                                                    top: '5%',\n                                                    width: '16%',\n                                                    height: '28%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83E\\uDDB8 Pixel Heroes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"0.9 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-yellow-200 text-xs\",\n                                                                    children: \"+8.5%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-6 bg-yellow-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-yellow-300 rounded\",\n                                                                        style: {\n                                                                            width: '55%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '42%',\n                                                    width: '16%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC31 Cyber Cats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"1.4 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-teal-200 text-xs\",\n                                                                    children: \"+7.2%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-teal-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-teal-300 rounded\",\n                                                                        style: {\n                                                                            width: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '20%',\n                                                    top: '42%',\n                                                    width: '15%',\n                                                    height: '23%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDE80 Space NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"2.1 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-indigo-200 text-xs\",\n                                                                    children: \"+6.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-4 bg-indigo-400 bg-opacity-30 rounded mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full bg-indigo-300 rounded\",\n                                                                        style: {\n                                                                            width: '45%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-pink-600 to-pink-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '37%',\n                                                    top: '42%',\n                                                    width: '13%',\n                                                    height: '20%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83D\\uDC8E Gems\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-xs font-bold\",\n                                                                    children: \"1.7 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-pink-200 text-xs\",\n                                                                    children: \"+5.4%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg border border-white\",\n                                                style: {\n                                                    left: '52%',\n                                                    top: '42%',\n                                                    width: '12%',\n                                                    height: '18%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"\\uD83C\\uDFAD Art Club\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-xs font-bold\",\n                                                                    children: \"0.8 ETH\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-orange-200 text-xs\",\n                                                                    children: \"+4.9%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Top 100 Platform Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"7d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-purple-600 text-white rounded\",\n                                            children: \"30d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"90d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"1y\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-4 mb-4 text-xs text-gray-500 font-medium border-b border-gray-200 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1\",\n                                children: \"Rank\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-3\",\n                                children: \"User\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"NFTs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: \"Growth\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: [\n                            {\n                                rank: 1,\n                                name: '@alice_crypto',\n                                avatar: 'AC',\n                                score: 2547,\n                                nfts: 89,\n                                volume: 45.2,\n                                growth: 12.5,\n                                scoreBar: 95,\n                                nftBar: 78,\n                                volumeBar: 85,\n                                growthBar: 92\n                            },\n                            {\n                                rank: 2,\n                                name: '@bob_nft_master',\n                                avatar: 'BN',\n                                score: 2341,\n                                nfts: 76,\n                                volume: 38.7,\n                                growth: 8.2,\n                                scoreBar: 88,\n                                nftBar: 65,\n                                volumeBar: 72,\n                                growthBar: 78\n                            },\n                            {\n                                rank: 3,\n                                name: '@charlie_artist',\n                                avatar: 'CA',\n                                score: 2156,\n                                nfts: 92,\n                                volume: 52.1,\n                                growth: 5.7,\n                                scoreBar: 82,\n                                nftBar: 85,\n                                volumeBar: 95,\n                                growthBar: 65\n                            },\n                            {\n                                rank: 4,\n                                name: '@diana_collector',\n                                avatar: 'DC',\n                                score: 1987,\n                                nfts: 45,\n                                volume: 28.9,\n                                growth: -2.1,\n                                scoreBar: 75,\n                                nftBar: 42,\n                                volumeBar: 55,\n                                growthBar: 25\n                            },\n                            {\n                                rank: 5,\n                                name: '@eve_crypto_queen',\n                                avatar: 'EC',\n                                score: 1834,\n                                nfts: 67,\n                                volume: 34.5,\n                                growth: 3.4,\n                                scoreBar: 70,\n                                nftBar: 58,\n                                volumeBar: 65,\n                                growthBar: 58\n                            },\n                            {\n                                rank: 6,\n                                name: '@frank_nft_lord',\n                                avatar: 'FN',\n                                score: 1723,\n                                nfts: 54,\n                                volume: 29.8,\n                                growth: 1.2,\n                                scoreBar: 65,\n                                nftBar: 48,\n                                volumeBar: 58,\n                                growthBar: 52\n                            },\n                            {\n                                rank: 7,\n                                name: '@grace_digital',\n                                avatar: 'GD',\n                                score: 1645,\n                                nfts: 71,\n                                volume: 31.2,\n                                growth: 4.8,\n                                scoreBar: 62,\n                                nftBar: 62,\n                                volumeBar: 60,\n                                growthBar: 62\n                            },\n                            {\n                                rank: 8,\n                                name: '@henry_meta',\n                                avatar: 'HM',\n                                score: 1567,\n                                nfts: 38,\n                                volume: 22.4,\n                                growth: -1.5,\n                                scoreBar: 58,\n                                nftBar: 35,\n                                volumeBar: 42,\n                                growthBar: 35\n                            }\n                        ].map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-gray-700 text-sm font-medium\",\n                                        children: user.rank\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs font-bold\",\n                                                    children: user.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-900 text-sm font-medium\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500 text-xs\",\n                                                        children: \"Verified Creator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: user.score.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.scoreBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: user.nfts\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.nftBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-900 text-sm font-bold mb-1\",\n                                                children: [\n                                                    user.volume,\n                                                    \" ETH\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(user.volumeBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold mb-1 \".concat(user.growth >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                children: [\n                                                    user.growth >= 0 ? '+' : '',\n                                                    user.growth,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 rounded-full \".concat(user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'),\n                                                    style: {\n                                                        width: \"\".concat(user.growthBar, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, user.rank, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Recent Transactions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-blue-800\",\n                                                    children: \"GEN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Digital Art #123\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"User: @alice_crypto • generation • 5m ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: \"0.5 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-green-800\",\n                                                    children: \"SHR\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Punk Avatar #456\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"User: @bob_nft • share • 12m ago\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CurrencyDollarIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-gray-900\",\n                                                    children: \"0.3 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                        lineNumber: 742,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\basic-analytics.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(BasicAnalytics, \"j5oenyuohU+ku7NNaS0gpL/DV3w=\");\n_c = BasicAnalytics;\nvar _c;\n$RefreshReg$(_c, \"BasicAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/basic-analytics.tsx\n"));

/***/ })

});