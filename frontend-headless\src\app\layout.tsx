import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import QueryProvider from "@/providers/query-provider";
import { AuthProvider } from "@/contexts/auth-context";
import { ToastProvider } from "@/contexts/toast-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Social NFT Platform - Connect, Engage, Evolve",
  description: "Join Web3 campaigns through social media engagement and earn evolving NFTs. Help blockchain projects grow while building your digital collection.",
  keywords: "NFT, Web3, Social Media, Blockchain, Campaigns, Digital Assets",
  authors: [{ name: "Social NFT Platform" }],
  openGraph: {
    title: "Social NFT Platform",
    description: "Connect, Engage, Evolve - Earn NFTs through social engagement",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        <QueryProvider>
          <AuthProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
