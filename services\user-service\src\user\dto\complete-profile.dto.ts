import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class SocialAccountDto {
  @ApiProperty({
    description: 'Social platform name',
    example: 'twitter',
    enum: ['twitter', 'farcaster', 'lens'],
  })
  @IsString()
  platform: 'twitter' | 'farcaster' | 'lens';

  @ApiProperty({
    description: 'Username on the platform',
    example: 'johndoe',
  })
  @IsString()
  @MaxLength(100)
  username: string;

  @ApiPropertyOptional({
    description: 'User ID on the platform',
    example: '*********',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  userId?: string;

  @ApiPropertyOptional({
    description: 'Access token for the platform',
  })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiPropertyOptional({
    description: 'Whether the account is verified',
    example: true,
  })
  @IsOptional()
  isVerified?: boolean;
}

export class NotificationPreferencesDto {
  @ApiPropertyOptional({
    description: 'Enable email notifications',
    example: true,
    default: true,
  })
  @IsOptional()
  emailNotifications?: boolean = true;

  @ApiPropertyOptional({
    description: 'Enable push notifications',
    example: true,
    default: true,
  })
  @IsOptional()
  pushNotifications?: boolean = true;

  @ApiPropertyOptional({
    description: 'Enable campaign notifications',
    example: true,
    default: true,
  })
  @IsOptional()
  campaignNotifications?: boolean = true;

  @ApiPropertyOptional({
    description: 'Enable marketplace notifications',
    example: true,
    default: true,
  })
  @IsOptional()
  marketplaceNotifications?: boolean = true;

  @ApiPropertyOptional({
    description: 'Enable NFT evolution notifications',
    example: true,
    default: true,
  })
  @IsOptional()
  nftEvolutionNotifications?: boolean = true;
}

export class CompleteProfileDto {
  @ApiProperty({
    description: 'User display name',
    example: 'John Doe',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100, { message: 'Display name must not exceed 100 characters' })
  displayName: string;

  @ApiPropertyOptional({
    description: 'User bio/description',
    example: 'Crypto enthusiast and NFT collector passionate about Web3',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Bio must not exceed 500 characters' })
  bio?: string;

  @ApiPropertyOptional({
    description: 'Profile image URL',
    example: 'https://example.com/profile.jpg',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Profile image must be a valid URL' })
  profileImage?: string;

  @ApiPropertyOptional({
    description: 'User location',
    example: 'San Francisco, CA',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Location must not exceed 100 characters' })
  location?: string;

  @ApiPropertyOptional({
    description: 'User website URL',
    example: 'https://johndoe.com',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Website must be a valid URL' })
  website?: string;

  @ApiPropertyOptional({
    description: 'Social media accounts',
    type: [SocialAccountDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SocialAccountDto)
  socialAccounts?: SocialAccountDto[];

  @ApiPropertyOptional({
    description: 'Notification preferences',
    type: NotificationPreferencesDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationPreferencesDto)
  notificationPreferences?: NotificationPreferencesDto;

  @ApiPropertyOptional({
    description: 'User interests/tags',
    example: ['crypto', 'nft', 'defi', 'web3'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  interests?: string[];
}
