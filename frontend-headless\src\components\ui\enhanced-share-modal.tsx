'use client'

import { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { 
  XMarkIcon, 
  ShareIcon,
  LinkIcon,
  PhotoIcon,
  DocumentDuplicateIcon,
  CheckIcon,
  QrCodeIcon
} from '@heroicons/react/24/outline'
import { useToast } from '@/contexts/toast-context'

interface NFT {
  id: string
  name: string
  rarity: string
  score: number
  twitterHandle: string
  metadata?: {
    image?: string
    description?: string
  }
  imageUrl?: string
}

interface EnhancedShareModalProps {
  nft: NFT | null
  isOpen: boolean
  onClose: () => void
}

export default function EnhancedShareModal({ nft, isOpen, onClose }: EnhancedShareModalProps) {
  const { showSuccess, showError } = useToast()
  const [copied, setCopied] = useState<string | null>(null)
  const [shareFormat, setShareFormat] = useState<'link' | 'image' | 'text'>('link')

  if (!nft) return null

  const shareUrl = `${window.location.origin}/nft/${nft.id}`
  const shareText = `Check out my ${nft.rarity} NFT "${nft.name}" generated from @${nft.twitterHandle} with a score of ${nft.score}! 🎨✨`

  const handleCopy = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(type)
      showSuccess('Copied!', `${type} copied to clipboard`)
      setTimeout(() => setCopied(null), 2000)
    } catch (error) {
      showError('Copy Failed', 'Failed to copy to clipboard')
    }
  }

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: nft.name,
          text: shareText,
          url: shareUrl
        })
      } catch (error) {
        console.log('Share cancelled or failed:', error)
      }
    } else {
      handleCopy(`${shareText} ${shareUrl}`, 'Share text')
    }
  }

  const handleSocialShare = (platform: string) => {
    const encodedText = encodeURIComponent(shareText)
    const encodedUrl = encodeURIComponent(shareUrl)
    
    const urls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodeURIComponent(nft.name)}`,
      telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`
    }

    window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400')
  }

  const downloadAsImage = () => {
    const imageUrl = nft.metadata?.image || nft.imageUrl
    if (imageUrl) {
      const link = document.createElement('a')
      link.href = imageUrl
      link.download = `${nft.name.replace(/\s+/g, '_')}_NFT.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      showSuccess('Downloaded!', 'NFT image downloaded successfully')
    } else {
      showError('Download Failed', 'No image available for download')
    }
  }

  const generateQRCode = () => {
    // Simple QR code generation using a service
    const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(shareUrl)}`
    window.open(qrUrl, '_blank')
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <ShareIcon className="h-6 w-6 text-blue-600" />
                    <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                      Share NFT
                    </Dialog.Title>
                  </div>
                  <button
                    onClick={onClose}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="p-6 space-y-6">
                  {/* NFT Preview */}
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-3 rounded-lg overflow-hidden">
                      {nft.metadata?.image || nft.imageUrl ? (
                        <img 
                          src={nft.metadata?.image || nft.imageUrl} 
                          alt={nft.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <PhotoIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <h4 className="font-medium text-gray-900">{nft.name}</h4>
                    <p className="text-sm text-gray-600">@{nft.twitterHandle} • {nft.rarity}</p>
                  </div>

                  {/* Share Format Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Share Format</label>
                    <div className="grid grid-cols-3 gap-2">
                      <button
                        onClick={() => setShareFormat('link')}
                        className={`p-3 text-center border rounded-lg transition-colors ${
                          shareFormat === 'link' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <LinkIcon className="h-5 w-5 mx-auto mb-1" />
                        <span className="text-xs">Link</span>
                      </button>
                      <button
                        onClick={() => setShareFormat('image')}
                        className={`p-3 text-center border rounded-lg transition-colors ${
                          shareFormat === 'image' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <PhotoIcon className="h-5 w-5 mx-auto mb-1" />
                        <span className="text-xs">Image</span>
                      </button>
                      <button
                        onClick={() => setShareFormat('text')}
                        className={`p-3 text-center border rounded-lg transition-colors ${
                          shareFormat === 'text' 
                            ? 'border-blue-500 bg-blue-50 text-blue-700' 
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <DocumentDuplicateIcon className="h-5 w-5 mx-auto mb-1" />
                        <span className="text-xs">Text</span>
                      </button>
                    </div>
                  </div>

                  {/* Share Content */}
                  <div>
                    {shareFormat === 'link' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Share Link</label>
                        <div className="flex">
                          <input
                            type="text"
                            value={shareUrl}
                            readOnly
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-sm bg-gray-50"
                          />
                          <button
                            onClick={() => handleCopy(shareUrl, 'Link')}
                            className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-white hover:bg-gray-50 transition-colors"
                          >
                            {copied === 'Link' ? (
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            ) : (
                              <DocumentDuplicateIcon className="h-4 w-4 text-gray-600" />
                            )}
                          </button>
                        </div>
                      </div>
                    )}

                    {shareFormat === 'text' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Share Text</label>
                        <div className="relative">
                          <textarea
                            value={`${shareText} ${shareUrl}`}
                            readOnly
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 resize-none"
                          />
                          <button
                            onClick={() => handleCopy(`${shareText} ${shareUrl}`, 'Text')}
                            className="absolute top-2 right-2 p-1 hover:bg-gray-200 rounded transition-colors"
                          >
                            {copied === 'Text' ? (
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            ) : (
                              <DocumentDuplicateIcon className="h-4 w-4 text-gray-600" />
                            )}
                          </button>
                        </div>
                      </div>
                    )}

                    {shareFormat === 'image' && (
                      <div className="text-center">
                        <button
                          onClick={downloadAsImage}
                          className="w-full px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <PhotoIcon className="h-5 w-5 inline mr-2" />
                          Download NFT Image
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Social Media Sharing */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Share on Social Media</label>
                    <div className="grid grid-cols-5 gap-2">
                      {[
                        { name: 'Twitter', color: 'bg-blue-400', platform: 'twitter' },
                        { name: 'Facebook', color: 'bg-blue-600', platform: 'facebook' },
                        { name: 'LinkedIn', color: 'bg-blue-700', platform: 'linkedin' },
                        { name: 'Reddit', color: 'bg-orange-500', platform: 'reddit' },
                        { name: 'Telegram', color: 'bg-blue-500', platform: 'telegram' }
                      ].map((social) => (
                        <button
                          key={social.platform}
                          onClick={() => handleSocialShare(social.platform)}
                          className={`p-3 ${social.color} text-white rounded-lg hover:opacity-90 transition-opacity`}
                          title={`Share on ${social.name}`}
                        >
                          <span className="text-xs font-medium">{social.name[0]}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Additional Options */}
                  <div className="flex space-x-3">
                    <button
                      onClick={handleNativeShare}
                      className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <ShareIcon className="h-4 w-4 inline mr-2" />
                      Native Share
                    </button>
                    <button
                      onClick={generateQRCode}
                      className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <QrCodeIcon className="h-4 w-4 inline mr-2" />
                      QR Code
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
