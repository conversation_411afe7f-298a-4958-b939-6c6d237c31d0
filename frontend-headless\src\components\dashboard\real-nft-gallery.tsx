'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { nftApi } from '@/lib/api'
import { CubeIcon, SparklesIcon, EyeIcon } from '@heroicons/react/24/outline'
import NFTDetailModal from './nft-detail-modal'
import { LoadingSkeleton } from '@/components/ui/loading-skeleton'
import EnhancedNFTCard from './enhanced-nft-card'

interface NFT {
  id: string
  name: string
  rarity: string
  score: number
  twitterHandle: string
  createdAt: string
  metadata?: {
    name: string
    description?: string
    image?: string
    external_url?: string
    attributes?: Array<{
      trait_type: string
      value: string | number
      display_type?: string
    }>
    background_color?: string
  }
  status?: string
  blockchain?: string
  tokenId?: string
  generationParams?: Record<string, any>
}

interface NFTGalleryProps {
  limit?: number
  showHeader?: boolean
  refreshTrigger?: number // When this changes, refresh the gallery
}

export default function RealNFTGallery({ limit = 6, showHeader = true, refreshTrigger }: NFTGalleryProps) {
  const { user } = useAuth()
  const [nfts, setNfts] = useState<NFT[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    if (user?.id) {
      fetchUserNFTs()
    }
  }, [user?.id, refreshTrigger]) // Also refresh when refreshTrigger changes

  const fetchUserNFTs = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      setError(null)

      console.log('🔄 Fetching NFTs for user:', user.id)
      const response = await nftApi.getUserNFTs(user.id)

      if (response.success && response.data) {
        // Handle different possible response structures
        const nftData = Array.isArray(response.data) ? response.data : ((response.data as any).nfts || [])
        console.log('✅ NFTs fetched successfully:', nftData.length)
        setNfts(nftData.slice(0, limit))
      } else {
        console.log('⚠️ No NFTs found or API error:', response.error)
        setError(response.error || 'Failed to fetch NFTs')
      }
    } catch (error: any) {
      console.error('❌ Error fetching NFTs:', error)
      setError('Failed to load NFTs')
    } finally {
      setIsLoading(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'epic':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'rare':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'common':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleViewNFT = (nft: NFT) => {
    console.log('NFT clicked:', nft.id)
    console.log('NFT data being passed to modal:', nft)
    setSelectedNFT(nft)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedNFT(null)
  }

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg">
        {showHeader && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <CubeIcon className="h-5 w-5 mr-2" />
              Your NFTs
            </h3>
          </div>
        )}
        <div className="p-6">
          <LoadingSkeleton rows={3} showAvatar={true} />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white shadow rounded-lg">
        {showHeader && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <CubeIcon className="h-5 w-5 mr-2" />
              Your NFTs
            </h3>
          </div>
        )}
        <div className="p-6">
          <div className="text-center py-8">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading NFTs</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
            <div className="mt-6">
              <button
                onClick={fetchUserNFTs}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (nfts.length === 0) {
    return (
      <div className="bg-white shadow rounded-lg">
        {showHeader && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <CubeIcon className="h-5 w-5 mr-2" />
              Your NFTs
            </h3>
          </div>
        )}
        <div className="p-6">
          <div className="text-center py-8">
            <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No NFTs yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by analyzing Twitter profiles to generate your first NFT!
            </p>
            <div className="mt-6">
              <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <SparklesIcon className="h-4 w-4 mr-2" />
                Analyze Profile
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      {showHeader && (
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <CubeIcon className="h-5 w-5 mr-2" />
            Your NFTs ({nfts.length})
            {isLoading && (
              <svg className="animate-spin ml-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
          </h3>
        </div>
      )}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
          {nfts.map((nft) => (
            <EnhancedNFTCard
              key={nft.id}
              nft={nft}
              onClick={() => handleViewNFT(nft)}
            />
          ))}
        </div>
        {nfts.length > 0 && (
          <div className="mt-6">
            <button className="w-full text-center text-sm text-blue-600 hover:text-blue-500 font-medium">
              View all NFTs →
            </button>
          </div>
        )}
      </div>

      {/* NFT Detail Modal */}
      <NFTDetailModal
        nft={selectedNFT}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  )
}
