// Enterprise Listing Query Model (Read Side) - Template
import { IsString, IsOptional, IsInt, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ListingQueryDto {
  @ApiProperty({ description: 'Listing ID' })
  id: string;

  @ApiProperty({ description: 'Display title' })
  displayTitle: string;

  @ApiProperty({ description: 'Display price' })
  displayPrice: string;

  @ApiProperty({ description: 'NFT ID' })
  nftId: string;

  @ApiProperty({ description: 'Seller ID' })
  sellerId: string;

  @ApiProperty({ description: 'Listing status' })
  status: string;
}
