'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/contexts/toast-context'
import { analysisApi, nftApi } from '@/lib/api'
import DetailedAnalysisResults from './detailed-analysis-results'
import ProgressIndicator, { ANALYSIS_STEPS, NFT_GENERATION_STEPS } from '@/components/ui/progress-indicator'
import { 
  MagnifyingGlassIcon, 
  SparklesIcon, 
  ChartBarIcon,
  CubeIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline'

interface AnalysisResult {
  id: string
  twitterHandle: string
  score: number
  status: string
  createdAt: string
  analysisData?: {
    profile: {
      followerCount: number
      followingCount: number
      tweetCount: number
      engagementRate: number
      accountAge: number
      isVerified: boolean
      hasProfileImage: boolean
      hasBio: boolean
    }
    metrics: {
      contentQuality: number
      activityLevel: number
      influenceScore: number
      authenticity: number
      engagement: number
    }
    breakdown: {
      followerScore: number
      engagementScore: number
      contentScore: number
      activityScore: number
      profileScore: number
    }
    nftRecommendation?: {
      score: number
      rarity: string
      reasoning: string[]
    }
  }
}

interface NFTResult {
  id: string
  name: string
  rarity: string
  score: number
}

interface ProfileAnalyzerProps {
  onNFTGenerated?: () => void // Callback to refresh NFT gallery
}

export default function ProfileAnalyzer({ onNFTGenerated }: ProfileAnalyzerProps) {
  const { user } = useAuth()
  const { showSuccess, showError } = useToast()
  const [twitterHandle, setTwitterHandle] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isGeneratingNFT, setIsGeneratingNFT] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [nftResult, setNftResult] = useState<NFTResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showAnalysisProgress, setShowAnalysisProgress] = useState(false)
  const [showNFTProgress, setShowNFTProgress] = useState(false)
  const [analysisSteps, setAnalysisSteps] = useState(ANALYSIS_STEPS)
  const [nftSteps, setNFTSteps] = useState(NFT_GENERATION_STEPS)
  const [currentAnalysisStep, setCurrentAnalysisStep] = useState<string>('')
  const [currentNFTStep, setCurrentNFTStep] = useState<string>('')

  // Enhanced customization options
  const [showCustomization, setShowCustomization] = useState(false)
  const [customizationOptions, setCustomizationOptions] = useState({
    style: 'modern',
    theme: 'social',
    colorScheme: 'auto', // auto, vibrant, minimal, dark
    includeStats: true,
    includeTwitterHandle: true,
    backgroundPattern: 'gradient' // gradient, geometric, minimal
  })

  const handleAnalyze = async () => {
    if (!user?.id || !twitterHandle.trim()) return

    try {
      setIsAnalyzing(true)
      setError(null)
      setAnalysisResult(null)
      setNftResult(null)
      setShowAnalysisProgress(true)

      // Reset analysis steps
      setAnalysisSteps(ANALYSIS_STEPS.map(step => ({ ...step, status: 'pending' })))

      console.log('🔍 Analyzing profile:', twitterHandle)

      // Simulate step-by-step progress
      const simulateProgress = async () => {
        const steps = ['fetch-profile', 'analyze-metrics', 'calculate-score', 'save-results']

        for (let i = 0; i < steps.length; i++) {
          setCurrentAnalysisStep(steps[i])

          // Simulate processing time for each step
          if (i === 0) {
            await new Promise(resolve => setTimeout(resolve, 1500)) // Fetch profile
          } else if (i === 1) {
            await new Promise(resolve => setTimeout(resolve, 2500)) // Analyze metrics
          } else if (i === 2) {
            await new Promise(resolve => setTimeout(resolve, 1000)) // Calculate score
          } else {
            await new Promise(resolve => setTimeout(resolve, 500)) // Save results
          }
        }
      }

      // Start progress simulation and API call in parallel
      const [_, response] = await Promise.all([
        simulateProgress(),
        analysisApi.analyzeTwitterProfile({
          twitterHandle: twitterHandle.replace('@', ''),
          userId: user.id,
          analysisType: 'comprehensive'
        })
      ])

      if (response.success && response.data) {
        console.log('✅ Analysis successful:', response.data)
        setAnalysisResult(response.data)
        showSuccess(
          'Profile Analysis Complete!',
          `@${response.data.twitterHandle} scored ${response.data.score}/100`
        )

        // Mark all steps as completed
        setAnalysisSteps(prev => prev.map(step => ({ ...step, status: 'completed' })))
      } else {
        const errorMsg = response.error || 'Analysis failed'
        setError(errorMsg)
        showError('Analysis Failed', errorMsg)

        // Mark current step as error (temporarily disabled)
        // setAnalysisSteps(prev => prev.map(step =>
        //   step.id === currentAnalysisStep ? { ...step, status: 'error' } : step
        // ))
      }
    } catch (error: any) {
      console.error('❌ Analysis error:', error)
      setError('Failed to analyze profile')

      // Mark current step as error (temporarily disabled)
      // setAnalysisSteps(prev => prev.map(step =>
      //   step.id === currentAnalysisStep ? { ...step, status: 'error' } : step
      // ))
    } finally {
      setIsAnalyzing(false)
      setTimeout(() => setShowAnalysisProgress(false), 2000) // Hide progress after 2 seconds
    }
  }

  const handleGenerateNFT = async () => {
    if (!user?.id || !analysisResult?.id) return

    try {
      setIsGeneratingNFT(true)
      setError(null)
      // setShowNFTProgress(true)

      // Reset NFT generation steps (temporarily disabled)
      // setNFTSteps(NFT_GENERATION_STEPS.map(step => ({ ...step, status: 'pending' })))

      console.log('🎨 Generating NFT from analysis:', analysisResult.id)

      // Simulate step-by-step progress (temporarily disabled)
      // const simulateNFTProgress = async () => {
      //   const steps = ['prepare-data', 'generate-image', 'create-metadata', 'mint-nft', 'finalize']
      //
      //   for (let i = 0; i < steps.length; i++) {
      //     setCurrentNFTStep(steps[i])
      //
      //     // Simulate processing time for each step
      //     if (i === 0) {
      //       await new Promise(resolve => setTimeout(resolve, 1000)) // Prepare data
      //     } else if (i === 1) {
      //       await new Promise(resolve => setTimeout(resolve, 4000)) // Generate image (longest step)
      //     } else if (i === 2) {
      //       await new Promise(resolve => setTimeout(resolve, 1500)) // Create metadata
      //     } else if (i === 3) {
      //       await new Promise(resolve => setTimeout(resolve, 2000)) // Mint NFT
      //     } else {
      //       await new Promise(resolve => setTimeout(resolve, 800)) // Finalize
      //     }
      //   }
      // }

      // Start API call directly (progress simulation disabled)
      const response = await nftApi.generateNFTFromAnalysis({
        userId: user.id,
        analysisId: analysisResult.id,
        customization: {
          style: customizationOptions.style,
          theme: customizationOptions.theme,
          colorScheme: customizationOptions.colorScheme,
          includeStats: customizationOptions.includeStats,
          includeTwitterHandle: customizationOptions.includeTwitterHandle,
          backgroundPattern: customizationOptions.backgroundPattern
        }
      })

      if (response.success && response.data) {
        console.log('✅ NFT generated successfully:', response.data)
        setNftResult(response.data)

        // Show success toast
        showSuccess(
          '🎉 NFT Generated!',
          `${response.data.rarity} NFT created and added to your collection`
        )

        // Mark all steps as completed (temporarily disabled)
        // setNFTSteps(prev => prev.map(step => ({ ...step, status: 'completed' })))

        // Trigger NFT gallery refresh
        if (onNFTGenerated) {
          onNFTGenerated()
        }
      } else {
        const errorMsg = response.error || 'NFT generation failed'
        setError(errorMsg)
        showError('NFT Generation Failed', errorMsg)

        // Mark current step as error (temporarily disabled)
        // setNFTSteps(prev => prev.map(step =>
        //   step.id === currentNFTStep ? { ...step, status: 'error' } : step
        // ))
      }
    } catch (error: any) {
      console.error('❌ NFT generation error:', error)
      setError('Failed to generate NFT')

      // Mark current step as error (temporarily disabled)
      // setNFTSteps(prev => prev.map(step =>
      //   step.id === currentNFTStep ? { ...step, status: 'error' } : step
      // ))
    } finally {
      setIsGeneratingNFT(false)
      // setTimeout(() => setShowNFTProgress(false), 3000) // Hide progress after 3 seconds
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'epic':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'rare':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
          Analyze Twitter Profile
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          Analyze a Twitter profile and generate an NFT based on social engagement
        </p>
      </div>
      
      <div className="p-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div>
            <label htmlFor="twitter-handle" className="block text-sm font-medium text-gray-700">
              Twitter Handle
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                @
              </span>
              <input
                type="text"
                id="twitter-handle"
                value={twitterHandle}
                onChange={(e) => setTwitterHandle(e.target.value)}
                className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="username"
                disabled={isAnalyzing}
              />
            </div>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={!twitterHandle.trim() || isAnalyzing}
            className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {isAnalyzing ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Analyzing...
              </>
            ) : (
              <>
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Analyze Profile
              </>
            )}
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Analysis Progress */}
        {showAnalysisProgress && (
          <div className="mt-6">
            <ProgressIndicator
              steps={analysisSteps}
              currentStep={currentAnalysisStep}
              onComplete={() => setShowAnalysisProgress(false)}
              className="animate-fade-in"
            />
          </div>
        )}

        {/* NFT Generation Progress */}
        {showNFTProgress && (
          <div className="mt-6">
            <ProgressIndicator
              steps={nftSteps}
              currentStep={currentNFTStep}
              onComplete={() => setShowNFTProgress(false)}
              className="animate-fade-in"
            />
          </div>
        )}

        {/* Analysis Result */}
        {analysisResult && (
          <div className="mt-6 space-y-4">
            {/* Quick Summary */}
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-900">Analysis Complete</h4>
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">@{analysisResult.twitterHandle}</span>
                <span className={`text-lg font-bold ${getScoreColor(analysisResult.score)}`}>
                  {analysisResult.score}/100
                </span>
              </div>

              {/* NFT Customization Options */}
              <div className="mt-4">
                <button
                  onClick={() => setShowCustomization(!showCustomization)}
                  className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200"
                >
                  <SparklesIcon className="h-4 w-4 mr-2" />
                  {showCustomization ? 'Hide' : 'Show'} Customization Options
                </button>

                {showCustomization && (
                  <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50 space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Customize Your NFT</h4>

                    {/* Style Selection */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-2">Style</label>
                      <select
                        value={customizationOptions.style}
                        onChange={(e) => setCustomizationOptions(prev => ({ ...prev, style: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="modern">Modern</option>
                        <option value="classic">Classic</option>
                        <option value="futuristic">Futuristic</option>
                        <option value="minimalist">Minimalist</option>
                      </select>
                    </div>

                    {/* Theme Selection */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-2">Theme</label>
                      <select
                        value={customizationOptions.theme}
                        onChange={(e) => setCustomizationOptions(prev => ({ ...prev, theme: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="social">Social Media</option>
                        <option value="tech">Technology</option>
                        <option value="nature">Nature</option>
                        <option value="space">Space</option>
                        <option value="abstract">Abstract</option>
                      </select>
                    </div>

                    {/* Color Scheme */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-2">Color Scheme</label>
                      <select
                        value={customizationOptions.colorScheme}
                        onChange={(e) => setCustomizationOptions(prev => ({ ...prev, colorScheme: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="auto">Auto (Based on Score)</option>
                        <option value="vibrant">Vibrant</option>
                        <option value="minimal">Minimal</option>
                        <option value="dark">Dark Mode</option>
                        <option value="pastel">Pastel</option>
                      </select>
                    </div>

                    {/* Background Pattern */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-2">Background</label>
                      <select
                        value={customizationOptions.backgroundPattern}
                        onChange={(e) => setCustomizationOptions(prev => ({ ...prev, backgroundPattern: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="gradient">Gradient</option>
                        <option value="geometric">Geometric</option>
                        <option value="minimal">Minimal</option>
                        <option value="textured">Textured</option>
                      </select>
                    </div>

                    {/* Toggle Options */}
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={customizationOptions.includeStats}
                          onChange={(e) => setCustomizationOptions(prev => ({ ...prev, includeStats: e.target.checked }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-xs text-gray-700">Include detailed stats</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={customizationOptions.includeTwitterHandle}
                          onChange={(e) => setCustomizationOptions(prev => ({ ...prev, includeTwitterHandle: e.target.checked }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-xs text-gray-700">Show Twitter handle</span>
                      </label>
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={handleGenerateNFT}
                disabled={isGeneratingNFT}
                className="mt-4 w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isGeneratingNFT ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating NFT...
                  </>
                ) : (
                  <>
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    Generate Custom NFT
                  </>
                )}
              </button>
            </div>

            {/* Detailed Analysis Results */}
            {analysisResult.analysisData && (
              <DetailedAnalysisResults
                twitterHandle={analysisResult.twitterHandle}
                score={analysisResult.score}
                analysisData={analysisResult.analysisData}
              />
            )}
          </div>
        )}

        {/* NFT Result */}
        {nftResult && (
          <div className="mt-4 p-4 border border-green-200 rounded-lg bg-green-50 animate-pulse">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900">🎉 NFT Generated!</h4>
              <CubeIcon className="h-5 w-5 text-green-500" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Name:</span>
                <span className="text-sm font-medium">{nftResult.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Rarity:</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRarityColor(nftResult.rarity)}`}>
                  {nftResult.rarity}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Score:</span>
                <span className="text-sm font-bold text-green-600">{nftResult.score}</span>
              </div>
            </div>
            <div className="mt-3 text-xs text-green-600 font-medium">
              ✅ NFT has been added to your collection! Check "Your NFTs" section.
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
