# JWT Persistence Testing Script
Write-Host "=== JWT PERSISTENCE TESTING ===" -ForegroundColor Cyan
Write-Host "Testing complete JWT token persistence flow" -ForegroundColor Green

$userServiceUrl = "http://localhost:3011"
$timestamp = Get-Date -Format "yyyyMMddHHmmss"

# Test 1: Create a test user for persistence testing
Write-Host "`n1. Creating test user for persistence testing..." -ForegroundColor Yellow
$testUser = @{
    username = "persisttest$timestamp"
    email = "persisttest$<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $regResponse = Invoke-RestMethod -Uri "$userServiceUrl/auth/register" -Method Post -Body $testUser -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Test user created successfully!" -ForegroundColor Green
    Write-Host "Username: $($regResponse.user.username)" -ForegroundColor Green
    Write-Host "User ID: $($regResponse.user.id)" -ForegroundColor Green
    Write-Host "Token Length: $($regResponse.accessToken.Length) characters" -ForegroundColor Green
    
    $global:testToken = $regResponse.accessToken
    $global:testUserId = $regResponse.user.id
    $global:testUsername = $regResponse.user.username
} catch {
    Write-Host "❌ Test user creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Verify token works for profile access
Write-Host "`n2. Testing token authentication..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $global:testToken"
        "Content-Type" = "application/json"
    }
    $profileResponse = Invoke-RestMethod -Uri "$userServiceUrl/auth/profile" -Method Get -Headers $headers -TimeoutSec 10
    Write-Host "✅ Token authentication successful!" -ForegroundColor Green
    Write-Host "Profile Username: $($profileResponse.username)" -ForegroundColor Green
    Write-Host "Profile Email: $($profileResponse.email)" -ForegroundColor Green
} catch {
    Write-Host "❌ Token authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎯 JWT Persistence Test Data Ready!" -ForegroundColor Green
Write-Host "Test Credentials for Frontend:" -ForegroundColor Cyan
Write-Host "Email: persisttest$<EMAIL>" -ForegroundColor Yellow
Write-Host "Password: password123" -ForegroundColor Yellow
Write-Host "`nUse these credentials to test frontend JWT persistence!" -ForegroundColor Green
