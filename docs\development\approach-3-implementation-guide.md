# Approach 3: Independent Mock Services - Implementation Guide

## Overview
Comprehensive step-by-step guide for implementing Independent Mock Services architecture in the Social NFT Platform.

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [Directory Structure](#directory-structure)
- [Service Design Patterns](#service-design-patterns)
- [API Gateway Configuration](#api-gateway-configuration)
- [Environment Management](#environment-management)
- [Implementation Steps](#implementation-steps)
- [Testing Strategy](#testing-strategy)
- [Deployment Strategy](#deployment-strategy)

## Architecture Overview

### Core Concept
Independent Mock Services architecture creates **completely separate services** for mock and real implementations, with environment-based routing to determine which service to use.

### System Architecture
```
Frontend (3000)
    ↓
API Gateway (3010)
    ↓
[Environment Router]
    ↓
┌─────────────────┬─────────────────┐
│  MOCK SERVICES  │  REAL SERVICES  │
├─────────────────┼─────────────────┤
│ Mock Twitter    │ Real Twitter    │
│ Service (3020)  │ Service (3002)  │
├─────────────────┼─────────────────┤
│ Mock Blockchain │ Real Blockchain │
│ Service (3021)  │ Service (3004)  │
├─────────────────┼─────────────────┤
│ Mock NFT        │ Real NFT        │
│ Storage (3022)  │ Storage (3006)  │
└─────────────────┴─────────────────┘
```

### Key Principles
1. **Complete Separation** - Mock and real services are entirely independent
2. **Identical APIs** - Both services implement the same interface contracts
3. **Environment Switching** - Router determines which service to use
4. **Production Clean** - Mock services never deployed to production
5. **Development Friendly** - Easy switching for testing and development

### Service Categories
- **External API Mocks** - Twitter, blockchain, NFT storage
- **Internal Services** - User, Project, Analytics (remain unchanged)
- **Shared Services** - Database, messaging (used by both mock and real)

## Directory Structure

### 🚨 IMPORTANT: Correct Service Locations
- **API Gateway:** `services/api-gateway/` (NOT in root directory)
- **All Services:** Located in `services/` directory
- **Shared Code:** Each service has its own `src/shared/` directory
- **Mock Services:** Located in `services/development-services/`

### Project Organization
```
social-nft-platform-v2/
├── frontend-nextjs/                    # Frontend application
├── services/
│   ├── api-gateway/                   # API Gateway service (Port 3010)
│   │   └── src/shared/               # API Gateway shared interfaces
│   ├── user-service/                  # User management (existing, Port 3011)
│   │   └── src/shared/               # User service shared utilities
│   ├── project-service/               # Project management (existing, Port 3006)
│   │   └── src/shared/               # Project service shared utilities
│   ├── analytics-service/             # Analytics service (existing, Port 3001)
│   │   └── src/shared/               # Analytics service shared utilities
│   ├── profile-analysis-service/      # Profile analysis (existing, Port 3002)
│   │   └── src/shared/               # Profile analysis shared utilities
│   └── development-services/          # Mock implementation services
│       ├── mock-twitter-service/      # Mock Twitter API (Port 3020)
│       │   └── src/interfaces/       # Mock Twitter service interfaces
│       ├── mock-blockchain-service/   # Mock blockchain (Port 3021)
│       │   └── src/interfaces/       # Mock blockchain service interfaces
│       └── mock-nft-storage-service/  # Mock NFT storage (Port 3022)
│           └── src/interfaces/       # Mock NFT storage service interfaces
├── tools/                            # Development tools and scripts
│   └── scripts/                      # Environment switching scripts
├── libs/                             # Testing libraries and utilities
│   └── testing/                      # Testing frameworks and utilities
└── docs/                             # Documentation
```

### Service Structure Template
Each service (mock or real) follows the same structure:
```
service-name/
├── src/
│   ├── controllers/          # API endpoint handlers
│   ├── services/            # Business logic
│   ├── models/              # Data models and interfaces
│   ├── routes/              # Express routes
│   ├── middleware/          # Custom middleware
│   └── app.ts               # Express app configuration
├── tests/                   # Unit and integration tests
├── package.json             # Dependencies and scripts
├── Dockerfile              # Container configuration
├── .env.example            # Environment variables template
└── README.md               # Service-specific documentation
```

### Port Allocation Strategy
- **Frontend:** 3000
- **API Gateway:** 3010 (services/api-gateway/)
- **Existing Services:**
  - Analytics Service: 3001 (services/analytics-service/)
  - Profile Analysis Service: 3002 (services/profile-analysis-service/)
  - Project Service: 3006 (services/project-service/)
  - User Service: 3011 (services/user-service/)
- **Mock Services:** 3020-3029 (services/development-services/)
  - Mock Twitter: 3020
  - Mock Blockchain: 3021
  - Mock NFT Storage: 3022
  - Reserved for future mocks: 3023-3029

## Service Design Patterns

### API Contract Consistency
Both mock and real services must implement identical API contracts:

```typescript
// Shared interface (in @social-nft/shared)
interface TwitterService {
  authenticateUser(credentials: TwitterCredentials): Promise<AuthResult>;
  getUserProfile(userId: string): Promise<TwitterProfile>;
  getFollowers(userId: string): Promise<TwitterUser[]>;
  postTweet(userId: string, content: string): Promise<TweetResult>;
}

// Mock implementation
class MockTwitterService implements TwitterService {
  async authenticateUser(credentials: TwitterCredentials): Promise<AuthResult> {
    return {
      success: true,
      user: { id: 'mock_123', username: 'MockUser' },
      accessToken: 'mock_token_' + Date.now()
    };
  }
}

// Real implementation
class RealTwitterService implements TwitterService {
  async authenticateUser(credentials: TwitterCredentials): Promise<AuthResult> {
    const response = await this.twitterAPI.authenticate(credentials);
    return this.mapToAuthResult(response);
  }
}
```

### Mock Data Strategy
Mock services should provide:
1. **Realistic data** that matches real API response structures
2. **Consistent behavior** across multiple calls
3. **Configurable scenarios** for testing different cases
4. **Performance simulation** (optional delays)

### Error Simulation
Mock services should simulate real-world scenarios:
```typescript
class MockTwitterService {
  async getUserProfile(userId: string): Promise<TwitterProfile> {
    // Simulate rate limiting
    if (Math.random() < 0.1) {
      throw new Error('Rate limit exceeded');
    }

    // Simulate user not found
    if (userId === 'nonexistent') {
      throw new Error('User not found');
    }

    return mockUserProfile;
  }
}
```

## API Gateway Configuration

### Environment-Based Routing
The API Gateway routes requests to appropriate services based on environment configuration:

```typescript
// api-gateway/src/config/serviceRouter.ts
interface ServiceConfig {
  name: string;
  mockPort: number;
  realPort: number;
  basePath: string;
}

const SERVICE_CONFIGS: ServiceConfig[] = [
  {
    name: 'twitter',
    mockPort: 3020,
    realPort: 3002,
    basePath: '/api/twitter'
  },
  {
    name: 'blockchain',
    mockPort: 3021,
    realPort: 3004,
    basePath: '/api/blockchain'
  },
  {
    name: 'nft-storage',
    mockPort: 3022,
    realPort: 3006,
    basePath: '/api/nft-storage'
  }
];

class ServiceRouter {
  private useMockServices: boolean;

  constructor() {
    this.useMockServices = process.env.USE_MOCK_SERVICES === 'true';
  }

  getServiceUrl(serviceName: string): string {
    const config = SERVICE_CONFIGS.find(s => s.name === serviceName);
    if (!config) throw new Error(`Service ${serviceName} not configured`);

    const port = this.useMockServices ? config.mockPort : config.realPort;
    return `http://localhost:${port}`;
  }
}
```

### Route Configuration
```typescript
// api-gateway/src/routes/index.ts
import { createProxyMiddleware } from 'http-proxy-middleware';

const serviceRouter = new ServiceRouter();

// Twitter service routes
app.use('/api/twitter', createProxyMiddleware({
  target: serviceRouter.getServiceUrl('twitter'),
  changeOrigin: true,
  pathRewrite: { '^/api/twitter': '' }
}));

// Blockchain service routes
app.use('/api/blockchain', createProxyMiddleware({
  target: serviceRouter.getServiceUrl('blockchain'),
  changeOrigin: true,
  pathRewrite: { '^/api/blockchain': '' }
}));
```

## Environment Management

### Environment Configuration Files
```bash
# .env.development.mock (Development with mock services)
NODE_ENV=development
USE_MOCK_SERVICES=true
TWITTER_SERVICE_URL=http://localhost:3020
BLOCKCHAIN_SERVICE_URL=http://localhost:3021
NFT_STORAGE_SERVICE_URL=http://localhost:3022

# .env.development.real (Development with real services)
NODE_ENV=development
USE_MOCK_SERVICES=false
TWITTER_SERVICE_URL=http://localhost:3002
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
NFT_STORAGE_SERVICE_URL=http://localhost:3006

# .env.production (Production - only real services)
NODE_ENV=production
USE_MOCK_SERVICES=false
TWITTER_SERVICE_URL=http://localhost:3002
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
NFT_STORAGE_SERVICE_URL=http://localhost:3006
```

### Switching Between Environments
```bash
# Use mock services for development
cp .env.development.mock .env

# Use real services for testing
cp .env.development.real .env

# Production deployment
cp .env.production .env
```

## Implementation Steps

### Phase 1: Setup Infrastructure (Session 1)
1. **Create directory structure** for production-services and development-services
2. **Set up port allocation** strategy (3020+ for mock services)
3. **Configure API Gateway** environment-based routing
4. **Create shared interfaces** for service contracts

### Phase 2: Implement Mock Services (Session 2-3)
1. **Mock Twitter Service** - Authentication, profile data, followers
2. **Mock Blockchain Service** - NFT minting, transactions, wallet integration
3. **Mock NFT Storage Service** - Metadata storage, image hosting

### Phase 3: Integration & Testing (Session 4)
1. **Test environment switching** between mock and real services
2. **Validate API contracts** consistency between mock and real
3. **End-to-end testing** with both service types
4. **Performance testing** and optimization

### Phase 4: Documentation & Deployment (Session 5)
1. **Complete service documentation** for each mock service
2. **Deployment configuration** for production (exclude mock services)
3. **CI/CD pipeline** updates for environment management
4. **Team training** on switching between environments

## Testing Strategy

### Multi-Environment Testing
1. **Mock Service Testing** - Fast development and unit testing
2. **Real Service Testing** - Integration testing with actual APIs
3. **Switching Testing** - Verify environment switching works correctly
4. **Contract Testing** - Ensure mock and real services have identical APIs

### Test Scenarios
```typescript
// Test both mock and real implementations
describe('Twitter Service', () => {
  const testCases = [
    { env: 'mock', serviceUrl: 'http://localhost:3020' },
    { env: 'real', serviceUrl: 'http://localhost:3002' }
  ];

  testCases.forEach(({ env, serviceUrl }) => {
    describe(`${env} implementation`, () => {
      it('should authenticate user', async () => {
        const service = new TwitterServiceClient(serviceUrl);
        const result = await service.authenticateUser(mockCredentials);
        expect(result.success).toBe(true);
      });
    });
  });
});
```

### Automated Testing Pipeline
1. **Unit Tests** - Run against mock services for speed
2. **Integration Tests** - Run against both mock and real services
3. **Contract Tests** - Verify API compatibility between mock and real
4. **End-to-End Tests** - Full user journey with environment switching

## Deployment Strategy

### Development Deployment
```yaml
# docker-compose.development.yml
version: '3.8'
services:
  api-gateway:
    build: ./api-gateway
    environment:
      - USE_MOCK_SERVICES=true

  # Mock services (development only)
  mock-twitter-service:
    build: ./services/development-services/mock-twitter-service
    ports:
      - "3020:3000"

  # Real services (also available in development)
  twitter-service:
    build: ./services/production-services/twitter-service
    ports:
      - "3002:3000"
```

### Production Deployment
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  api-gateway:
    build: ./api-gateway
    environment:
      - USE_MOCK_SERVICES=false

  # Only real services in production
  twitter-service:
    build: ./services/production-services/twitter-service
    ports:
      - "3002:3000"

  # Mock services are NOT included in production
```

### Deployment Commands
```bash
# Development with mock services
docker-compose -f docker-compose.development.yml up

# Development with real services
USE_MOCK_SERVICES=false docker-compose -f docker-compose.development.yml up

# Production deployment
docker-compose -f docker-compose.production.yml up
```
