const fs = require('fs');
const path = require('path');

function validateEnterpriseImplementation() {
  console.log('🔍 Validating Enterprise Implementation...');
  
  const requiredFiles = [
    // Core Infrastructure
    'src/common/interceptors/request-context.interceptor.ts',
    'src/common/middleware/correlation-id.middleware.ts',
    'src/common/interceptors/performance-monitoring.interceptor.ts',
    
    // Error Handling
    'src/common/filters/global-exception.filter.ts',
    'src/common/exceptions/business.exceptions.ts',
    'src/common/dto/error-response.dto.ts',
    
    // Health Checks
    'src/health/database-health.service.ts',
    'src/health/cache-health.service.ts',
    'src/health/health.controller.ts',
    
    // API Controllers
    'src/user/controllers/user-query.controller.ts',
    'src/user/controllers/user-command.controller.ts',
    'src/config/swagger.config.ts',
    
    // Integration
    'src/user/user.module.ts',
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - MISSING`);
      allFilesExist = false;
    }
  });
  
  if (allFilesExist) {
    console.log('\n🎉 ALL ENTERPRISE API LAYER FILES CREATED SUCCESSFULLY!');
    console.log('✅ Template-First approach completed without termination errors');
    console.log('✅ Ready for service startup and testing');
  } else {
    console.log('\n❌ Some files are missing. Please check the implementation.');
  }
}

validateEnterpriseImplementation();
