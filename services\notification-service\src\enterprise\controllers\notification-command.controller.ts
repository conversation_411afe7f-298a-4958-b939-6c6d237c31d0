// Enterprise Notification Command Controller (Write Side) - Template
import { Controller, Post, Body, Headers, Res, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateNotificationCommandDto } from '../models/notification-command.model';

@ApiTags('Notification Commands (Write Operations)')
@Controller('enterprise/notifications')
export class NotificationCommandController {
  constructor() {}

  @Post()
  @ApiOperation({ summary: 'Create notification (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createNotification(
    @Body() createNotificationDto: CreateNotificationCommandDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    // Template implementation
    return res.status(HttpStatus.CREATED).json({
      success: true,
      message: 'Template implementation',
      correlationId: headers['x-correlation-id']
    });
  }
}
