<!DOCTYPE html>
<html>
<head>
    <title>NFT Campaign Rule Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .nft { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; }
        button { margin: 5px; padding: 10px 15px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 NFT Campaign Rule Analysis</h1>
    
    <div class="section">
        <h3>Current NFT Data Analysis</h3>
        <button onclick="analyzeNFTData()">Analyze NFT-Campaign Relationship</button>
        <div id="nftAnalysis"></div>
    </div>
    
    <div class="section">
        <h3>Business Rule Validation</h3>
        <button onclick="validateBusinessRule()">Check One NFT Per Campaign Rule</button>
        <div id="ruleValidation"></div>
    </div>
    
    <div class="section">
        <h3>Fix Data Inconsistency</h3>
        <button onclick="fixDataConsistency()">Fix NFT-Campaign Data</button>
        <div id="dataFix"></div>
    </div>

    <script>
        function analyzeNFTData() {
            const storedNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            const currentUserId = 'persisttest20250528191812'; // Test user
            const userNFTs = storedNFTs.filter(nft => String(nft.userId).includes(currentUserId));
            
            const analysis = document.getElementById('nftAnalysis');
            
            if (userNFTs.length === 0) {
                analysis.innerHTML = '<div class="error">❌ No NFTs found for current user</div>';
                return;
            }
            
            // Group NFTs by campaign
            const nftsByCampaign = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                if (!nftsByCampaign[campaignId]) {
                    nftsByCampaign[campaignId] = [];
                }
                nftsByCampaign[campaignId].push(nft);
            });
            
            let analysisHTML = `
                <div class="success">✅ Found ${userNFTs.length} NFTs for user</div>
                <div><strong>NFTs by Campaign:</strong></div>
            `;
            
            Object.keys(nftsByCampaign).forEach(campaignId => {
                const nfts = nftsByCampaign[campaignId];
                const violatesRule = nfts.length > 1;
                
                analysisHTML += `
                    <div class="nft ${violatesRule ? 'error' : 'success'}">
                        <strong>Campaign: ${campaignId}</strong><br>
                        NFT Count: ${nfts.length} ${violatesRule ? '❌ VIOLATES RULE' : '✅ FOLLOWS RULE'}<br>
                        NFTs: ${nfts.map(nft => nft.name).join(', ')}
                    </div>
                `;
            });
            
            analysis.innerHTML = analysisHTML;
        }

        function validateBusinessRule() {
            const storedNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            const currentUserId = 'persisttest20250528191812';
            const userNFTs = storedNFTs.filter(nft => String(nft.userId).includes(currentUserId));
            
            const validation = document.getElementById('ruleValidation');
            
            // Check for violations
            const campaignCounts = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                campaignCounts[campaignId] = (campaignCounts[campaignId] || 0) + 1;
            });
            
            const violations = Object.keys(campaignCounts).filter(campaignId => campaignCounts[campaignId] > 1);
            
            if (violations.length === 0) {
                validation.innerHTML = `
                    <div class="success">✅ Business Rule Compliance</div>
                    <div>All campaigns have maximum 1 NFT per user</div>
                    <div>Total Campaigns: ${Object.keys(campaignCounts).length}</div>
                    <div>Total NFTs: ${userNFTs.length}</div>
                `;
            } else {
                validation.innerHTML = `
                    <div class="error">❌ Business Rule Violations Found</div>
                    <div><strong>Campaigns with multiple NFTs:</strong></div>
                    ${violations.map(campaignId => 
                        `<div class="warning">Campaign ${campaignId}: ${campaignCounts[campaignId]} NFTs</div>`
                    ).join('')}
                    <div><strong>Expected:</strong> ${Object.keys(campaignCounts).length} NFTs (1 per campaign)</div>
                    <div><strong>Actual:</strong> ${userNFTs.length} NFTs</div>
                `;
            }
        }

        function fixDataConsistency() {
            const storedNFTs = JSON.parse(localStorage.getItem('user_nfts') || '[]');
            const currentUserId = 'persisttest20250528191812';
            const userNFTs = storedNFTs.filter(nft => String(nft.userId).includes(currentUserId));
            const otherNFTs = storedNFTs.filter(nft => !String(nft.userId).includes(currentUserId));
            
            // Keep only the most recent NFT per campaign
            const fixedNFTs = {};
            userNFTs.forEach(nft => {
                const campaignId = nft.campaignId || 'unknown';
                if (!fixedNFTs[campaignId] || new Date(nft.createdAt) > new Date(fixedNFTs[campaignId].createdAt)) {
                    fixedNFTs[campaignId] = nft;
                }
            });
            
            const fixedUserNFTs = Object.values(fixedNFTs);
            const allFixedNFTs = [...otherNFTs, ...fixedUserNFTs];
            
            localStorage.setItem('user_nfts', JSON.stringify(allFixedNFTs));
            
            const fixResult = document.getElementById('dataFix');
            fixResult.innerHTML = `
                <div class="success">✅ Data Fixed Successfully</div>
                <div><strong>Before:</strong> ${userNFTs.length} NFTs</div>
                <div><strong>After:</strong> ${fixedUserNFTs.length} NFTs</div>
                <div><strong>Removed:</strong> ${userNFTs.length - fixedUserNFTs.length} duplicate NFTs</div>
                <div><strong>Rule:</strong> 1 NFT per campaign enforced</div>
                <br>
                <button onclick="window.location.reload()">Refresh Page</button>
            `;
        }

        // Auto-analyze on page load
        window.onload = function() {
            analyzeNFTData();
            validateBusinessRule();
        };
    </script>
</body>
</html>
