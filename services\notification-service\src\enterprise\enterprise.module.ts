// Enterprise Notification Module - Template
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NotificationCommandController } from './controllers/notification-command.controller';
import { NotificationQueryController } from './controllers/notification-query.controller';
import { PrismaService } from './shared/prisma.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    })
  ],
  controllers: [
    NotificationCommandController,
    NotificationQueryController
  ],
  providers: [PrismaService],
  exports: [PrismaService]
})
export class EnterpriseModule {}
