# Environment Configuration Example
# Generated from MASTER_REFERENCE.md

# Core Configuration
NODE_ENV=development
USE_MOCK_SERVICES=true
API_GATEWAY_PORT=3010

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password_here

# Service URLs - Real Services
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
ANALYTICS_SERVICE_URL=http://localhost:3007
NOTIFICATION_SERVICE_URL=http://localhost:3008

# Service URLs - Mock Services (Development)
MOCK_TWITTER_SERVICE_URL=http://localhost:3020
MOCK_BLOCKCHAIN_SERVICE_URL=http://localhost:3021
MOCK_NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Database Names
USER_DB_NAME=user_service_db
PROFILE_ANALYSIS_DB_NAME=profile_analysis_db
NFT_GENERATION_DB_NAME=nft_generation_db
BLOCKCHAIN_DB_NAME=blockchain_service_db
PROJECT_DB_NAME=project_service_db
MARKETPLACE_DB_NAME=marketplace_service_db
ANALYTICS_DB_NAME=analytics_service_db
NOTIFICATION_DB_NAME=notification_service_db

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true
