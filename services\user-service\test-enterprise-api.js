const axios = require('axios');

const BASE_URL = 'http://localhost:3011/api';

async function testEnterpriseAPI() {
  console.log('🚀 Testing Enterprise API Layer...');
  
  try {
    // Test 1: Health Check
    console.log('\n🏥 Test 1: Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health/simple`);
    console.log('✅ Health check passed:', healthResponse.data);
    
    // Test 2: Create User
    console.log('\n👤 Test 2: Create User...');
    const createUserData = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'SecurePassword123!',
      role: 'user'
    };
    
    const createResponse = await axios.post(`${BASE_URL}/users`, createUserData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Correlation-ID': 'test-correlation-123'
      }
    });
    console.log('✅ User created:', createResponse.data);
    const userId = createResponse.data.data.id;
    
    // Test 3: Get User by ID
    console.log('\n🔍 Test 3: Get User by ID...');
    const getUserResponse = await axios.get(`${BASE_URL}/users/${userId}`, {
      headers: { 'X-Correlation-ID': 'test-correlation-456' }
    });
    console.log('✅ User retrieved:', getUserResponse.data);
    
    console.log('\n🎉 ALL ENTERPRISE API TESTS PASSED!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message);
  }
}

testEnterpriseAPI();
