// Enterprise Blockchain Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model BlockchainTransactionCommand {
  id                  String   @id @default(cuid())

  // Core Transaction Data
  transactionHash     String?  // Blockchain transaction hash
  transactionType     String   // "mint", "transfer", "burn", "approve"
  contractAddress     String   // Smart contract address
  tokenId             String?  // NFT token ID
  fromAddress         String?  // Sender address
  toAddress           String?  // Recipient address

  // Transaction Details
  gasPrice            String?  // Gas price in wei
  gasLimit            String?  // Gas limit
  gasUsed             String?  // Actual gas used
  value               String?  // Transaction value in wei

  // Status
  status              String   @default("pending") // pending, confirmed, failed
  blockNumber         String?  // Block number when confirmed
  confirmations       Int      @default(0)

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  @@map("blockchain_transaction_commands")
}

model SmartContractCommand {
  id                  String   @id @default(cuid())

  // Core Contract Data
  contractAddress     String   @unique
  contractName        String
  contractType        String   // "ERC721", "ERC1155", "Custom"
  network             String   // "ethereum", "polygon", "sepolia"
  deploymentTxHash    String?  // Deployment transaction hash

  // Contract Details
  abi                 Json?    // Contract ABI
  bytecode            String?  // Contract bytecode
  deployedAt          DateTime?

  // Status
  status              String   @default("active") // active, deprecated, paused
  isVerified          Boolean  @default(false)

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  @@map("smart_contract_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model BlockchainQuery {
  id                  String   @id

  // Optimized Display Data
  displayHash         String
  displayType         String
  displayStatus       String
  contractAddress     String
  tokenId             String?

  // Transaction Info
  fromAddress         String?
  toAddress           String?
  blockNumber         String?
  confirmations       Int

  // Performance Metrics
  gasUsed             String?
  gasCost             String?
  processingTime      Float?

  // Status & Metrics
  isSuccessful        Boolean
  errorMessage        String?

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("blockchain_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "transaction", "contract", "mint", "transfer"
  entityId        String
  action          String   // "create", "update", "confirm", "deploy", "mint"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model BlockchainEvent {
  id              String   @id @default(cuid())
  eventType       String   // "transaction_created", "nft_minted", "contract_deployed"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Transaction/Contract ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("blockchain_events")
}
