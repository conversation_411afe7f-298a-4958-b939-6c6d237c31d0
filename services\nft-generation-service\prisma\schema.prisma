// Enterprise NFT Generation Service - Prisma Schema
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model NftCommand {
  id                  String   @id @default(cuid())
  
  // Core NFT Data
  userId              String
  projectId           String?
  campaignId          String?
  templateId          String?
  tokenId             String?
  
  // NFT Content
  name                String
  description         String?
  imageUrl            String?
  metadataUrl         String?
  attributes          Json?
  metadata            Json?
  rarity              String?  // common, rare, epic, legendary, mythic
  score               Int?     // Analysis score (0-100)
  
  // Generation Data
  generationParams    Json
  generationStatus    String   @default("pending") // pending, generating, completed, failed
  generationError     String?
  
  // Blockchain Data
  contractAddress     String?
  blockchainTxHash    String?
  blockchainStatus    String   @default("not_minted") // not_minted, minting, minted, failed
  ipfsHash            String?  // IPFS hash for metadata
  
  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  updatedBy           String?
  version             Int      @default(1)
  
  // Enterprise Compliance
  dataClassification  String   @default("internal") // public, internal, confidential, restricted
  retentionPolicy     String   @default("7years")
  
  @@map("nft_commands")
  @@index([userId])
  @@index([projectId])
  @@index([campaignId])
  @@index([generationStatus])
  @@index([blockchainStatus])
  @@index([createdAt])
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model NftQuery {
  id                  String   @id
  
  // Optimized Display Data
  displayName         String
  displayDescription  String?
  thumbnailUrl        String?
  fullImageUrl        String?
  rarity              String?  // common, rare, epic, legendary, mythic
  score               Int?     // Analysis score (0-100)
  
  // User & Project Info
  userId              String
  username            String?
  projectId           String?
  projectName         String?
  campaignId          String?
  campaignName        String?
  
  // Status & Metrics
  status              String   // draft, generated, minted, listed, sold
  generationTime      Int?     // milliseconds
  mintingTime         Int?     // milliseconds
  viewCount           Int      @default(0)
  likeCount           Int      @default(0)
  shareCount          Int      @default(0)
  
  // Marketplace Data
  isListed            Boolean  @default(false)
  currentPrice        String?  // Decimal as string
  lastSalePrice       String?
  totalSales          Int      @default(0)
  
  // Performance Metrics
  avgResponseTime     Float?
  lastAccessedAt      DateTime?
  popularityScore     Float    @default(0.0)
  
  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt
  
  @@map("nft_queries")
  @@index([userId])
  @@index([status])
  @@index([rarity])
  @@index([isListed])
  @@index([popularityScore])
  @@index([createdAt])
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "nft", "generation", "minting"
  entityId        String
  action          String   // "create", "update", "delete", "generate", "mint"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())
  
  @@map("audit_logs")
  @@index([entityType, entityId])
  @@index([userId])
  @@index([correlationId])
  @@index([createdAt])
}

model NftEvent {
  id              String   @id @default(cuid())
  eventType       String   // "nft_created", "generation_started", "generation_completed", "minting_started", "minting_completed"
  eventVersion    String   @default("1.0")
  aggregateId     String   // NFT ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())
  
  @@map("nft_events")
  @@index([aggregateId])
  @@index([eventType])
  @@index([correlationId])
  @@index([createdAt])
}
