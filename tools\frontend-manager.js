#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');

class FrontendManager {
  constructor() {
    this.frontendPath = path.join(__dirname, '..', 'frontend-nextjs');
    this.expectedPort = 3000;
  }

  async checkPort(port) {
    return new Promise((resolve) => {
      exec(`netstat -an | grep ":${port}"`, (error, stdout) => {
        resolve(stdout.includes(`${port}`));
      });
    });
  }

  async killNodeProcesses() {
    return new Promise((resolve) => {
      exec('taskkill /F /IM node.exe', (error) => {
        // Ignore errors - process might not exist
        resolve();
      });
    });
  }

  async startFrontend() {
    console.log('🚀 Starting Frontend Manager...');
    
    // Check if port 3000 is in use
    const port3000InUse = await this.checkPort(3000);
    const port3001InUse = await this.checkPort(3001);
    
    if (port3000InUse) {
      console.log('⚠️  Port 3000 is in use. Killing existing processes...');
      await this.killNodeProcesses();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    }
    
    if (port3001InUse) {
      console.log('⚠️  Port 3001 is in use. Killing existing processes...');
      await this.killNodeProcesses();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    }
    
    console.log('✅ Starting frontend on port 3000...');
    exec(`cd ${this.frontendPath} && npm run dev`, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Frontend start error:', error);
        return;
      }
      console.log('📊 Frontend output:', stdout);
    });
    
    // Wait and verify
    setTimeout(async () => {
      const isRunning = await this.checkPort(3000);
      if (isRunning) {
        console.log('✅ Frontend successfully started on http://localhost:3000');
      } else {
        console.log('❌ Frontend failed to start on port 3000');
      }
    }, 5000);
  }

  async status() {
    console.log('📊 Frontend Status Check...');
    
    const port3000 = await this.checkPort(3000);
    const port3001 = await this.checkPort(3001);
    
    console.log(`Port 3000: ${port3000 ? '🟢 IN USE' : '🔴 FREE'}`);
    console.log(`Port 3001: ${port3001 ? '🟢 IN USE' : '🔴 FREE'}`);
    
    if (port3000) {
      console.log('✅ Frontend appears to be running on http://localhost:3000');
    } else if (port3001) {
      console.log('⚠️  Frontend running on wrong port 3001 - use "restart" command');
    } else {
      console.log('❌ Frontend not running - use "start" command');
    }
  }

  async restart() {
    console.log('🔄 Restarting Frontend...');
    await this.killNodeProcesses();
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
    await this.startFrontend();
  }
}

// CLI Interface
const manager = new FrontendManager();
const command = process.argv[2];

switch (command) {
  case 'start':
    manager.startFrontend();
    break;
  case 'status':
    manager.status();
    break;
  case 'restart':
    manager.restart();
    break;
  default:
    console.log('Frontend Manager Commands:');
    console.log('  start   - Start frontend on port 3000');
    console.log('  status  - Check frontend status');
    console.log('  restart - Kill and restart frontend');
    console.log('');
    console.log('Usage: node tools/frontend-manager.js <command>');
}
