import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service health status' })
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('blockchain-status')
  @ApiOperation({ summary: 'Check blockchain connectivity status' })
  @ApiResponse({ status: 200, description: 'Blockchain connectivity status' })
  getBlockchainStatus() {
    return this.appService.getBlockchainStatus();
  }
}
