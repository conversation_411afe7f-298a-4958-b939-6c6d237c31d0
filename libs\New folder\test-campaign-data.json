{"name": "Test Web3 Project Campaign v2", "description": "A test campaign for our Social NFT Platform to verify campaign creation and participation workflow", "logoUrl": "https://example.com/logo.png", "bannerUrl": "https://example.com/banner.png", "websiteUrl": "https://testproject.com", "twitterHandle": "@testproject", "startDate": "2025-05-26T13:15:00.000Z", "endDate": "2025-06-27T12:00:00.000Z", "maxParticipants": 1000, "analysisParameters": {"hasAvatar": {"enabled": true, "weight": 10}, "hasBanner": {"enabled": true, "weight": 5}, "hasBio": {"enabled": true, "weight": 15}, "followerCount": {"enabled": true, "weight": 25, "ranges": [{"min": 0, "max": 100, "score": 5}, {"min": 101, "max": 500, "score": 10}, {"min": 501, "max": 1000, "score": 15}, {"min": 1001, "max": 5000, "score": 20}, {"min": 5001, "max": 999999, "score": 25}]}, "followingCount": {"enabled": true, "weight": 5}, "tweetCount": {"enabled": true, "weight": 10}, "accountAge": {"enabled": true, "weight": 15}, "campaignEngagement": {"enabled": true, "weight": 20}, "contentQuality": {"enabled": true, "weight": 15}, "referrals": {"enabled": true, "weight": 10}}, "nftSettings": {"theme": "abstract", "style": "modern", "baseTemplate": "geometric-001", "rarityThresholds": {"common": 50, "rare": 75, "legendary": 90}}, "blockchainConfig": {"supportedNetworks": ["ethereum", "polygon", "bsc"], "defaultNetwork": "polygon"}, "metadata": {"category": "<PERSON><PERSON><PERSON>", "tags": ["test", "web3", "defi"], "priority": "high"}}