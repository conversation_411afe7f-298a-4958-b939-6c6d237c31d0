import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('User Service API')
    .setDescription('Enterprise User Service with CQRS, Event Sourcing, and Audit Trails')
    .setVersion('1.0.0')
    .addTag('Users - Query', 'User read operations (CQRS Query side)')
    .addTag('Users - Command', 'User write operations (CQRS Command side)')
    .addTag('Health', 'Service health checks and monitoring')
    .addTag('Audit', 'Audit trail and compliance operations')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-Correlation-ID',
        in: 'header',
        description: 'Correlation ID for request tracking',
      },
      'correlation-id',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });
}
