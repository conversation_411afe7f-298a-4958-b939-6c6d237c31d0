# Docker Smart Caching Template - User Service

## 🎯 **SMART DOCKER CACHING IMPLEMENTATION**

**Based on:** Sample app smart caching patterns  
**Goal:** 70% faster builds, bandwidth efficiency  
**Target:** <60 second rebuilds for code changes  

## 📋 **DOCKERFILE.DEV TEMPLATE**

### **Multi-Stage Build Configuration:**
```dockerfile
# Dependencies Stage - CACHED (rarely changes)
FROM node:18-alpine AS dependencies

WORKDIR /app

# Copy only package files for dependency caching
COPY services/user-service/package*.json ./

# Copy shared libraries (also rarely change)
COPY libs/common ./libs/common

# Install dependencies - THIS LAYER WILL BE CACHED
RUN npm install --legacy-peer-deps

# Development Stage - NEVER CACHED (changes frequently)
FROM node:18-alpine AS development

WORKDIR /app

# Copy cached dependencies from previous stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/libs ./libs
COPY --from=dependencies /app/package*.json ./

# Copy source code - THIS LAYER IS NEVER CACHED
COPY services/user-service/src ./src
COPY services/user-service/tsconfig.json ./
COPY services/user-service/nest-cli.json ./
COPY services/user-service/prisma ./prisma

# Build the application - REBUILDS ONLY WHEN SOURCE CHANGES
RUN npm run build

# Generate Prisma client
RUN npx prisma generate

# Set environment variables
ENV NODE_ENV=development
ENV NODE_OPTIONS="--max-old-space-size=640"

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
USER nestjs

# Expose port
EXPOSE 3011

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3011/api/health || exit 1

# Start the application
CMD ["node", "dist/main.js"]
```

## 🔧 **DOCKER COMPOSE CONFIGURATION**

### **Memory-Optimized Service Configuration:**
```yaml
user-service:
  build:
    context: .
    dockerfile: services/user-service/Dockerfile.dev
    target: development
    cache_from:
      - node:18-alpine
  environment:
    - NODE_ENV=development
    - NODE_OPTIONS=--max-old-space-size=640
    - DATABASE_URL=****************************************/user_service
  deploy:
    resources:
      limits:
        memory: 896M
      reservations:
        memory: 448M
```

---
**Status:** ✅ Template Ready  
**Usage:** Implement on Day 5 of migration plan
