# SpecStory explanation file
.specstory/.what-is-this.md

# Dependencies
node_modules/
*/node_modules/
**/node_modules/

# Build outputs
dist/
build/
*/dist/
*/build/
**/dist/
**/build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
