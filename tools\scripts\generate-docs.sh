#!/bin/bash

# Documentation Generation Script
# Generates all documentation from MASTER_REFERENCE.md (Single Source of Truth)

set -e

echo "🔄 Generating documentation from MASTER_REFERENCE.md..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if MASTER_REFERENCE.md exists
if [ ! -f "docs/MASTER_REFERENCE.md" ]; then
    echo -e "${RED}❌ Error: docs/MASTER_REFERENCE.md not found!${NC}"
    echo "Please create the master reference file first."
    exit 1
fi

echo -e "${BLUE}📚 Found MASTER_REFERENCE.md - Single Source of Truth${NC}"

# Create docs directory if it doesn't exist
mkdir -p docs/generated

# Generate API Endpoints Reference from Master
echo -e "${YELLOW}🔄 Generating API_ENDPOINTS.md from master reference...${NC}"
cat > docs/generated/API_ENDPOINTS.md << 'EOF'
# API Endpoints Reference
## Auto-generated from MASTER_REFERENCE.md

> **⚠️ AUTO-GENERATED FILE - DO NOT EDIT MANUALLY**  
> **Source:** [MASTER_REFERENCE.md](../MASTER_REFERENCE.md)  
> **Generated:** $(date)

This file is automatically generated from the master reference.
For the complete and authoritative documentation, see MASTER_REFERENCE.md.

EOF

# Extract API endpoints section from master reference
awk '/## 📡 API ENDPOINTS/,/^---$/' docs/MASTER_REFERENCE.md | head -n -1 >> docs/generated/API_ENDPOINTS.md

# Generate Service Configuration Reference
echo -e "${YELLOW}🔄 Generating SERVICE_CONFIG.md from master reference...${NC}"
cat > docs/generated/SERVICE_CONFIG.md << 'EOF'
# Service Configuration Reference
## Auto-generated from MASTER_REFERENCE.md

> **⚠️ AUTO-GENERATED FILE - DO NOT EDIT MANUALLY**  
> **Source:** [MASTER_REFERENCE.md](../MASTER_REFERENCE.md)  
> **Generated:** $(date)

EOF

# Extract service configuration section from master reference
awk '/## 🔌 SERVICE CONFIGURATION/,/## 📡 API ENDPOINTS/' docs/MASTER_REFERENCE.md | head -n -1 >> docs/generated/SERVICE_CONFIG.md

# Generate Environment Configuration
echo -e "${YELLOW}🔄 Generating ENVIRONMENT_CONFIG.md from master reference...${NC}"
cat > docs/generated/ENVIRONMENT_CONFIG.md << 'EOF'
# Environment Configuration Reference
## Auto-generated from MASTER_REFERENCE.md

> **⚠️ AUTO-GENERATED FILE - DO NOT EDIT MANUALLY**  
> **Source:** [MASTER_REFERENCE.md](../MASTER_REFERENCE.md)  
> **Generated:** $(date)

EOF

# Extract environment configuration section from master reference
awk '/## 🔄 ENVIRONMENT CONFIGURATION/,/## 📝 CHANGE LOG/' docs/MASTER_REFERENCE.md | head -n -1 >> docs/generated/ENVIRONMENT_CONFIG.md

# Generate .env.example from master reference
echo -e "${YELLOW}🔄 Generating .env.example from master reference...${NC}"
cat > .env.example << 'EOF'
# Environment Configuration Example
# Generated from MASTER_REFERENCE.md

# Core Configuration
NODE_ENV=development
USE_MOCK_SERVICES=true
API_GATEWAY_PORT=3010

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password_here

# Service URLs - Real Services
USER_SERVICE_URL=http://localhost:3011
PROFILE_ANALYSIS_SERVICE_URL=http://localhost:3002
NFT_GENERATION_SERVICE_URL=http://localhost:3003
BLOCKCHAIN_SERVICE_URL=http://localhost:3004
PROJECT_SERVICE_URL=http://localhost:3005
MARKETPLACE_SERVICE_URL=http://localhost:3006
ANALYTICS_SERVICE_URL=http://localhost:3007
NOTIFICATION_SERVICE_URL=http://localhost:3008

# Service URLs - Mock Services (Development)
MOCK_TWITTER_SERVICE_URL=http://localhost:3020
MOCK_BLOCKCHAIN_SERVICE_URL=http://localhost:3021
MOCK_NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Database Names
USER_DB_NAME=user_service_db
PROFILE_ANALYSIS_DB_NAME=profile_analysis_db
NFT_GENERATION_DB_NAME=nft_generation_db
BLOCKCHAIN_DB_NAME=blockchain_service_db
PROJECT_DB_NAME=project_service_db
MARKETPLACE_DB_NAME=marketplace_service_db
ANALYTICS_DB_NAME=analytics_service_db
NOTIFICATION_DB_NAME=notification_service_db

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true
EOF

# Create README for generated docs
cat > docs/generated/README.md << 'EOF'
# Generated Documentation

This directory contains auto-generated documentation files created from the master reference.

## Files

- `API_ENDPOINTS.md` - Complete API endpoints reference
- `SERVICE_CONFIG.md` - Service configuration and ports
- `ENVIRONMENT_CONFIG.md` - Environment variables and settings

## Important Notes

⚠️ **DO NOT EDIT THESE FILES MANUALLY**

These files are automatically generated from `docs/MASTER_REFERENCE.md`.
To make changes:

1. Edit `docs/MASTER_REFERENCE.md` (the single source of truth)
2. Run `./tools/scripts/generate-docs.sh` to regenerate all files

## Generation

To regenerate all documentation:

```bash
./tools/scripts/generate-docs.sh
```
EOF

echo -e "${GREEN}✅ Documentation generation complete!${NC}"
echo ""
echo -e "${BLUE}📁 Generated files:${NC}"
echo "  - docs/generated/API_ENDPOINTS.md"
echo "  - docs/generated/SERVICE_CONFIG.md" 
echo "  - docs/generated/ENVIRONMENT_CONFIG.md"
echo "  - .env.example"
echo "  - docs/generated/README.md"
echo ""
echo -e "${GREEN}🎉 All documentation is now synchronized with MASTER_REFERENCE.md!${NC}"
echo -e "${YELLOW}💡 Remember: Only edit MASTER_REFERENCE.md, then run this script to update everything else.${NC}"
