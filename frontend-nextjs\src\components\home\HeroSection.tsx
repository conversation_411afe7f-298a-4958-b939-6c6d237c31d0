'use client'

import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon
} from '@chakra-ui/react'
import { FaTwitter, FaRocket, FaTrophy, FaUsers } from 'react-icons/fa'
import Link from 'next/link'

export default function HeroSection() {
  return (
    <Box bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" color="white" py={20}>
      <Container maxW="container.xl">
        <VStack gap={8} textAlign="center">
          <Heading as="h1" size="2xl" fontWeight="bold">
            Social NFT Platform
          </Heading>
          <Text fontSize="xl" maxW="600px">
            Connect your Twitter, join Web3 campaigns, and earn evolving NFTs
          </Text>
          
          <HStack gap={4}>
            <Link href="/auth/login">
              <Button 
                size="lg" 
                bg="white" 
                color="blue.600" 
                leftIcon={<Icon as={FaTwitter} />}
              >
                Connect Twitter
              </Button>
            </Link>
            <Link href="/projects">
              <Button 
                size="lg" 
                variant="outline" 
                borderColor="white" 
                color="white"
              >
                Explore Projects
              </Button>
            </Link>
          </HStack>
        </VStack>
      </Container>
    </Box>
  )
}
