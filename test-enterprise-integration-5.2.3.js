// Step 5.2.3 - Audit Trail Validation Test
const { PrismaClient } = require('@prisma/client');

async function testAuditTrails() {
  console.log('📋 Step 5.2.3: Audit Trail Validation Test');
  
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: 'postgresql://postgres:1111@localhost:5432/user_service'
      }
    }
  });
  
  try {
    // Test 1: Verify Audit Logs Exist
    console.log('\n📊 Test 1: Verify Audit Logs Exist...');
    const auditCount = await prisma.auditLog.count();
    console.log('✅ Audit logs in database:', auditCount);
    console.log('   - Audit logging active:', auditCount > 0);
    
    // Test 2: Check Recent Audit Entries
    console.log('\n🕐 Test 2: Check Recent Audit Entries...');
    const recentAudits = await prisma.auditLog.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        entityType: true,
        action: true,
        createdAt: true,
        userId: true,
        sessionId: true,
      }
    });
    
    console.log('✅ Recent audit entries:');
    recentAudits.forEach((audit, index) => {
      console.log(`   ${index + 1}. ${audit.action} on ${audit.entityType} at ${audit.createdAt.toISOString()}`);
    });
    
    // Test 3: Verify Event Sourcing
    console.log('\n🎯 Test 3: Verify Event Sourcing...');
    const eventCount = await prisma.userEvent.count();
    const recentEvents = await prisma.userEvent.findMany({
      take: 2,
      orderBy: { createdAt: 'desc' },
      select: {
        eventType: true,
        eventVersion: true,
        correlationId: true,
        createdAt: true,
      }
    });
    
    console.log('✅ Event sourcing status:');
    console.log('   - Total events:', eventCount);
    console.log('   - Recent events:', recentEvents.length);
    if (recentEvents.length > 0) {
      console.log('   - Latest event type:', recentEvents[0].eventType);
      console.log('   - Correlation tracking:', !!recentEvents[0].correlationId);
    }
    
    console.log('\n🎉 Step 5.2.3 PASSED: Audit Trails Working');
    return true;
    
  } catch (error) {
    console.error('❌ Step 5.2.3 FAILED:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

testAuditTrails();
