# Development Environment with Mock Services
# Use this configuration for fast development without external API dependencies

NODE_ENV=development
USE_MOCK_SERVICES=true

# API Gateway Configuration
API_GATEWAY_PORT=3010

# Mock Service URLs (Development Services)
TWITTER_SERVICE_URL=http://localhost:3020
BLOCKCHAIN_SERVICE_URL=http://localhost:3021
NFT_STORAGE_SERVICE_URL=http://localhost:3022

# Real Service URLs (Production Services - still available in development)
USER_SERVICE_URL=http://localhost:3011
PROJECT_SERVICE_URL=http://localhost:3005
ANALYTICS_SERVICE_URL=http://localhost:3001

# Database Configuration (Real database for all environments)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=1111

# Service-specific Database Names
USER_DB_NAME=user_service_db
PROJECT_DB_NAME=project_service_db
ANALYTICS_DB_NAME=analytics_service_db

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Development Flags
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true
