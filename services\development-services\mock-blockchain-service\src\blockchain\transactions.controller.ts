import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('transactions')
@Controller('transactions')
export class TransactionsController {

  @Get(':hash')
  @ApiOperation({ summary: 'Get transaction details' })
  @ApiResponse({ status: 200, description: 'Transaction details retrieved successfully' })
  async getTransaction(@Param('hash') hash: string) {
    return {
      status: 'success',
      data: {
        hash,
        blockNumber: 18500000 + Math.floor(Math.random() * 1000),
        from: '0x1234567890abcdef',
        to: '0xMockNFTContract123',
        value: '0',
        gasUsed: 85000,
        gasPrice: '20000000000',
        status: 'confirmed',
        timestamp: new Date().toISOString()
      }
    };
  }
}
