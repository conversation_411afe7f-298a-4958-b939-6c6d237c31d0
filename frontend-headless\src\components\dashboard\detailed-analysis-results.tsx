'use client'

import { 
  ChartBarIcon, 
  UserIcon, 
  HeartIcon, 
  ChatBubbleLeftIcon,
  CheckBadgeIcon,
  CalendarIcon,
  UsersIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

interface AnalysisData {
  profile: {
    followerCount: number
    followingCount: number
    tweetCount: number
    engagementRate: number
    accountAge: number
    isVerified: boolean
    hasProfileImage: boolean
    hasBio: boolean
  }
  metrics: {
    contentQuality: number
    activityLevel: number
    influenceScore: number
    authenticity: number
    engagement: number
  }
  breakdown: {
    followerScore: number
    engagementScore: number
    contentScore: number
    activityScore: number
    profileScore: number
  }
  nftRecommendation?: {
    score: number
    rarity: string
    reasoning: string[]
  }
}

interface DetailedAnalysisResultsProps {
  twitterHandle: string
  score: number
  analysisData: AnalysisData
}

export default function DetailedAnalysisResults({ 
  twitterHandle, 
  score, 
  analysisData 
}: DetailedAnalysisResultsProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    if (score >= 40) return 'text-orange-600 bg-orange-50 border-orange-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-yellow-500'
    if (score >= 40) return 'bg-orange-500'
    return 'bg-red-500'
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const ProgressBar = ({ label, value, icon: Icon }: { 
    label: string
    value: number
    icon: any
  }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Icon className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">{label}</span>
        </div>
        <span className="text-sm font-bold text-gray-900">{Math.round(value)}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(value)}`}
          style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
        />
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <div className={`p-4 rounded-lg border ${getScoreColor(score)}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">@{twitterHandle}</h3>
            <p className="text-sm opacity-90">Overall Analysis Score</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{score}</div>
            <div className="text-sm opacity-90">out of 100</div>
          </div>
        </div>
      </div>

      {/* Profile Overview */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3 flex items-center">
          <UserIcon className="h-5 w-5 mr-2" />
          Profile Overview
        </h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center">
                <UsersIcon className="h-4 w-4 mr-1" />
                Followers
              </span>
              <span className="font-medium">{formatNumber(analysisData.profile.followerCount)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-1" />
                Tweets
              </span>
              <span className="font-medium">{formatNumber(analysisData.profile.tweetCount)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center">
                <HeartIcon className="h-4 w-4 mr-1" />
                Engagement
              </span>
              <span className="font-medium">{analysisData.profile.engagementRate.toFixed(1)}%</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Following</span>
              <span className="font-medium">{formatNumber(analysisData.profile.followingCount)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                Account Age
              </span>
              <span className="font-medium">{analysisData.profile.accountAge} days</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 flex items-center">
                <CheckBadgeIcon className="h-4 w-4 mr-1" />
                Verified
              </span>
              <span className={`font-medium ${analysisData.profile.isVerified ? 'text-blue-600' : 'text-gray-500'}`}>
                {analysisData.profile.isVerified ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="bg-white border rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-4 flex items-center">
          <ChartBarIcon className="h-5 w-5 mr-2" />
          Detailed Metrics
        </h4>
        <div className="space-y-4">
          <ProgressBar 
            label="Content Quality" 
            value={analysisData.metrics.contentQuality} 
            icon={DocumentTextIcon}
          />
          <ProgressBar 
            label="Activity Level" 
            value={analysisData.metrics.activityLevel} 
            icon={ChartBarIcon}
          />
          <ProgressBar 
            label="Influence Score" 
            value={analysisData.metrics.influenceScore} 
            icon={UsersIcon}
          />
          <ProgressBar 
            label="Authenticity" 
            value={analysisData.metrics.authenticity} 
            icon={CheckBadgeIcon}
          />
          <ProgressBar 
            label="Engagement" 
            value={analysisData.metrics.engagement} 
            icon={HeartIcon}
          />
        </div>
      </div>

      {/* Score Breakdown */}
      <div className="bg-white border rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-4">Score Breakdown</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {Object.entries(analysisData.breakdown).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-600 capitalize">
                {key.replace('Score', '').replace(/([A-Z])/g, ' $1').trim()}
              </span>
              <span className="font-medium">{Math.round(value)}</span>
            </div>
          ))}
        </div>
      </div>

      {/* NFT Recommendation */}
      {analysisData.nftRecommendation && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">NFT Recommendation</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Recommended Rarity</span>
              <span className="font-medium capitalize">{analysisData.nftRecommendation.rarity}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">NFT Score</span>
              <span className="font-medium">{analysisData.nftRecommendation.score}</span>
            </div>
            {analysisData.nftRecommendation.reasoning && (
              <div className="mt-3">
                <span className="text-sm text-gray-600 block mb-2">Reasoning:</span>
                <ul className="text-xs text-gray-700 space-y-1">
                  {analysisData.nftRecommendation.reasoning.map((reason, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      {reason}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
