import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RealtimeService } from '../services/realtime.service';
import { 
  WebSocketEvent,
  NotificationDto,
  ActivityFeedItemDto,
  ChatMessageDto,
  SendMessageDto,
  RealTimeMetricsDto,
  WebSocketResponseDto 
} from '../dto/realtime.dto';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
  userRooms?: Set<string>;
}

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/realtime',
})
export class RealtimeGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RealtimeGateway.name);
  private connectedUsers = new Map<string, AuthenticatedSocket>();
  private userRooms = new Map<string, Set<string>>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly realtimeService: RealtimeService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
    
    // Start real-time metrics broadcasting
    this.startMetricsBroadcast();
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      this.logger.log(`Client attempting to connect: ${client.id}`);

      // Extract token from handshake
      const token = this.extractTokenFromHandshake(client);
      if (!token) {
        this.logger.warn(`No token provided for client: ${client.id}`);
        client.disconnect();
        return;
      }

      // Verify JWT token
      const payload = await this.verifyToken(token);
      if (!payload) {
        this.logger.warn(`Invalid token for client: ${client.id}`);
        client.disconnect();
        return;
      }

      // Authenticate user
      client.userId = payload.sub;
      client.username = payload.username;
      client.userRooms = new Set();

      // Store connected user
      this.connectedUsers.set(client.userId, client);
      
      // Join user to their personal room
      const userRoom = `user:${client.userId}`;
      client.join(userRoom);
      client.userRooms.add(userRoom);

      // Join user to global activity room
      client.join('global:activity');
      client.userRooms.add('global:activity');

      // Update user rooms mapping
      if (!this.userRooms.has(client.userId)) {
        this.userRooms.set(client.userId, new Set());
      }
      this.userRooms.get(client.userId)!.add(userRoom);
      this.userRooms.get(client.userId)!.add('global:activity');

      // Send welcome message
      client.emit(WebSocketEvent.CONNECT, {
        event: WebSocketEvent.CONNECT,
        data: {
          message: 'Connected successfully',
          userId: client.userId,
          rooms: Array.from(client.userRooms),
        },
        timestamp: new Date().toISOString(),
      });

      // Send pending notifications
      await this.sendPendingNotifications(client.userId);

      // Update user online status
      await this.realtimeService.updateUserOnlineStatus(client.userId, true);

      this.logger.log(`Client connected successfully: ${client.id} (User: ${client.userId})`);

    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}: ${error.message}`);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    try {
      if (client.userId) {
        this.logger.log(`Client disconnecting: ${client.id} (User: ${client.userId})`);

        // Remove from connected users
        this.connectedUsers.delete(client.userId);

        // Clean up user rooms
        if (this.userRooms.has(client.userId)) {
          this.userRooms.delete(client.userId);
        }

        // Update user online status
        await this.realtimeService.updateUserOnlineStatus(client.userId, false);

        this.logger.log(`Client disconnected: ${client.id} (User: ${client.userId})`);
      } else {
        this.logger.log(`Unauthenticated client disconnected: ${client.id}`);
      }
    } catch (error) {
      this.logger.error(`Disconnect error for client ${client.id}: ${error.message}`);
    }
  }

  @SubscribeMessage(WebSocketEvent.JOIN_ROOM)
  async handleJoinRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        return { error: 'Not authenticated' };
      }

      const { room } = data;
      
      // Validate room access
      const canJoin = await this.validateRoomAccess(client.userId, room);
      if (!canJoin) {
        return { error: 'Access denied to room' };
      }

      // Join room
      client.join(room);
      client.userRooms!.add(room);

      // Update user rooms mapping
      if (!this.userRooms.has(client.userId)) {
        this.userRooms.set(client.userId, new Set());
      }
      this.userRooms.get(client.userId)!.add(room);

      this.logger.log(`User ${client.userId} joined room: ${room}`);

      return {
        event: WebSocketEvent.JOIN_ROOM,
        data: { room, joined: true },
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Join room error: ${error.message}`);
      return { error: 'Failed to join room' };
    }
  }

  @SubscribeMessage(WebSocketEvent.LEAVE_ROOM)
  async handleLeaveRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        return { error: 'Not authenticated' };
      }

      const { room } = data;

      // Leave room
      client.leave(room);
      client.userRooms!.delete(room);

      // Update user rooms mapping
      if (this.userRooms.has(client.userId)) {
        this.userRooms.get(client.userId)!.delete(room);
      }

      this.logger.log(`User ${client.userId} left room: ${room}`);

      return {
        event: WebSocketEvent.LEAVE_ROOM,
        data: { room, left: true },
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Leave room error: ${error.message}`);
      return { error: 'Failed to leave room' };
    }
  }

  @SubscribeMessage(WebSocketEvent.MESSAGE_SEND)
  @UsePipes(new ValidationPipe())
  async handleSendMessage(
    @MessageBody() sendMessageDto: SendMessageDto,
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        return { error: 'Not authenticated' };
      }

      // Create message through service
      const message = await this.realtimeService.createChatMessage(sendMessageDto, client.userId);

      // Broadcast message to room
      const response: WebSocketResponseDto = {
        event: WebSocketEvent.MESSAGE_RECEIVE,
        data: message,
        timestamp: new Date().toISOString(),
        target: sendMessageDto.roomId,
      };

      this.server.to(sendMessageDto.roomId).emit(WebSocketEvent.MESSAGE_RECEIVE, response);

      this.logger.log(`Message sent by ${client.userId} to room ${sendMessageDto.roomId}`);

      return {
        event: WebSocketEvent.MESSAGE_SEND,
        data: { success: true, messageId: message.id },
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Send message error: ${error.message}`);
      return { error: 'Failed to send message' };
    }
  }

  @SubscribeMessage(WebSocketEvent.NOTIFICATION_READ)
  async handleNotificationRead(
    @MessageBody() data: { notificationId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        return { error: 'Not authenticated' };
      }

      await this.realtimeService.markNotificationAsRead(data.notificationId, client.userId);

      return {
        event: WebSocketEvent.NOTIFICATION_READ,
        data: { notificationId: data.notificationId, read: true },
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Mark notification read error: ${error.message}`);
      return { error: 'Failed to mark notification as read' };
    }
  }

  /**
   * Send notification to specific user
   */
  async sendNotificationToUser(userId: string, notification: NotificationDto) {
    const userRoom = `user:${userId}`;
    
    const response: WebSocketResponseDto = {
      event: WebSocketEvent.NOTIFICATION,
      data: notification,
      timestamp: new Date().toISOString(),
      target: userId,
    };

    this.server.to(userRoom).emit(WebSocketEvent.NOTIFICATION, response);
    this.logger.log(`Notification sent to user: ${userId}`);
  }

  /**
   * Broadcast activity update to all users
   */
  async broadcastActivityUpdate(activity: ActivityFeedItemDto) {
    const response: WebSocketResponseDto = {
      event: WebSocketEvent.ACTIVITY_UPDATE,
      data: activity,
      timestamp: new Date().toISOString(),
    };

    this.server.to('global:activity').emit(WebSocketEvent.ACTIVITY_UPDATE, response);
    this.logger.log(`Activity update broadcasted: ${activity.type}`);
  }

  /**
   * Broadcast analytics update
   */
  async broadcastAnalyticsUpdate(metrics: RealTimeMetricsDto) {
    const response: WebSocketResponseDto = {
      event: WebSocketEvent.ANALYTICS_UPDATE,
      data: metrics,
      timestamp: new Date().toISOString(),
    };

    this.server.to('global:activity').emit(WebSocketEvent.ANALYTICS_UPDATE, response);
  }

  /**
   * Send campaign update to participants
   */
  async sendCampaignUpdate(campaignId: string, updateData: any) {
    const campaignRoom = `campaign:${campaignId}`;
    
    const response: WebSocketResponseDto = {
      event: WebSocketEvent.CAMPAIGN_UPDATE,
      data: updateData,
      timestamp: new Date().toISOString(),
      target: campaignRoom,
    };

    this.server.to(campaignRoom).emit(WebSocketEvent.CAMPAIGN_UPDATE, response);
    this.logger.log(`Campaign update sent for campaign: ${campaignId}`);
  }

  /**
   * Send marketplace update
   */
  async sendMarketplaceUpdate(updateData: any) {
    const response: WebSocketResponseDto = {
      event: WebSocketEvent.MARKETPLACE_UPDATE,
      data: updateData,
      timestamp: new Date().toISOString(),
    };

    this.server.to('global:activity').emit(WebSocketEvent.MARKETPLACE_UPDATE, response);
    this.logger.log('Marketplace update broadcasted');
  }

  /**
   * Extract token from handshake
   */
  private extractTokenFromHandshake(client: Socket): string | null {
    const token = client.handshake.auth?.token || 
                 client.handshake.headers?.authorization?.replace('Bearer ', '') ||
                 client.handshake.query?.token;
    
    return typeof token === 'string' ? token : null;
  }

  /**
   * Verify JWT token
   */
  private async verifyToken(token: string): Promise<any> {
    try {
      const secret = this.configService.get<string>('JWT_SECRET');
      return await this.jwtService.verifyAsync(token, { secret });
    } catch (error) {
      this.logger.warn(`Token verification failed: ${error.message}`);
      return null;
    }
  }

  /**
   * Validate room access for user
   */
  private async validateRoomAccess(userId: string, room: string): Promise<boolean> {
    // Allow access to user's own room
    if (room === `user:${userId}`) {
      return true;
    }

    // Allow access to global rooms
    if (room.startsWith('global:')) {
      return true;
    }

    // Validate campaign room access
    if (room.startsWith('campaign:')) {
      const campaignId = room.replace('campaign:', '');
      return await this.realtimeService.validateCampaignAccess(userId, campaignId);
    }

    // Validate chat room access
    if (room.startsWith('chat:')) {
      const chatRoomId = room.replace('chat:', '');
      return await this.realtimeService.validateChatRoomAccess(userId, chatRoomId);
    }

    // Default deny
    return false;
  }

  /**
   * Send pending notifications to user
   */
  private async sendPendingNotifications(userId: string) {
    try {
      const notifications = await this.realtimeService.getPendingNotifications(userId);
      
      for (const notification of notifications) {
        await this.sendNotificationToUser(userId, notification);
      }
    } catch (error) {
      this.logger.error(`Failed to send pending notifications to ${userId}: ${error.message}`);
    }
  }

  /**
   * Start real-time metrics broadcasting
   */
  private startMetricsBroadcast() {
    // Broadcast metrics every 30 seconds
    setInterval(async () => {
      try {
        const metrics = await this.realtimeService.getRealTimeMetrics();
        await this.broadcastAnalyticsUpdate(metrics);
      } catch (error) {
        this.logger.error(`Failed to broadcast metrics: ${error.message}`);
      }
    }, 30000);
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get user connection status
   */
  isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Get all connected user IDs
   */
  getConnectedUserIds(): string[] {
    return Array.from(this.connectedUsers.keys());
  }
}
