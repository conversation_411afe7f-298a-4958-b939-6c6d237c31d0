'use client'

import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Button,
  Input
} from '@chakra-ui/react'
import { useState, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface AvatarUploadProps {
  onAvatarChange: (avatarUrl: string) => void
  currentAvatar?: string
}

export default function AvatarUpload({ onAvatarChange, currentAvatar }: AvatarUploadProps) {
  const { user } = useAuth()
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatar || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Simple toast replacement
  const showMessage = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    alert(`${type.toUpperCase()}: ${message}`)
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      showMessage('Please select an image file', 'error')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showMessage('Please select an image smaller than 5MB', 'error')
      return
    }

    setIsUploading(true)

    try {
      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setPreviewUrl(result)
      }
      reader.readAsDataURL(file)

      // Simulate upload (in real app, upload to cloud storage)
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Generate mock avatar URL (in real app, this would be the uploaded file URL)
      const mockAvatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.username || 'default'}&backgroundColor=b6e3f4`

      onAvatarChange(mockAvatarUrl)

      showMessage('Your profile picture has been updated successfully', 'success')
    } catch (error) {
      showMessage('Failed to upload avatar. Please try again.', 'error')
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveAvatar = () => {
    setPreviewUrl(null)
    onAvatarChange('')

    showMessage('Your profile picture has been removed', 'info')
  }

  const getAvatarDisplay = () => {
    if (previewUrl) {
      return previewUrl
    }

    // Default avatar using DiceBear API
    return `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.username || 'default'}&backgroundColor=e0e7ff`
  }

  return (
    <Box>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        📸 Profile Picture
      </Text>

      <VStack gap={4} align="center">
        {/* Avatar Display */}
        <Box
          w="120px"
          h="120px"
          borderRadius="full"
          overflow="hidden"
          border="4px solid"
          borderColor="blue.200"
          bg="gray.100"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          {getAvatarDisplay() ? (
            <img
              src={getAvatarDisplay()}
              alt="Profile Avatar"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />
          ) : (
            <Text fontSize="3xl" color="gray.400">
              👤
            </Text>
          )}
        </Box>

        {/* Upload Controls */}
        <VStack gap={2}>
          <HStack gap={3}>
            <Button
              size="sm"
              colorScheme="blue"
              onClick={handleFileSelect}
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : (previewUrl ? 'Change Avatar' : 'Upload Avatar')}
            </Button>

            {previewUrl && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleRemoveAvatar}
                disabled={isUploading}
              >
                Remove
              </Button>
            )}
          </HStack>

          <Text fontSize="xs" color="gray.500" textAlign="center">
            Supported formats: JPG, PNG, GIF<br />
            Maximum size: 5MB
          </Text>
        </VStack>

        {/* Hidden File Input */}
        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          display="none"
        />
      </VStack>
    </Box>
  )
}
