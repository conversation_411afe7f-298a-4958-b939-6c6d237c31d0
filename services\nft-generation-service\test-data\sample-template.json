{"name": "Social Media Influencer Template", "description": "A dynamic template for social media influencers with engagement-based visual elements", "category": "social", "rarity": "Rare", "campaignId": "test-campaign-001", "baseImagePath": "templates/social-influencer-base.svg", "previewImagePath": "templates/social-influencer-preview.png", "templateType": "dynamic", "layerConfiguration": {"layers": [{"name": "background", "imagePath": "layers/background-rare.png", "position": {"top": 0, "left": 0}, "blendMode": "normal", "conditions": []}, {"name": "follower_badge", "imagePath": "layers/follower-badge-high.png", "position": {"top": 20, "left": 20}, "blendMode": "overlay", "conditions": [{"field": "followers", "operator": "gte", "value": 10000}]}, {"name": "verification_badge", "imagePath": "layers/verified-badge.png", "position": {"top": 20, "left": 350}, "blendMode": "normal", "conditions": [{"field": "verified", "operator": "eq", "value": true}]}]}, "attributeRanges": {"followers": {"min": 0, "max": 10000000}, "engagement": {"min": 0, "max": 1}, "tweets": {"min": 0, "max": 100000}}, "scoreThresholds": {"legendary": {"threshold": 95}, "epic": {"threshold": 85}, "rare": {"threshold": 70}, "uncommon": {"threshold": 50}, "common": {"threshold": 0}}, "dynamicAttributes": {"rules": [{"attributeName": "influence_tier", "displayName": "Influence Tier", "source": "followers", "type": "tier", "valueMapping": {"ranges": [{"min": 0, "max": 1000, "value": "Emerging"}, {"min": 1001, "max": 10000, "value": "Rising"}, {"min": 10001, "max": 100000, "value": "Established"}, {"min": 100001, "max": 1000000, "value": "Major"}, {"min": 1000001, "max": 999999999, "value": "Mega"}]}}, {"attributeName": "engagement_level", "displayName": "Engagement Level", "source": "engagement", "type": "percentage", "conditions": [{"field": "engagement", "operator": "gt", "value": 0}]}, {"attributeName": "content_creator_type", "displayName": "Creator Type", "source": "tweets", "type": "string", "valueMapping": {"ranges": [{"min": 0, "max": 100, "value": "Casual"}, {"min": 101, "max": 1000, "value": "Regular"}, {"min": 1001, "max": 10000, "value": "Active"}, {"min": 10001, "max": 999999, "value": "Prolific"}]}}]}, "generationRules": {"colorScheme": {"primary": "#4169E1", "secondary": "#1E90FF", "accent": "#87CEEB"}, "effects": ["blue_shimmer", "dynamic_elements"], "textOverlays": {"enabled": true, "positions": ["bottom_center"], "fontFamily": "<PERSON><PERSON>", "fontSize": 14}}, "version": 1, "isActive": true}