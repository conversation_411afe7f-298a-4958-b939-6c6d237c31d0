"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/profile-analyzer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/profile-analyzer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileAnalyzer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_toast_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/toast-context */ \"(app-pages-browser)/./src/contexts/toast-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _detailed_analysis_results__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./detailed-analysis-results */ \"(app-pages-browser)/./src/components/dashboard/detailed-analysis-results.tsx\");\n/* harmony import */ var _components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress-indicator */ \"(app-pages-browser)/./src/components/ui/progress-indicator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,CubeIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,CubeIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,CubeIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,CubeIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CheckCircleIcon,CubeIcon,MagnifyingGlassIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProfileAnalyzer(param) {\n    let { onNFTGenerated } = param;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showSuccess, showError } = (0,_contexts_toast_context__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [twitterHandle, setTwitterHandle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingNFT, setIsGeneratingNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nftResult, setNftResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAnalysisProgress, setShowAnalysisProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTProgress, setShowNFTProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisSteps, setAnalysisSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__.ANALYSIS_STEPS);\n    const [nftSteps, setNFTSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__.NFT_GENERATION_STEPS);\n    const [currentAnalysisStep, setCurrentAnalysisStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentNFTStep, setCurrentNFTStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Enhanced customization options\n    const [showCustomization, setShowCustomization] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customizationOptions, setCustomizationOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        style: 'modern',\n        theme: 'social',\n        colorScheme: 'auto',\n        includeStats: true,\n        includeTwitterHandle: true,\n        backgroundPattern: 'gradient' // gradient, geometric, minimal\n    });\n    const handleAnalyze = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id) || !twitterHandle.trim()) return;\n        try {\n            setIsAnalyzing(true);\n            setError(null);\n            setAnalysisResult(null);\n            setNftResult(null);\n            setShowAnalysisProgress(true);\n            // Reset analysis steps\n            setAnalysisSteps(_components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__.ANALYSIS_STEPS.map((step)=>({\n                    ...step,\n                    status: 'pending'\n                })));\n            console.log('🔍 Analyzing profile:', twitterHandle);\n            // Simulate step-by-step progress\n            const simulateProgress = async ()=>{\n                const steps = [\n                    'fetch-profile',\n                    'analyze-metrics',\n                    'calculate-score',\n                    'save-results'\n                ];\n                for(let i = 0; i < steps.length; i++){\n                    setCurrentAnalysisStep(steps[i]);\n                    // Simulate processing time for each step\n                    if (i === 0) {\n                        await new Promise((resolve)=>setTimeout(resolve, 1500)) // Fetch profile\n                        ;\n                    } else if (i === 1) {\n                        await new Promise((resolve)=>setTimeout(resolve, 2500)) // Analyze metrics\n                        ;\n                    } else if (i === 2) {\n                        await new Promise((resolve)=>setTimeout(resolve, 1000)) // Calculate score\n                        ;\n                    } else {\n                        await new Promise((resolve)=>setTimeout(resolve, 500)) // Save results\n                        ;\n                    }\n                }\n            };\n            // Start progress simulation and API call in parallel\n            const [_, response] = await Promise.all([\n                simulateProgress(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_4__.analysisApi.analyzeTwitterProfile({\n                    twitterHandle: twitterHandle.replace('@', ''),\n                    userId: user.id,\n                    analysisType: 'comprehensive'\n                })\n            ]);\n            if (response.success && response.data) {\n                console.log('✅ Analysis successful:', response.data);\n                setAnalysisResult(response.data);\n                showSuccess('Profile Analysis Complete!', \"@\".concat(response.data.twitterHandle, \" scored \").concat(response.data.score, \"/100\"));\n                // Mark all steps as completed\n                setAnalysisSteps((prev)=>prev.map((step)=>({\n                            ...step,\n                            status: 'completed'\n                        })));\n            } else {\n                const errorMsg = response.error || 'Analysis failed';\n                setError(errorMsg);\n                showError('Analysis Failed', errorMsg);\n            // Mark current step as error (temporarily disabled)\n            // setAnalysisSteps(prev => prev.map(step =>\n            //   step.id === currentAnalysisStep ? { ...step, status: 'error' } : step\n            // ))\n            }\n        } catch (error) {\n            console.error('❌ Analysis error:', error);\n            setError('Failed to analyze profile');\n        // Mark current step as error (temporarily disabled)\n        // setAnalysisSteps(prev => prev.map(step =>\n        //   step.id === currentAnalysisStep ? { ...step, status: 'error' } : step\n        // ))\n        } finally{\n            setIsAnalyzing(false);\n            setTimeout(()=>setShowAnalysisProgress(false), 2000) // Hide progress after 2 seconds\n            ;\n        }\n    };\n    const handleGenerateNFT = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id) || !(analysisResult === null || analysisResult === void 0 ? void 0 : analysisResult.id)) return;\n        try {\n            setIsGeneratingNFT(true);\n            setError(null);\n            // setShowNFTProgress(true)\n            // Reset NFT generation steps (temporarily disabled)\n            // setNFTSteps(NFT_GENERATION_STEPS.map(step => ({ ...step, status: 'pending' })))\n            console.log('🎨 Generating NFT from analysis:', analysisResult.id);\n            // Simulate step-by-step progress (temporarily disabled)\n            // const simulateNFTProgress = async () => {\n            //   const steps = ['prepare-data', 'generate-image', 'create-metadata', 'mint-nft', 'finalize']\n            //\n            //   for (let i = 0; i < steps.length; i++) {\n            //     setCurrentNFTStep(steps[i])\n            //\n            //     // Simulate processing time for each step\n            //     if (i === 0) {\n            //       await new Promise(resolve => setTimeout(resolve, 1000)) // Prepare data\n            //     } else if (i === 1) {\n            //       await new Promise(resolve => setTimeout(resolve, 4000)) // Generate image (longest step)\n            //     } else if (i === 2) {\n            //       await new Promise(resolve => setTimeout(resolve, 1500)) // Create metadata\n            //     } else if (i === 3) {\n            //       await new Promise(resolve => setTimeout(resolve, 2000)) // Mint NFT\n            //     } else {\n            //       await new Promise(resolve => setTimeout(resolve, 800)) // Finalize\n            //     }\n            //   }\n            // }\n            // Start API call directly (progress simulation disabled)\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.nftApi.generateNFTFromAnalysis({\n                userId: user.id,\n                analysisId: analysisResult.id,\n                customization: {\n                    style: 'modern',\n                    theme: 'social'\n                }\n            });\n            if (response.success && response.data) {\n                console.log('✅ NFT generated successfully:', response.data);\n                setNftResult(response.data);\n                // Show success toast\n                showSuccess('🎉 NFT Generated!', \"\".concat(response.data.rarity, \" NFT created and added to your collection\"));\n                // Mark all steps as completed (temporarily disabled)\n                // setNFTSteps(prev => prev.map(step => ({ ...step, status: 'completed' })))\n                // Trigger NFT gallery refresh\n                if (onNFTGenerated) {\n                    onNFTGenerated();\n                }\n            } else {\n                const errorMsg = response.error || 'NFT generation failed';\n                setError(errorMsg);\n                showError('NFT Generation Failed', errorMsg);\n            // Mark current step as error (temporarily disabled)\n            // setNFTSteps(prev => prev.map(step =>\n            //   step.id === currentNFTStep ? { ...step, status: 'error' } : step\n            // ))\n            }\n        } catch (error) {\n            console.error('❌ NFT generation error:', error);\n            setError('Failed to generate NFT');\n        // Mark current step as error (temporarily disabled)\n        // setNFTSteps(prev => prev.map(step =>\n        //   step.id === currentNFTStep ? { ...step, status: 'error' } : step\n        // ))\n        } finally{\n            setIsGeneratingNFT(false);\n        // setTimeout(() => setShowNFTProgress(false), 3000) // Hide progress after 3 seconds\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getRarityColor = (rarity)=>{\n        switch(rarity.toLowerCase()){\n            case 'legendary':\n                return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n            case 'epic':\n                return 'bg-purple-100 text-purple-800 border-purple-200';\n            case 'rare':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            default:\n                return 'bg-green-100 text-green-800 border-green-200';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            \"Analyze Twitter Profile\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Analyze a Twitter profile and generate an NFT based on social engagement\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"twitter-handle\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Twitter Handle\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex rounded-md shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm\",\n                                                children: \"@\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"twitter-handle\",\n                                                value: twitterHandle,\n                                                onChange: (e)=>setTwitterHandle(e.target.value),\n                                                className: \"flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                                                placeholder: \"username\",\n                                                disabled: isAnalyzing\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAnalyze,\n                                disabled: !twitterHandle.trim() || isAnalyzing,\n                                className: \"w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analyzing...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analyze Profile\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 rounded-md bg-red-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    showAnalysisProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            steps: analysisSteps,\n                            currentStep: currentAnalysisStep,\n                            onComplete: ()=>setShowAnalysisProgress(false),\n                            className: \"animate-fade-in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    showNFTProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress_indicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            steps: nftSteps,\n                            currentStep: currentNFTStep,\n                            onComplete: ()=>setShowNFTProgress(false),\n                            className: \"animate-fade-in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 rounded-lg bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Analysis Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"@\",\n                                                    analysisResult.twitterHandle\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold \".concat(getScoreColor(analysisResult.score)),\n                                                children: [\n                                                    analysisResult.score,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGenerateNFT,\n                                        disabled: isGeneratingNFT,\n                                        className: \"mt-4 w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                        children: isGeneratingNFT ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            className: \"opacity-25\",\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"10\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            className: \"opacity-75\",\n                                                            fill: \"currentColor\",\n                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generating NFT...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Generate NFT\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            analysisResult.analysisData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_detailed_analysis_results__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                twitterHandle: analysisResult.twitterHandle,\n                                score: analysisResult.score,\n                                analysisData: analysisResult.analysisData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    nftResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 border border-green-200 rounded-lg bg-green-50 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: \"\\uD83C\\uDF89 NFT Generated!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CheckCircleIcon_CubeIcon_MagnifyingGlassIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Name:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: nftResult.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Rarity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(getRarityColor(nftResult.rarity)),\n                                                children: nftResult.rarity\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Score:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-600\",\n                                                children: nftResult.score\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 text-xs text-green-600 font-medium\",\n                                children: '✅ NFT has been added to your collection! Check \"Your NFTs\" section.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\profile-analyzer.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileAnalyzer, \"iNTzTB3/EyfLN5XfTrVMXqY/1eo=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_toast_context__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = ProfileAnalyzer;\nvar _c;\n$RefreshReg$(_c, \"ProfileAnalyzer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/profile-analyzer.tsx\n"));

/***/ })

});