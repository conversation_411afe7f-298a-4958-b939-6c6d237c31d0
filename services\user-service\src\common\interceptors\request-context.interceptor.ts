import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { RequestContext } from '../../user/interfaces/request-context.interface';

@Injectable()
export class RequestContextInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    
    // Create request context
    const requestContext: RequestContext = {
      correlationId: request.headers['x-correlation-id'] || uuidv4(),
      requestId: uuidv4(),
      userId: request.user?.id,
      sessionId: request.headers['x-session-id'],
      ipAddress: request.ip || request.connection.remoteAddress,
      userAgent: request.headers['user-agent'],
      timestamp: new Date(),
    };
    
    // Attach context to request
    request.context = requestContext;
    
    // Add correlation ID to response headers
    const response = context.switchToHttp().getResponse();
    response.setHeader('X-Correlation-ID', requestContext.correlationId);
    response.setHeader('X-Request-ID', requestContext.requestId);
    
    return next.handle();
  }
}
