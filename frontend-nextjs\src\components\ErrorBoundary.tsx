'use client'

import {
  <PERSON>,
  But<PERSON>,
  Container,
  <PERSON><PERSON>,
  Text,
  VStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'

interface ErrorDisplayProps {
  title?: string
  message?: string
  showRetry?: boolean
  onRetry?: () => void
  showHome?: boolean
}

export default function ErrorDisplay({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  showRetry = true,
  onRetry,
  showHome = true
}: ErrorDisplayProps) {
  const router = useRouter()

  const handleRetry = () => {
    if (onRetry) {
      onRetry()
    } else {
      window.location.reload()
    }
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  return (
    <Container maxW="container.md" py={12}>
      <VStack gap={6} textAlign="center">
        <Alert status="error" borderRadius="lg" p={6}>
          <AlertIcon boxSize="40px" mr={4} />
          <Box>
            <AlertTitle fontSize="lg" mb={2}>
              {title}
            </AlertTitle>
            <AlertDescription fontSize="md">
              {message}
            </AlertDescription>
          </Box>
        </Alert>

        <VStack gap={3}>
          {showRetry && (
            <Button colorScheme="blue" size="lg" onClick={handleRetry}>
              Try Again
            </Button>
          )}
          
          {showHome && (
            <Button variant="outline" size="lg" onClick={handleGoHome}>
              Go to Dashboard
            </Button>
          )}
        </VStack>

        <Text fontSize="sm" color="gray.500">
          If this problem persists, please contact support.
        </Text>
      </VStack>
    </Container>
  )
}

// Network Error Component
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorDisplay
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection and try again."
      onRetry={onRetry}
    />
  )
}

// Not Found Error Component
export function NotFoundError({ message = 'The page you are looking for does not exist.' }: { message?: string }) {
  return (
    <ErrorDisplay
      title="Page Not Found"
      message={message}
      showRetry={false}
    />
  )
}

// Permission Error Component
export function PermissionError() {
  return (
    <ErrorDisplay
      title="Access Denied"
      message="You don't have permission to access this resource. Please log in or contact support."
      showRetry={false}
    />
  )
}
