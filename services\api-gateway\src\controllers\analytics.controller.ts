import { <PERSON>, Get, Post, Body, Param, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('analytics')
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly proxyService: ProxyService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check for analytics service' })
  @ApiResponse({ status: 200, description: 'Analytics service is healthy' })
  async healthCheck(@Headers() headers: any, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        '/api/health',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: {
          ...response.data,
          gateway: 'api-gateway',
          timestamp: new Date().toISOString(),
        }
      });
    } catch (error) {
      return res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          message: 'Analytics service unavailable',
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('platform-overview')
  @ApiOperation({ summary: 'Get comprehensive platform analytics overview' })
  @ApiResponse({ status: 200, description: 'Platform overview analytics retrieved successfully' })
  async getPlatformOverview(@Headers() headers: any, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        '/api/platform-overview',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve platform overview',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('user/:userId/insights')
  @ApiOperation({ summary: 'Get user-specific analytics insights' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User insights retrieved successfully' })
  async getUserInsights(
    @Param('userId') userId: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        `/api/user/${userId}/insights`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve user insights',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('nft-performance')
  @ApiOperation({ summary: 'Get NFT performance analytics' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period (7d, 30d, 90d, 1y)' })
  @ApiQuery({ name: 'rarity', required: false, description: 'Filter by rarity' })
  @ApiResponse({ status: 200, description: 'NFT performance analytics retrieved successfully' })
  async getNFTPerformance(
    @Query('period') period: string = '30d',
    @Query('rarity') rarity: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const queryParams = new URLSearchParams();
      if (period) queryParams.append('period', period);
      if (rarity) queryParams.append('rarity', rarity);

      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        `/api/nft-performance?${queryParams.toString()}`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve NFT performance analytics',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('engagement-metrics')
  @ApiOperation({ summary: 'Get user engagement metrics' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period (7d, 30d, 90d, 1y)' })
  @ApiResponse({ status: 200, description: 'Engagement metrics retrieved successfully' })
  async getEngagementMetrics(
    @Query('period') period: string = '30d',
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        `/api/engagement-metrics?period=${period}`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve engagement metrics',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('top-gainers-losers')
  @ApiOperation({ summary: 'Get top gainers and losers (Yaps Kaito style)' })
  @ApiQuery({ name: 'period', required: false, description: 'Time period (24h, 7d, 30d)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results to return' })
  @ApiResponse({ status: 200, description: 'Top gainers and losers retrieved successfully' })
  async getTopGainersLosers(
    @Query('period') period: string = '24h',
    @Query('limit') limit: string = '10',
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        `/api/top-gainers-losers?period=${period}&limit=${limit}`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve top gainers and losers',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('collection-market-value')
  @ApiOperation({ summary: 'Get collection market value data for bubble maps' })
  @ApiResponse({ status: 200, description: 'Collection market value data retrieved successfully' })
  async getCollectionMarketValue(@Headers() headers: any, @Res() res: Response) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        '/api/collection-market-value',
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve collection market value data',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Get('recent-transactions')
  @ApiOperation({ summary: 'Get recent transactions display' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of transactions to return' })
  @ApiResponse({ status: 200, description: 'Recent transactions retrieved successfully' })
  async getRecentTransactions(
    @Query('limit') limit: string = '20',
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        `/api/recent-transactions?limit=${limit}`,
        'GET',
        null,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to retrieve recent transactions',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  @Post('track-event')
  @ApiOperation({ summary: 'Track user analytics event' })
  @ApiResponse({ status: 200, description: 'Event tracked successfully' })
  async trackEvent(
    @Body() eventData: any,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        'analytics-service',
        '/api/track-event',
        'POST',
        eventData,
        headers
      );

      return res.status(response.status).json({
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          message: 'Failed to track event',
          details: error.message,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }
}
