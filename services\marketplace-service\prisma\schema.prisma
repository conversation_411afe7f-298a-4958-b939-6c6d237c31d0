// Enterprise Marketplace Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model ListingCommand {
  id                  String   @id @default(cuid())

  // Core Listing Data
  nftId               String
  sellerId            String
  price               String   // Decimal as string
  currency            String   @default("ETH")
  listingType         String   // "fixed", "auction"

  // Auction Data
  startingBid         String?
  reservePrice        String?
  auctionEndTime      DateTime?

  // Status
  status              String   @default("active") // active, sold, cancelled, expired

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  updatedBy           String?
  version             Int      @default(1)

  @@map("listing_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model ListingQuery {
  id                  String   @id

  // Optimized Display Data
  displayTitle        String
  displayPrice        String
  displayCurrency     String
  nftId               String
  nftName             String?
  nftImageUrl         String?

  // Seller Info
  sellerId            String
  sellerUsername      String?

  // Status & Metrics
  status              String
  listingType         String
  viewCount           Int      @default(0)
  favoriteCount       Int      @default(0)

  // Performance Metrics
  avgResponseTime     Float?
  popularityScore     Float    @default(0.0)

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("listing_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "listing", "transaction", "offer"
  entityId        String
  action          String   // "create", "update", "delete", "purchase"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model MarketplaceEvent {
  id              String   @id @default(cuid())
  eventType       String   // "listing_created", "item_purchased", "offer_made"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Listing/Transaction ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("marketplace_events")
}
