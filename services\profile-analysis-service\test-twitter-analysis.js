const axios = require('axios');

const ANALYSIS_SERVICE_URL = 'http://localhost:3002/api';

async function testTwitterAnalysis() {
  console.log('🔍 Testing Twitter Analysis Business Logic...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${ANALYSIS_SERVICE_URL}/analysis/health`);
    console.log('✅ Health Check:', {
      status: healthResponse.data.success,
      service: healthResponse.data.data?.service,
      uptime: Math.round(healthResponse.data.data?.uptime || 0)
    });
    console.log('');

    // Test 2: Analyze Twitter Profile
    console.log('2. Testing Twitter Profile Analysis...');
    const analysisRequest = {
      twitterHandle: 'testuser123',
      userId: 'user_test_' + Date.now()
    };

    const analysisResponse = await axios.post(
      `${ANALYSIS_SERVICE_URL}/analysis/twitter-profile`,
      analysisRequest,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-correlation-id': `test-${Date.now()}`
        }
      }
    );

    console.log('✅ Twitter Analysis Success:', {
      success: analysisResponse.data.success,
      analysisId: analysisResponse.data.data?.id,
      twitterHandle: analysisResponse.data.data?.twitterHandle,
      score: analysisResponse.data.data?.score,
      status: analysisResponse.data.data?.status,
      rarity: analysisResponse.data.data?.analysisData?.nftRecommendation?.rarity
    });

    const analysisId = analysisResponse.data.data.id;
    const userId = analysisRequest.userId;
    console.log('');

    // Test 3: Get Analysis Results
    console.log('3. Testing Get Analysis Results...');
    const resultsResponse = await axios.get(
      `${ANALYSIS_SERVICE_URL}/analysis/results/${analysisId}`,
      {
        headers: {
          'x-correlation-id': `test-results-${Date.now()}`
        }
      }
    );

    console.log('✅ Analysis Results Retrieved:', {
      success: resultsResponse.data.success,
      analysisId: resultsResponse.data.data?.id,
      score: resultsResponse.data.data?.score,
      status: resultsResponse.data.data?.status
    });

    // Display detailed analysis data
    if (resultsResponse.data.data?.analysisData) {
      const analysisData = resultsResponse.data.data.analysisData;
      console.log('📊 Detailed Analysis Results:');
      console.log('  Profile Metrics:', {
        followers: analysisData.profile?.followerCount,
        following: analysisData.profile?.followingCount,
        tweets: analysisData.profile?.tweetCount,
        engagementRate: analysisData.profile?.engagementRate,
        accountAge: analysisData.profile?.accountAge,
        verified: analysisData.profile?.isVerified
      });
      console.log('  Analysis Scores:', {
        contentQuality: analysisData.metrics?.contentQuality,
        activityLevel: analysisData.metrics?.activityLevel,
        influenceScore: analysisData.metrics?.influenceScore,
        authenticity: analysisData.metrics?.authenticity,
        engagement: analysisData.metrics?.engagement
      });
      console.log('  NFT Recommendation:', {
        score: analysisData.nftRecommendation?.score,
        rarity: analysisData.nftRecommendation?.rarity,
        reasoning: analysisData.nftRecommendation?.reasoning
      });
    }
    console.log('');

    // Test 4: Get User Analysis History
    console.log('4. Testing User Analysis History...');
    const historyResponse = await axios.get(
      `${ANALYSIS_SERVICE_URL}/analysis/history?userId=${userId}&limit=5`,
      {
        headers: {
          'x-correlation-id': `test-history-${Date.now()}`
        }
      }
    );

    console.log('✅ Analysis History Retrieved:', {
      success: historyResponse.data.success,
      totalAnalyses: historyResponse.data.data?.total,
      analysesReturned: historyResponse.data.data?.analyses?.length
    });
    console.log('');

    // Test 5: Re-analyze Profile
    console.log('5. Testing Profile Re-analysis...');
    const reanalysisResponse = await axios.put(
      `${ANALYSIS_SERVICE_URL}/analysis/${analysisId}/reanalyze`,
      {},
      {
        headers: {
          'x-correlation-id': `test-reanalysis-${Date.now()}`
        }
      }
    );

    console.log('✅ Re-analysis Success:', {
      success: reanalysisResponse.data.success,
      previousScore: reanalysisResponse.data.comparison?.previousScore,
      newScore: reanalysisResponse.data.comparison?.newScore,
      scoreDifference: reanalysisResponse.data.comparison?.scoreDifference
    });
    console.log('');

    // Test 6: Test with Different Twitter Handle
    console.log('6. Testing Analysis with Different Handle...');
    const secondAnalysisRequest = {
      twitterHandle: '@elonmusk',  // Test with @ symbol
      userId: userId
    };

    const secondAnalysisResponse = await axios.post(
      `${ANALYSIS_SERVICE_URL}/analysis/twitter-profile`,
      secondAnalysisRequest,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-correlation-id': `test-second-${Date.now()}`
        }
      }
    );

    console.log('✅ Second Analysis Success:', {
      success: secondAnalysisResponse.data.success,
      twitterHandle: secondAnalysisResponse.data.data?.twitterHandle,
      score: secondAnalysisResponse.data.data?.score,
      rarity: secondAnalysisResponse.data.data?.analysisData?.nftRecommendation?.rarity
    });
    console.log('');

    // Test 7: Updated History Check
    console.log('7. Testing Updated Analysis History...');
    const updatedHistoryResponse = await axios.get(
      `${ANALYSIS_SERVICE_URL}/analysis/history?userId=${userId}&limit=10`,
      {
        headers: {
          'x-correlation-id': `test-updated-history-${Date.now()}`
        }
      }
    );

    console.log('✅ Updated History Retrieved:', {
      success: updatedHistoryResponse.data.success,
      totalAnalyses: updatedHistoryResponse.data.data?.total,
      analysesReturned: updatedHistoryResponse.data.data?.analyses?.length
    });

    if (updatedHistoryResponse.data.data?.analyses) {
      console.log('📋 Analysis History Summary:');
      updatedHistoryResponse.data.data.analyses.forEach((analysis, index) => {
        console.log(`  ${index + 1}. ${analysis.twitterHandle} - Score: ${analysis.score} - Rarity: ${analysis.analysisData?.nftRecommendation?.rarity || 'N/A'}`);
      });
    }
    console.log('');

    // Summary
    console.log('🎉 All Twitter Analysis Tests Passed!');
    console.log('');
    console.log('📊 Test Summary:');
    console.log('- ✅ Health Check: Working');
    console.log('- ✅ Twitter Profile Analysis: Working');
    console.log('- ✅ Analysis Results Retrieval: Working');
    console.log('- ✅ User Analysis History: Working');
    console.log('- ✅ Profile Re-analysis: Working');
    console.log('- ✅ Handle Cleaning (@symbol): Working');
    console.log('- ✅ Multiple Analyses per User: Working');
    console.log('');
    console.log('🚀 Profile Analysis Service Business Logic: FULLY FUNCTIONAL!');

  } catch (error) {
    console.error('❌ Twitter Analysis Test Failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure Profile Analysis Service is running on port 3002');
    console.log('2. Check if Mock Twitter Service is running on port 3020');
    console.log('3. Verify database connections');
    console.log('4. Check service logs for detailed error information');
  }
}

testTwitterAnalysis();
