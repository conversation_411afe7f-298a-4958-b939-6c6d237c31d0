const { PrismaClient } = require('@prisma/client');

async function testEnterpriseServices() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🚀 Testing Enterprise Services...');

    // Clean up any existing test data first
    console.log('\n🧹 Cleaning up existing test data...');
    await prisma.auditLog.deleteMany({ where: { entityType: 'User' } });
    await prisma.userEvent.deleteMany({});
    await prisma.userQuery.deleteMany({});
    await prisma.userCommand.deleteMany({});
    console.log('✅ Cleanup completed');

    // Test 1: Create a test user in command model
    console.log('\n📝 Test 1: Creating test user...');
    const timestamp = Date.now();
    const testUser = await prisma.userCommand.create({
      data: {
        username: `testuser_${timestamp}`,
        email: `test_${timestamp}@example.com`,
        password: 'hashedpassword123',
        role: 'user',
        createdBy: 'system',
        updatedBy: 'system',
      }
    });
    console.log('✅ User created in command model:', testUser.id);
    
    // Test 2: Create corresponding query model
    console.log('\n📊 Test 2: Creating query model...');
    await prisma.userQuery.create({
      data: {
        id: testUser.id,
        username: testUser.username,
        email: testUser.email,
        displayName: testUser.username,
        role: testUser.role,
        isActive: testUser.isActive,
        isEmailVerified: testUser.isEmailVerified,
        createdAt: testUser.createdAt,
        lastUpdated: new Date(),
      }
    });
    console.log('✅ Query model created successfully');
    
    // Test 3: Create audit log
    console.log('\n📋 Test 3: Creating audit log...');
    await prisma.auditLog.create({
      data: {
        entityType: 'User',
        entityId: testUser.id,
        action: 'CREATE',
        newValues: testUser,
        userId: testUser.id, // Use the actual user ID instead of 'system'
        sessionId: 'test-session',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      }
    });
    console.log('✅ Audit log created successfully');
    
    // Test 4: Create event log
    console.log('\n🎯 Test 4: Creating event log...');
    await prisma.userEvent.create({
      data: {
        aggregateId: testUser.id,
        eventType: 'UserCreated',
        eventVersion: 1,
        eventData: {
          userId: testUser.id,
          username: testUser.username,
          email: testUser.email,
        },
        correlationId: 'test-correlation-123',
        createdBy: 'system',
      }
    });
    console.log('✅ Event log created successfully');
    
    // Test 5: Query all data
    console.log('\n🔍 Test 5: Querying all enterprise data...');
    
    const [commands, queries, audits, events] = await Promise.all([
      prisma.userCommand.count(),
      prisma.userQuery.count(),
      prisma.auditLog.count(),
      prisma.userEvent.count(),
    ]);
    
    console.log('📊 Enterprise Data Summary:');
    console.log(`   - User Commands: ${commands}`);
    console.log(`   - User Queries: ${queries}`);
    console.log(`   - Audit Logs: ${audits}`);
    console.log(`   - User Events: ${events}`);
    
    // Test 6: Test relationships
    console.log('\n🔗 Test 6: Testing relationships...');
    const userWithRelations = await prisma.userCommand.findUnique({
      where: { id: testUser.id },
      include: {
        auditLogs: true,
        events: true,
      }
    });
    
    console.log('✅ Relationships working:');
    console.log(`   - Audit logs: ${userWithRelations.auditLogs.length}`);
    console.log(`   - Events: ${userWithRelations.events.length}`);
    
    console.log('\n🎉 ALL ENTERPRISE SERVICES TESTS PASSED!');
    console.log('✅ CQRS models working');
    console.log('✅ Audit trails working');
    console.log('✅ Event sourcing working');
    console.log('✅ Relationships working');
    console.log('✅ Enterprise schema fully functional');
    
  } catch (error) {
    console.error('❌ Enterprise services test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

testEnterpriseServices();
