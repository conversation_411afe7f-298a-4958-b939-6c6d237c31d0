import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'Mock Blockchain Service',
      version: '1.0.0',
      description: 'Mock Blockchain API for Social NFT Platform Development',
      port: 3021,
      environment: process.env.NODE_ENV || 'development',
      type: 'mock',
      endpoints: {
        health: '/health',
        docs: '/api/docs',
        nft: '/nft',
        wallet: '/wallet',
        transactions: '/transactions'
      },
      timestamp: new Date().toISOString()
    };
  }

  healthCheck() {
    return {
      status: 'healthy',
      service: 'mock-blockchain-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    };
  }
}
