# Service Creation Checklist

## Pre-Creation Planning

### ✅ Step 1: Service Definition
- [ ] Service name defined (e.g., "product")
- [ ] Service description written
- [ ] Port number assigned (next available: 3003, 3004, 3005...)
- [ ] Business domain identified
- [ ] Service boundaries defined

### ✅ Step 2: Naming Convention Validation
- [ ] Database name: `{{SERVICE_NAME_SNAKE}}_service` (e.g., `product_service`)
- [ ] Docker image: `{{SERVICE_NAME_KEBAB}}-service` (e.g., `product-service`)
- [ ] Container name: `social-commerce-{{SERVICE_NAME_KEBAB}}-service`
- [ ] Queue name: `{{SERVICE_NAME_SNAKE}}_queue` (e.g., `product_queue`)
- [ ] Environment variables: `{{SERVICE_NAME_UPPER}}_SERVICE_*`

## Template Implementation

### ✅ Step 3: Copy and Customize Template
- [ ] Copy template directory: `cp -r docs/templates/service-template services/{{SERVICE_NAME_KEBAB}}`
- [ ] Replace `{{SERVICE_NAME}}` with actual name (e.g., "product")
- [ ] Replace `{{SERVICE_NAME_KEBAB}}` with kebab-case (e.g., "product-service")
- [ ] Replace `{{SERVICE_NAME_SNAKE}}` with snake_case (e.g., "product_service")
- [ ] Replace `{{SERVICE_NAME_UPPER}}` with UPPER_CASE (e.g., "PRODUCT")
- [ ] Replace `{{SERVICE_PORT}}` with assigned port (e.g., "3003")
- [ ] Replace `{{SERVICE_DESCRIPTION}}` with description

### ✅ Step 4: Configuration Files
- [ ] Update `package.json` with correct name and description
- [ ] Update `Dockerfile` with correct service references
- [ ] Update `src/main.ts` with correct service configuration
- [ ] Create `tsconfig.json` and `nest-cli.json`
- [ ] Update `README.md` with service-specific information

## Infrastructure Integration

### ✅ Step 5: Docker Compose Integration
- [ ] Add service to `docker-compose.yml`:
```yaml
{{SERVICE_NAME_KEBAB}}:
  build:
    context: .
    dockerfile: services/{{SERVICE_NAME_KEBAB}}/Dockerfile
  container_name: social-commerce-{{SERVICE_NAME_KEBAB}}-service
  depends_on:
    postgres:
      condition: service_healthy
    rabbitmq:
      condition: service_healthy
  environment:
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USERNAME: postgres
    DB_PASSWORD: ${DB_PASSWORD:-1111}
    DB_DATABASE: {{SERVICE_NAME_SNAKE}}_service
    DB_SYNCHRONIZE: ${DB_SYNCHRONIZE:-false}
    DB_LOGGING: "true"
    JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
    JWT_EXPIRES_IN: 1h
    HTTP_PORT: {{SERVICE_PORT}}
    MICROSERVICE_PORT: {{SERVICE_PORT}}
    NODE_ENV: development
    RABBITMQ_URL: amqp://admin:admin@rabbitmq:5672
    RABBITMQ_QUEUE: {{SERVICE_NAME_SNAKE}}_queue
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:{{SERVICE_PORT}}/api/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s
  networks:
    - social-commerce-network
  ports:
    - "{{SERVICE_PORT}}:{{SERVICE_PORT}}"
  volumes:
    - ./services/{{SERVICE_NAME_KEBAB}}:/app
    - /app/node_modules
    - ./libs:/libs
```

### ✅ Step 6: Database Configuration
- [ ] Add database to `POSTGRES_MULTIPLE_DATABASES`:
```bash
POSTGRES_MULTIPLE_DATABASES=user_service,store_service,{{SERVICE_NAME_SNAKE}}_service
```

### ✅ Step 7: Environment Variables
- [ ] Add to `.env`:
```bash
# {{SERVICE_NAME_UPPER}} Service
{{SERVICE_NAME_UPPER}}_SERVICE_PORT={{SERVICE_PORT}}
{{SERVICE_NAME_UPPER}}_SERVICE_URL=http://{{SERVICE_NAME_KEBAB}}:{{SERVICE_PORT}}/api
DB_DATABASE_{{SERVICE_NAME_UPPER}}={{SERVICE_NAME_SNAKE}}_service
RABBITMQ_{{SERVICE_NAME_UPPER}}_QUEUE={{SERVICE_NAME_SNAKE}}_queue
```

- [ ] Add to `.env.example` with same variables

## Implementation

### ✅ Step 8: Core Implementation
- [ ] Implement `app.module.ts` with required modules
- [ ] Create health controller in `shared/controllers/health.controller.ts`
- [ ] Implement main business feature in `{{SERVICE_NAME}}-management/`
- [ ] Create entity in `entities/{{SERVICE_NAME}}.entity.ts`
- [ ] Create DTOs in `dto/` directory
- [ ] Implement service in `services/{{SERVICE_NAME}}.service.ts`
- [ ] Implement controller in `controllers/{{SERVICE_NAME}}.controller.ts`
- [ ] Implement repository in `repositories/{{SERVICE_NAME}}.repository.ts`

### ✅ Step 9: API Gateway Integration
- [ ] Add service to API Gateway routes
- [ ] Update API Gateway environment variables
- [ ] Add service dependency to API Gateway in docker-compose.yml

## Testing and Validation

### ✅ Step 10: Build and Test
- [ ] Build Docker image: `docker build -f services/{{SERVICE_NAME_KEBAB}}/Dockerfile -t {{SERVICE_NAME_KEBAB}} .`
- [ ] Start service: `docker-compose up -d {{SERVICE_NAME_KEBAB}}`
- [ ] Verify health endpoint: `curl http://localhost:{{SERVICE_PORT}}/api/health`
- [ ] Check Swagger docs: `http://localhost:{{SERVICE_PORT}}/api/docs`
- [ ] Test basic CRUD operations
- [ ] Verify database connection and table creation

### ✅ Step 11: Integration Testing
- [ ] Test service communication through API Gateway
- [ ] Verify environment variable configuration
- [ ] Test service startup and shutdown
- [ ] Verify logging and error handling
- [ ] Test with other services

## Documentation

### ✅ Step 12: Documentation Updates
- [ ] Update main README.md with new service
- [ ] Document API endpoints
- [ ] Update architecture diagrams
- [ ] Add service to development workflow documentation
- [ ] Update service naming conventions compliance

## Final Validation

### ✅ Step 13: Compliance Check
- [ ] All naming conventions followed
- [ ] Service follows established patterns
- [ ] No duplicate code or configurations
- [ ] Proper error handling implemented
- [ ] Security best practices applied
- [ ] Performance considerations addressed

---

**✅ Service Creation Complete!**

**Next Steps:**
1. Implement business logic
2. Add comprehensive tests
3. Set up monitoring and logging
4. Plan feature development
5. Document API contracts
