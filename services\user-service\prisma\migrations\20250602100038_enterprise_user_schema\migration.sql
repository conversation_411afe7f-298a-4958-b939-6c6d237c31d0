-- CreateTable
CREATE TABLE "user_commands" (
    "id" TEXT NOT NULL,
    "username" VARCHAR(50) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "password" VARCHAR(255) NOT NULL,
    "twitter_username" VA<PERSON>HA<PERSON>(50),
    "twitter_id" VARCHAR(50),
    "role" VARCHAR(20) NOT NULL DEFAULT 'user',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_email_verified" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" VARCHAR(50),
    "updated_by" VARCHAR(50),
    "version" INTEGER NOT NULL DEFAULT 1,
    "last_login_at" TIMESTAMP(3),
    "password_changed_at" TIMESTAMP(3),
    "failed_login_attempts" INTEGER NOT NULL DEFAULT 0,
    "locked_until" TIMESTAMP(3),
    "data_classification" TEXT NOT NULL DEFAULT 'internal',
    "retention_policy" TEXT NOT NULL DEFAULT '7years',

    CONSTRAINT "user_commands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_queries" (
    "id" TEXT NOT NULL,
    "username" VARCHAR(50) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "display_name" VARCHAR(100),
    "twitter_username" VARCHAR(50),
    "twitter_followers" INTEGER,
    "twitter_verified" BOOLEAN,
    "role" VARCHAR(20) NOT NULL DEFAULT 'user',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_email_verified" BOOLEAN NOT NULL DEFAULT false,
    "total_nfts" INTEGER NOT NULL DEFAULT 0,
    "total_campaigns" INTEGER NOT NULL DEFAULT 0,
    "total_transactions" INTEGER NOT NULL DEFAULT 0,
    "total_value" DECIMAL(18,8) NOT NULL DEFAULT 0,
    "avg_response_time" DOUBLE PRECISION,
    "last_activity_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL,
    "last_updated" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_queries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_events" (
    "id" TEXT NOT NULL,
    "aggregate_id" TEXT NOT NULL,
    "event_type" VARCHAR(50) NOT NULL,
    "event_version" INTEGER NOT NULL,
    "event_data" JSONB NOT NULL,
    "metadata" JSONB,
    "causation_id" TEXT,
    "correlation_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,

    CONSTRAINT "user_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "entity_type" VARCHAR(50) NOT NULL,
    "entity_id" TEXT NOT NULL,
    "action" VARCHAR(50) NOT NULL,
    "old_values" JSONB,
    "new_values" JSONB,
    "changed_fields" TEXT[],
    "user_id" TEXT,
    "session_id" TEXT,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "reason" TEXT,
    "approved_by" TEXT,
    "compliance_flags" TEXT[],
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_commands_username_key" ON "user_commands"("username");

-- CreateIndex
CREATE UNIQUE INDEX "user_commands_email_key" ON "user_commands"("email");

-- CreateIndex
CREATE INDEX "user_commands_email_idx" ON "user_commands"("email");

-- CreateIndex
CREATE INDEX "user_commands_username_idx" ON "user_commands"("username");

-- CreateIndex
CREATE INDEX "user_commands_is_active_idx" ON "user_commands"("is_active");

-- CreateIndex
CREATE INDEX "user_commands_created_at_idx" ON "user_commands"("created_at");

-- CreateIndex
CREATE INDEX "user_commands_last_login_at_idx" ON "user_commands"("last_login_at");

-- CreateIndex
CREATE UNIQUE INDEX "user_queries_username_key" ON "user_queries"("username");

-- CreateIndex
CREATE UNIQUE INDEX "user_queries_email_key" ON "user_queries"("email");

-- CreateIndex
CREATE INDEX "user_queries_email_idx" ON "user_queries"("email");

-- CreateIndex
CREATE INDEX "user_queries_username_idx" ON "user_queries"("username");

-- CreateIndex
CREATE INDEX "user_queries_is_active_idx" ON "user_queries"("is_active");

-- CreateIndex
CREATE INDEX "user_queries_total_nfts_idx" ON "user_queries"("total_nfts");

-- CreateIndex
CREATE INDEX "user_queries_last_activity_at_idx" ON "user_queries"("last_activity_at");

-- CreateIndex
CREATE INDEX "user_events_aggregate_id_event_version_idx" ON "user_events"("aggregate_id", "event_version");

-- CreateIndex
CREATE INDEX "user_events_event_type_idx" ON "user_events"("event_type");

-- CreateIndex
CREATE INDEX "user_events_created_at_idx" ON "user_events"("created_at");

-- CreateIndex
CREATE INDEX "user_events_correlation_id_idx" ON "user_events"("correlation_id");

-- CreateIndex
CREATE INDEX "audit_logs_entity_type_entity_id_idx" ON "audit_logs"("entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "audit_logs_user_id_idx" ON "audit_logs"("user_id");

-- CreateIndex
CREATE INDEX "audit_logs_action_idx" ON "audit_logs"("action");

-- CreateIndex
CREATE INDEX "audit_logs_created_at_idx" ON "audit_logs"("created_at");

-- AddForeignKey
ALTER TABLE "user_events" ADD CONSTRAINT "user_events_aggregate_id_fkey" FOREIGN KEY ("aggregate_id") REFERENCES "user_commands"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_commands"("id") ON DELETE SET NULL ON UPDATE CASCADE;
