# API Endpoints Testing Documentation

## Overview
This document provides detailed information about all API endpoints that have been tested and verified.

**Last Updated:** May 25, 2025  
**Testing Status:** Phase 1A Complete  
**Base URL:** http://localhost:3010

## API Gateway Endpoints

### Health & Status Endpoints

#### GET /api/health
**Purpose:** Check API Gateway and all service health  
**Authentication:** None required  
**Response:** 200 OK
```json
{
  "status": "ok",
  "service": "api-gateway",
  "timestamp": "2025-05-25T23:38:03.257Z",
  "version": "1.0.0",
  "uptime": 219.4977669,
  "services": [
    {
      "name": "user-service",
      "status": "healthy",
      "url": "http://localhost:3001",
      "responseTime": 9,
      "health": {
        "status": "ok",
        "service": "user-service",
        "timestamp": "2025-05-25T23:38:03.248Z",
        "version": "1.0.0",
        "uptime": 1532.1390825,
        "port": "3001",
        "environment": "development"
      }
    }
  ]
}
```

#### GET /api/services
**Purpose:** List all registered services  
**Authentication:** None required  
**Response:** 200 OK with service list

#### GET /api
**Purpose:** API Gateway status  
**Authentication:** None required  
**Response:** 200 OK with welcome message

## User Service Endpoints (via API Gateway)

### Authentication Endpoints

#### POST /api/users/register
**Purpose:** Register a new user  
**Authentication:** None required  
**Content-Type:** application/json

**Request Body:**
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "twitterUsername": "testuser"
}
```

**Success Response:** 201 Created
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "uuid",
    "username": "testuser",
    "email": "<EMAIL>",
    "twitterUsername": "testuser",
    "role": "user",
    "createdAt": "2025-05-25T23:03:08.000Z"
  },
  "token": "jwt-token-here"
}
```

**Error Responses:**
- **409 Conflict:** User already exists
- **400 Bad Request:** Invalid input data

#### POST /api/users/login
**Purpose:** Authenticate user and get JWT token  
**Authentication:** None required  
**Content-Type:** application/json

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response:** 201 Created
```json
{
  "message": "Login successful",
  "user": {
    "id": "uuid",
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user"
  },
  "token": "jwt-token-here"
}
```

**Error Responses:**
- **401 Unauthorized:** Invalid credentials
- **400 Bad Request:** Missing email or password

#### GET /api/users/profile
**Purpose:** Get current user profile  
**Authentication:** Bearer token required  
**Headers:** Authorization: Bearer {jwt-token}

**Success Response:** 200 OK
```json
{
  "id": "uuid",
  "username": "testuser",
  "email": "<EMAIL>",
  "twitterUsername": "testuser",
  "role": "user",
  "createdAt": "2025-05-25T23:03:08.000Z",
  "updatedAt": "2025-05-25T23:03:08.000Z"
}
```

**Error Responses:**
- **401 Unauthorized:** Invalid or missing token
- **404 Not Found:** User not found

## Profile Analysis Service Endpoints (via API Gateway)

### Analysis Endpoints

#### POST /api/analysis/twitter-profile
**Purpose:** Analyze a Twitter profile  
**Authentication:** Bearer token required  
**Content-Type:** application/json

**Request Body:**
```json
{
  "twitterHandle": "username",
  "userId": "user-uuid",
  "projectId": "project-uuid",
  "parameters": {
    "includeMetrics": true,
    "analysisDepth": "detailed"
  }
}
```

**Success Response:** 201 Created
```json
{
  "id": "analysis-uuid",
  "twitterHandle": "username",
  "status": "completed",
  "results": {
    "followerCount": 1250,
    "followingCount": 890,
    "tweetCount": 3420,
    "engagementRate": 4.2,
    "sentimentScore": 0.75,
    "activityScore": 8.5,
    "influenceScore": 6.8
  },
  "createdAt": "2025-05-25T23:03:08.000Z"
}
```

#### GET /api/analysis/results/:analysisId
**Purpose:** Get analysis results by ID  
**Authentication:** Bearer token required  
**Parameters:** analysisId (UUID)

**Success Response:** 200 OK with analysis data

#### GET /api/analysis/history
**Purpose:** Get user's analysis history  
**Authentication:** Bearer token required  
**Query Parameters:** 
- limit (optional): Number of results
- offset (optional): Pagination offset

**Success Response:** 200 OK with analysis list

## CORS Configuration

### Allowed Origins
- http://localhost:3000
- http://localhost:3100
- http://localhost:3001
- http://localhost:3002

### Allowed Methods
- GET, HEAD, PUT, PATCH, POST, DELETE, OPTIONS

### Allowed Headers
- Content-Type
- Authorization
- Accept

### Credentials
- Enabled (Access-Control-Allow-Credentials: true)

## Rate Limiting

### Configuration
- **Limit:** 100 requests per minute per IP
- **Window:** 60 seconds
- **Headers:** X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset

### Response on Limit Exceeded
- **Status:** 429 Too Many Requests
- **Body:** Rate limit exceeded message

## Security Headers

### Helmet.js Configuration
- Content-Security-Policy
- Cross-Origin-Opener-Policy
- Cross-Origin-Resource-Policy
- Origin-Agent-Cluster
- Referrer-Policy
- Strict-Transport-Security
- X-Content-Type-Options
- X-DNS-Prefetch-Control
- X-Download-Options
- X-Frame-Options
- X-Permitted-Cross-Domain-Policies
- X-XSS-Protection

## Testing Status Summary

### ✅ Fully Tested Endpoints
- All API Gateway endpoints
- All User Service endpoints
- All Profile Analysis Service endpoints
- CORS preflight requests
- Rate limiting functionality
- Error handling scenarios

### 🔧 Pending Testing
- Project Service endpoints (not yet integrated)
- NFT Generation Service endpoints (not yet integrated)
- Blockchain Service endpoints (not yet integrated)
- Marketplace Service endpoints (not yet integrated)
