// Final Debug Test for API Gateway Integration
const axios = require('axios');

async function debugAPIGatewayFinal() {
  console.log('🔍 Final Debug: API Gateway Integration...\n');
  
  try {
    // Test 1: Direct Project Service (Known Working)
    console.log('1️⃣ Testing Direct Project Service...');
    const directResponse = await axios.get('http://localhost:3005/enterprise/projects');
    console.log('✅ Direct response:', {
      status: directResponse.status,
      contentType: directResponse.headers['content-type'],
      dataType: typeof directResponse.data,
      dataLength: JSON.stringify(directResponse.data).length,
      data: directResponse.data
    });
    console.log('');
    
    // Test 2: API Gateway Health (Should work)
    console.log('2️⃣ Testing API Gateway Health...');
    const healthResponse = await axios.get('http://localhost:3010/api/health');
    console.log('✅ Health response:', {
      status: healthResponse.status,
      projectServiceStatus: healthResponse.data.services?.find(s => s.name === 'project-service')?.healthy
    });
    console.log('');
    
    // Test 3: API Gateway Projects (The Problem)
    console.log('3️⃣ Testing API Gateway Projects (The Issue)...');
    try {
      const gatewayResponse = await axios.get('http://localhost:3010/api/projects', {
        timeout: 10000,
        validateStatus: () => true // Accept any status code
      });
      
      console.log('✅ Gateway response received:', {
        status: gatewayResponse.status,
        statusText: gatewayResponse.statusText,
        contentType: gatewayResponse.headers['content-type'],
        dataType: typeof gatewayResponse.data,
        data: gatewayResponse.data
      });
      
    } catch (error) {
      console.log('❌ Gateway error details:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        responseHeaders: error.response?.headers
      });
    }
    
    // Test 4: Raw HTTP Request to API Gateway
    console.log('\n4️⃣ Testing Raw HTTP Request...');
    try {
      const rawResponse = await axios({
        method: 'GET',
        url: 'http://localhost:3010/api/projects',
        timeout: 10000,
        transformResponse: [(data) => {
          console.log('🔍 Raw response data:', {
            type: typeof data,
            length: data ? data.length : 'null',
            content: data,
            isNull: data === null,
            isUndefined: data === undefined,
            isEmpty: data === ''
          });
          return data;
        }]
      });
      
      console.log('✅ Raw response processed:', rawResponse.data);
      
    } catch (error) {
      console.log('❌ Raw request failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugAPIGatewayFinal().catch(console.error);
