'use client'

import { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import {
  XMarkIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  HashtagIcon,
  ChartBarIcon,
  GlobeAltIcon,
  CheckBadgeIcon,
  SparklesIcon,
  HeartIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline'
import EnhancedShareModal from '@/components/ui/enhanced-share-modal'

interface NFT {
  id: string
  name: string
  rarity: string
  score: number
  twitterHandle: string
  createdAt: string
  metadata?: {
    description?: string
    image?: string
    external_url?: string
    attributes?: Array<{
      trait_type: string
      value: string | number
      display_type?: string
    }>
    background_color?: string
  }
  status?: string
  blockchain?: string
  tokenId?: string
  imageUrl?: string
}

interface NFTDetailModalProps {
  nft: NFT | null
  isOpen: boolean
  onClose: () => void
}

export default function NFTDetailModal({ nft, isOpen, onClose }: NFTDetailModalProps) {
  const [showShareModal, setShowShareModal] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)

  if (!nft) return null

  // Debug: Log NFT data to see what we're working with
  console.log('NFT Detail Modal - NFT Data:', nft)

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'from-yellow-400 to-orange-500'
      case 'epic':
        return 'from-purple-500 to-pink-500'
      case 'rare':
        return 'from-blue-500 to-cyan-500'
      case 'common':
        return 'from-gray-400 to-gray-600'
      default:
        return 'from-green-500 to-emerald-500'
    }
  }

  const getRarityBadgeColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'epic':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'rare':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'common':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const handleFavorite = () => {
    setIsFavorited(!isFavorited)
    // TODO: Implement favorite functionality with backend
  }

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
    // TODO: Implement bookmark functionality with backend
  }

  const handleDownload = () => {
    const imageUrl = nft.metadata?.image || nft.imageUrl
    if (imageUrl) {
      const link = document.createElement('a')
      link.href = imageUrl
      link.download = `${nft.name.replace(/\s+/g, '_')}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      console.log('No image URL available for download')
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${getRarityColor(nft.rarity)} flex items-center justify-center`}>
                      <SparklesIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        {nft.name}
                      </Dialog.Title>
                      <p className="text-sm text-gray-600">@{nft.twitterHandle}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleFavorite}
                      className={`p-2 transition-colors ${
                        isFavorited
                          ? 'text-red-500 hover:text-red-600'
                          : 'text-gray-400 hover:text-gray-600'
                      }`}
                      title="Favorite NFT"
                    >
                      <HeartIcon className={`h-5 w-5 ${isFavorited ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      onClick={handleBookmark}
                      className={`p-2 transition-colors ${
                        isBookmarked
                          ? 'text-blue-500 hover:text-blue-600'
                          : 'text-gray-400 hover:text-gray-600'
                      }`}
                      title="Bookmark NFT"
                    >
                      <BookmarkIcon className={`h-5 w-5 ${isBookmarked ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      onClick={handleShare}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Share NFT"
                    >
                      <ShareIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={handleDownload}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Download NFT"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={onClose}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
                  {/* NFT Image */}
                  <div className="space-y-4">
                    <div className={`aspect-square rounded-xl bg-gradient-to-br ${getRarityColor(nft.rarity)} p-1`}>
                      <div className="w-full h-full bg-white rounded-lg flex items-center justify-center">
                        {nft.metadata?.image || nft.imageUrl ? (
                          <img 
                            src={nft.metadata?.image || nft.imageUrl} 
                            alt={nft.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <div className="text-center">
                            <SparklesIcon className="h-24 w-24 mx-auto text-gray-300 mb-4" />
                            <p className="text-gray-500">NFT Image</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-lg p-4 text-center">
                        <div className="text-2xl font-bold text-gray-900">{nft.score}</div>
                        <div className="text-sm text-gray-600">Score</div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4 text-center">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRarityBadgeColor(nft.rarity)}`}>
                          {nft.rarity}
                        </span>
                        <div className="text-sm text-gray-600 mt-1">Rarity</div>
                      </div>
                    </div>
                  </div>

                  {/* NFT Details */}
                  <div className="space-y-6">
                    {/* Description */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                      <p className="text-sm text-gray-600">
                        {nft.metadata?.description ||
                         `A unique ${nft.rarity} NFT generated from @${nft.twitterHandle}'s Twitter profile analysis. This NFT represents their social influence and engagement metrics with a score of ${nft.score}.`}
                      </p>
                    </div>

                    {/* Properties */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Properties</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center text-xs text-gray-500 mb-1">
                            <CalendarIcon className="h-3 w-3 mr-1" />
                            Created
                          </div>
                          <div className="text-sm font-medium text-gray-900">
                            {formatDate(nft.createdAt)}
                          </div>
                        </div>
                        
                        {nft.tokenId && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center text-xs text-gray-500 mb-1">
                              <HashtagIcon className="h-3 w-3 mr-1" />
                              Token ID
                            </div>
                            <div className="text-sm font-medium text-gray-900">
                              #{nft.tokenId}
                            </div>
                          </div>
                        )}

                        {nft.blockchain && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center text-xs text-gray-500 mb-1">
                              <GlobeAltIcon className="h-3 w-3 mr-1" />
                              Blockchain
                            </div>
                            <div className="text-sm font-medium text-gray-900">
                              {nft.blockchain}
                            </div>
                          </div>
                        )}

                        {nft.status && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center text-xs text-gray-500 mb-1">
                              <CheckBadgeIcon className="h-3 w-3 mr-1" />
                              Status
                            </div>
                            <div className="text-sm font-medium text-gray-900 capitalize">
                              {nft.status}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Attributes */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Attributes</h4>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {/* Default attributes based on NFT data */}
                        <div className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">Rarity</span>
                          <span className="text-sm font-medium text-gray-900 capitalize">{nft.rarity}</span>
                        </div>
                        <div className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">Score</span>
                          <span className="text-sm font-medium text-gray-900">{nft.score}/100</span>
                        </div>
                        <div className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">Twitter Handle</span>
                          <span className="text-sm font-medium text-gray-900">@{nft.twitterHandle}</span>
                        </div>

                        {/* Additional metadata attributes if available */}
                        {nft.metadata?.attributes && nft.metadata.attributes.map((attr, index) => (
                          <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                            <span className="text-sm text-gray-600">{attr.trait_type}</span>
                            <span className="text-sm font-medium text-gray-900">
                              {attr.display_type === 'number' ? attr.value : attr.value}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* External Link */}
                    {nft.metadata?.external_url && (
                      <div>
                        <a 
                          href={nft.metadata.external_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-500"
                        >
                          <GlobeAltIcon className="h-4 w-4 mr-1" />
                          View External Details
                        </a>
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Generated from Twitter profile analysis
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={handleShare}
                        className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Share NFT
                      </button>
                      <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-200 text-gray-900 text-sm font-medium rounded-md hover:bg-gray-300 transition-colors"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>

        {/* Enhanced Share Modal */}
        <EnhancedShareModal
          nft={nft}
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
        />
      </Dialog>
    </Transition>
  )
}
