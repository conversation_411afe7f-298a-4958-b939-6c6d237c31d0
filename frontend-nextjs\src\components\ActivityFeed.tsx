'use client'

import {
  <PERSON>,
  VS<PERSON>ck,
  HS<PERSON>ck,
  <PERSON>,
  <PERSON>ge,
  <PERSON>,
  But<PERSON>
} from '@chakra-ui/react'
import NextLink from 'next/link'

interface ActivityItem {
  id: string
  type: 'nft_generated' | 'campaign_joined' | 'profile_updated' | 'achievement'
  title: string
  description: string
  timestamp: string
  link?: string
  badge?: {
    text: string
    color: string
  }
}

function ActivityItemComponent({ item }: { item: ActivityItem }) {
  const getIcon = (type: string) => {
    switch (type) {
      case 'nft_generated': return '🎨'
      case 'campaign_joined': return '🎯'
      case 'profile_updated': return '👤'
      case 'achievement': return '🏆'
      default: return '📝'
    }
  }

  const getTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <HStack align="start" gap={4} p={4} bg="white" borderRadius="md" boxShadow="sm">
      <Box
        w="40px"
        h="40px"
        bg="blue.500"
        color="white"
        borderRadius="full"
        display="flex"
        alignItems="center"
        justifyContent="center"
        fontSize="lg"
        flexShrink={0}
      >
        {getIcon(item.type)}
      </Box>

      <VStack align="start" gap={1} flex="1">
        <HStack justify="space-between" w="full">
          <Text fontWeight="medium" fontSize="sm">
            {item.title}
          </Text>
          <Text fontSize="xs" color="gray.500">
            {getTimeAgo(item.timestamp)}
          </Text>
        </HStack>

        <Text fontSize="sm" color="gray.600">
          {item.description}
        </Text>

        <HStack gap={2}>
          {item.badge && (
            <Badge colorScheme={item.badge.color} size="sm">
              {item.badge.text}
            </Badge>
          )}
          {item.link && (
            <Link as={NextLink} href={item.link} fontSize="xs" color="blue.500">
              View details →
            </Link>
          )}
        </HStack>
      </VStack>
    </HStack>
  )
}

interface ActivityFeedProps {
  userId?: string
  limit?: number
}

export default function ActivityFeed({ userId, limit = 10 }: ActivityFeedProps) {
  // Mock data - in real app, this would come from API
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'nft_generated',
      title: 'NFT Generated',
      description: 'Generated a Rare NFT for "Summer Campaign 2024"',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      link: '/nfts',
      badge: { text: 'Rare', color: 'purple' }
    },
    {
      id: '2',
      type: 'campaign_joined',
      title: 'Joined Campaign',
      description: 'Joined "Eco-Friendly NFT Initiative" campaign',
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      link: '/campaigns',
      badge: { text: 'Active', color: 'green' }
    },
    {
      id: '3',
      type: 'achievement',
      title: 'Achievement Unlocked',
      description: 'Earned "NFT Collector" badge for generating 10+ NFTs',
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      badge: { text: 'Achievement', color: 'gold' }
    },
    {
      id: '4',
      type: 'profile_updated',
      title: 'Profile Updated',
      description: 'Updated social media connections and bio',
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      link: '/profile'
    },
    {
      id: '5',
      type: 'nft_generated',
      title: 'NFT Generated',
      description: 'Generated a Common NFT for "Tech Innovation Campaign"',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      link: '/nfts',
      badge: { text: 'Common', color: 'gray' }
    }
  ]

  const limitedActivities = activities.slice(0, limit)

  return (
    <Box>
      <HStack justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="bold">
          📈 Recent Activity
        </Text>
        <Link as={NextLink} href="/activity" fontSize="sm" color="blue.500">
          View all →
        </Link>
      </HStack>

      <VStack gap={3} align="stretch">
        {limitedActivities.length === 0 ? (
          <Box textAlign="center" py={8} bg="gray.50" borderRadius="md">
            <Text color="gray.500" mb={4}>
              No recent activity yet
            </Text>
            <Link as={NextLink} href="/campaigns">
              <Button colorScheme="blue" size="sm">
                Join Your First Campaign
              </Button>
            </Link>
          </Box>
        ) : (
          limitedActivities.map((activity) => (
            <ActivityItemComponent key={activity.id} item={activity} />
          ))
        )}
      </VStack>
    </Box>
  )
}
