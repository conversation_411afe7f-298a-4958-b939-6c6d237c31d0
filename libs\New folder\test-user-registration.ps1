# User Registration E2E Test
Write-Host "=== USER REGISTRATION E2E TEST ===" -ForegroundColor Cyan
Write-Host "Testing complete user registration flow" -ForegroundColor Green

# Test Configuration
$backendUrl = "http://localhost:3011"
$frontendUrl = "http://localhost:3000"
$timestamp = Get-Date -Format "yyyyMMddHHmmss"

# Test User Data
$testUser = @{
    username = "e2etest$timestamp"
    email = "e2etest$<EMAIL>"
    password = "password123"
    twitterUsername = "e2etest_twitter"
} | ConvertTo-Json

Write-Host "`nTest User: e2etest$timestamp" -ForegroundColor Yellow
Write-Host "Testing registration API..." -ForegroundColor Yellow

# Test 1: User Registration API
try {
    $response = Invoke-RestMethod -Uri "$backendUrl/auth/register" -Method Post -Body $testUser -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Registration successful!" -ForegroundColor Green
    Write-Host "User ID: $($response.user.id)" -ForegroundColor Green
    Write-Host "Token received: $($response.accessToken.Length) characters" -ForegroundColor Green
    $global:userId = $response.user.id
    $global:userToken = $response.accessToken
} catch {
    Write-Host "❌ Registration failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: User Login API
Write-Host "`nTesting login API..." -ForegroundColor Yellow
$loginData = @{
    email = "e2etest$<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$backendUrl/auth/login" -Method Post -Body $loginData -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Login successful!" -ForegroundColor Green
    Write-Host "Login token: $($loginResponse.accessToken.Length) characters" -ForegroundColor Green
    $global:loginToken = $loginResponse.accessToken
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 User Registration & Login E2E Test PASSED!" -ForegroundColor Green
Write-Host "User can successfully register and login through API" -ForegroundColor Green
