"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/layout */ \"(app-pages-browser)/./src/components/layout/layout.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/real-nft-gallery */ \"(app-pages-browser)/./src/components/dashboard/real-nft-gallery.tsx\");\n/* harmony import */ var _components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/profile-analyzer */ \"(app-pages-browser)/./src/components/dashboard/profile-analyzer.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/user/enhanced-user-profile */ \"(app-pages-browser)/./src/components/user/enhanced-user-profile.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst stats = [\n    {\n        name: 'Total NFTs',\n        value: '12',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        change: '+2',\n        changeType: 'positive'\n    },\n    {\n        name: 'Active Campaigns',\n        value: '3',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        change: '+1',\n        changeType: 'positive'\n    },\n    {\n        name: 'Rewards Earned',\n        value: '$1,234',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        change: '+$234',\n        changeType: 'positive'\n    },\n    {\n        name: 'Engagement Score',\n        value: '85%',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        change: '+5%',\n        changeType: 'positive'\n    }\n];\nconst recentNFTs = [\n    {\n        id: 1,\n        name: 'Cosmic Explorer #1234',\n        campaign: 'Space Campaign',\n        rarity: 'Rare',\n        image: 'https://via.placeholder.com/100x100/6366f1/ffffff?text=NFT',\n        value: '$45.00'\n    },\n    {\n        id: 2,\n        name: 'Digital Warrior #5678',\n        campaign: 'Gaming Campaign',\n        rarity: 'Epic',\n        image: 'https://via.placeholder.com/100x100/8b5cf6/ffffff?text=NFT',\n        value: '$78.00'\n    },\n    {\n        id: 3,\n        name: 'Cyber Punk #9012',\n        campaign: 'Tech Campaign',\n        rarity: 'Legendary',\n        image: 'https://via.placeholder.com/100x100/ec4899/ffffff?text=NFT',\n        value: '$156.00'\n    }\n];\nconst activeCampaigns = [\n    {\n        id: 1,\n        name: 'DeFi Revolution',\n        description: 'Promote the future of decentralized finance',\n        progress: 75,\n        reward: '$50 + NFT',\n        deadline: '3 days left',\n        participants: 1234\n    },\n    {\n        id: 2,\n        name: 'Green Blockchain',\n        description: 'Spread awareness about eco-friendly crypto',\n        progress: 45,\n        reward: '$30 + NFT',\n        deadline: '1 week left',\n        participants: 856\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [nftRefreshTrigger, setNftRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Function to trigger NFT gallery refresh\n    const handleNFTGenerated = ()=>{\n        console.log('🔄 Triggering NFT gallery refresh...');\n        setNftRefreshTrigger((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username),\n                                                        \"! Here's what's happening with your NFTs and campaigns.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Explore\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-8 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_7__.EnhancedUserProfile, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2 space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    limit: 6,\n                                                    refreshTrigger: nftRefreshTrigger\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    onNFTGenerated: handleNFTGenerated\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-white shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Create NFT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"View Analytics\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"gQgYv5PbPa0m+HOm4aUvjW4VTUk=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});