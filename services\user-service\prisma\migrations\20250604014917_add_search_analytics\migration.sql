-- CreateTable
CREATE TABLE "search_analytics" (
    "id" TEXT NOT NULL,
    "query" VARCHAR(500) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "result_count" INTEGER NOT NULL,
    "execution_time" INTEGER NOT NULL,
    "user_id" TEXT NOT NULL,
    "filters" JSONB NOT NULL DEFAULT '[]',
    "sort_criteria" JSONB NOT NULL DEFAULT '{}',
    "has_click" BOOLEAN NOT NULL DEFAULT false,
    "click_position" INTEGER,
    "session_id" VARCHAR(100),
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "search_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "search_analytics_query_idx" ON "search_analytics"("query");

-- CreateIndex
CREATE INDEX "search_analytics_type_idx" ON "search_analytics"("type");

-- CreateIndex
CREATE INDEX "search_analytics_user_id_idx" ON "search_analytics"("user_id");

-- CreateIndex
CREATE INDEX "search_analytics_timestamp_idx" ON "search_analytics"("timestamp");

-- CreateIndex
CREATE INDEX "search_analytics_has_click_idx" ON "search_analytics"("has_click");

-- CreateIndex
CREATE INDEX "search_analytics_session_id_idx" ON "search_analytics"("session_id");
