# Twitter API v2 Setup Guide

## 🐦 **Real Twitter API Integration**

The Profile Analysis Service now supports **real Twitter API v2** integration alongside the existing mock service fallback.

## 🔑 **Getting Twitter API Credentials**

### **Step 1: Create Twitter Developer Account**
1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
2. Sign in with your Twitter account
3. Apply for a developer account (usually approved instantly for basic access)

### **Step 2: Create a New App**
1. Click "Create App" in the developer dashboard
2. Fill in app details:
   - **App Name**: `Social NFT Platform - Profile Analysis`
   - **Description**: `Analyzing Twitter profiles for NFT generation in Social NFT Platform`
   - **Website**: Your platform URL or `https://example.com`
   - **Use Case**: Select "Making a bot" or "Academic research"

### **Step 3: Get API Keys**
1. Go to your app's "Keys and Tokens" tab
2. Copy the **Bearer Token** (this is what we need)
3. Optionally copy API Key and Secret for future OAuth features

## ⚙️ **Configuration**

### **Environment Variables**
Add these to your `.env` file:

```bash
# Enable Real Twitter API
USE_REAL_TWITTER_API=true
TWITTER_BEARER_TOKEN=your_actual_bearer_token_here

# Optional: For future OAuth features
TWITTER_API_KEY=your_api_key_here
TWITTER_API_SECRET=your_api_secret_here
```

### **Development vs Production**
```bash
# Development (uses mock data fallback)
USE_REAL_TWITTER_API=false
TWITTER_SERVICE_URL=http://localhost:3020

# Production (uses real Twitter API)
USE_REAL_TWITTER_API=true
TWITTER_BEARER_TOKEN=your_production_bearer_token
```

## 🔄 **How It Works**

### **Intelligent Fallback System**
1. **Real API First**: If `USE_REAL_TWITTER_API=true` and Bearer Token exists
2. **External Service Fallback**: If real API fails or not configured
3. **Mock Data Fallback**: If external service fails in development

### **Data Flow**
```
Profile Analysis Request
    ↓
Real Twitter API v2 (if enabled)
    ↓ (if fails)
External Twitter Service (Mock/Real)
    ↓ (if fails in dev)
Generated Mock Data
```

## 🧪 **Testing Real API Integration**

### **Test with Real Twitter Profiles**
```bash
# Set environment variables
USE_REAL_TWITTER_API=true
TWITTER_BEARER_TOKEN=your_bearer_token

# Restart the service
npm run start:dev

# Test with real profiles
curl -X POST http://localhost:3002/api/analysis/twitter-profile \
  -H "Content-Type: application/json" \
  -d '{"twitterHandle": "elonmusk", "userId": "test_user"}'
```

### **Expected Real Data**
- **Real follower counts** (e.g., Elon Musk: 150M+ followers)
- **Real verification status** (blue checkmarks)
- **Real profile images** and descriptions
- **Accurate account creation dates**

## 📊 **Rate Limits**

### **Twitter API v2 Limits**
- **Bearer Token**: 300 requests per 15 minutes
- **User Lookup**: 300 requests per 15 minutes
- **Sufficient for**: ~1200 profile analyses per hour

### **Best Practices**
- Cache analysis results to reduce API calls
- Implement exponential backoff for rate limit errors
- Monitor usage in Twitter Developer Dashboard

## 🚀 **Benefits of Real API**

### **Production-Ready Data**
- **Authentic analysis scores** based on real metrics
- **Accurate NFT rarity** calculations
- **Trustworthy platform** for users

### **Real-World Testing**
- Test with celebrity profiles (high follower counts)
- Test with verified accounts
- Test with various account types and ages

## 🔧 **Troubleshooting**

### **Common Issues**
1. **"User not found"**: Profile is private or suspended
2. **Rate limit exceeded**: Wait 15 minutes or implement caching
3. **Invalid Bearer Token**: Check token in Twitter Developer Portal
4. **API access denied**: Ensure app has proper permissions

### **Debugging**
```bash
# Check logs for API calls
tail -f logs/profile-analysis.log

# Test API connectivity
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "https://api.twitter.com/2/users/by/username/elonmusk"
```

## 🎯 **Next Steps**

1. **Get Twitter API credentials** from developer portal
2. **Update .env file** with real Bearer Token
3. **Test with real profiles** (celebrities, verified accounts)
4. **Compare results** with mock data to see the difference
5. **Deploy to production** with real API integration

**Real Twitter API integration makes our platform production-ready with authentic social media data!** 🚀
