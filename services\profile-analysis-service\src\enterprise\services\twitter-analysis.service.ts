import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../shared/prisma.service';
import axios from 'axios';
import { TwitterApi, TwitterApiReadOnly, UserV2 } from 'twitter-api-v2';

export interface TwitterProfileData {
  id: string;
  username: string;
  displayName: string;
  description?: string;
  followersCount: number;
  followingCount: number;
  tweetsCount: number;
  listedCount: number;
  verified: boolean;
  profileImageUrl?: string;
  location?: string;
  website?: string;
  createdAt: string;
}

export interface TwitterAnalysisResult {
  id: string;
  userId: string;
  twitterHandle: string;
  status: 'pending' | 'completed' | 'failed';
  score: number;
  analysisData: {
    profile: {
      followerCount: number;
      followingCount: number;
      tweetCount: number;
      engagementRate: number;
      accountAge: number;
      isVerified: boolean;
      hasProfileImage: boolean;
      hasBio: boolean;
    };
    metrics: {
      contentQuality: number;
      activityLevel: number;
      influenceScore: number;
      authenticity: number;
      engagement: number;
    };
    breakdown: {
      followerScore: number;
      engagementScore: number;
      contentScore: number;
      activityScore: number;
      profileScore: number;
    };
    nftRecommendation: {
      score: number;
      rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
      reasoning: string[];
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalyzeTwitterProfileDto {
  twitterHandle: string;
  userId?: string;
}

@Injectable()
export class TwitterAnalysisService {
  private readonly logger = new Logger(TwitterAnalysisService.name);
  private readonly twitterServiceUrl: string;
  private readonly twitterClient: TwitterApiReadOnly | null;
  private readonly useRealTwitterAPI: boolean;

  constructor(private readonly prisma: PrismaService) {
    // Check if real Twitter API credentials are available
    const bearerToken = process.env.TWITTER_BEARER_TOKEN;
    this.useRealTwitterAPI = !!bearerToken && process.env.USE_REAL_TWITTER_API === 'true';

    if (this.useRealTwitterAPI && bearerToken) {
      this.twitterClient = new TwitterApi(bearerToken).readOnly;
      this.logger.log('✅ Real Twitter API v2 client initialized');
    } else {
      this.twitterClient = null;
      this.logger.log('📱 Using external Twitter service or mock data');
    }

    // Fallback to external service (mock or real service)
    this.twitterServiceUrl = process.env.TWITTER_SERVICE_URL || 'http://localhost:3020';
  }

  /**
   * Analyze Twitter profile and generate comprehensive analysis
   */
  async analyzeTwitterProfile(analyzeDto: AnalyzeTwitterProfileDto): Promise<TwitterAnalysisResult> {
    const startTime = Date.now();
    const correlationId = `analysis-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      this.logger.log(`Starting Twitter analysis for: ${analyzeDto.twitterHandle}`, { correlationId });

      // Clean Twitter handle (remove @ if present)
      const cleanHandle = analyzeDto.twitterHandle.replace('@', '');

      // Create analysis record
      const analysis = await this.prisma.profileAnalysisCommand.create({
        data: {
          userId: analyzeDto.userId || 'anonymous',
          twitterHandle: cleanHandle,
          status: 'pending',
          correlationId,
          createdBy: analyzeDto.userId || 'system',
          updatedBy: analyzeDto.userId || 'system',
        },
      });

      // Fetch Twitter profile data
      const profileData = await this.fetchTwitterProfile(cleanHandle, correlationId);

      // Perform comprehensive analysis
      const analysisResults = await this.performAnalysis(profileData, correlationId);

      // Update analysis with results
      const updatedAnalysis = await this.prisma.profileAnalysisCommand.update({
        where: { id: analysis.id },
        data: {
          status: 'completed',
          score: analysisResults.score,
          analysisData: analysisResults.analysisData,
          completedAt: new Date(),
          updatedBy: analyzeDto.userId || 'system',
        },
      });

      // Create query model for fast reads
      await this.createQueryModel(updatedAnalysis);

      const responseTime = Date.now() - startTime;
      this.logger.log(`Analysis completed successfully in ${responseTime}ms`, { 
        correlationId, 
        score: analysisResults.score,
        rarity: analysisResults.analysisData.nftRecommendation.rarity
      });

      return {
        id: updatedAnalysis.id,
        userId: updatedAnalysis.userId,
        twitterHandle: updatedAnalysis.twitterHandle,
        status: updatedAnalysis.status as any,
        score: updatedAnalysis.score,
        analysisData: updatedAnalysis.analysisData as any,
        createdAt: updatedAnalysis.createdAt,
        updatedAt: updatedAnalysis.updatedAt,
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Analysis failed after ${responseTime}ms: ${error.message}`, {
        correlationId,
        error: error.stack,
        twitterHandle: analyzeDto.twitterHandle
      });

      throw new BadRequestException(`Twitter analysis failed: ${error.message}`);
    }
  }

  /**
   * Get analysis by ID
   */
  async getAnalysisById(id: string): Promise<TwitterAnalysisResult | null> {
    try {
      // Try query model first (faster)
      const queryResult = await this.prisma.profileAnalysisQuery.findUnique({
        where: { id },
      });

      if (queryResult) {
        return {
          id: queryResult.id,
          userId: queryResult.userId,
          twitterHandle: queryResult.twitterHandle,
          status: queryResult.status as any,
          score: queryResult.score,
          analysisData: queryResult.analysisData as any,
          createdAt: queryResult.createdAt,
          updatedAt: queryResult.updatedAt,
        };
      }

      // Fallback to command model
      const commandResult = await this.prisma.profileAnalysisCommand.findUnique({
        where: { id },
      });

      if (!commandResult) {
        return null;
      }

      return {
        id: commandResult.id,
        userId: commandResult.userId,
        twitterHandle: commandResult.twitterHandle,
        status: commandResult.status as any,
        score: commandResult.score,
        analysisData: commandResult.analysisData as any,
        createdAt: commandResult.createdAt,
        updatedAt: commandResult.updatedAt,
      };

    } catch (error) {
      this.logger.error(`Failed to get analysis by ID: ${error.message}`, { id, error: error.stack });
      throw error;
    }
  }

  /**
   * Get user's analysis history
   */
  async getUserAnalysisHistory(userId: string, limit: number = 10, offset: number = 0): Promise<{
    analyses: TwitterAnalysisResult[];
    total: number;
  }> {
    try {
      // Ensure limit and offset are valid numbers
      const validLimit = Math.max(1, Math.min(100, Number(limit) || 10));
      const validOffset = Math.max(0, Number(offset) || 0);

      const [analyses, total] = await Promise.all([
        this.prisma.profileAnalysisQuery.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          take: validLimit,
          skip: validOffset,
        }),
        this.prisma.profileAnalysisQuery.count({
          where: { userId },
        }),
      ]);

      return {
        analyses: analyses.map(analysis => ({
          id: analysis.id,
          userId: analysis.userId,
          twitterHandle: analysis.twitterHandle,
          status: analysis.status as any,
          score: analysis.score,
          analysisData: analysis.analysisData as any,
          createdAt: analysis.createdAt,
          updatedAt: analysis.updatedAt,
        })),
        total,
      };

    } catch (error) {
      this.logger.error(`Failed to get user analysis history: ${error.message}`, { userId, error: error.stack });
      throw error;
    }
  }

  /**
   * Fetch Twitter profile data from real API or external service
   */
  private async fetchTwitterProfile(username: string, correlationId: string): Promise<TwitterProfileData> {
    // Try real Twitter API first if available
    if (this.useRealTwitterAPI && this.twitterClient) {
      try {
        return await this.fetchFromRealTwitterAPI(username, correlationId);
      } catch (error) {
        this.logger.warn(`Real Twitter API failed, falling back to external service: ${error.message}`, { username, correlationId });
        // Fall through to external service
      }
    }

    // Fallback to external service (mock or real service)
    return await this.fetchFromExternalService(username, correlationId);
  }

  /**
   * Fetch Twitter profile data from real Twitter API v2
   */
  private async fetchFromRealTwitterAPI(username: string, correlationId: string): Promise<TwitterProfileData> {
    try {
      this.logger.debug(`Fetching from real Twitter API: @${username}`, { correlationId });

      // Remove @ if present
      const cleanUsername = username.replace('@', '');

      // Fetch user data from Twitter API v2
      const user = await this.twitterClient!.v2.userByUsername(cleanUsername, {
        'user.fields': [
          'id',
          'name',
          'username',
          'description',
          'public_metrics',
          'verified',
          'profile_image_url',
          'location',
          'url',
          'created_at'
        ]
      });

      if (!user.data) {
        throw new BadRequestException(`Twitter user @${cleanUsername} not found`);
      }

      return this.transformTwitterAPIUser(user.data);

    } catch (error) {
      this.logger.error(`Failed to fetch from real Twitter API: ${error.message}`, { username, correlationId });
      throw error;
    }
  }

  /**
   * Fetch Twitter profile data from external service
   */
  private async fetchFromExternalService(username: string, correlationId: string): Promise<TwitterProfileData> {
    try {
      this.logger.debug(`Fetching from external service: ${username}`, { correlationId });

      const response = await axios.get(`${this.twitterServiceUrl}/twitter/users/${username}`, {
        timeout: 10000,
        headers: {
          'x-correlation-id': correlationId,
        },
      });

      if (!response.data || response.data.status === 'error') {
        throw new Error(`Twitter profile not found: ${username}`);
      }

      const profileData = response.data.data || response.data;

      // Transform to our format
      return {
        id: profileData.id || `mock_${username}`,
        username: profileData.username || username,
        displayName: profileData.displayName || profileData.display_name || username,
        description: profileData.description || profileData.bio,
        followersCount: profileData.followersCount || profileData.followers_count || 0,
        followingCount: profileData.followingCount || profileData.following_count || 0,
        tweetsCount: profileData.tweetsCount || profileData.tweets_count || 0,
        listedCount: profileData.listedCount || profileData.listed_count || 0,
        verified: profileData.verified || false,
        profileImageUrl: profileData.profileImageUrl || profileData.profile_image_url,
        location: profileData.location,
        website: profileData.website,
        createdAt: profileData.createdAt || profileData.created_at || new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to fetch from external service: ${error.message}`, { username, correlationId });

      // Return mock data for development if external service fails
      if (process.env.NODE_ENV === 'development') {
        this.logger.warn(`Using mock data for Twitter profile: ${username}`, { correlationId });
        return this.generateMockProfileData(username);
      }

      throw error;
    }
  }

  /**
   * Perform comprehensive Twitter analysis
   */
  private async performAnalysis(profileData: TwitterProfileData, correlationId: string): Promise<{
    score: number;
    analysisData: TwitterAnalysisResult['analysisData'];
  }> {
    this.logger.debug(`Performing analysis for: ${profileData.username}`, { correlationId });

    // Calculate account age in days
    const accountAge = Math.floor((Date.now() - new Date(profileData.createdAt).getTime()) / (1000 * 60 * 60 * 24));

    // Calculate engagement rate (simplified)
    const engagementRate = profileData.followersCount > 0 
      ? Math.min(100, (profileData.tweetsCount / profileData.followersCount) * 100)
      : 0;

    // Calculate individual scores
    const followerScore = this.calculateFollowerScore(profileData.followersCount);
    const engagementScore = this.calculateEngagementScore(engagementRate);
    const contentScore = this.calculateContentScore(profileData.tweetsCount, accountAge);
    const activityScore = this.calculateActivityScore(profileData.tweetsCount, accountAge);
    const profileScore = this.calculateProfileScore(profileData);

    // Calculate overall metrics
    const contentQuality = Math.round((contentScore + profileScore) / 2);
    const activityLevel = activityScore;
    const influenceScore = Math.round((followerScore + engagementScore) / 2);
    const authenticity = this.calculateAuthenticity(profileData);
    const engagement = Math.round(engagementScore);

    // Calculate final score (weighted average)
    const finalScore = Math.round(
      (followerScore * 0.25) +
      (engagementScore * 0.20) +
      (contentScore * 0.20) +
      (activityScore * 0.15) +
      (profileScore * 0.10) +
      (authenticity * 0.10)
    );

    // Determine NFT rarity and recommendation
    const nftRecommendation = this.generateNFTRecommendation(finalScore, profileData);

    return {
      score: finalScore,
      analysisData: {
        profile: {
          followerCount: profileData.followersCount,
          followingCount: profileData.followingCount,
          tweetCount: profileData.tweetsCount,
          engagementRate: Math.round(engagementRate * 100) / 100,
          accountAge,
          isVerified: profileData.verified,
          hasProfileImage: !!profileData.profileImageUrl,
          hasBio: !!profileData.description,
        },
        metrics: {
          contentQuality,
          activityLevel,
          influenceScore,
          authenticity,
          engagement,
        },
        breakdown: {
          followerScore,
          engagementScore,
          contentScore,
          activityScore,
          profileScore,
        },
        nftRecommendation,
      },
    };
  }

  /**
   * Calculate follower score (0-100)
   */
  private calculateFollowerScore(followers: number): number {
    if (followers < 100) return 10;
    if (followers < 500) return 25;
    if (followers < 1000) return 40;
    if (followers < 5000) return 60;
    if (followers < 10000) return 75;
    if (followers < 50000) return 85;
    if (followers < 100000) return 90;
    return 95;
  }

  /**
   * Calculate engagement score (0-100)
   */
  private calculateEngagementScore(engagementRate: number): number {
    return Math.min(100, Math.max(0, Math.round(engagementRate * 10)));
  }

  /**
   * Calculate content score based on tweet frequency
   */
  private calculateContentScore(tweets: number, accountAge: number): number {
    if (accountAge === 0) return 50; // Default for new accounts
    
    const tweetsPerDay = tweets / accountAge;
    
    if (tweetsPerDay < 0.1) return 20;
    if (tweetsPerDay < 0.5) return 40;
    if (tweetsPerDay < 1) return 60;
    if (tweetsPerDay < 3) return 80;
    if (tweetsPerDay < 10) return 90;
    return 85; // Too many tweets might indicate spam
  }

  /**
   * Calculate activity score
   */
  private calculateActivityScore(tweets: number, accountAge: number): number {
    return this.calculateContentScore(tweets, accountAge); // Same logic for now
  }

  /**
   * Calculate profile completeness score
   */
  private calculateProfileScore(profile: TwitterProfileData): number {
    let score = 0;
    
    if (profile.description) score += 25;
    if (profile.profileImageUrl) score += 25;
    if (profile.location) score += 15;
    if (profile.website) score += 15;
    if (profile.verified) score += 20;
    
    return Math.min(100, score);
  }

  /**
   * Calculate authenticity score
   */
  private calculateAuthenticity(profile: TwitterProfileData): number {
    let score = 50; // Base score
    
    // Verified accounts get bonus
    if (profile.verified) score += 30;
    
    // Reasonable follower/following ratio
    const ratio = profile.followingCount > 0 ? profile.followersCount / profile.followingCount : 0;
    if (ratio > 0.1 && ratio < 10) score += 10;
    
    // Has profile image and bio
    if (profile.profileImageUrl && profile.description) score += 10;
    
    return Math.min(100, score);
  }

  /**
   * Generate NFT recommendation based on analysis
   */
  private generateNFTRecommendation(score: number, profile: TwitterProfileData): {
    score: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
    reasoning: string[];
  } {
    let rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic' = 'common';
    const reasoning: string[] = [];

    if (score >= 90) {
      rarity = 'mythic';
      reasoning.push('Exceptional social media presence');
    } else if (score >= 80) {
      rarity = 'legendary';
      reasoning.push('Outstanding engagement and influence');
    } else if (score >= 70) {
      rarity = 'epic';
      reasoning.push('Strong social media performance');
    } else if (score >= 50) {
      rarity = 'rare';
      reasoning.push('Good social media activity');
    } else {
      rarity = 'common';
      reasoning.push('Basic social media presence');
    }

    // Add specific reasoning
    if (profile.verified) reasoning.push('Verified account');
    if (profile.followersCount > 10000) reasoning.push('Large follower base');
    if (profile.description) reasoning.push('Complete profile');

    return { score, rarity, reasoning };
  }

  /**
   * Transform Twitter API user to our format
   */
  private transformTwitterAPIUser(user: UserV2): TwitterProfileData {
    return {
      id: user.id,
      username: user.username,
      displayName: user.name,
      description: user.description || '',
      followersCount: user.public_metrics?.followers_count || 0,
      followingCount: user.public_metrics?.following_count || 0,
      tweetsCount: user.public_metrics?.tweet_count || 0,
      listedCount: user.public_metrics?.listed_count || 0,
      verified: user.verified || false,
      profileImageUrl: user.profile_image_url?.replace('_normal', '_400x400') || '',
      location: user.location || '',
      website: user.url || '',
      createdAt: user.created_at || new Date().toISOString(),
    };
  }

  /**
   * Generate mock profile data for development
   */
  private generateMockProfileData(username: string): TwitterProfileData {
    const baseFollowers = Math.floor(Math.random() * 10000) + 100;
    
    return {
      id: `mock_${username}_${Date.now()}`,
      username,
      displayName: `Mock ${username}`,
      description: `Mock Twitter profile for ${username}`,
      followersCount: baseFollowers,
      followingCount: Math.floor(baseFollowers * 0.1) + 50,
      tweetsCount: Math.floor(Math.random() * 1000) + 10,
      listedCount: Math.floor(Math.random() * 100),
      verified: Math.random() > 0.8,
      profileImageUrl: 'https://via.placeholder.com/150',
      location: 'Mock Location',
      website: `https://${username}.com`,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    };
  }

  /**
   * Create query model for fast reads
   */
  private async createQueryModel(analysis: any): Promise<void> {
    try {
      await this.prisma.profileAnalysisQuery.upsert({
        where: { id: analysis.id },
        update: {
          userId: analysis.userId,
          twitterHandle: analysis.twitterHandle,
          status: analysis.status,
          progress: analysis.progress || 1.0,
          score: analysis.score,
          analysisData: analysis.analysisData,
          confidence: analysis.confidence,
          updatedAt: analysis.updatedAt,
        },
        create: {
          id: analysis.id,
          userId: analysis.userId,
          twitterHandle: analysis.twitterHandle,
          platform: analysis.platform || 'twitter',
          analysisType: analysis.analysisType || 'comprehensive',
          status: analysis.status,
          progress: analysis.progress || 1.0,
          score: analysis.score,
          analysisData: analysis.analysisData,
          confidence: analysis.confidence,
          createdAt: analysis.createdAt,
          updatedAt: analysis.updatedAt,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to create query model: ${error.message}`, { analysisId: analysis.id });
      // Don't throw - this is not critical for the main flow
    }
  }
}
