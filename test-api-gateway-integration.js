// Test API Gateway Integration with Project Service
const axios = require('axios');

const API_GATEWAY_URL = 'http://localhost:3010/api';
const PROJECT_SERVICE_URL = 'http://localhost:3005/api';

async function testAPIGatewayIntegration() {
  console.log('🧪 Testing API Gateway Integration with Project Service...\n');
  
  try {
    // Test 1: Direct Project Service Health Check
    console.log('1️⃣ Testing Direct Project Service Health...');
    const directHealth = await axios.get(`${PROJECT_SERVICE_URL}/health`);
    console.log('✅ Direct health check successful:', {
      status: directHealth.data.status,
      service: directHealth.data.service,
      port: directHealth.data.port
    });
    console.log('');
    
    // Test 2: Direct Project Service Enterprise Endpoint
    console.log('2️⃣ Testing Direct Project Service Enterprise Endpoint...');
    const directProjects = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects`);
    console.log('✅ Direct enterprise endpoint successful:', {
      success: directProjects.data.success,
      projectCount: directProjects.data.data.projects.length,
      pagination: directProjects.data.data.pagination
    });
    console.log('');
    
    // Test 3: API Gateway Health Check
    console.log('3️⃣ Testing API Gateway Health...');
    const gatewayHealth = await axios.get(`${API_GATEWAY_URL}/health`);
    console.log('✅ API Gateway health check successful:', {
      status: gatewayHealth.data.status,
      service: gatewayHealth.data.service
    });
    console.log('');
    
    // Test 4: API Gateway Projects Endpoint (The Integration Test)
    console.log('4️⃣ Testing API Gateway → Project Service Integration...');
    try {
      const gatewayProjects = await axios.get(`${API_GATEWAY_URL}/projects`);
      console.log('✅ API Gateway integration successful:', {
        success: gatewayProjects.data.success,
        projectCount: gatewayProjects.data.data.projects.length,
        pagination: gatewayProjects.data.data.pagination
      });
    } catch (error) {
      console.log('❌ API Gateway integration failed:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      });
      
      // Let's try to understand what's happening
      console.log('\n🔍 Debugging API Gateway request...');
      console.log('Request URL:', `${API_GATEWAY_URL}/projects`);
      console.log('Expected to proxy to:', `${PROJECT_SERVICE_URL}/enterprise/projects`);
    }
    console.log('');
    
    // Test 5: Create a Sample Project Through API Gateway
    console.log('5️⃣ Testing Project Creation Through API Gateway...');
    
    const sampleProject = {
      name: 'API Gateway Test Project',
      description: 'Testing project creation through API Gateway',
      ownerId: 'test-owner-gateway',
      category: 'Testing',
      images: ['https://example.com/test-image.jpg'],
      website: 'https://testproject.com',
      socialMediaLinks: {
        twitter: 'https://twitter.com/testproject'
      },
      duration: {
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        timezone: 'UTC'
      },
      analysisConfiguration: {
        fixedParameters: {
          hasBio: { weight: 10, enabled: true },
          hasAvatar: { weight: 5, enabled: true },
          hasBanner: { weight: 5, enabled: true },
          followerCategories: [
            { name: 'Small', min: 0, max: 1000, weight: 5 }
          ],
          engagementRate: { weight: 20, enabled: true },
          accountAge: { weight: 10, enabled: true }
        },
        variableParameters: {
          activityLevel: { weight: 15, enabled: true },
          contentQuality: { weight: 10, enabled: true },
          projectInteractions: { weight: 15, enabled: true },
          referrals: { weight: 5, enabled: true },
          campaignLoyalty: { weight: 10, enabled: true }
        },
        updateFrequencyHours: 24
      },
      nftConfiguration: {
        scoreThresholds: {
          common: 0,
          rare: 50,
          legendary: 80
        },
        design: {
          theme: 'modern',
          style: 'clean',
          mainColor: '#00ff88',
          fixedElements: ['logo']
        },
        evolutionTriggers: [
          { type: 'score_increase', condition: 'score >= 50', targetRarity: 'rare' }
        ],
        blockchainUpdatePolicy: 'batched'
      },
      blockchainNetwork: 'ethereum',
      isPublic: true,
      allowParticipation: true,
      maxParticipants: 500
    };
    
    try {
      const createResponse = await axios.post(`${API_GATEWAY_URL}/projects`, sampleProject, {
        headers: {
          'Content-Type': 'application/json',
          'X-Correlation-ID': 'test-gateway-integration'
        }
      });
      
      console.log('✅ Project creation through API Gateway successful:', {
        success: createResponse.data.success,
        projectId: createResponse.data.data.id,
        projectName: createResponse.data.data.name,
        status: createResponse.data.data.status
      });
      
      // Test 6: Retrieve the Created Project
      console.log('\n6️⃣ Testing Project Retrieval Through API Gateway...');
      const projectId = createResponse.data.data.id;
      const retrieveResponse = await axios.get(`${API_GATEWAY_URL}/projects/${projectId}?includeConfig=true`);
      
      console.log('✅ Project retrieval through API Gateway successful:', {
        success: retrieveResponse.data.success,
        projectId: retrieveResponse.data.data.id,
        projectName: retrieveResponse.data.data.name,
        hasConfiguration: !!retrieveResponse.data.data.analysisConfiguration
      });
      
      console.log('\n🎉 All API Gateway integration tests passed successfully!');
      
    } catch (error) {
      console.log('❌ Project creation through API Gateway failed:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      });
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testAPIGatewayIntegration().catch(console.error);
