# Troubleshooting Guide

## Overview
This guide provides solutions to common issues encountered during development of the Social NFT Platform v2.

**Last Updated:** May 25, 2025
**Scope:** Development Environment Issues

## Service Startup Issues

### 1. Port Already in Use

#### Problem
```
Error: listen EADDRINUSE: address already in use :::3001
```

#### Cause
Another process is already using the required port.

#### Solution (Windows)
```bash
# Find process using the port
netstat -ano | findstr :3001

# Kill the process (replace PID with actual process ID)
taskkill /PID 1234 /F
```

#### Solution (Linux/Mac)
```bash
# Find process using the port
lsof -i :3001

# Kill the process
kill -9 PID
```

#### Prevention
- Always stop services properly with Ctrl+C
- Use different ports for different environments
- Check running processes before starting services

### 2. Database Connection Failed

#### Problem
```
Error: Connection terminated unexpectedly
Error: password authentication failed for user "postgres"
```

#### Cause
- PostgreSQL service not running
- Incorrect database credentials
- Database doesn't exist
- Network connectivity issues

#### Solution Steps
1. **Verify PostgreSQL is running:**
   ```bash
   # Windows
   sc query postgresql-x64-14

   # Linux
   sudo systemctl status postgresql
   ```

2. **Check database exists:**
   ```sql
   psql -U postgres -l
   ```

3. **Test connection manually:**
   ```bash
   psql -h localhost -p 5432 -U your_username -d social_nft_platform
   ```

4. **Verify environment variables:**
   ```bash
   # Check .env file in service directory
   cat services/user-service/.env
   ```

5. **Create database if missing:**
   ```sql
   CREATE DATABASE social_nft_platform;
   CREATE USER social_nft_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE social_nft_platform TO social_nft_user;
   ```

### 3. Module Not Found Errors

#### Problem
```
Error: Cannot find module '@nestjs/common'
Error: Cannot find module '../shared/types'
```

#### Cause
- Dependencies not installed
- Corrupted node_modules
- Incorrect import paths
- Version conflicts

#### Solution
1. **Reinstall dependencies:**
   ```bash
   cd services/user-service
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Clear npm cache:**
   ```bash
   npm cache clean --force
   ```

3. **Check Node.js version:**
   ```bash
   node --version  # Should be 18.x or higher
   ```

4. **Verify package.json:**
   ```bash
   cat package.json | grep dependencies
   ```

### 4. TypeScript Compilation Errors

#### Problem
```
Error: Cannot find name 'Express'
Error: Property 'user' does not exist on type 'Request'
```

#### Cause
- Missing type definitions
- Incorrect TypeScript configuration
- Version mismatches

#### Solution
1. **Install missing types:**
   ```bash
   npm install --save-dev @types/express @types/node
   ```

2. **Clear TypeScript cache:**
   ```bash
   rm -rf dist/
   npx tsc --build --clean
   ```

3. **Check tsconfig.json:**
   ```json
   {
     "compilerOptions": {
       "target": "ES2020",
       "module": "commonjs",
       "strict": true,
       "esModuleInterop": true
     }
   }
   ```

### 5. Environment Variable Issues

#### Problem
```
Error: JWT_SECRET is not defined
Error: Database connection string is invalid
```

#### Cause
- Missing .env file
- Incorrect variable names
- Environment not loaded

#### Solution
1. **Check .env file exists:**
   ```bash
   ls -la services/user-service/.env
   ```

2. **Verify variable names:**
   ```bash
   # Check for typos in variable names
   cat .env | grep JWT_SECRET
   ```

3. **Test environment loading:**
   ```javascript
   console.log('JWT_SECRET:', process.env.JWT_SECRET);
   ```

4. **Copy from example:**
   ```bash
   cp .env.example .env
   ```

### 6. Service Communication Errors

#### Problem
```
Error: connect ECONNREFUSED 127.0.0.1:3001
Error: Service 'user-service' is not responding
```

#### Cause
- Service not started
- Wrong service URL
- Network issues
- Service crashed

#### Solution
1. **Check service status:**
   ```bash
   curl http://localhost:3001/health
   ```

2. **Verify service URLs in API Gateway:**
   ```typescript
   // services/api-gateway/src/app.service.ts
   private readonly services = [
     { name: 'user-service', url: 'http://localhost:3001' }
   ];
   ```

3. **Check service logs:**
   ```bash
   # Look for error messages in service terminal
   ```

4. **Restart services in order:**
   ```bash
   # 1. User Service
   # 2. Profile Analysis Service
   # 3. API Gateway
   ```

## Quick Diagnostic Commands

### Check All Services
```bash
# API Gateway health (includes all services)
curl http://localhost:3010/api/health

# Individual service health
curl http://localhost:3001/health
curl http://localhost:3002/health
```

### Check Ports
```bash
# Windows
netstat -ano | findstr "3001 3002 3010 3100"

# Linux/Mac
lsof -i :3001,:3002,:3010,:3100
```

### Check Processes
```bash
# Windows
tasklist | findstr node

# Linux/Mac
ps aux | grep node
```

### Check Database
```bash
# Test PostgreSQL connection
psql -h localhost -p 5432 -U postgres -c "SELECT version();"
```

## Frontend-Specific Issues

### 1. CORS Errors

#### Problem
```
Access to XMLHttpRequest at 'http://localhost:3010/api/users/register'
from origin 'http://localhost:3100' has been blocked by CORS policy
```

#### Cause
- API Gateway CORS not configured for frontend origin
- Missing CORS headers
- Preflight request failing

#### Solution
1. **Check API Gateway CORS configuration:**
   ```typescript
   // services/api-gateway/src/main.ts
   app.enableCors({
     origin: [
       'http://localhost:3100',  // Frontend origin
       'http://localhost:3000'
     ],
     credentials: true,
     methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allowedHeaders: ['Content-Type', 'Authorization', 'Accept']
   });
   ```

2. **Test CORS preflight:**
   ```bash
   curl -X OPTIONS \
     -H "Origin: http://localhost:3100" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     http://localhost:3010/api/users/register
   ```

3. **Restart API Gateway after changes**

### 2. Network Connection Refused

#### Problem
```
AxiosError: Network Error
code: "ERR_NETWORK"
net::ERR_CONNECTION_REFUSED
```

#### Cause
- API Gateway not running
- Wrong API URL in frontend
- Browser cache issues

#### Solution
1. **Verify API Gateway is running:**
   ```bash
   curl http://localhost:3010/api/health
   ```

2. **Check frontend API configuration:**
   ```typescript
   // frontend/src/config/api.ts
   export const API_CONFIG = {
     API_GATEWAY: 'http://localhost:3010'
   };
   ```

3. **Clear browser cache:**
   - Open DevTools (F12)
   - Right-click refresh → "Empty Cache and Hard Reload"
   - Or use incognito window

### 3. Authentication Token Issues

#### Problem
```
Error: Token is invalid
Error: User is not authenticated
```

#### Cause
- Token expired
- Token not stored properly
- Token format incorrect

#### Solution
1. **Check token in localStorage:**
   ```javascript
   // In browser console
   console.log(localStorage.getItem('authToken'));
   ```

2. **Verify token format:**
   ```javascript
   // Should be JWT format: header.payload.signature
   const token = localStorage.getItem('authToken');
   console.log(token.split('.').length); // Should be 3
   ```

3. **Clear and re-authenticate:**
   ```javascript
   localStorage.removeItem('authToken');
   // Login again
   ```

### 4. React Component Errors

#### Problem
```
Error: Cannot read property 'map' of undefined
Error: Objects are not valid as a React child
```

#### Cause
- Async data not loaded
- Incorrect data structure
- Missing error handling

#### Solution
1. **Add loading states:**
   ```typescript
   const [loading, setLoading] = useState(true);
   const [data, setData] = useState([]);

   if (loading) return <div>Loading...</div>;
   ```

2. **Add null checks:**
   ```typescript
   {data && data.map(item => (
     <div key={item.id}>{item.name}</div>
   ))}
   ```

3. **Add error boundaries:**
   ```typescript
   try {
     // API call
   } catch (error) {
     console.error('API Error:', error);
     setError(error.message);
   }
   ```

## Emergency Recovery Commands

### Complete Reset
```bash
# Stop all services (Ctrl+C in each terminal)

# Clear all node_modules
find . -name "node_modules" -type d -exec rm -rf {} +
find . -name "package-lock.json" -delete

# Reinstall everything
npm run install:all

# Restart services
npm run dev:user-service      # Terminal 1
npm run dev:analysis-service  # Terminal 2
npm run dev:api-gateway      # Terminal 3
npm run dev:frontend         # Terminal 4
```

### Database Reset
```sql
-- Connect as superuser
DROP DATABASE IF EXISTS social_nft_platform;
CREATE DATABASE social_nft_platform;
GRANT ALL PRIVILEGES ON DATABASE social_nft_platform TO social_nft_user;
```

### Browser Reset
```javascript
// Clear all localStorage
localStorage.clear();

// Clear all sessionStorage
sessionStorage.clear();

// Hard refresh
location.reload(true);
```
