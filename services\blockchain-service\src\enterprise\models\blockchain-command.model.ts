// Enterprise Blockchain Command Model (Write Side) - Template
import { IsString, IsOptional, IsEnum, IsInt } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum TransactionType {
  MINT = 'mint',
  TRANSFER = 'transfer',
  BURN = 'burn',
  APPROVE = 'approve'
}

export class CreateTransactionCommandDto {
  @ApiProperty({ description: 'Transaction type', enum: TransactionType })
  @IsEnum(TransactionType)
  transactionType: TransactionType;

  @ApiProperty({ description: 'Smart contract address' })
  @IsString()
  contractAddress: string;

  @ApiPropertyOptional({ description: 'NFT token ID' })
  @IsOptional()
  @IsString()
  tokenId?: string;
}
