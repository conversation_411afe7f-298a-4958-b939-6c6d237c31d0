# Production Twitter Authentication Implementation Guide

## Step 1: Real Twitter API Integration

### 1.1 Twitter Developer Setup
```bash
# Required Twitter API credentials
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_BEARER_TOKEN=your_bearer_token
TWITTER_CALLBACK_URL=http://localhost:3010/api/auth/twitter/callback
```

### 1.2 Update Profile Analysis Service

#### Enhanced Twitter Auth Controller
```typescript
// services/profile-analysis-service/src/enterprise/controllers/twitter-auth.controller.ts

@Get('login')
async initiateTwitterAuth(@Res() res: Response) {
  const useMockTwitter = process.env.USE_MOCK_SERVICES === 'true';
  
  if (!useMockTwitter) {
    // Real Twitter OAuth 2.0 with PKCE
    const state = crypto.randomBytes(32).toString('hex');
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto.createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
    
    // Store state and code verifier in Redis/session
    await this.storeOAuthState(state, codeVerifier);
    
    const twitterAuthUrl = new URL('https://twitter.com/i/oauth2/authorize');
    twitterAuthUrl.searchParams.set('response_type', 'code');
    twitterAuthUrl.searchParams.set('client_id', process.env.TWITTER_CLIENT_ID);
    twitterAuthUrl.searchParams.set('redirect_uri', process.env.TWITTER_CALLBACK_URL);
    twitterAuthUrl.searchParams.set('scope', 'tweet.read users.read');
    twitterAuthUrl.searchParams.set('state', state);
    twitterAuthUrl.searchParams.set('code_challenge', codeChallenge);
    twitterAuthUrl.searchParams.set('code_challenge_method', 'S256');
    
    return res.redirect(twitterAuthUrl.toString());
  }
  // ... mock implementation
}

@Get('callback')
async handleTwitterCallback(
  @Query('code') code: string,
  @Query('state') state: string,
  @Res() res: Response
) {
  const useMockTwitter = process.env.USE_MOCK_SERVICES === 'true';
  
  if (!useMockTwitter) {
    // Verify state and get code verifier
    const codeVerifier = await this.getOAuthState(state);
    if (!codeVerifier) {
      throw new UnauthorizedException('Invalid OAuth state');
    }
    
    // Exchange code for access token
    const tokenResponse = await this.exchangeCodeForToken(code, codeVerifier);
    
    // Get user data from Twitter
    const userData = await this.getTwitterUserData(tokenResponse.access_token);
    
    // Store user in database
    const user = await this.userService.createOrUpdateTwitterUser(userData);
    
    // Generate JWT token
    const jwtToken = await this.authService.generateJWT(user);
    
    // Redirect to frontend with token
    const frontendUrl = `${process.env.FRONTEND_URL}/auth/twitter/callback?token=${jwtToken}`;
    return res.redirect(frontendUrl);
  }
  // ... mock implementation
}

private async exchangeCodeForToken(code: string, codeVerifier: string) {
  const tokenUrl = 'https://api.twitter.com/2/oauth2/token';
  const response = await axios.post(tokenUrl, {
    grant_type: 'authorization_code',
    client_id: process.env.TWITTER_CLIENT_ID,
    client_secret: process.env.TWITTER_CLIENT_SECRET,
    code,
    redirect_uri: process.env.TWITTER_CALLBACK_URL,
    code_verifier: codeVerifier
  }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  });
  
  return response.data;
}

private async getTwitterUserData(accessToken: string) {
  const userUrl = 'https://api.twitter.com/2/users/me?user.fields=profile_image_url,public_metrics,verified';
  const response = await axios.get(userUrl, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  
  return response.data.data;
}
```

## Step 2: Backend JWT Authentication

### 2.1 JWT Service Implementation
```typescript
// services/user-service/src/auth/jwt.service.ts

@Injectable()
export class JwtService {
  constructor(
    @Inject('JWT_SECRET') private jwtSecret: string,
    private userService: UserService
  ) {}

  async generateJWT(user: User): Promise<string> {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      provider: user.provider,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    return jwt.sign(payload, this.jwtSecret, { algorithm: 'HS256' });
  }

  async verifyJWT(token: string): Promise<User | null> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      const user = await this.userService.findById(decoded.sub);
      return user;
    } catch (error) {
      return null;
    }
  }

  async refreshToken(refreshToken: string): Promise<string | null> {
    // Implement refresh token logic
    const user = await this.validateRefreshToken(refreshToken);
    if (user) {
      return this.generateJWT(user);
    }
    return null;
  }
}
```

### 2.2 Authentication Middleware
```typescript
// services/api-gateway/src/middleware/auth.middleware.ts

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private jwtService: JwtService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = this.extractTokenFromHeader(req);
    
    if (token) {
      const user = await this.jwtService.verifyJWT(token);
      if (user) {
        req['user'] = user;
      }
    }
    
    next();
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

## Step 3: Database User Storage

### 3.1 User Entity (Prisma Schema)
```prisma
// services/user-service/prisma/schema.prisma

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  displayName String?
  profileImage String?
  provider    String   // 'twitter', 'google', etc.
  providerId  String   // Twitter user ID
  isVerified  Boolean  @default(false)
  
  // Twitter-specific data
  twitterHandle    String?
  followerCount    Int?
  followingCount   Int?
  tweetCount       Int?
  
  // Authentication
  refreshTokens RefreshToken[]
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLoginAt DateTime?
  
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  @@map("refresh_tokens")
}
```

### 3.2 User Service Implementation
```typescript
// services/user-service/src/user/user.service.ts

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async createOrUpdateTwitterUser(twitterData: any): Promise<User> {
    const existingUser = await this.prisma.user.findUnique({
      where: { providerId: twitterData.id }
    });

    if (existingUser) {
      // Update existing user
      return this.prisma.user.update({
        where: { id: existingUser.id },
        data: {
          displayName: twitterData.name,
          username: twitterData.username,
          profileImage: twitterData.profile_image_url,
          followerCount: twitterData.public_metrics?.followers_count,
          followingCount: twitterData.public_metrics?.following_count,
          tweetCount: twitterData.public_metrics?.tweet_count,
          isVerified: twitterData.verified,
          lastLoginAt: new Date()
        }
      });
    } else {
      // Create new user
      return this.prisma.user.create({
        data: {
          email: `${twitterData.username}@twitter.placeholder`,
          username: twitterData.username,
          displayName: twitterData.name,
          profileImage: twitterData.profile_image_url,
          provider: 'twitter',
          providerId: twitterData.id,
          twitterHandle: twitterData.username,
          followerCount: twitterData.public_metrics?.followers_count,
          followingCount: twitterData.public_metrics?.following_count,
          tweetCount: twitterData.public_metrics?.tweet_count,
          isVerified: twitterData.verified,
          lastLoginAt: new Date()
        }
      });
    }
  }

  async findById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({ where: { id } });
  }

  async findByProviderId(providerId: string): Promise<User | null> {
    return this.prisma.user.findUnique({ where: { providerId } });
  }
}
```

## Step 4: Token Refresh Logic

### 4.1 Refresh Token Service
```typescript
// services/user-service/src/auth/refresh-token.service.ts

@Injectable()
export class RefreshTokenService {
  constructor(private prisma: PrismaService) {}

  async createRefreshToken(userId: string): Promise<string> {
    const token = crypto.randomBytes(64).toString('hex');
    const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

    await this.prisma.refreshToken.create({
      data: {
        token,
        userId,
        expiresAt
      }
    });

    return token;
  }

  async validateRefreshToken(token: string): Promise<User | null> {
    const refreshToken = await this.prisma.refreshToken.findUnique({
      where: { token },
      include: { user: true }
    });

    if (!refreshToken || refreshToken.expiresAt < new Date()) {
      if (refreshToken) {
        await this.prisma.refreshToken.delete({ where: { id: refreshToken.id } });
      }
      return null;
    }

    return refreshToken.user;
  }

  async revokeRefreshToken(token: string): Promise<void> {
    await this.prisma.refreshToken.deleteMany({ where: { token } });
  }

  async revokeAllUserTokens(userId: string): Promise<void> {
    await this.prisma.refreshToken.deleteMany({ where: { userId } });
  }
}
```

### 4.2 Frontend Token Management
```typescript
// frontend-headless/src/lib/auth-token.ts

class AuthTokenManager {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  async refreshAccessToken(): Promise<string | null> {
    if (!this.refreshToken) return null;

    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: this.refreshToken })
      });

      if (response.ok) {
        const data = await response.json();
        this.setTokens(data.accessToken, data.refreshToken);
        return data.accessToken;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    this.clearTokens();
    return null;
  }

  setTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
}
```

## Implementation Priority

1. **Phase 1**: Database User Storage (Foundation)
2. **Phase 2**: Backend JWT Authentication (Security)
3. **Phase 3**: Real Twitter API Integration (External)
4. **Phase 4**: Token Refresh Logic (UX Enhancement)

## Environment Configuration

```bash
# Production Environment Variables
USE_MOCK_SERVICES=false
TWITTER_CLIENT_ID=your_real_client_id
TWITTER_CLIENT_SECRET=your_real_client_secret
TWITTER_BEARER_TOKEN=your_bearer_token
JWT_SECRET=your_super_secure_jwt_secret
DATABASE_URL=postgresql://user:password@localhost:5432/social_nft_platform
REDIS_URL=redis://localhost:6379
FRONTEND_URL=http://localhost:3000
```

This guide provides a complete roadmap for transitioning from mock authentication to a production-ready system with real Twitter integration, secure JWT handling, and proper user management.
