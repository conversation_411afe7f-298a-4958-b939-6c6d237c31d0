-- Enterprise Social NFT Platform - Database Initialization
-- Create all service databases

-- User Service Database
CREATE DATABASE user_service;

-- Profile Analysis Service Database
CREATE DATABASE profile_analysis_service;

-- NFT Generation Service Database
CREATE DATABASE nft_generation_service;

-- Blockchain Service Database
CREATE DATABASE blockchain_service;

-- Project Service Database
CREATE DATABASE project_service;

-- Marketplace Service Database
CREATE DATABASE marketplace_service;

-- Analytics Service Database
CREATE DATABASE analytics_service;

-- Notification Service Database
CREATE DATABASE notification_service;

-- Grant permissions to postgres user
GRANT ALL PRIVILEGES ON DATABASE user_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE profile_analysis_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE nft_generation_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE blockchain_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE project_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE marketplace_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE analytics_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE notification_service TO postgres;
