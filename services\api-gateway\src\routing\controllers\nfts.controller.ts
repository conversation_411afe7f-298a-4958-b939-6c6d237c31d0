import { Controller, Get, Post, Patch, Delete, Body, Param, Query, Headers, Req, Res, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('NFTs')
@Controller('nfts')
export class NftsController {
  constructor(private readonly proxyService: ProxyService) {}

  @Post('generate-from-analysis')
  @ApiOperation({ summary: 'Generate NFT from Profile Analysis results' })
  @ApiResponse({ status: 201, description: 'NFT generated successfully from profile analysis' })
  @ApiResponse({ status: 400, description: 'Invalid input or analysis not found' })
  @ApiResponse({ status: 404, description: 'Profile analysis not found' })
  async generateNftFromAnalysis(@Body() generateDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('🎨 API Gateway: NFT generation from analysis request received');
      console.log('📊 API Gateway: Generate DTO:', generateDto);

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nfts/generate-from-analysis',
        'POST',
        generateDto,
        req.headers,
        req.query
      );

      console.log('✅ API Gateway: NFT Generation Service response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: NFT generation error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'NFT generation failed',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all NFTs with optional filtering' })
  @ApiResponse({ status: 200, description: 'NFTs retrieved successfully' })
  async getNfts(@Query() query: any, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('📊 API Gateway: Get NFTs request received');

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nfts',
        'GET',
        null,
        req.headers,
        query
      );

      console.log('✅ API Gateway: NFT query response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: NFT query error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to retrieve NFTs',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific NFT by ID' })
  @ApiResponse({ status: 200, description: 'NFT retrieved successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async getNft(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('🔍 API Gateway: Get NFT by ID request:', id);

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nfts/${id}`,
        'GET',
        null,
        req.headers,
        req.query
      );

      console.log('✅ API Gateway: NFT by ID response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: NFT by ID error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to retrieve NFT',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Post()
  @ApiOperation({ summary: 'Create new NFT' })
  @ApiResponse({ status: 201, description: 'NFT created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createNft(@Body() createNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('🎨 API Gateway: Create NFT request received');

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        '/api/nfts',
        'POST',
        createNftDto,
        req.headers,
        req.query
      );

      console.log('✅ API Gateway: Create NFT response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Create NFT error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to create NFT',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update NFT' })
  @ApiResponse({ status: 200, description: 'NFT updated successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async updateNft(@Param('id') id: string, @Body() updateNftDto: any, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('🔄 API Gateway: Update NFT request:', id);

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nfts/${id}`,
        'PUT',
        updateNftDto,
        req.headers,
        req.query
      );

      console.log('✅ API Gateway: Update NFT response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Update NFT error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to update NFT',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete NFT' })
  @ApiResponse({ status: 200, description: 'NFT deleted successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async deleteNft(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('🗑️ API Gateway: Delete NFT request:', id);

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nfts/${id}`,
        'DELETE',
        null,
        req.headers,
        req.query
      );

      console.log('✅ API Gateway: Delete NFT response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: Delete NFT error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to delete NFT',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('user/:userId/history')
  @ApiOperation({ summary: 'Get user NFT generation history' })
  @ApiResponse({ status: 200, description: 'NFT generation history retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid userId' })
  async getUserNftHistory(@Param('userId') userId: string, @Query() query: any, @Req() req: Request, @Res() res: Response) {
    try {
      console.log('📊 API Gateway: Get user NFT history request:', userId);

      const response = await this.proxyService.forwardRequest(
        'nft-generation-service',
        `/api/nfts/user/${userId}/history`,
        'GET',
        null,
        req.headers,
        query
      );

      console.log('✅ API Gateway: User NFT history response:', response.status);
      return res.status(response.status).json(response.data);
    } catch (error) {
      console.error('❌ API Gateway: User NFT history error:', error);
      return res.status(error.status || HttpStatus.BAD_GATEWAY).json({
        message: error.message || 'Failed to retrieve NFT history',
        service: 'nft-generation-service',
        timestamp: new Date().toISOString()
      });
    }
  }
}
