// Service-specific base URLs
export const SERVICES = {
  USER_SERVICE: process.env.NEXT_PUBLIC_USER_SERVICE_URL || 'http://localhost:3011',
  PROJECT_SERVICE: process.env.NEXT_PUBLIC_PROJECT_SERVICE_URL || 'http://localhost:3005',
  NFT_SERVICE: process.env.NEXT_PUBLIC_NFT_SERVICE_URL || 'http://localhost:3003',
  PROFILE_SERVICE: process.env.NEXT_PUBLIC_PROFILE_SERVICE_URL || 'http://localhost:3002',
  API_GATEWAY: process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'
};

export const API_CONFIG = {
  BASE_URL: SERVICES.API_GATEWAY, // Use API Gateway for all requests (production-like)
  SERVICES,
  ENDPOINTS: {
    AUTH: {
      REGISTER: '/api/auth/register',  // API Gateway routes with /api prefix
      LOGIN: '/api/auth/login',
      PROFILE: '/api/auth/profile',
      LOGOUT: '/api/auth/logout'
    },
    CAMPAIGNS: {
      LIST: '/api/campaigns',
      DETAILS: '/api/campaigns',
      JOIN: '/api/campaigns'
    },
    NFTS: {
      USER_NFTS: '/api/nft-generation/user',
      GENERATE: '/api/nft-generation/generate',
      UPDATE: '/api/nft-generation/update'
    },
    ANALYSIS: {
      HISTORY: '/api/analysis/history',
      ANALYZE: '/api/analysis/twitter-profile'
    },
    HEALTH: '/health'
  }
};

export const API_TIMEOUT = 10000; // 10 seconds
