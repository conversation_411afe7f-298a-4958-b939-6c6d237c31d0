'use client'

'use client';

import { useState } from 'react'
import Layout from '@/components/layout/layout'
import { ProtectedRoute } from '@/contexts/auth-context'
import BasicAnalytics from '@/components/dashboard/basic-analytics'
import KaitoStyleAnalytics from '@/components/dashboard/kaito-style-analytics'

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('clean')

  return (
    <ProtectedRoute>
      <Layout>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Tab Navigation */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('clean')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'clean'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Clean Implementation
                  </button>
                  <button
                    onClick={() => setActiveTab('kaito')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'kaito'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Kaito-Style Implementation
                  </button>
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'clean' && <BasicAnalytics />}
            {activeTab === 'kaito' && <KaitoStyleAnalytics />}
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
