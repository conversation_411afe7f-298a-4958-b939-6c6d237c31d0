'use client'

import {
  <PERSON>,
  But<PERSON>,
  Container,
  Heading,
  Text,
  VStack,
  HStack
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function NotFound() {
  const router = useRouter()

  return (
    <Container maxW="container.md" py={20}>
      <VStack gap={8} textAlign="center">
        {/* 404 Visual */}
        <Box>
          <Text fontSize="8xl" fontWeight="bold" color="blue.500" lineHeight="1">
            404
          </Text>
          <Heading as="h1" size="xl" mt={4}>
            Page Not Found
          </Heading>
        </Box>

        {/* Description */}
        <VStack gap={4}>
          <Text fontSize="lg" color="gray.600" maxW="md">
            Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you entered the wrong URL.
          </Text>
        </VStack>

        {/* Action Buttons */}
        <HStack gap={4} wrap="wrap" justify="center">
          <Button colorScheme="blue" size="lg" onClick={() => router.back()}>
            Go Back
          </Button>
          
          <Link href="/dashboard">
            <Button variant="outline" size="lg">
              Go to Dashboard
            </Button>
          </Link>
          
          <Link href="/campaigns">
            <Button variant="ghost" size="lg">
              Browse Campaigns
            </Button>
          </Link>
        </HStack>

        {/* Help Text */}
        <Box pt={8} borderTop="1px" borderColor="gray.200" w="full">
          <Text fontSize="sm" color="gray.500">
            Need help? Check out our{' '}
            <Link href="#" style={{ color: 'blue', textDecoration: 'underline' }}>
              help center
            </Link>{' '}
            or{' '}
            <Link href="#" style={{ color: 'blue', textDecoration: 'underline' }}>
              contact support
            </Link>
            .
          </Text>
        </Box>
      </VStack>
    </Container>
  )
}
