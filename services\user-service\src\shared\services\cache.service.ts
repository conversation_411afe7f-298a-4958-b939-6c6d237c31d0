import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private redis: Redis;
  private isRedisAvailable = false;
  private lastConnectionAttempt = 0;
  private connectionRetryInterval = 30000; // 30 seconds

  // In-memory fallback for development when Redis is unavailable
  private memoryCache = new Map<string, { value: any; expiry: number }>();
  private cleanupInterval: NodeJS.Timeout;

  constructor(private readonly configService: ConfigService) {
    this.initializeRedis();
    this.initializeMemoryCache();
  }

  private initializeRedis(): void {
    try {
      const redisUrl = this.configService.get<string>('REDIS_URL', 'redis://localhost:6379');

      this.redis = new Redis(redisUrl, {
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
      });

      this.redis.on('connect', () => {
        this.isRedisAvailable = true;
        this.logger.log('✅ Connected to Redis cache');
      });

      this.redis.on('error', (error) => {
        this.isRedisAvailable = false;
        // Only log Redis errors every 30 seconds to reduce noise
        const now = Date.now();
        if (now - this.lastConnectionAttempt > this.connectionRetryInterval) {
          this.logger.warn('❌ Redis unavailable - running without cache (will retry in 30s)');
          this.lastConnectionAttempt = now;
        }
      });

      this.redis.on('close', () => {
        this.isRedisAvailable = false;
        this.logger.debug('🔌 Redis connection closed');
      });

    } catch (error) {
      this.logger.error('Failed to initialize Redis:', error);
    }
  }

  private initializeMemoryCache(): void {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.expiry < now) {
          this.memoryCache.delete(key);
        }
      }
    }, 5 * 60 * 1000);

    this.logger.log('🧠 In-memory cache fallback initialized');
  }

  async get<T>(key: string): Promise<T | null> {
    if (!this.isRedisAvailable) {
      // Use memory cache fallback
      const entry = this.memoryCache.get(key);
      if (entry && entry.expiry > Date.now()) {
        this.logger.debug(`Memory cache hit for key: ${key}`);
        return entry.value;
      }
      return null;
    }

    try {
      const value = await this.redis.get(key);

      if (!value) {
        return null;
      }

      const parsed = JSON.parse(value);

      this.logger.debug(`Redis cache hit for key: ${key}`);
      return parsed;

    } catch (error) {
      this.isRedisAvailable = false;
      this.logger.debug(`Redis get error for key ${key} - falling back to memory cache`);

      // Fallback to memory cache
      const entry = this.memoryCache.get(key);
      if (entry && entry.expiry > Date.now()) {
        this.logger.debug(`Memory cache fallback hit for key: ${key}`);
        return entry.value;
      }
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds: number = 300): Promise<void> {
    if (!this.isRedisAvailable) {
      // Use memory cache fallback
      const expiry = Date.now() + (ttlSeconds * 1000);
      this.memoryCache.set(key, { value, expiry });
      this.logger.debug(`Memory cache set for key: ${key}, TTL: ${ttlSeconds}s`);
      return;
    }

    try {
      const serialized = JSON.stringify(value);
      await this.redis.setex(key, ttlSeconds, serialized);

      this.logger.debug(`Redis cache set for key: ${key}, TTL: ${ttlSeconds}s`);

    } catch (error) {
      this.isRedisAvailable = false;
      this.logger.debug(`Redis set error for key ${key} - falling back to memory cache`);

      // Fallback to memory cache
      const expiry = Date.now() + (ttlSeconds * 1000);
      this.memoryCache.set(key, { value, expiry });
      this.logger.debug(`Memory cache fallback set for key: ${key}, TTL: ${ttlSeconds}s`);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
      this.logger.debug(`Cache deleted for key: ${key}`);

    } catch (error) {
      this.logger.error(`Cache delete error for key ${key}:`, error);
    }
  }

  async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.logger.debug(`Cache deleted ${keys.length} keys matching pattern: ${pattern}`);
      }

    } catch (error) {
      this.logger.error(`Cache delete pattern error for ${pattern}:`, error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;

    } catch (error) {
      this.logger.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);

    } catch (error) {
      this.logger.error(`Cache TTL error for key ${key}:`, error);
      return -1;
    }
  }

  async healthCheck(): Promise<{ status: string; latency?: number }> {
    try {
      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;

      return {
        status: 'healthy',
        latency,
      };

    } catch (error) {
      this.logger.error('Cache health check failed:', error);
      return {
        status: 'unhealthy',
      };
    }
  }

  async getStats(): Promise<any> {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        memory: this.parseRedisInfo(info),
        keyspace: this.parseRedisInfo(keyspace),
      };

    } catch (error) {
      this.logger.error('Failed to get cache stats:', error);
      return null;
    }
  }

  private parseRedisInfo(info: string): any {
    const result: any = {};
    
    info.split('\r\n').forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    });

    return result;
  }

  async onModuleDestroy(): Promise<void> {
    try {
      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Clear memory cache
      this.memoryCache.clear();

      // Disconnect from Redis
      if (this.redis) {
        await this.redis.quit();
        this.logger.log('🔌 Disconnected from Redis cache');
      }

      this.logger.log('🧠 Memory cache cleared');
    } catch (error) {
      this.logger.error('Error disconnecting from cache:', error);
    }
  }
}
