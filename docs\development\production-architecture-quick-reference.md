# Production-like Architecture Quick Reference

## 🚀 Quick Start

### Service URLs
```
Frontend:     http://localhost:3000
API Gateway:  http://localhost:3010  ← ALL frontend requests go here
User Service: http://localhost:3011
Profile:      http://localhost:3002
Project:      http://localhost:3005
NFT:          http://localhost:3003
```

### ✅ DO's
- Use API Gateway for ALL requests
- Use real JWT tokens
- Store data in PostgreSQL
- Handle all error states
- Document violations immediately

### ❌ DON'Ts
- Direct service calls
- Mock token logic
- Mixed implementations
- localStorage authentication fallbacks
- Silent error handling

## 🔧 Code Templates

### API Configuration
```typescript
export const API_CONFIG = {
  BASE_URL: 'http://localhost:3010', // API Gateway
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      PROFILE: '/api/auth/profile'
    }
  }
};
```

### Authenticated Request
```typescript
const response = await axios.get(
  `${API_CONFIG.BASE_URL}/api/auth/profile`,
  { headers: { 'Authorization': `Bearer ${token}` } }
);
```

### Error Handling
```typescript
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  if (error.response?.status === 401) {
    // Redirect to login
    router.push('/auth/login');
  }
  throw error;
}
```

## 📋 Common Patterns

### Authentication Flow
1. User action → Frontend
2. Frontend → API Gateway
3. API Gateway → User Service
4. User Service → Database
5. Real JWT token returned
6. Token used for subsequent requests

### Protected Route
```typescript
const ProtectedPage = () => {
  const { user, isLoading } = useAuth();
  
  if (isLoading) return <Loading />;
  if (!user) return <Redirect to="/auth/login" />;
  
  return <PageContent />;
};
```
