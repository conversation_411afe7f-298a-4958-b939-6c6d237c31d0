import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TwitterService } from './twitter.service';
import { TwitterUser } from '../interfaces/twitter.interfaces';

@ApiTags('twitter')
@Controller('twitter')
export class TwitterController {
  constructor(private readonly twitterService: TwitterService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check' })
  async healthCheck() {
    return this.twitterService.healthCheck();
  }

  @Get('users')
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async getAllUsers() {
    return {
      status: 'success',
      data: await this.twitterService.getAllUsers(),
      message: 'Users retrieved successfully'
    };
  }

  @Get('users/:id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  async getUserById(@Param('id') id: string) {
    const user = await this.twitterService.getUserById(id);
    return {
      status: user ? 'success' : 'error',
      data: user,
      message: user ? 'User retrieved successfully' : 'User not found'
    };
  }
}
