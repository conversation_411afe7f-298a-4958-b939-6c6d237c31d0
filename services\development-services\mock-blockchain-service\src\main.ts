import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for development
  app.enableCors({
    origin: ['http://localhost:3000', 'http://localhost:3010'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Mock Blockchain Service')
    .setDescription('Mock Blockchain API for Social NFT Platform Development')
    .setVersion('1.0')
    .addTag('blockchain')
    .addTag('nft')
    .addTag('wallet')
    .addTag('transactions')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Start server on port 3021
  const port = process.env.PORT || 3021;
  await app.listen(port);

  console.log(`⛓️ Mock Blockchain Service is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🔄 Environment: ${process.env.NODE_ENV || 'development'}`);
}

bootstrap();
