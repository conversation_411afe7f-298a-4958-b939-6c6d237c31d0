-- Create databases for all services
CREATE DATABASE user_service;
CREATE DATABASE marketplace_service;
CREATE DATABASE nft_generation_service;
CREATE DATABASE project_service;
CREATE DATABASE analytics_service;
CREATE DATABASE notification_service;

-- Create users with appropriate permissions
CREATE USER user_service_user WITH PASSWORD 'user_service_pass';
CREATE USER marketplace_service_user WITH PASSWORD 'marketplace_service_pass';
CREATE USER nft_generation_service_user WITH PASSWORD 'nft_generation_service_pass';
CREATE USER project_service_user WITH PASSWORD 'project_service_pass';
CREATE USER analytics_service_user WITH PASSWORD 'analytics_service_pass';
CREATE USER notification_service_user WITH PASSWORD 'notification_service_pass';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE user_service TO user_service_user;
GRANT ALL PRIVILEGES ON DATABASE marketplace_service TO marketplace_service_user;
GRANT ALL PRIVILEGES ON DATABASE nft_generation_service TO nft_generation_service_user;
GRA<PERSON> ALL PRIVILEGES ON DATABASE project_service TO project_service_user;
GRANT ALL PRIVILEGES ON DATABASE analytics_service TO analytics_service_user;
GRANT ALL PRIVILEGES ON DATABASE notification_service TO notification_service_user;

-- Grant permissions to postgres user for development
GRANT ALL PRIVILEGES ON DATABASE user_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE marketplace_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE nft_generation_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE project_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE analytics_service TO postgres;
GRANT ALL PRIVILEGES ON DATABASE notification_service TO postgres;
