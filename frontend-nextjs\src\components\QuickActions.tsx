'use client'

import {
  Box,
  SimpleGrid,
  Button,
  VStack,
  Text,
  HStack,
  Icon
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface QuickActionProps {
  title: string
  description: string
  icon: string
  href: string
  colorScheme?: string
  isExternal?: boolean
}

function QuickActionCard({ title, description, icon, href, colorScheme = 'blue', isExternal = false }: QuickActionProps) {
  const router = useRouter()

  const handleClick = () => {
    if (isExternal) {
      window.open(href, '_blank')
    } else {
      router.push(href)
    }
  }

  return (
    <Button
      as="div"
      h="auto"
      p={6}
      bg="white"
      borderRadius="lg"
      boxShadow="md"
      border="2px solid"
      borderColor="transparent"
      _hover={{
        borderColor: `${colorScheme}.200`,
        transform: 'translateY(-2px)',
        boxShadow: 'lg'
      }}
      transition="all 0.2s"
      cursor="pointer"
      onClick={handleClick}
    >
      <VStack gap={3} textAlign="center">
        <Box fontSize="3xl" color={`${colorScheme}.500`}>
          {icon}
        </Box>
        <VStack gap={1}>
          <Text fontWeight="bold" fontSize="md" color="gray.800">
            {title}
          </Text>
          <Text fontSize="sm" color="gray.600" textAlign="center">
            {description}
          </Text>
        </VStack>
      </VStack>
    </Button>
  )
}

interface QuickActionsProps {
  userId?: string
}

export default function QuickActions({ userId }: QuickActionsProps) {
  const quickActions = [
    {
      title: 'Browse Campaigns',
      description: 'Discover new campaigns to join and generate NFTs',
      icon: '🎯',
      href: '/campaigns',
      colorScheme: 'blue'
    },
    {
      title: 'View My NFTs',
      description: 'Check your NFT collection and manage your assets',
      icon: '🎨',
      href: '/nfts',
      colorScheme: 'purple'
    },
    {
      title: 'Update Profile',
      description: 'Manage your social connections and profile settings',
      icon: '👤',
      href: '/profile',
      colorScheme: 'green'
    },
    {
      title: 'Generate NFT',
      description: 'Quick generate from your most recent campaign',
      icon: '⚡',
      href: '/campaigns', // Will redirect to most recent campaign
      colorScheme: 'orange'
    },
    {
      title: 'Help Center',
      description: 'Get help and learn how to use the platform',
      icon: '❓',
      href: '#',
      colorScheme: 'gray'
    },
    {
      title: 'Share Platform',
      description: 'Invite friends to join the Social NFT Platform',
      icon: '📤',
      href: '#',
      colorScheme: 'pink'
    }
  ]

  return (
    <Box>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        ⚡ Quick Actions
      </Text>
      <SimpleGrid columns={{ base: 2, md: 3, lg: 6 }} gap={4}>
        {quickActions.map((action, index) => (
          <QuickActionCard key={index} {...action} />
        ))}
      </SimpleGrid>
    </Box>
  )
}
