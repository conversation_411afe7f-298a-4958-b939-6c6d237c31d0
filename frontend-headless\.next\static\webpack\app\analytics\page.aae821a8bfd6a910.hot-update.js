"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/layout */ \"(app-pages-browser)/./src/components/layout/layout.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_dashboard_basic_analytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/basic-analytics */ \"(app-pages-browser)/./src/components/dashboard/basic-analytics.tsx\");\n/* harmony import */ var _components_dashboard_kaito_style_analytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/kaito-style-analytics */ \"(app-pages-browser)/./src/components/dashboard/kaito-style-analytics.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('clean');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('clean'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'clean' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: \"Clean Implementation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('kaito'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'kaito' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: \"Kaito-Style Implementation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'clean' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_basic_analytics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 39\n                        }, this),\n                        activeTab === 'kaito' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_kaito_style_analytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 39\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"n4zZZ0fk505rVYAhQ2hrI5UnkRs=\");\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/kaito-style-analytics.tsx":
/*!************************************************************!*\
  !*** ./src/components/dashboard/kaito-style-analytics.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KaitoStyleAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction KaitoStyleAnalytics() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border-b border-gray-700 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"NFT Platform Leaderboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 bg-teal-600 px-3 py-1 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Certified Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"bg-gray-700 border border-gray-600 rounded px-3 py-1 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"7d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"30d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"90d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-teal-600 hover:bg-teal-700 px-4 py-2 rounded text-sm font-medium\",\n                                    children: \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: \"Yapper Leaderboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Top 10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            {\n                                                rank: 1,\n                                                name: '@alice_crypto',\n                                                score: 2.5,\n                                                change: '+12.5%',\n                                                trend: 'up'\n                                            },\n                                            {\n                                                rank: 2,\n                                                name: '@bob_nft',\n                                                score: 2.3,\n                                                change: '+8.2%',\n                                                trend: 'up'\n                                            },\n                                            {\n                                                rank: 3,\n                                                name: '@charlie_art',\n                                                score: 2.1,\n                                                change: '+5.7%',\n                                                trend: 'up'\n                                            },\n                                            {\n                                                rank: 4,\n                                                name: '@diana_meta',\n                                                score: 1.9,\n                                                change: '-2.1%',\n                                                trend: 'down'\n                                            },\n                                            {\n                                                rank: 5,\n                                                name: '@eve_crypto',\n                                                score: 1.8,\n                                                change: '+3.4%',\n                                                trend: 'up'\n                                            }\n                                        ].map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 hover:bg-gray-700 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm w-6\",\n                                                                children: user.rank\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 59,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-bold text-white\",\n                                                                    children: user.name.slice(1, 3).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 61,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 60,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white text-sm font-medium\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                        lineNumber: 64,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: [\n                                                                            user.score,\n                                                                            \"k\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                        lineNumber: 65,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 63,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-bold \".concat(user.trend === 'up' ? 'text-green-400' : 'text-red-400'),\n                                                        children: user.change\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.rank, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: \"Performance Treemap\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Size: Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Color: Growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-96 bg-gray-900 rounded-lg p-4 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-green-600 to-green-700 rounded\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '5%',\n                                                    width: '28%',\n                                                    height: '40%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"@alice_crypto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 95,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-200 text-xs\",\n                                                                    children: \"Epic NFT Creator\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 96,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"2.5k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 99,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-200 text-xs\",\n                                                                    children: \"+12.5% ↗\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 100,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-blue-600 to-blue-700 rounded\",\n                                                style: {\n                                                    left: '32%',\n                                                    top: '5%',\n                                                    width: '25%',\n                                                    height: '35%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-bold text-sm\",\n                                                                    children: \"@bob_nft\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"Rare Collector\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 110,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-lg font-bold\",\n                                                                    children: \"2.3k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 113,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-200 text-xs\",\n                                                                    children: \"+8.2% ↗\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-purple-600 to-purple-700 rounded\",\n                                                style: {\n                                                    left: '59%',\n                                                    top: '5%',\n                                                    width: '20%',\n                                                    height: '30%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-bold text-xs\",\n                                                                    children: \"@charlie_art\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-purple-200 text-xs\",\n                                                                    children: \"Artist\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"2.1k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-purple-200 text-xs\",\n                                                                    children: \"+5.7%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-red-600 to-red-700 rounded\",\n                                                style: {\n                                                    left: '81%',\n                                                    top: '5%',\n                                                    width: '17%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"@diana_meta\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"1.9k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-red-200 text-xs\",\n                                                                    children: \"-2.1% ↘\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-teal-600 to-teal-700 rounded\",\n                                                style: {\n                                                    left: '2%',\n                                                    top: '47%',\n                                                    width: '18%',\n                                                    height: '25%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"@eve_crypto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"1.8k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-teal-200 text-xs\",\n                                                                    children: \"+3.4%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bg-gradient-to-br from-orange-600 to-orange-700 rounded\",\n                                                style: {\n                                                    left: '22%',\n                                                    top: '47%',\n                                                    width: '15%',\n                                                    height: '20%'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 h-full flex flex-col justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"@frank_nft\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-bold\",\n                                                                    children: \"1.6k\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-orange-200 text-xs\",\n                                                                    children: \"+1.2%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"NFT Platform Social Graph\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Connections: 1,247\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Active Users: 89\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-96 bg-gray-900 rounded-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-16 h-16 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center border-4 border-teal-300\",\n                                    style: {\n                                        left: '50%',\n                                        top: '50%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"HUB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute inset-0 w-full h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"20%\",\n                                            y2: \"20%\",\n                                            stroke: \"#10B981\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"80%\",\n                                            y2: \"25%\",\n                                            stroke: \"#3B82F6\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"15%\",\n                                            y2: \"70%\",\n                                            stroke: \"#8B5CF6\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"85%\",\n                                            y2: \"75%\",\n                                            stroke: \"#F59E0B\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"30%\",\n                                            y2: \"85%\",\n                                            stroke: \"#EF4444\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"50%\",\n                                            y1: \"50%\",\n                                            x2: \"70%\",\n                                            y2: \"15%\",\n                                            stroke: \"#06B6D4\",\n                                            strokeWidth: \"2\",\n                                            opacity: \"0.6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"20%\",\n                                            y1: \"20%\",\n                                            x2: \"80%\",\n                                            y2: \"25%\",\n                                            stroke: \"#10B981\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"15%\",\n                                            y1: \"70%\",\n                                            x2: \"30%\",\n                                            y2: \"85%\",\n                                            stroke: \"#8B5CF6\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"80%\",\n                                            y1: \"25%\",\n                                            x2: \"85%\",\n                                            y2: \"75%\",\n                                            stroke: \"#3B82F6\",\n                                            strokeWidth: \"1\",\n                                            opacity: \"0.3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center border-2 border-green-300\",\n                                    style: {\n                                        left: '20%',\n                                        top: '20%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"AC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center border-2 border-blue-300\",\n                                    style: {\n                                        left: '80%',\n                                        top: '25%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"BN\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-11 h-11 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center border-2 border-purple-300\",\n                                    style: {\n                                        left: '15%',\n                                        top: '70%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"CA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-9 h-9 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center border-2 border-yellow-300\",\n                                    style: {\n                                        left: '85%',\n                                        top: '75%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"DM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-8 h-8 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center border-2 border-red-300\",\n                                    style: {\n                                        left: '30%',\n                                        top: '85%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"EC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute w-10 h-10 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center border-2 border-cyan-300\",\n                                    style: {\n                                        left: '70%',\n                                        top: '15%',\n                                        transform: 'translate(-50%, -50%)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"FN\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                [\n                                    {\n                                        x: '10%',\n                                        y: '40%',\n                                        color: 'from-pink-400 to-pink-600',\n                                        border: 'border-pink-300'\n                                    },\n                                    {\n                                        x: '90%',\n                                        y: '50%',\n                                        color: 'from-indigo-400 to-indigo-600',\n                                        border: 'border-indigo-300'\n                                    },\n                                    {\n                                        x: '40%',\n                                        y: '10%',\n                                        color: 'from-emerald-400 to-emerald-600',\n                                        border: 'border-emerald-300'\n                                    },\n                                    {\n                                        x: '60%',\n                                        y: '90%',\n                                        color: 'from-violet-400 to-violet-600',\n                                        border: 'border-violet-300'\n                                    }\n                                ].map((node, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-6 h-6 bg-gradient-to-r \".concat(node.color, \" rounded-full border \").concat(node.border),\n                                        style: {\n                                            left: node.x,\n                                            top: node.y,\n                                            transform: 'translate(-50%, -50%)'\n                                        }\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-4 right-4 bg-gray-800 bg-opacity-90 p-3 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-teal-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Platform Hub\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Top Creators\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Active Traders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Top 100 NFT Platform Users\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"7d\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"30d\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"90d\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"1y\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-4 mb-4 text-xs text-gray-400 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1\",\n                                    children: \"Rank\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: \"User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-2\",\n                                    children: \"Score\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-2\",\n                                    children: \"NFTs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-2\",\n                                    children: \"Volume\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-2\",\n                                    children: \"Growth\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                            children: [\n                                {\n                                    rank: 1,\n                                    name: '@alice_crypto',\n                                    avatar: 'AC',\n                                    score: 2547,\n                                    nfts: 89,\n                                    volume: 45.2,\n                                    growth: 12.5,\n                                    scoreBar: 95,\n                                    nftBar: 78,\n                                    volumeBar: 85,\n                                    growthBar: 92\n                                },\n                                {\n                                    rank: 2,\n                                    name: '@bob_nft_master',\n                                    avatar: 'BN',\n                                    score: 2341,\n                                    nfts: 76,\n                                    volume: 38.7,\n                                    growth: 8.2,\n                                    scoreBar: 88,\n                                    nftBar: 65,\n                                    volumeBar: 72,\n                                    growthBar: 78\n                                },\n                                {\n                                    rank: 3,\n                                    name: '@charlie_artist',\n                                    avatar: 'CA',\n                                    score: 2156,\n                                    nfts: 92,\n                                    volume: 52.1,\n                                    growth: 5.7,\n                                    scoreBar: 82,\n                                    nftBar: 85,\n                                    volumeBar: 95,\n                                    growthBar: 65\n                                },\n                                {\n                                    rank: 4,\n                                    name: '@diana_collector',\n                                    avatar: 'DC',\n                                    score: 1987,\n                                    nfts: 45,\n                                    volume: 28.9,\n                                    growth: -2.1,\n                                    scoreBar: 75,\n                                    nftBar: 42,\n                                    volumeBar: 55,\n                                    growthBar: 25\n                                },\n                                {\n                                    rank: 5,\n                                    name: '@eve_crypto_queen',\n                                    avatar: 'EC',\n                                    score: 1834,\n                                    nfts: 67,\n                                    volume: 34.5,\n                                    growth: 3.4,\n                                    scoreBar: 70,\n                                    nftBar: 58,\n                                    volumeBar: 65,\n                                    growthBar: 58\n                                }\n                            ].map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-12 gap-4 items-center p-3 hover:bg-gray-700 rounded-lg transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 text-gray-300 text-sm font-medium\",\n                                            children: user.rank\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-xs font-bold\",\n                                                        children: user.avatar\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: \"Verified Creator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-sm font-bold mb-1\",\n                                                    children: user.score.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(user.scoreBar, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-sm font-bold mb-1\",\n                                                    children: user.nfts\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(user.nftBar, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-sm font-bold mb-1\",\n                                                    children: [\n                                                        user.volume,\n                                                        \" ETH\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(user.volumeBar, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold mb-1 \".concat(user.growth >= 0 ? 'text-green-400' : 'text-red-400'),\n                                                    children: [\n                                                        user.growth >= 0 ? '+' : '',\n                                                        user.growth,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2 rounded-full \".concat(user.growth >= 0 ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-red-400 to-red-600'),\n                                                        style: {\n                                                            width: \"\".concat(user.growthBar, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, user.rank, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\kaito-style-analytics.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = KaitoStyleAnalytics;\nvar _c;\n$RefreshReg$(_c, \"KaitoStyleAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/kaito-style-analytics.tsx\n"));

/***/ })

});