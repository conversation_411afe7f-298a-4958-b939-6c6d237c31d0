# Smart Docker Caching for Social Commerce Platform

## 🎯 **Overview**

Our platform implements **Smart Docker Caching** to optimize development workflow while maintaining bandwidth efficiency. This system caches dependencies (which change rarely) but always rebuilds source code (which changes frequently).

## 🚀 **Key Benefits**

### **Performance Improvements**
- **Fast Builds**: 20-60 seconds vs 200+ seconds for full rebuilds
- **Bandwidth Efficient**: Dependencies only downloaded when package.json changes
- **Development Speed**: Code changes reflected in ~1 minute
- **Resource Optimization**: Reduced CPU and memory usage during builds

### **Developer Experience**
- **Consistent**: Same caching strategy across all services
- **Reliable**: No phantom cache issues
- **Flexible**: Different rebuild strategies for different scenarios
- **Automated**: Smart scripts handle complexity

## 🏗️ **Architecture**

### **Multi-Stage Docker Strategy**

```dockerfile
# Stage 1: Dependencies (CACHED)
FROM node:18-alpine AS dependencies
WORKDIR /app
COPY services/[service-name]/package*.json ./
COPY libs/common ./libs/common
RUN npm install --legacy-peer-deps

# Stage 2: Development (FRESH)
FROM node:18-alpine AS development
WORKDIR /app
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/libs ./libs
COPY --from=dependencies /app/package*.json ./
COPY services/[service-name]/src ./src
COPY services/[service-name]/tsconfig.json ./
COPY services/[service-name]/nest-cli.json ./
RUN npm run build
```

### **Caching Layers**

| **Layer** | **Content** | **Cache Strategy** | **Rebuild Trigger** |
|-----------|-------------|-------------------|-------------------|
| **Dependencies** | npm packages, libs/common | ✅ **CACHED** | package.json changes |
| **Source Code** | src/, config files | ❌ **NEVER CACHED** | Any code change |
| **Build Output** | dist/ folder | ❌ **NEVER CACHED** | Source code changes |

## 📁 **File Structure**

```
social-commerce-platform/
├── services/
│   ├── api-gateway/
│   │   ├── Dockerfile          # Production build
│   │   └── Dockerfile.dev      # Smart caching build
│   ├── user-service/
│   │   ├── Dockerfile          # Production build
│   │   └── Dockerfile.dev      # Smart caching build
│   └── [other-services]/
│       ├── Dockerfile          # Production build
│       └── Dockerfile.dev      # Smart caching build
├── docker-compose.yml          # Production configuration
├── docker-compose.override.yml # Development with smart caching
└── scripts/
    ├── dev-restart-service.sh  # Smart rebuild single service
    └── standardize-security.sh # Platform-wide rebuild
```

## 🔧 **Implementation Details**

### **Docker Compose Override**

All services use smart caching in development:

```yaml
services:
  service-name:
    build:
      context: .
      dockerfile: services/service-name/Dockerfile.dev
      target: development
      cache_from:
        - node:18-alpine
    environment:
      - NODE_ENV=development
```

### **Smart Dockerfile Template**

Each service follows this pattern:

```dockerfile
# Dependencies Stage - CACHED
FROM node:18-alpine AS dependencies
WORKDIR /app
COPY services/SERVICE_NAME/package*.json ./
COPY libs/common ./libs/common
RUN npm install --legacy-peer-deps

# Development Stage - FRESH
FROM node:18-alpine AS development
WORKDIR /app
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/libs ./libs
COPY --from=dependencies /app/package*.json ./
COPY services/SERVICE_NAME/src ./src
COPY services/SERVICE_NAME/tsconfig.json ./
COPY services/SERVICE_NAME/nest-cli.json ./
RUN npm run build
ENV NODE_ENV=development
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
USER nestjs
EXPOSE PORT_NUMBER
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:PORT_NUMBER/api/health || exit 1
CMD ["node", "dist/main.js"]
```

## 🛠️ **Usage Guide**

### **Development Scripts**

#### **Single Service Rebuild**
```bash
# Smart rebuild (dependencies cached, source fresh)
./scripts/dev-restart-service.sh service-name

# Force rebuild dependencies (when package.json changes)
./scripts/dev-restart-service.sh service-name --force-deps
```

#### **Platform-Wide Rebuild**
```bash
# Rebuild all services with smart caching
./scripts/standardize-security.sh
```

### **Common Scenarios**

#### **Daily Development (Most Common)**
```bash
# You changed source code in user-service
./scripts/dev-restart-service.sh user-service
# Result: ~30-60 second rebuild
```

#### **Added New npm Package**
```bash
# You modified package.json in product-service
./scripts/dev-restart-service.sh product-service --force-deps
# Result: ~120-180 second rebuild (downloads new packages)
```

#### **Fresh Environment Setup**
```bash
# First time setup or major changes
./scripts/standardize-security.sh
# Result: All services rebuilt with smart caching
```

### **Performance Comparison**

| **Scenario** | **Without Smart Caching** | **With Smart Caching** | **Time Saved** |
|--------------|---------------------------|------------------------|----------------|
| **Code Change** | 200+ seconds | 30-60 seconds | **70-85%** |
| **Package.json Change** | 200+ seconds | 120-180 seconds | **10-40%** |
| **Fresh Build** | 200+ seconds | 60-120 seconds | **40-70%** |
| **Bandwidth Usage** | Full download every time | Only when dependencies change | **80-95%** |

## 🔍 **Monitoring & Debugging**

### **Build Success Indicators**

Look for these messages in build output:

```bash
✅ Dependencies CACHED:
CACHED [service dependencies 5/5] RUN npm install --legacy-peer-deps

✅ Source Code FRESH:
[service development 9/11] RUN npm run build

✅ Fast Build Time:
[+] Building 45.2s (21/21) FINISHED
```

### **Troubleshooting**

#### **Build Failures**
```bash
# Clear all caches and rebuild
docker system prune -a
./scripts/dev-restart-service.sh service-name --force-deps
```

#### **Dependency Issues**
```bash
# Force dependency rebuild
./scripts/dev-restart-service.sh service-name --force-deps
```

#### **Import/Path Issues**
```bash
# Check if libs/common is properly copied
docker run --rm service-image ls -la /app/libs/
```

## 📊 **Cache Management**

### **What Gets Cached**
- ✅ Node.js base image layers
- ✅ npm package installations
- ✅ libs/common shared library
- ✅ package.json and package-lock.json

### **What Never Gets Cached**
- ❌ Source code (src/ directory)
- ❌ Configuration files (tsconfig.json, nest-cli.json)
- ❌ Build output (dist/ directory)
- ❌ Environment-specific files

### **Cache Invalidation**
Cache is automatically invalidated when:
- package.json or package-lock.json changes
- libs/common shared library changes
- Docker base image updates
- Manual cache clearing with --force-deps

## 🎯 **Best Practices**

### **For Developers**

#### **Daily Workflow**
1. **Code Changes**: Always use `./scripts/dev-restart-service.sh service-name`
2. **Package Changes**: Always use `--force-deps` flag when modifying package.json
3. **Testing**: Verify builds complete in under 60 seconds for code changes
4. **Monitoring**: Watch for "CACHED" messages in build output

#### **Do's and Don'ts**

✅ **DO:**
- Use smart caching scripts for all development
- Monitor build times to ensure caching is working
- Use `--force-deps` when adding/removing npm packages
- Keep Dockerfile.dev and Dockerfile in sync for production builds

❌ **DON'T:**
- Use `docker-compose build --no-cache` for regular development
- Modify Dockerfile.dev without understanding the caching strategy
- Ignore build time increases (indicates caching issues)
- Mix production and development Docker configurations

### **For AI Agents**

#### **Mandatory Rules**
1. **Always use smart caching scripts** for service rebuilds
2. **Never use `--no-cache`** unless explicitly requested
3. **Always check build times** to verify caching is working
4. **Use `--force-deps`** when package.json is modified
5. **Follow the Dockerfile.dev template** for new services

#### **Implementation Checklist**
When creating new services:
- [ ] Create `Dockerfile.dev` using the template
- [ ] Add service to `docker-compose.override.yml`
- [ ] Test smart caching with `./scripts/dev-restart-service.sh`
- [ ] Verify dependencies are cached on second build
- [ ] Document any service-specific requirements

## 🔧 **Implementation Guide**

### **Adding Smart Caching to New Service**

#### **Step 1: Create Dockerfile.dev**
```bash
# Copy template and customize
cp services/store-service/Dockerfile.dev services/new-service/Dockerfile.dev
# Edit ports, service name, and specific requirements
```

#### **Step 2: Update Docker Compose**
```yaml
# Add to docker-compose.override.yml
new-service:
  build:
    context: .
    dockerfile: services/new-service/Dockerfile.dev
    target: development
    cache_from:
      - node:18-alpine
  environment:
    - NODE_ENV=development
```

#### **Step 3: Test Implementation**
```bash
# Test smart caching
./scripts/dev-restart-service.sh new-service

# Verify caching works
./scripts/dev-restart-service.sh new-service
# Should complete in <60 seconds on second run
```

### **Migrating Existing Service**

#### **Step 1: Backup Current Configuration**
```bash
cp services/existing-service/Dockerfile services/existing-service/Dockerfile.backup
```

#### **Step 2: Create Smart Dockerfile**
```bash
# Use template and customize for existing service
cp docs/templates/Dockerfile.dev.template services/existing-service/Dockerfile.dev
```

#### **Step 3: Update Compose Configuration**
```bash
# Modify docker-compose.override.yml to use Dockerfile.dev
```

#### **Step 4: Test Migration**
```bash
# Test with existing service
./scripts/dev-restart-service.sh existing-service --force-deps
```

## 📈 **Performance Metrics**

### **Expected Build Times**

| **Service Size** | **First Build** | **Code Change** | **Dependency Change** |
|------------------|----------------|-----------------|---------------------|
| **Small** (API Gateway) | 60-90s | 20-40s | 90-120s |
| **Medium** (User/Store) | 90-120s | 30-60s | 120-150s |
| **Large** (Product/Order) | 120-180s | 40-80s | 150-200s |

### **Bandwidth Usage**

| **Operation** | **Without Caching** | **With Smart Caching** | **Savings** |
|---------------|---------------------|------------------------|-------------|
| **Code Change** | 150-300 MB | 0-5 MB | **95-98%** |
| **Dependency Change** | 150-300 MB | 50-150 MB | **50-75%** |
| **Fresh Environment** | 1-2 GB | 300-600 MB | **60-70%** |

## 🚨 **Troubleshooting Guide**

### **Common Issues**

#### **"Module not found" Errors**
```bash
# Check if libs/common is properly copied
docker run --rm -it service-image ls -la /app/libs/

# Fix: Ensure Dockerfile.dev copies libs/common correctly
COPY libs/common ./libs/common
```

#### **Slow Build Times**
```bash
# Check if caching is working
docker images | grep service-name

# Look for CACHED messages in build output
# If missing, dependencies aren't being cached
```

#### **Build Failures After Package Changes**
```bash
# Force dependency rebuild
./scripts/dev-restart-service.sh service-name --force-deps

# If still failing, clear all caches
docker system prune -a
```

### **Emergency Procedures**

#### **Complete Cache Reset**
```bash
# Nuclear option - clears all Docker caches
docker system prune -a --volumes
docker-compose down
docker-compose up -d
```

#### **Service-Specific Reset**
```bash
# Reset specific service
docker-compose stop service-name
docker rmi $(docker images | grep service-name | awk '{print $3}')
./scripts/dev-restart-service.sh service-name --force-deps
```
