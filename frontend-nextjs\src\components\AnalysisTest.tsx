'use client'

import {
  Box,
  Button,
  Input,
  VStack,
  HStack,
  Text,
  Heading
} from '@chakra-ui/react'
import { useState } from 'react'

export default function AnalysisTest() {
  const [twitterHandle, setTwitterHandle] = useState('')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testDirectAPI = async () => {
    if (!twitterHandle.trim()) {
      setError('Please enter a Twitter username')
      return
    }

    try {
      setLoading(true)
      setError('')
      setResult(null)

      console.log('🔍 Testing Profile Analysis Service via API Gateway...')

      // Test API call through API Gateway (avoids CORS issues)
      const response = await fetch('/api/analysis/twitter-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          twitterHandle: twitterHandle.replace('@', '').trim()
        })
      })

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ Profile Analysis Response:', data)
      setResult(data)
    } catch (err: any) {
      console.error('❌ Profile Analysis Error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testAPIGateway = async () => {
    if (!twitterHandle.trim()) {
      setError('Please enter a Twitter username')
      return
    }

    try {
      setLoading(true)
      setError('')
      setResult(null)

      console.log('🔍 Testing with Authentication Headers...')

      // Test API Gateway call with authentication
      const token = localStorage.getItem('token')
      const headers: any = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/analysis/twitter-profile', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          twitterHandle: twitterHandle.replace('@', '').trim()
        })
      })

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ Authenticated API Response:', data)
      setResult(data)
    } catch (err: any) {
      console.error('❌ Authenticated API Error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
      <VStack gap={4} align="stretch">
        <Heading size="md">🧪 Twitter Analysis API Test</Heading>

        <VStack gap={3} align="stretch">
          <Text fontSize="sm" color="gray.600">
            Test Twitter profile analysis integration
          </Text>

          <HStack>
            <Input
              placeholder="Enter Twitter username (e.g., testuser)"
              value={twitterHandle}
              onChange={(e) => setTwitterHandle(e.target.value)}
              disabled={loading}
            />
          </HStack>

          <HStack gap={2}>
            <Button
              colorScheme="blue"
              onClick={testDirectAPI}
              loading={loading}
              size="sm"
            >
              Test Analysis API
            </Button>
            <Button
              colorScheme="green"
              onClick={testAPIGateway}
              loading={loading}
              size="sm"
            >
              Test with Auth
            </Button>
          </HStack>
        </VStack>

        {error && (
          <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
            <Text color="red.600" fontWeight="medium">
              ❌ {error}
            </Text>
          </Box>
        )}

        {loading && (
          <Box p={4} bg="blue.50" borderRadius="md">
            <Text color="blue.700" fontWeight="medium">
              🔄 Testing API integration...
            </Text>
          </Box>
        )}

        {result && (
          <Box p={4} bg="green.50" borderRadius="md" borderLeft="4px solid" borderColor="green.500">
            <VStack gap={2} align="stretch">
              <Text fontWeight="bold" color="green.700">
                ✅ API Test Successful!
              </Text>

              {result.data && (
                <VStack gap={1} align="stretch" fontSize="sm">
                  <Text><strong>Handle:</strong> {result.data.twitterHandle}</Text>
                  <Text><strong>Status:</strong> {result.data.status}</Text>
                  {result.data.results && (
                    <>
                      <Text><strong>Followers:</strong> {result.data.results.profile?.followerCount?.toLocaleString()}</Text>
                      <Text><strong>Engagement:</strong> {result.data.results.metrics?.engagementRate?.toFixed(2)}%</Text>
                      <Text><strong>Score:</strong> {result.data.results.nftRecommendation?.score}/100</Text>
                      <Text><strong>Rarity:</strong> {result.data.results.nftRecommendation?.rarity}</Text>
                    </>
                  )}
                </VStack>
              )}

              <Box mt={2}>
                <Text fontSize="xs" color="gray.600">Raw Response:</Text>
                <Box bg="gray.100" p={2} borderRadius="md" fontSize="xs" fontFamily="mono">
                  <pre>{JSON.stringify(result, null, 2)}</pre>
                </Box>
              </Box>
            </VStack>
          </Box>
        )}
      </VStack>
    </Box>
  )
}
