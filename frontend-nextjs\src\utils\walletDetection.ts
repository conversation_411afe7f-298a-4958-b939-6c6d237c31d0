// Wallet Detection Utility
// Handles MetaMask and other wallet detection gracefully

export interface WalletInfo {
  isInstalled: boolean;
  isConnected: boolean;
  provider: any;
  error?: string;
}

export class WalletDetection {
  /**
   * Check if MetaMask is installed
   */
  static isMetaMaskInstalled(): boolean {
    if (typeof window === 'undefined') return false;
    
    try {
      return !!(window as any).ethereum?.isMetaMask;
    } catch (error) {
      console.log('MetaMask detection error (safe to ignore):', error);
      return false;
    }
  }

  /**
   * Get wallet information safely
   */
  static getWalletInfo(): WalletInfo {
    if (typeof window === 'undefined') {
      return {
        isInstalled: false,
        isConnected: false,
        provider: null,
        error: 'Server-side rendering'
      };
    }

    try {
      const ethereum = (window as any).ethereum;
      
      if (!ethereum) {
        return {
          isInstalled: false,
          isConnected: false,
          provider: null,
          error: 'No wallet detected'
        };
      }

      return {
        isInstalled: true,
        isConnected: ethereum.isConnected?.() || false,
        provider: ethereum,
        error: null
      };
    } catch (error: any) {
      console.log('Wallet detection error (safe to ignore):', error.message);
      return {
        isInstalled: false,
        isConnected: false,
        provider: null,
        error: error.message
      };
    }
  }

  /**
   * Suppress MetaMask injection errors
   */
  static suppressMetaMaskErrors(): void {
    if (typeof window === 'undefined') return;

    // Override console.error to filter MetaMask injection errors
    const originalError = console.error;
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // Filter out MetaMask extension errors
      if (
        message.includes('MetaMask extension not found') ||
        message.includes('chrome-extension://') ||
        message.includes('inpage.js')
      ) {
        // Silently ignore MetaMask injection errors
        return;
      }
      
      // Log other errors normally
      originalError.apply(console, args);
    };
  }
}

// Auto-suppress MetaMask errors on import
if (typeof window !== 'undefined') {
  WalletDetection.suppressMetaMaskErrors();
}
