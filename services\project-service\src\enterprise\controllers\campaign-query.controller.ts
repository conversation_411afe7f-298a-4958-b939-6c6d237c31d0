// Enterprise Campaign Query Controller (Read Side) - Requirements-Driven Implementation
import { 
  <PERSON>, 
  Get, 
  Param, 
  Query, 
  <PERSON>ers, 
  <PERSON>s, 
  HttpStatus,
  ParseBoolPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiHeader } from '@nestjs/swagger';
import { Response } from 'express';
import { CampaignQueryService, CampaignListQuery, CampaignStatsQuery } from '../services/campaign-query.service';

@ApiTags('Campaign Queries (Read Operations)')
@Controller('enterprise/campaigns')
export class CampaignQueryController {
  constructor(private readonly campaignQueryService: CampaignQueryService) {}

  @Get('search')
  @ApiOperation({ 
    summary: 'Search campaigns with advanced filtering',
    description: 'Search campaigns by name, description, project with filters'
  })
  @ApiQuery({ name: 'q', description: 'Search term' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by campaign type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @ApiQuery({ name: 'minParticipants', required: false, description: 'Minimum participants' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchCampaigns(
    @Query('q') searchTerm: string,
    @Query('type') campaignType: string,
    @Query('status') status: string,
    @Query('active') isActive: boolean,
    @Query('minParticipants') minParticipants: number,
    @Res() res: Response
  ) {
    try {
      const filters = {
        campaignType,
        status,
        isActive,
        minParticipants,
      };

      const result = await this.campaignQueryService.searchCampaigns(searchTerm, filters);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('stats')
  @ApiOperation({ 
    summary: 'Get campaign statistics and analytics',
    description: 'Retrieve aggregated statistics for dashboard and monitoring'
  })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'ownerId', required: false, description: 'Filter by owner ID' })
  @ApiQuery({ name: 'timeRange', required: false, enum: ['7d', '30d', '90d', 'all'], description: 'Time range for statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getCampaignStats(
    @Query('projectId') projectId: string,
    @Query('ownerId') ownerId: string,
    @Query('timeRange') timeRange: '7d' | '30d' | '90d' | 'all',
    @Res() res: Response
  ) {
    try {
      const query: CampaignStatsQuery = { projectId, ownerId, timeRange };
      const result = await this.campaignQueryService.getCampaignStats(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get('project/:projectId')
  @ApiOperation({ 
    summary: 'Get campaigns by project',
    description: 'Retrieve all campaigns for a specific project'
  })
  @ApiParam({ name: 'projectId', description: 'Project ID' })
  @ApiQuery({ name: 'includeConfig', required: false, type: Boolean, description: 'Include full configuration' })
  @ApiResponse({ status: 200, description: 'Project campaigns retrieved successfully' })
  async getCampaignsByProject(
    @Param('projectId') projectId: string,
    @Query('includeConfig', new DefaultValuePipe(false), ParseBoolPipe) includeConfig: boolean,
    @Res() res: Response
  ) {
    try {
      const result = await this.campaignQueryService.getCampaignsByProject(projectId, includeConfig);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get campaigns list with filtering and pagination',
    description: 'Retrieve campaigns with support for filtering, search, and pagination'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by campaign type' })
  @ApiQuery({ name: 'ownerId', required: false, description: 'Filter by owner ID' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' })
  @ApiResponse({ status: 200, description: 'Campaigns list retrieved successfully' })
  async getCampaigns(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('projectId') projectId: string,
    @Query('status') status: string,
    @Query('type') campaignType: string,
    @Query('ownerId') ownerId: string,
    @Query('search') search: string,
    @Query('active') isActive: boolean,
    @Res() res: Response
  ) {
    try {
      const query: CampaignListQuery = {
        page,
        limit,
        projectId,
        status,
        campaignType,
        ownerId,
        search,
        isActive,
      };

      const result = await this.campaignQueryService.getCampaigns(query);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
      });
    }
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get campaign by ID with complete details',
    description: 'Retrieve campaign details with optional configuration for authorized users'
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiQuery({ name: 'includeConfig', required: false, type: Boolean, description: 'Include full configuration' })
  @ApiResponse({ status: 200, description: 'Campaign retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async getCampaignById(
    @Param('id') id: string,
    @Query('includeConfig', new DefaultValuePipe(false), ParseBoolPipe) includeConfig: boolean,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.campaignQueryService.getCampaignById(id, includeConfig);
      return res.status(HttpStatus.OK).json({
        ...result,
        correlationId,
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }
}
