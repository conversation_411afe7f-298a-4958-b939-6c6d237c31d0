{"name": "profile-analysis-service", "version": "1.0.0", "description": "Twitter profile analysis service for Social NFT Platform", "main": "dist/main.js", "scripts": {"start:dev": "nest start --watch", "build": "nest build", "start": "nest start", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.13", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^6.8.2", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "jsonwebtoken": "^9.0.2", "prisma": "^6.8.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.2", "twitter-api-v2": "^1.23.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "typescript": "^5.1.3"}}