// Enterprise NFT Command Controller (Write Side)
import { <PERSON>, Post, Put, Body, Param, Headers, <PERSON>s, HttpStatus, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateNftCommandDto, UpdateNftCommandDto } from '../models/nft-command.model';
import { NftCommandService } from '../services/nft-command.service';
import { ProfileNftGenerationService, GenerateNftFromAnalysisDto } from '../services/profile-nft-generation.service';

@ApiTags('NFT Commands (Write Operations)')
@Controller('nfts')
export class NftCommandController {
  constructor(
    private readonly nftCommandService: NftCommandService,
    private readonly profileNftGenerationService: ProfileNftGenerationService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new NFT (Enterprise CQRS Command)' })
  @ApiResponse({ status: 201, description: 'NFT created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 503, description: 'Service temporarily unavailable' })
  async createNft(
    @Body() createNftDto: CreateNftCommandDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-create-${Date.now()}`;
      
      const result = await this.nftCommandService.createNft(createNftDto, {
        correlationId,
        userId: headers['x-user-id'],
        sessionId: headers['x-session-id'],
        ipAddress: headers['x-forwarded-for'] || headers['x-real-ip'],
        userAgent: headers['user-agent']
      });

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'NFT service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update NFT (Enterprise CQRS Command)' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 200, description: 'NFT updated successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async updateNft(
    @Param('id') id: string,
    @Body() updateNftDto: UpdateNftCommandDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-update-${Date.now()}`;
      
      const result = await this.nftCommandService.updateNft(id, updateNftDto, {
        correlationId,
        userId: headers['x-user-id'],
        sessionId: headers['x-session-id'],
        ipAddress: headers['x-forwarded-for'] || headers['x-real-ip'],
        userAgent: headers['user-agent']
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'NFT service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Post(':id/generate')
  @ApiOperation({ summary: 'Start NFT generation (Enterprise Command)' })
  @ApiParam({ name: 'id', description: 'NFT ID' })
  @ApiResponse({ status: 202, description: 'Generation started' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async startGeneration(
    @Param('id') id: string,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-generate-${Date.now()}`;
      
      const result = await this.nftCommandService.startGeneration(id, {
        correlationId,
        userId: headers['x-user-id']
      });

      return res.status(HttpStatus.ACCEPTED).json({
        success: true,
        data: result,
        correlationId
      });
    } catch (error) {
      return res.status(error.status || HttpStatus.SERVICE_UNAVAILABLE).json({
        success: false,
        error: {
          code: error.code || 'ServiceUnavailableException',
          message: error.message || 'Generation service temporarily unavailable',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Post('generate-from-analysis')
  @ApiOperation({ summary: 'Generate NFT from Profile Analysis results' })
  @ApiBody({
    description: 'Profile Analysis NFT generation request',
    schema: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          description: 'User ID requesting the NFT generation',
          example: 'user_123'
        },
        analysisId: {
          type: 'string',
          description: 'Profile Analysis ID to base NFT on',
          example: 'analysis_456'
        },
        campaignId: {
          type: 'string',
          description: 'Campaign ID (optional)',
          example: 'campaign_789'
        },
        customization: {
          type: 'object',
          properties: {
            backgroundColor: { type: 'string', example: '#7c3aed' },
            style: { type: 'string', enum: ['modern', 'classic', 'artistic', 'minimal'], example: 'modern' },
            theme: { type: 'string', enum: ['tech', 'nature', 'abstract', 'social'], example: 'social' }
          }
        }
      },
      required: ['userId', 'analysisId']
    }
  })
  @ApiResponse({
    status: 201,
    description: 'NFT generated successfully from profile analysis',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            analysisId: { type: 'string' },
            name: { type: 'string' },
            description: { type: 'string' },
            rarity: { type: 'string' },
            score: { type: 'number' },
            attributes: { type: 'array' },
            metadata: { type: 'object' },
            imageUrl: { type: 'string' },
            status: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid input or analysis not found' })
  @ApiResponse({ status: 404, description: 'Profile analysis not found' })
  async generateNftFromAnalysis(
    @Body() generateDto: GenerateNftFromAnalysisDto,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-analysis-gen-${Date.now()}`;

      const result = await this.profileNftGenerationService.generateNftFromAnalysis(generateDto);

      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: result,
        message: 'NFT generated successfully from profile analysis',
        correlationId
      });

    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_REQUEST).json({
        success: false,
        error: {
          code: error.code || 'NftGenerationException',
          message: error.message || 'Failed to generate NFT from analysis',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }

  @Get('user/:userId/history')
  @ApiOperation({ summary: 'Get user NFT generation history' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results (default: 10)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Offset for pagination (default: 0)' })
  @ApiResponse({
    status: 200,
    description: 'NFT generation history retrieved successfully'
  })
  @ApiResponse({ status: 400, description: 'Invalid userId' })
  async getUserNftHistory(
    @Param('userId') userId: string,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Headers() headers: any,
    @Res() res: Response
  ) {
    try {
      const correlationId = headers['x-correlation-id'] || `nft-history-${Date.now()}`;

      const result = await this.profileNftGenerationService.getUserNftHistory(userId, limit, offset);

      return res.status(HttpStatus.OK).json({
        success: true,
        data: {
          ...result,
          limit,
          offset
        },
        correlationId
      });

    } catch (error) {
      return res.status(error.status || HttpStatus.BAD_REQUEST).json({
        success: false,
        error: {
          code: error.code || 'NftHistoryException',
          message: error.message || 'Failed to retrieve NFT history',
          timestamp: new Date().toISOString(),
          correlationId: headers['x-correlation-id']
        }
      });
    }
  }
}
