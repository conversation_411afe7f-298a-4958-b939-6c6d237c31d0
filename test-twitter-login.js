#!/usr/bin/env node

/**
 * Twitter Login Test Script
 * Tests the complete Twitter OAuth flow
 */

const http = require('http');

async function testTwitterLogin() {
  console.log('🐦 Testing Twitter Login Flow\n');

  // Test 1: Check if Twitter OAuth endpoint exists
  console.log('1. Testing Twitter OAuth endpoint...');
  
  try {
    const response = await fetch('http://localhost:3010/api/auth/twitter/login', {
      method: 'GET',
      redirect: 'manual' // Don't follow redirects
    });
    
    if (response.status === 302) {
      const location = response.headers.get('location');
      console.log('✅ Twitter OAuth endpoint working');
      console.log('📍 Redirect URL:', location);
      
      if (location && location.includes('mock_auth_code')) {
        console.log('🔧 Mock OAuth flow detected');
      } else if (location && location.includes('twitter.com')) {
        console.log('🔗 Real Twitter OAuth flow detected');
      }
    } else {
      console.log('❌ Unexpected response status:', response.status);
    }
  } catch (error) {
    console.log('❌ Twitter OAuth endpoint error:', error.message);
  }

  // Test 2: Test callback URL parsing
  console.log('\n2. Testing callback URL parsing...');
  
  const mockCallbackUrl = 'http://localhost:3000/auth/twitter/callback?code=mock_auth_code_123&state=mock_state';
  const url = new URL(mockCallbackUrl);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');
  
  console.log('📝 Mock callback URL:', mockCallbackUrl);
  console.log('🔑 Extracted code:', code);
  console.log('🏷️ Extracted state:', state);
  
  if (code && state) {
    console.log('✅ Callback URL parsing works');
  } else {
    console.log('❌ Callback URL parsing failed');
  }

  // Test 3: Check frontend callback page
  console.log('\n3. Testing frontend callback page...');
  
  try {
    const callbackResponse = await fetch(mockCallbackUrl);
    
    if (callbackResponse.ok) {
      console.log('✅ Frontend callback page accessible');
      console.log('📄 Response status:', callbackResponse.status);
    } else {
      console.log('❌ Frontend callback page error:', callbackResponse.status);
    }
  } catch (error) {
    console.log('❌ Frontend callback page error:', error.message);
  }

  // Test 4: Check auth context endpoints
  console.log('\n4. Testing auth context endpoints...');
  
  const authEndpoints = [
    'http://localhost:3010/api/auth/twitter',
    'http://localhost:3011/api/auth/twitter',
    'http://localhost:3002/api/auth/twitter/login'
  ];
  
  for (const endpoint of authEndpoints) {
    try {
      const response = await fetch(endpoint, { 
        method: 'GET',
        redirect: 'manual'
      });
      
      console.log(`📡 ${endpoint}: ${response.status}`);
      
      if (response.status === 302) {
        const location = response.headers.get('location');
        console.log(`   ↩️ Redirects to: ${location}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
    }
  }

  console.log('\n🧪 Manual Test Instructions:');
  console.log('═'.repeat(50));
  console.log('1. Go to: http://localhost:3000/auth/login');
  console.log('2. Click the Twitter button');
  console.log('3. Should redirect through OAuth flow');
  console.log('4. Should end up on dashboard with user logged in');
  console.log('');
  console.log('🔍 Check browser console for debug messages:');
  console.log('   • "🐦 Profile Analysis Service: Twitter OAuth initiation"');
  console.log('   • "🔧 Processing mock Twitter authentication"');
  console.log('   • "✅ Auth Context: User updated successfully"');
  console.log('');
  console.log('📊 Expected Flow:');
  console.log('   Login Page → Twitter OAuth → Callback Page → Dashboard');
}

// Add fetch polyfill for Node.js
if (typeof fetch === 'undefined') {
  global.fetch = async (url, options = {}) => {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const httpModule = isHttps ? require('https') : require('http');
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {}
      };
      
      const req = httpModule.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            headers: {
              get: (name) => res.headers[name.toLowerCase()]
            },
            text: () => Promise.resolve(data),
            json: () => Promise.resolve(JSON.parse(data))
          });
        });
      });
      
      req.on('error', reject);
      
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  };
}

testTwitterLogin().catch(console.error);
