<!DOCTYPE html>
<html>
<head>
    <title>JWT Token Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .token { word-break: break-all; background: #f5f5f5; padding: 10px; border-radius: 3px; }
        button { margin: 5px; padding: 10px 15px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🔐 JWT Token Debugging Tool</h1>
    
    <div class="section">
        <h3>Current Token Status</h3>
        <button onclick="checkToken()">Check Current Token</button>
        <button onclick="clearToken()">Clear Token</button>
        <div id="tokenStatus"></div>
    </div>
    
    <div class="section">
        <h3>Test Token Storage</h3>
        <button onclick="setTestToken()">Set Test Token</button>
        <button onclick="testTokenPersistence()">Test Persistence</button>
        <div id="testResults"></div>
    </div>
    
    <div class="section">
        <h3>Login Test</h3>
        <button onclick="testLogin()">Test Login API</button>
        <div id="loginResults"></div>
    </div>

    <script>
        function checkToken() {
            const token = localStorage.getItem('auth_token');
            const status = document.getElementById('tokenStatus');
            
            if (token) {
                status.innerHTML = `
                    <div class="success">✅ Token Found</div>
                    <div class="token">Token: ${token.substring(0, 50)}...</div>
                    <div>Length: ${token.length} characters</div>
                `;
            } else {
                status.innerHTML = '<div class="error">❌ No token found in localStorage</div>';
            }
        }

        function clearToken() {
            localStorage.removeItem('auth_token');
            document.getElementById('tokenStatus').innerHTML = '<div class="success">✅ Token cleared</div>';
        }

        function setTestToken() {
            const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test-token-' + Date.now();
            localStorage.setItem('auth_token', testToken);
            document.getElementById('testResults').innerHTML = '<div class="success">✅ Test token set</div>';
            checkToken();
        }

        function testTokenPersistence() {
            const token = localStorage.getItem('auth_token');
            const results = document.getElementById('testResults');
            
            if (token) {
                results.innerHTML = '<div class="success">✅ Token persists across page operations</div>';
            } else {
                results.innerHTML = '<div class="error">❌ Token not persisting</div>';
            }
        }

        async function testLogin() {
            const results = document.getElementById('loginResults');
            results.innerHTML = '<div>🔄 Testing login API...</div>';
            
            try {
                const response = await fetch('http://localhost:3011/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('auth_token', data.accessToken);
                    results.innerHTML = `
                        <div class="success">✅ Login successful!</div>
                        <div>Token saved to localStorage</div>
                    `;
                    checkToken();
                } else {
                    results.innerHTML = `<div class="error">❌ Login failed: ${response.status}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="error">❌ Login error: ${error.message}</div>`;
            }
        }

        // Check token on page load
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
