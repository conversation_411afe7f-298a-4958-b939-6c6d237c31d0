import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'ok',
      service: 'marketplace-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      port: process.env.PORT || 3007,
      environment: process.env.NODE_ENV || 'development',
    };
  }

  getMarketplaceStatus() {
    return {
      status: 'ok',
      service: 'marketplace-service',
      timestamp: new Date().toISOString(),
      marketplace: {
        fee_percentage: parseFloat(process.env.MARKETPLACE_FEE_PERCENTAGE) || 2.5,
        minimum_listing_price: parseFloat(process.env.MINIMUM_LISTING_PRICE) || 0.001,
        maximum_listing_price: parseFloat(process.env.MAXIMUM_LISTING_PRICE) || 1000,
        default_auction_duration: parseInt(process.env.DEFAULT_AUCTION_DURATION) || 7,
        escrow_enabled: process.env.ESCROW_ENABLED === 'true',
      },
      supported_currencies: (process.env.SUPPORTED_CURRENCIES || 'ETH,MATIC,USDC,USDT').split(','),
      default_currency: process.env.DEFAULT_CURRENCY || 'ETH',
      integrations: {
        nft_generation_service: process.env.NFT_GENERATION_SERVICE_URL || 'http://localhost:3004',
        blockchain_service: process.env.BLOCKCHAIN_SERVICE_URL || 'http://localhost:3005',
        user_service: process.env.USER_SERVICE_URL || 'http://localhost:3001',
        notification_service: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3008',
      },
      external_apis: {
        coingecko: process.env.COINGECKO_API_URL || 'https://api.coingecko.com/api/v3',
        opensea: process.env.OPENSEA_API_URL || 'https://api.opensea.io/api/v1',
      },
    };
  }
}
