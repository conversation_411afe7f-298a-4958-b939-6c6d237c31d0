// Enterprise Notification Service - Prisma Schema Template
// CQRS Pattern with Command/Query Models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENTERPRISE COMMAND MODELS (Write Side)
// ========================================

model NotificationCommand {
  id                  String   @id @default(cuid())

  // Core Notification Data
  type                String   // "email", "push", "sms", "in_app"
  channel             String   // "email", "push_notification", "sms", "system"
  recipientId         String   // User ID receiving notification
  recipientEmail      String?  // Email address (for email notifications)
  recipientPhone      String?  // Phone number (for SMS notifications)

  // Message Content
  subject             String?  // Email subject or notification title
  message             String   // Notification message/body
  templateId          String?  // Template ID if using template
  templateData        Json?    // Template variables

  // Delivery Settings
  priority            String   @default("normal") // low, normal, high, urgent
  scheduledAt         DateTime? // For scheduled notifications
  expiresAt           DateTime? // Notification expiration

  // Status
  status              String   @default("pending") // pending, sent, delivered, failed, expired

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  // Enterprise Compliance
  dataClassification  String   @default("internal")
  retentionPolicy     String   @default("7years")

  @@map("notification_commands")
}

model TemplateCommand {
  id                  String   @id @default(cuid())

  // Core Template Data
  name                String   @unique
  type                String   // "email", "push", "sms", "in_app"
  category            String?  // "welcome", "alert", "reminder", "marketing"

  // Template Content
  subject             String?  // Email subject template
  bodyTemplate        String   // Message body template with variables
  variables           Json?    // Available template variables

  // Settings
  isActive            Boolean  @default(true)
  language            String   @default("en")

  // Status
  status              String   @default("active") // active, inactive, deprecated

  // Enterprise Audit Fields
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?
  version             Int      @default(1)

  @@map("template_commands")
}

// ========================================
// ENTERPRISE QUERY MODELS (Read Side)
// ========================================

model NotificationQuery {
  id                  String   @id

  // Optimized Display Data
  displayType         String
  displayChannel      String
  displaySubject      String?
  displayMessage      String
  recipientId         String
  recipientName       String?

  // Status & Delivery Info
  status              String
  priority            String
  deliveredAt         DateTime?
  readAt              DateTime?

  // Performance Metrics
  deliveryTime        Float?   // Time to deliver in seconds
  openRate            Float?   // For email notifications
  clickRate           Float?   // For notifications with links

  // Aggregation Data
  retryCount          Int      @default(0)
  errorMessage        String?

  // Timestamps
  createdAt           DateTime
  lastUpdated         DateTime @updatedAt

  @@map("notification_queries")
}

// ========================================
// ENTERPRISE AUDIT & EVENT SOURCING
// ========================================

model AuditLog {
  id              String   @id @default(cuid())
  entityType      String   // "notification", "template", "delivery"
  entityId        String
  action          String   // "create", "send", "deliver", "read", "retry"
  oldValues       Json?
  newValues       Json?
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?
  correlationId   String?
  createdAt       DateTime @default(now())

  @@map("audit_logs")
}

model NotificationEvent {
  id              String   @id @default(cuid())
  eventType       String   // "notification_sent", "email_delivered", "push_opened"
  eventVersion    String   @default("1.0")
  aggregateId     String   // Notification/Template ID
  eventData       Json
  correlationId   String?
  causationId     String?
  userId          String?
  createdAt       DateTime @default(now())

  @@map("notification_events")
}
