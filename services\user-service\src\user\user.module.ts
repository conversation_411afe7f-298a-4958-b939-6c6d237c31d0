import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';

// Services
import { UserCommandService } from './services/user-command.service';
import { UserQueryService } from './services/user-query.service';
import { UserAnalyticsService } from './services/user-analytics.service';
import { AuthService } from './services/auth.service';
import { RBACService } from './services/rbac.service';
import { CampaignService } from './services/campaign.service';
import { NFTService } from './services/nft.service';
import { MarketplaceService } from './services/marketplace.service';
import { AnalyticsService } from './services/analytics.service';
import { RealtimeService } from './services/realtime.service';
import { SearchService } from './services/search.service';
import { AuditService } from '../audit/audit.service';
import { CacheService } from '../shared/services/cache.service';
import { PrismaService } from '../prisma/prisma.service';
import { TwitterUserService } from '../enterprise/services/twitter-user.service';

// Controllers
import { UserQueryController } from './controllers/user-query.controller';
import { UserCommandController } from './controllers/user-command.controller';
import { AuthController } from './controllers/auth.controller';
import { RBACController } from './controllers/rbac.controller';
import { CampaignController } from './controllers/campaign.controller';
import { NFTController } from './controllers/nft.controller';
import { MarketplaceController } from './controllers/marketplace.controller';
import { AnalyticsController } from './controllers/analytics.controller';
import { RealtimeController } from './controllers/realtime.controller';
import { SearchController } from './controllers/search.controller';
import { HealthController } from '../health/health.controller';
import { TwitterUserController } from './controllers/twitter-user.controller';

// Gateways
import { RealtimeGateway } from './gateways/realtime.gateway';

// Health Services
import { DatabaseHealthService } from '../health/database-health.service';
import { CacheHealthService } from '../health/cache-health.service';

@Module({
  imports: [
    TerminusModule,
    ConfigModule,
    HttpModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN') || '15m',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [UserQueryController, UserCommandController, AuthController, RBACController, CampaignController, NFTController, MarketplaceController, AnalyticsController, RealtimeController, SearchController, HealthController, TwitterUserController],
  providers: [
    PrismaService,
    UserCommandService,
    UserQueryService,
    UserAnalyticsService,
    AuthService,
    RBACService,
    CampaignService,
    NFTService,
    MarketplaceService,
    AnalyticsService,
    RealtimeService,
    SearchService,
    AuditService,
    CacheService,
    DatabaseHealthService,
    CacheHealthService,
    RealtimeGateway,
    TwitterUserService,
  ],
  exports: [UserCommandService, UserQueryService, AuthService, RBACService, CampaignService, NFTService, MarketplaceService, AnalyticsService, RealtimeService, SearchService, AuditService, RealtimeGateway, TwitterUserService],
})
export class UserModule {}
