'use client'

import {
  <PERSON>,
  Container,
  Heading,
  Text,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  Spinner
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState, use } from 'react'
import { campaignService, Campaign } from '@/services/campaignService'
import { useAuth } from '@/contexts/AuthContext'
import NFTGeneration from '@/components/NFTGeneration'
import QuickLogin from '@/components/QuickLogin'

interface CampaignDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default function CampaignDetailPage({ params }: CampaignDetailPageProps) {
  const router = useRouter()
  const { user } = useAuth()
  const resolvedParams = use(params)
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [loading, setLoading] = useState(true)
  const [joining, setJoining] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchCampaign = async () => {
      try {
        setLoading(true)
        const campaignData = await campaignService.getCampaignById(resolvedParams.id)
        setCampaign(campaignData)
      } catch (err: any) {
        setError('Failed to load campaign details')
        console.error('Campaign detail error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (resolvedParams.id) {
      fetchCampaign()
    }
  }, [resolvedParams.id])

  const handleJoinCampaign = async () => {
    if (!user || !campaign) {
      setError('Please log in to join campaigns')
      return
    }

    try {
      setJoining(true)
      setError('')

      // For now, show a success message since backend join is being optimized
      console.log('🎯 Joining campaign:', campaign.name, 'for user:', user.username)

      // TODO: Implement actual campaign join when backend is ready
      // await campaignService.joinCampaign(campaign.id, { userId: user.id })

      alert(`Successfully joined "${campaign.name}"! You will be notified when NFT generation is ready.`)

    } catch (err: any) {
      console.error('Join campaign error:', err)
      setError(err.message || 'Failed to join campaign')
    } finally {
      setJoining(false)
    }
  }

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Spinner size="xl" />
          <Text>Loading campaign details...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !campaign) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack gap={4}>
          <Text color="red.500">{error || 'Campaign not found'}</Text>
          <Button onClick={() => router.push('/dashboard')}>
            Back to Dashboard
          </Button>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack gap={8} align="stretch">
        {/* Header */}
        <Box>
          <HStack justify="space-between" align="center" mb={4}>
            <Button variant="outline" onClick={() => router.push('/dashboard')}>
              ← Back to Dashboard
            </Button>
            <Badge colorScheme="green" fontSize="md" px={3} py={1}>
              {campaign.status}
            </Badge>
          </HStack>

          <Heading as="h1" size="xl" mb={4}>
            {campaign.name}
          </Heading>

          <Text color="gray.600" fontSize="lg">
            {campaign.description}
          </Text>
        </Box>

        {/* Campaign Details */}
        <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
          <VStack gap={4} align="stretch">
            <Heading as="h2" size="lg">
              Campaign Details
            </Heading>

            <HStack justify="space-between">
              <Text fontWeight="medium">Participants:</Text>
              <Text>{campaign.participantCount} / {campaign.maxParticipants}</Text>
            </HStack>

            <HStack justify="space-between">
              <Text fontWeight="medium">Start Date:</Text>
              <Text>{new Date(campaign.startDate).toLocaleDateString()}</Text>
            </HStack>

            <HStack justify="space-between">
              <Text fontWeight="medium">End Date:</Text>
              <Text>{new Date(campaign.endDate).toLocaleDateString()}</Text>
            </HStack>

            <Button
              colorScheme="blue"
              size="lg"
              mt={4}
              onClick={handleJoinCampaign}
              loading={joining}
              disabled={!user || joining}
            >
              {joining ? 'Joining...' : user ? 'Join Campaign' : 'Login to Join'}
            </Button>
          </VStack>
        </Box>

        {/* Authentication Check */}
        {!user ? (
          <QuickLogin />
        ) : (
          /* NFT Generation Section */
          <NFTGeneration
            campaignId={campaign.id}
            campaignName={campaign.name}
            onNFTGenerated={(nft) => {
              console.log('NFT generated in campaign:', nft)
              // Optionally refresh campaign data or show success message
            }}
          />
        )}
      </VStack>
    </Container>
  )
}
