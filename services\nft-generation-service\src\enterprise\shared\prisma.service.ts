// Enterprise Prisma Service
import { Injectable, <PERSON><PERSON>, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      },
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error']
    });

    // Simple error logging
    this.$on('error' as never, (e: any) => {
      this.logger.error(`Database error: ${e.message}`, e.target);
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log('✅ Connected to PostgreSQL database');
    } catch (error) {
      this.logger.error('❌ Failed to connect to database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.logger.log('✅ Disconnected from PostgreSQL database');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database:', error);
    }
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Get database statistics
  async getDatabaseStats() {
    try {
      const [nftCommands, nftQueries, auditLogs, events] = await Promise.all([
        this.nftCommand.count(),
        this.nftQuery.count(),
        this.auditLog.count(),
        this.nftEvent.count()
      ]);

      return {
        nftCommands,
        nftQueries,
        auditLogs,
        events,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Failed to get database stats:', error);
      throw error;
    }
  }
}
