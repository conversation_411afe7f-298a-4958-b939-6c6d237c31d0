// Enterprise Project Command Controller (Write Side) - Requirements-Driven Implementation
import {
  <PERSON>,
  Post,
  Put,
  Delete,
  Body,
  Param,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  HttpStatus,
  UseGuards,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, <PERSON>piParam, Api<PERSON>eader } from '@nestjs/swagger';
import { Response } from 'express';
import { CreateProjectCommandDto, UpdateProjectCommandDto } from '../models/project-command.model';
import { ProjectCommandService } from '../services/project-command.service';

@ApiTags('Project Commands (Write Operations)')
@Controller('enterprise/projects')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class ProjectCommandController {
  constructor(private readonly projectCommandService: ProjectCommandService) {}

  @Post()
  @ApiOperation({
    summary: 'Create new project with complete configuration',
    description: 'Creates a new project with analysis parameters, NFT configuration, and blockchain settings'
  })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data or configuration' })
  @ApiResponse({ status: 409, description: 'Project with this name already exists' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async createProject(
    @Body() createProjectDto: CreateProjectCommandDto,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.projectCommandService.createProject(
        createProjectDto,
        correlationId || `proj-create-${Date.now()}`
      );

      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update existing project',
    description: 'Updates project configuration including analysis parameters and NFT settings'
  })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async updateProject(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectCommandDto,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.projectCommandService.updateProject(
        id,
        updateProjectDto,
        correlationId || `proj-update-${Date.now()}`
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete project (soft delete)',
    description: 'Archives the project while maintaining audit trail and data integrity'
  })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Project deleted successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiHeader({ name: 'X-Correlation-ID', description: 'Request correlation ID' })
  async deleteProject(
    @Param('id') id: string,
    @Headers('x-correlation-id') correlationId: string,
    @Res() res: Response
  ) {
    try {
      const result = await this.projectCommandService.deleteProject(
        id,
        correlationId || `proj-delete-${Date.now()}`
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message,
        correlationId,
      });
    }
  }
}
