// Project Service - Main Application Module
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EnterpriseModule } from './enterprise/enterprise.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    EnterpriseModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
