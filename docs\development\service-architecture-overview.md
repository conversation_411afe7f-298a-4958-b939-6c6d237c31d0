# Service Architecture Overview

## Current Architecture Status
**Date:** May 25, 2025  
**Project:** Social NFT Platform v2  
**Architecture:** Hybrid Microservices with API Gateway

## Active Services (services/ directory)

### ✅ Fully Implemented & Running

#### 1. API Gateway
- **Location:** `services/api-gateway/`
- **Port:** 3010
- **Status:** ✅ Running
- **Features:**
  - Request routing to microservices
  - JWT authentication middleware
  - CORS configuration
  - Rate limiting (100 req/min)
  - Health monitoring
  - Request/response logging
  - Security headers (Helmet.js)

#### 2. User Service
- **Location:** `services/user-service/`
- **Port:** 3001
- **Status:** ✅ Running
- **Features:**
  - User registration/login
  - JWT token generation
  - Password hashing (bcrypt)
  - PostgreSQL integration
  - User profile management
  - Swagger documentation

#### 3. Profile Analysis Service
- **Location:** `services/profile-analysis-service/`
- **Port:** 3002
- **Status:** ✅ Running
- **Features:**
  - Twitter profile analysis
  - Mock data generation
  - Analysis history tracking
  - PostgreSQL integration
  - Configurable parameters

#### 4. Frontend Application
- **Location:** `frontend/`
- **Port:** 3100
- **Status:** ✅ Running
- **Framework:** React with TypeScript
- **UI Library:** Chakra UI
- **Features:**
  - User authentication UI
  - Registration/Login forms
  - Dashboard interface
  - API integration
  - Error handling

## Service Integration Status

### ✅ Completed Integrations
1. **API Gateway ↔ User Service**
   - Authentication endpoints
   - User management
   - Health checks

2. **API Gateway ↔ Profile Analysis Service**
   - Twitter analysis endpoints
   - Results retrieval
   - History tracking

3. **Frontend ↔ API Gateway**
   - Authentication flow
   - CORS configuration
   - Error handling

### 🔧 Pending Integrations
1. **Project Service** - Not yet migrated from packages/
2. **NFT Generation Service** - Not yet migrated from packages/
3. **Blockchain Service** - Not yet migrated from packages/
4. **Marketplace Service** - Not yet migrated from packages/
5. **Analytics Service** - Not yet migrated from packages/

## Port Allocation
- **3000:** Reserved for future use
- **3001:** User Service ✅
- **3002:** Profile Analysis Service ✅
- **3003:** NFT Generation Service (planned)
- **3004:** Blockchain Service (planned)
- **3005:** Marketplace Service (planned)
- **3006:** Project Service (planned)
- **3007:** Analytics Service (planned)
- **3010:** API Gateway ✅
- **3100:** Frontend ✅

## Database Configuration
- **Type:** PostgreSQL
- **Status:** ✅ Connected
- **Services Using DB:**
  - User Service (user data)
  - Profile Analysis Service (analysis results)

## API Gateway Routing
```
/api/users/*          → User Service (3001)
/api/analysis/*       → Profile Analysis Service (3002)
/api/health           → API Gateway health check
/api/services         → Service status overview
```

## Next Integration Targets
1. **Phase 1B:** Project Service integration
2. **Phase 1C:** NFT Generation Service integration
3. **Phase 1D:** Blockchain Service integration
4. **Phase 1E:** Marketplace Service integration
