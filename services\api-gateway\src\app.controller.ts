import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('Gateway')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get API Gateway status' })
  @ApiResponse({ status: 200, description: 'API Gateway is running' })
  getStatus(): string {
    return this.appService.getStatus();
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for API Gateway and all services' })
  @ApiResponse({ status: 200, description: 'Health status of all services' })
  async getHealth() {
    return await this.appService.getHealthCheck();
  }

  @Get('services')
  @ApiOperation({ summary: 'Get status of all registered services' })
  @ApiResponse({ status: 200, description: 'Status of all microservices' })
  async getServicesStatus() {
    return await this.appService.getServicesStatus();
  }
}
