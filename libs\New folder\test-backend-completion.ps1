# Backend Completion Testing Script
Write-Host "=== BACKEND COMPLETION TESTING ===" -ForegroundColor Cyan
Write-Host "Testing all services to identify working vs non-working endpoints..." -ForegroundColor Green

$workingEndpoints = @()
$failingEndpoints = @()

# Test 1: User Service (Known Working)
Write-Host "`n1. Testing User Service..." -ForegroundColor Yellow
try {
    # Health Check
    $userHealth = Invoke-RestMethod -Uri "http://localhost:3011/health" -Method Get
    Write-Host "✅ User Service Health: OK" -ForegroundColor Green
    $workingEndpoints += "User Service Health"
    
    # Registration (Already tested - working)
    Write-Host "✅ User Registration: WORKING" -ForegroundColor Green
    $workingEndpoints += "User Registration"
    
    # Login (Already tested - working)
    Write-Host "✅ User Login: WORKING" -ForegroundColor Green
    $workingEndpoints += "User Login"
    
} catch {
    Write-Host "❌ User Service Error: $($_.Exception.Message)" -ForegroundColor Red
    $failingEndpoints += "User Service"
}

# Test 2: Profile Analysis Service
Write-Host "`n2. Testing Profile Analysis Service..." -ForegroundColor Yellow
try {
    # Health Check
    $profileHealth = Invoke-RestMethod -Uri "http://localhost:3002/health" -Method Get
    Write-Host "✅ Profile Analysis Health: OK" -ForegroundColor Green
    $workingEndpoints += "Profile Analysis Health"
    
    # Get Analysis History (Known Working)
    $analysisHistory = Invoke-RestMethod -Uri "http://localhost:3002/twitter-analysis/history" -Method Get
    Write-Host "✅ Profile Analysis History: $($analysisHistory.Count) analyses found" -ForegroundColor Green
    $workingEndpoints += "Profile Analysis History"
    
} catch {
    Write-Host "❌ Profile Analysis Error: $($_.Exception.Message)" -ForegroundColor Red
    $failingEndpoints += "Profile Analysis"
}

# Test 3: Project Service
Write-Host "`n3. Testing Project Service..." -ForegroundColor Yellow
try {
    # Health Check
    $projectHealth = Invoke-RestMethod -Uri "http://localhost:3005/health" -Method Get
    Write-Host "✅ Project Service Health: OK" -ForegroundColor Green
    $workingEndpoints += "Project Service Health"
    
    # Get Campaigns (Known Working)
    $campaigns = Invoke-RestMethod -Uri "http://localhost:3005/api/campaigns" -Method Get
    Write-Host "✅ Campaign Discovery: $($campaigns.Count) campaigns found" -ForegroundColor Green
    $workingEndpoints += "Campaign Discovery"
    
} catch {
    Write-Host "❌ Project Service Error: $($_.Exception.Message)" -ForegroundColor Red
    $failingEndpoints += "Project Service"
}

# Test 4: NFT Generation Service
Write-Host "`n4. Testing NFT Generation Service..." -ForegroundColor Yellow
try {
    # Health Check
    $nftHealth = Invoke-RestMethod -Uri "http://localhost:3003/api/health" -Method Get
    Write-Host "✅ NFT Generation Health: OK" -ForegroundColor Green
    $workingEndpoints += "NFT Generation Health"
    
    # Get User NFTs (Known Working)
    $userNFTs = Invoke-RestMethod -Uri "http://localhost:3003/api/nft-generation/user/test-user-123" -Method Get
    Write-Host "✅ NFT User Query: Working" -ForegroundColor Green
    $workingEndpoints += "NFT User Query"
    
} catch {
    Write-Host "❌ NFT Generation Error: $($_.Exception.Message)" -ForegroundColor Red
    $failingEndpoints += "NFT Generation"
}

# Test 5: All Other Services Health
Write-Host "`n5. Testing Remaining Services Health..." -ForegroundColor Yellow
$otherServices = @(
    @{name="Blockchain Service"; url="http://localhost:3004/api/health"},
    @{name="Marketplace Service"; url="http://localhost:3006/api/health"},
    @{name="Analytics Service"; url="http://localhost:3007/api/health"},
    @{name="Notification Service"; url="http://localhost:3008/api/health"},
    @{name="API Gateway"; url="http://localhost:3010/api/health"}
)

foreach ($service in $otherServices) {
    try {
        $health = Invoke-RestMethod -Uri $service.url -Method Get
        Write-Host "✅ $($service.name): Healthy" -ForegroundColor Green
        $workingEndpoints += "$($service.name) Health"
    } catch {
        Write-Host "❌ $($service.name): Unhealthy" -ForegroundColor Red
        $failingEndpoints += "$($service.name) Health"
    }
}

# Summary Report
Write-Host "`n=== BACKEND COMPLETION SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ WORKING ENDPOINTS: $($workingEndpoints.Count)" -ForegroundColor Green
foreach ($endpoint in $workingEndpoints) {
    Write-Host "  ✅ $endpoint" -ForegroundColor Green
}

Write-Host "`n❌ FAILING ENDPOINTS: $($failingEndpoints.Count)" -ForegroundColor Red
foreach ($endpoint in $failingEndpoints) {
    Write-Host "  ❌ $endpoint" -ForegroundColor Red
}

Write-Host "`n🎯 BACKEND STATUS:" -ForegroundColor Cyan
$successRate = [math]::Round(($workingEndpoints.Count / ($workingEndpoints.Count + $failingEndpoints.Count)) * 100, 1)
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if($successRate -gt 80){"Green"}elseif($successRate -gt 60){"Yellow"}else{"Red"})

if ($successRate -gt 80) {
    Write-Host "🎉 BACKEND IS MOSTLY OPERATIONAL!" -ForegroundColor Green
    Write-Host "Ready for frontend integration with working services." -ForegroundColor Green
} else {
    Write-Host "⚠️ BACKEND NEEDS MORE WORK" -ForegroundColor Yellow
    Write-Host "Focus on fixing failing endpoints before frontend integration." -ForegroundColor Yellow
}

Write-Host "`nBackend Completion Testing Complete!" -ForegroundColor Cyan
