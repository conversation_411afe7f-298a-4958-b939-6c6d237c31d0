import { IsString, IsOptional, <PERSON>Enum, IsNumber, IsBoolean, IsArray, ValidateNested, Min, Max, IsUrl, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum ListingStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SOLD = 'sold',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
}

export enum ListingType {
  FIXED_PRICE = 'fixed_price',
  AUCTION = 'auction',
  DUTCH_AUCTION = 'dutch_auction',
  BUNDLE = 'bundle',
  OFFER = 'offer',
}

export enum TransactionType {
  LISTING = 'listing',
  SALE = 'sale',
  OFFER = 'offer',
  BID = 'bid',
  TRANSFER = 'transfer',
  CANCEL = 'cancel',
}

export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum PaymentMethod {
  ETH = 'eth',
  WETH = 'weth',
  USDC = 'usdc',
  USDT = 'usdt',
  DAI = 'dai',
  MATIC = 'matic',
  BNB = 'bnb',
}

export class CreateListingDto {
  @ApiProperty({
    description: 'NFT ID to list',
    example: 'nft_123',
  })
  @IsString()
  nftId: string;

  @ApiProperty({
    description: 'Listing type',
    enum: ListingType,
    example: ListingType.FIXED_PRICE,
  })
  @IsEnum(ListingType)
  listingType: ListingType;

  @ApiProperty({
    description: 'Listing price in wei or smallest unit',
    example: '1000000000000000000',
  })
  @IsString()
  price: string;

  @ApiProperty({
    description: 'Payment method/currency',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  @IsEnum(PaymentMethod)
  currency: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Listing duration in seconds',
    example: 604800,
    default: 604800,
  })
  @IsOptional()
  @IsNumber()
  @Min(3600)
  duration?: number = 604800; // 7 days default

  @ApiPropertyOptional({
    description: 'Starting price for auctions',
    example: '500000000000000000',
  })
  @IsOptional()
  @IsString()
  startingPrice?: string;

  @ApiPropertyOptional({
    description: 'Reserve price for auctions',
    example: '800000000000000000',
  })
  @IsOptional()
  @IsString()
  reservePrice?: string;

  @ApiPropertyOptional({
    description: 'Buy now price for auctions',
    example: '2000000000000000000',
  })
  @IsOptional()
  @IsString()
  buyNowPrice?: string;

  @ApiPropertyOptional({
    description: 'Listing title',
    example: 'Rare Cosmic Warrior NFT',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Listing description',
    example: 'A legendary cosmic warrior with extraordinary powers and unique traits.',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Listing tags for categorization',
    example: ['rare', 'cosmic', 'warrior', 'legendary'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Whether to accept offers',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  acceptOffers?: boolean = true;

  @ApiPropertyOptional({
    description: 'Minimum offer amount',
    example: '100000000000000000',
  })
  @IsOptional()
  @IsString()
  minOfferAmount?: string;

  @ApiPropertyOptional({
    description: 'Additional listing metadata',
    example: {
      featured: false,
      category: 'gaming',
      collection: 'cosmic_warriors',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateListingDto {
  @ApiPropertyOptional({
    description: 'Updated listing price',
    example: '1200000000000000000',
  })
  @IsOptional()
  @IsString()
  price?: string;

  @ApiPropertyOptional({
    description: 'Updated listing status',
    enum: ListingStatus,
    example: ListingStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ListingStatus)
  status?: ListingStatus;

  @ApiPropertyOptional({
    description: 'Updated listing duration',
    example: 1209600,
  })
  @IsOptional()
  @IsNumber()
  @Min(3600)
  duration?: number;

  @ApiPropertyOptional({
    description: 'Updated listing title',
    example: 'Updated Rare Cosmic Warrior NFT',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Updated listing description',
    example: 'Updated description with new features.',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Updated listing tags',
    example: ['rare', 'cosmic', 'warrior', 'legendary', 'updated'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Updated accept offers setting',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  acceptOffers?: boolean;

  @ApiPropertyOptional({
    description: 'Updated minimum offer amount',
    example: '150000000000000000',
  })
  @IsOptional()
  @IsString()
  minOfferAmount?: string;

  @ApiPropertyOptional({
    description: 'Updated listing metadata',
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class CreateOfferDto {
  @ApiProperty({
    description: 'Listing ID to make offer on',
    example: 'listing_456',
  })
  @IsString()
  listingId: string;

  @ApiProperty({
    description: 'Offer amount in wei or smallest unit',
    example: '900000000000000000',
  })
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'Payment method/currency for offer',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  @IsEnum(PaymentMethod)
  currency: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Offer expiration time',
    example: '2025-06-10T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Offer message or notes',
    example: 'I love this NFT and would like to add it to my collection!',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({
    description: 'Additional offer metadata',
    example: {
      urgent: false,
      bundleOffer: false,
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class AcceptOfferDto {
  @ApiProperty({
    description: 'Offer ID to accept',
    example: 'offer_789',
  })
  @IsString()
  offerId: string;

  @ApiPropertyOptional({
    description: 'Seller signature for transaction',
    example: '0x1234567890abcdef...',
  })
  @IsOptional()
  @IsString()
  signature?: string;

  @ApiPropertyOptional({
    description: 'Additional acceptance metadata',
    example: {
      acceptanceMessage: 'Thank you for your offer!',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class BuyNowDto {
  @ApiProperty({
    description: 'Listing ID to purchase',
    example: 'listing_456',
  })
  @IsString()
  listingId: string;

  @ApiProperty({
    description: 'Purchase amount (should match listing price)',
    example: '1000000000000000000',
  })
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'Payment method/currency',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  @IsEnum(PaymentMethod)
  currency: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Buyer signature for transaction',
    example: '0xabcdef1234567890...',
  })
  @IsOptional()
  @IsString()
  signature?: string;

  @ApiPropertyOptional({
    description: 'Gas price for transaction',
    example: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  gasPrice?: number;

  @ApiPropertyOptional({
    description: 'Additional purchase metadata',
    example: {
      purchaseMessage: 'Excited to own this NFT!',
    },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class MarketplaceListingResponseDto {
  @ApiProperty({
    description: 'Listing ID',
    example: 'listing_456',
  })
  id: string;

  @ApiProperty({
    description: 'NFT ID being listed',
    example: 'nft_123',
  })
  nftId: string;

  @ApiProperty({
    description: 'NFT details',
  })
  nft: {
    id: string;
    metadata: any;
    rarity: string;
    imageUrl: string;
    tokenId?: string;
    contractAddress?: string;
  };

  @ApiProperty({
    description: 'Seller user ID',
    example: 'user_123',
  })
  sellerId: string;

  @ApiProperty({
    description: 'Seller details',
  })
  seller: {
    id: string;
    username: string;
    displayName?: string;
  };

  @ApiProperty({
    description: 'Listing type',
    enum: ListingType,
    example: ListingType.FIXED_PRICE,
  })
  listingType: ListingType;

  @ApiProperty({
    description: 'Listing status',
    enum: ListingStatus,
    example: ListingStatus.ACTIVE,
  })
  status: ListingStatus;

  @ApiProperty({
    description: 'Listing price',
    example: '1000000000000000000',
  })
  price: string;

  @ApiProperty({
    description: 'Payment currency',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  currency: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Starting price for auctions',
    example: '500000000000000000',
  })
  startingPrice?: string;

  @ApiPropertyOptional({
    description: 'Current highest bid for auctions',
    example: '750000000000000000',
  })
  currentBid?: string;

  @ApiPropertyOptional({
    description: 'Number of bids',
    example: 5,
  })
  bidCount?: number;

  @ApiProperty({
    description: 'Listing title',
    example: 'Rare Cosmic Warrior NFT',
  })
  title: string;

  @ApiProperty({
    description: 'Listing description',
    example: 'A legendary cosmic warrior with extraordinary powers.',
  })
  description: string;

  @ApiProperty({
    description: 'Listing tags',
    example: ['rare', 'cosmic', 'warrior'],
  })
  tags: string[];

  @ApiProperty({
    description: 'Whether offers are accepted',
    example: true,
  })
  acceptOffers: boolean;

  @ApiPropertyOptional({
    description: 'Minimum offer amount',
    example: '100000000000000000',
  })
  minOfferAmount?: string;

  @ApiProperty({
    description: 'Listing creation timestamp',
    example: '2025-06-03T22:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Listing expiration timestamp',
    example: '2025-06-10T22:00:00Z',
  })
  expiresAt: string;

  @ApiPropertyOptional({
    description: 'Listing sold timestamp',
    example: '2025-06-05T14:30:00Z',
  })
  soldAt?: string;

  @ApiProperty({
    description: 'View count',
    example: 127,
  })
  viewCount: number;

  @ApiProperty({
    description: 'Favorite count',
    example: 23,
  })
  favoriteCount: number;

  @ApiPropertyOptional({
    description: 'Additional listing metadata',
    example: {
      featured: false,
      category: 'gaming',
    },
  })
  metadata?: Record<string, any>;
}

export class MarketplaceOfferResponseDto {
  @ApiProperty({
    description: 'Offer ID',
    example: 'offer_789',
  })
  id: string;

  @ApiProperty({
    description: 'Listing ID',
    example: 'listing_456',
  })
  listingId: string;

  @ApiProperty({
    description: 'Buyer user ID',
    example: 'user_456',
  })
  buyerId: string;

  @ApiProperty({
    description: 'Buyer details',
  })
  buyer: {
    id: string;
    username: string;
    displayName?: string;
  };

  @ApiProperty({
    description: 'Offer amount',
    example: '900000000000000000',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment currency',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  currency: PaymentMethod;

  @ApiProperty({
    description: 'Offer status',
    enum: TransactionStatus,
    example: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @ApiPropertyOptional({
    description: 'Offer message',
    example: 'I love this NFT!',
  })
  message?: string;

  @ApiProperty({
    description: 'Offer creation timestamp',
    example: '2025-06-04T10:00:00Z',
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Offer expiration timestamp',
    example: '2025-06-11T10:00:00Z',
  })
  expiresAt?: string;

  @ApiPropertyOptional({
    description: 'Offer acceptance timestamp',
    example: '2025-06-04T15:30:00Z',
  })
  acceptedAt?: string;

  @ApiPropertyOptional({
    description: 'Additional offer metadata',
  })
  metadata?: Record<string, any>;
}

export class MarketplaceTransactionResponseDto {
  @ApiProperty({
    description: 'Transaction ID',
    example: 'tx_123',
  })
  id: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: TransactionType,
    example: TransactionType.SALE,
  })
  type: TransactionType;

  @ApiProperty({
    description: 'Transaction status',
    enum: TransactionStatus,
    example: TransactionStatus.CONFIRMED,
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'NFT ID involved in transaction',
    example: 'nft_123',
  })
  nftId: string;

  @ApiProperty({
    description: 'Listing ID (if applicable)',
    example: 'listing_456',
  })
  listingId?: string;

  @ApiProperty({
    description: 'Seller user ID',
    example: 'user_123',
  })
  sellerId: string;

  @ApiProperty({
    description: 'Buyer user ID',
    example: 'user_456',
  })
  buyerId: string;

  @ApiProperty({
    description: 'Transaction amount',
    example: '1000000000000000000',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment currency',
    enum: PaymentMethod,
    example: PaymentMethod.ETH,
  })
  currency: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Blockchain transaction hash',
    example: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
  })
  transactionHash?: string;

  @ApiPropertyOptional({
    description: 'Block number',
    example: 18500000,
  })
  blockNumber?: number;

  @ApiPropertyOptional({
    description: 'Gas used',
    example: 150000,
  })
  gasUsed?: number;

  @ApiPropertyOptional({
    description: 'Gas price in gwei',
    example: 20,
  })
  gasPrice?: number;

  @ApiPropertyOptional({
    description: 'Platform fee amount',
    example: '25000000000000000',
  })
  platformFee?: string;

  @ApiPropertyOptional({
    description: 'Royalty fee amount',
    example: '50000000000000000',
  })
  royaltyFee?: string;

  @ApiProperty({
    description: 'Transaction creation timestamp',
    example: '2025-06-04T15:30:00Z',
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Transaction confirmation timestamp',
    example: '2025-06-04T15:32:00Z',
  })
  confirmedAt?: string;

  @ApiPropertyOptional({
    description: 'Additional transaction metadata',
  })
  metadata?: Record<string, any>;
}

export class MarketplaceAnalyticsDto {
  @ApiProperty({
    description: 'Total volume in ETH',
    example: '1250.75',
  })
  totalVolume: string;

  @ApiProperty({
    description: 'Volume in last 24 hours',
    example: '45.25',
  })
  volume24h: string;

  @ApiProperty({
    description: 'Volume change percentage (24h)',
    example: 12.5,
  })
  volumeChange24h: number;

  @ApiProperty({
    description: 'Total number of sales',
    example: 1847,
  })
  totalSales: number;

  @ApiProperty({
    description: 'Sales in last 24 hours',
    example: 23,
  })
  sales24h: number;

  @ApiProperty({
    description: 'Average sale price',
    example: '0.678',
  })
  averagePrice: string;

  @ApiProperty({
    description: 'Floor price',
    example: '0.125',
  })
  floorPrice: string;

  @ApiProperty({
    description: 'Ceiling price',
    example: '15.5',
  })
  ceilingPrice: string;

  @ApiProperty({
    description: 'Total active listings',
    example: 342,
  })
  activeListings: number;

  @ApiProperty({
    description: 'Total unique owners',
    example: 1205,
  })
  uniqueOwners: number;

  @ApiProperty({
    description: 'Market cap estimate',
    example: '2847.32',
  })
  marketCap: string;

  @ApiProperty({
    description: 'Analytics generation timestamp',
    example: '2025-06-04T16:00:00Z',
  })
  generatedAt: string;
}
