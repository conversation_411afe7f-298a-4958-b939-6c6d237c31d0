"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ListBulletIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = ListBulletIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ListBulletIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ListBulletIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction Squares2X2Icon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z\"\n    }));\n}\n_c = Squares2X2Icon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(Squares2X2Icon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"Squares2X2Icon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1NxdWFyZXMyWDJJY29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQy9CLFNBQVNDLGVBQWUsS0FJdkIsRUFBRUMsTUFBTTtRQUplLEVBQ3RCQyxLQUFLLEVBQ0xDLE9BQU8sRUFDUCxHQUFHQyxPQUNKLEdBSnVCO0lBS3RCLE9BQU8sV0FBVyxHQUFFTCxnREFBbUIsQ0FBQyxPQUFPTyxPQUFPQyxNQUFNLENBQUM7UUFDM0RDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsUUFBUTtRQUNSLGVBQWU7UUFDZixhQUFhO1FBQ2JDLEtBQUtaO1FBQ0wsbUJBQW1CRTtJQUNyQixHQUFHQyxRQUFRRixRQUFRLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsU0FBUztRQUMzRGUsSUFBSVg7SUFDTixHQUFHRCxTQUFTLE1BQU0sV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxRQUFRO1FBQ3pEZ0IsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLEdBQUc7SUFDTDtBQUNGO0tBdEJTakI7QUF1QlQsTUFBTWtCLGFBQWEsV0FBVyxHQUFHbkIsNkNBQWdCLENBQUNDOztBQUNsRCxpRUFBZWtCLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccnpcXERvY3VtZW50c1xcQXVnbWVudFxcc29jaWFsLW5mdC1wbGF0Zm9ybS12MlxcZnJvbnRlbmQtaGVhZGxlc3NcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXFNxdWFyZXMyWDJJY29uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gU3F1YXJlczJYMkljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIk0zLjc1IDZBMi4yNSAyLjI1IDAgMCAxIDYgMy43NWgyLjI1QTIuMjUgMi4yNSAwIDAgMSAxMC41IDZ2Mi4yNWEyLjI1IDIuMjUgMCAwIDEtMi4yNSAyLjI1SDZhMi4yNSAyLjI1IDAgMCAxLTIuMjUtMi4yNVY2Wk0zLjc1IDE1Ljc1QTIuMjUgMi4yNSAwIDAgMSA2IDEzLjVoMi4yNWEyLjI1IDIuMjUgMCAwIDEgMi4yNSAyLjI1VjE4YTIuMjUgMi4yNSAwIDAgMS0yLjI1IDIuMjVINkEyLjI1IDIuMjUgMCAwIDEgMy43NSAxOHYtMi4yNVpNMTMuNSA2YTIuMjUgMi4yNSAwIDAgMSAyLjI1LTIuMjVIMThBMi4yNSAyLjI1IDAgMCAxIDIwLjI1IDZ2Mi4yNUEyLjI1IDIuMjUgMCAwIDEgMTggMTAuNWgtMi4yNWEyLjI1IDIuMjUgMCAwIDEtMi4yNS0yLjI1VjZaTTEzLjUgMTUuNzVhMi4yNSAyLjI1IDAgMCAxIDIuMjUtMi4yNUgxOGEyLjI1IDIuMjUgMCAwIDEgMi4yNSAyLjI1VjE4QTIuMjUgMi4yNSAwIDAgMSAxOCAyMC4yNWgtMi4yNUEyLjI1IDIuMjUgMCAwIDEgMTMuNSAxOHYtMi4yNVpcIlxuICB9KSk7XG59XG5jb25zdCBGb3J3YXJkUmVmID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKFNxdWFyZXMyWDJJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbIlJlYWN0IiwiU3F1YXJlczJYMkljb24iLCJzdmdSZWYiLCJ0aXRsZSIsInRpdGxlSWQiLCJwcm9wcyIsImNyZWF0ZUVsZW1lbnQiLCJPYmplY3QiLCJhc3NpZ24iLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJyZWYiLCJpZCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiLCJGb3J3YXJkUmVmIiwiZm9yd2FyZFJlZiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TagIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction TagIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 6h.008v.008H6V6Z\"\n    }));\n}\n_c = TagIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TagIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"TagIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/layout */ \"(app-pages-browser)/./src/components/layout/layout.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/real-nft-gallery */ \"(app-pages-browser)/./src/components/dashboard/real-nft-gallery.tsx\");\n/* harmony import */ var _components_dashboard_nft_collection_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/nft-collection-manager */ \"(app-pages-browser)/./src/components/dashboard/nft-collection-manager.tsx\");\n/* harmony import */ var _components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/profile-analyzer */ \"(app-pages-browser)/./src/components/dashboard/profile-analyzer.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/user/enhanced-user-profile */ \"(app-pages-browser)/./src/components/user/enhanced-user-profile.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CubeIcon,MagnifyingGlassIcon,PlusIcon,TrophyIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst stats = [\n    {\n        name: 'Total NFTs',\n        value: '12',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        change: '+2',\n        changeType: 'positive'\n    },\n    {\n        name: 'Active Campaigns',\n        value: '3',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        change: '+1',\n        changeType: 'positive'\n    },\n    {\n        name: 'Rewards Earned',\n        value: '$1,234',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        change: '+$234',\n        changeType: 'positive'\n    },\n    {\n        name: 'Engagement Score',\n        value: '85%',\n        icon: _barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        change: '+5%',\n        changeType: 'positive'\n    }\n];\nconst recentNFTs = [\n    {\n        id: 1,\n        name: 'Cosmic Explorer #1234',\n        campaign: 'Space Campaign',\n        rarity: 'Rare',\n        image: 'https://via.placeholder.com/100x100/6366f1/ffffff?text=NFT',\n        value: '$45.00'\n    },\n    {\n        id: 2,\n        name: 'Digital Warrior #5678',\n        campaign: 'Gaming Campaign',\n        rarity: 'Epic',\n        image: 'https://via.placeholder.com/100x100/8b5cf6/ffffff?text=NFT',\n        value: '$78.00'\n    },\n    {\n        id: 3,\n        name: 'Cyber Punk #9012',\n        campaign: 'Tech Campaign',\n        rarity: 'Legendary',\n        image: 'https://via.placeholder.com/100x100/ec4899/ffffff?text=NFT',\n        value: '$156.00'\n    }\n];\nconst activeCampaigns = [\n    {\n        id: 1,\n        name: 'DeFi Revolution',\n        description: 'Promote the future of decentralized finance',\n        progress: 75,\n        reward: '$50 + NFT',\n        deadline: '3 days left',\n        participants: 1234\n    },\n    {\n        id: 2,\n        name: 'Green Blockchain',\n        description: 'Spread awareness about eco-friendly crypto',\n        progress: 45,\n        reward: '$30 + NFT',\n        deadline: '1 week left',\n        participants: 856\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [nftRefreshTrigger, setNftRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showAdvancedCollection, setShowAdvancedCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Function to trigger NFT gallery refresh\n    const handleNFTGenerated = ()=>{\n        console.log('🔄 Triggering NFT gallery refresh...');\n        setNftRefreshTrigger((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username),\n                                                        \"! Here's what's happening with your NFTs and campaigns.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Explore\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-3 gap-8 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_user_enhanced_user_profile__WEBPACK_IMPORTED_MODULE_8__.EnhancedUserProfile, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"xl:col-span-2 space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 flex justify-end\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAdvancedCollection(!showAdvancedCollection),\n                                                            className: \"text-sm text-blue-600 hover:text-blue-500 font-medium\",\n                                                            children: showAdvancedCollection ? 'Simple View' : 'Advanced Collection Manager'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showAdvancedCollection ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_nft_collection_manager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        refreshTrigger: nftRefreshTrigger\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_real_nft_gallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        limit: 6,\n                                                        refreshTrigger: nftRefreshTrigger\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-300 hover:scale-[1.02]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_analyzer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    onNFTGenerated: handleNFTGenerated\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-white shadow rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Create NFT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Join Campaign\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CubeIcon_MagnifyingGlassIcon_PlusIcon_TrophyIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"View Analytics\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"Um2q32fQGT/wCsWSA1rQ2uTy0+E=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/nft-collection-manager.tsx":
/*!*************************************************************!*\
  !*** ./src/components/dashboard/nft-collection-manager.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NFTCollectionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ListBulletIcon,MagnifyingGlassIcon,ShareIcon,Squares2X2Icon,TagIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\");\n/* harmony import */ var _enhanced_nft_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./enhanced-nft-card */ \"(app-pages-browser)/./src/components/dashboard/enhanced-nft-card.tsx\");\n/* harmony import */ var _nft_detail_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./nft-detail-modal */ \"(app-pages-browser)/./src/components/dashboard/nft-detail-modal.tsx\");\n/* harmony import */ var _components_ui_enhanced_share_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/enhanced-share-modal */ \"(app-pages-browser)/./src/components/ui/enhanced-share-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NFTCollectionManager(param) {\n    let { refreshTrigger } = param;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [nfts, setNfts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredNfts, setFilteredNfts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedNFT, setSelectedNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailModal, setShowDetailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    // Filters and search\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rarityFilter, setRarityFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [selectedNFTs, setSelectedNFTs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NFTCollectionManager.useEffect\": ()=>{\n            if (user === null || user === void 0 ? void 0 : user.id) {\n                fetchUserNFTs();\n            }\n        }\n    }[\"NFTCollectionManager.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id,\n        refreshTrigger\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NFTCollectionManager.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"NFTCollectionManager.useEffect\"], [\n        nfts,\n        searchQuery,\n        rarityFilter,\n        sortBy\n    ]);\n    const fetchUserNFTs = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        try {\n            setIsLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.nftApi.getUserNFTs(user.id);\n            if (response.success && response.data) {\n                const nftData = Array.isArray(response.data) ? response.data : response.data.nfts || [];\n                setNfts(nftData);\n            }\n        } catch (error) {\n            console.error('Error fetching NFTs:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...nfts\n        ];\n        // Search filter\n        if (searchQuery) {\n            filtered = filtered.filter((nft)=>nft.name.toLowerCase().includes(searchQuery.toLowerCase()) || nft.twitterHandle.toLowerCase().includes(searchQuery.toLowerCase()));\n        }\n        // Rarity filter\n        if (rarityFilter !== 'all') {\n            filtered = filtered.filter((nft)=>nft.rarity.toLowerCase() === rarityFilter.toLowerCase());\n        }\n        // Sort\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case 'newest':\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case 'oldest':\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case 'score':\n                    return b.score - a.score;\n                case 'rarity':\n                    const rarityOrder = {\n                        legendary: 4,\n                        epic: 3,\n                        rare: 2,\n                        common: 1\n                    };\n                    return (rarityOrder[b.rarity.toLowerCase()] || 0) - (rarityOrder[a.rarity.toLowerCase()] || 0);\n                default:\n                    return 0;\n            }\n        });\n        setFilteredNfts(filtered);\n    };\n    const handleNFTClick = (nft)=>{\n        setSelectedNFT(nft);\n        setShowDetailModal(true);\n    };\n    const handleShare = (nft)=>{\n        setSelectedNFT(nft);\n        setShowShareModal(true);\n    };\n    const handleBulkAction = (action)=>{\n        console.log(\"Bulk action: \".concat(action, \" on NFTs:\"), selectedNFTs);\n    // TODO: Implement bulk actions\n    };\n    const toggleNFTSelection = (nftId)=>{\n        setSelectedNFTs((prev)=>prev.includes(nftId) ? prev.filter((id)=>id !== nftId) : [\n                ...prev,\n                nftId\n            ]);\n    };\n    const getRarityStats = ()=>{\n        const stats = nfts.reduce((acc, nft)=>{\n            acc[nft.rarity.toLowerCase()] = (acc[nft.rarity.toLowerCase()] || 0) + 1;\n            return acc;\n        }, {});\n        return stats;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"My NFT Collection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        nfts.length,\n                                        \" NFTs total\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                selectedNFTs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                selectedNFTs.length,\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction('share'),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            title: \"Share selected\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction('download'),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            title: \"Download selected\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(viewMode === 'grid' ? 'list' : 'grid'),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                    title: \"Switch to \".concat(viewMode === 'grid' ? 'list' : 'grid', \" view\"),\n                                    children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200 bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search NFTs...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: rarityFilter,\n                                        onChange: (e)=>setRarityFilter(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Rarities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"common\",\n                                                children: \"Common\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"rare\",\n                                                children: \"Rare\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"epic\",\n                                                children: \"Epic\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"legendary\",\n                                                children: \"Legendary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"newest\",\n                                                children: \"Newest First\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"oldest\",\n                                                children: \"Oldest First\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"score\",\n                                                children: \"Highest Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"rarity\",\n                                                children: \"Rarity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex flex-wrap gap-2\",\n                        children: Object.entries(getRarityStats()).map((param)=>{\n                            let [rarity, count] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    rarity,\n                                    \": \",\n                                    count\n                                ]\n                            }, rarity, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: filteredNfts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ListBulletIcon_MagnifyingGlassIcon_ShareIcon_Squares2X2Icon_TagIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900\",\n                            children: \"No NFTs found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500\",\n                            children: searchQuery || rarityFilter !== 'all' ? 'Try adjusting your filters or search terms.' : 'Start by analyzing Twitter profiles to generate your first NFT!'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 \".concat(viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'),\n                    children: filteredNfts.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-2 left-2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedNFTs.includes(nft.id),\n                                        onChange: ()=>toggleNFTSelection(nft.id),\n                                        className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_nft_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    nft: nft,\n                                    onClick: ()=>handleNFTClick(nft)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, nft.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nft_detail_modal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                nft: selectedNFT,\n                isOpen: showDetailModal,\n                onClose: ()=>setShowDetailModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_share_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                nft: selectedNFT,\n                isOpen: showShareModal,\n                onClose: ()=>setShowShareModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Augment\\\\social-nft-platform-v2\\\\frontend-headless\\\\src\\\\components\\\\dashboard\\\\nft-collection-manager.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(NFTCollectionManager, \"xbf76aMguPHN8QpwNBz7LJSivLQ=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = NFTCollectionManager;\nvar _c;\n$RefreshReg$(_c, \"NFTCollectionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/nft-collection-manager.tsx\n"));

/***/ })

});