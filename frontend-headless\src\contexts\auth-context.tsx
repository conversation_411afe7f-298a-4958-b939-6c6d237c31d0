'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { authApi } from '@/lib/api'
import { storage } from '@/lib/utils'

// Types
export interface User {
  id: string
  username: string
  email: string
  displayName?: string
  profileImage?: string
  role: string
  isEmailVerified: boolean
  createdAt: string
  lastUpdated?: string
  totalNfts?: number
}

export interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (emailOrUsername: string, password: string) => Promise<{ success: boolean; error?: string }>
  loginWithTwitter: () => Promise<{ success: boolean; error?: string }>
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>
  logout: () => void
  updateUser: (data: Partial<User>) => void
  refreshAuth: () => Promise<void>
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
  displayName?: string
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize auth state
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      console.log('🔄 Auth Context: Initializing enhanced authentication state')
      const token = storage.get('auth_token', null)
      const savedUser = storage.get('user', null)

      console.log('🔍 Debug - Token exists:', !!token)
      console.log('🔍 Debug - User exists:', !!savedUser)

      if (token) {
        console.log(`🔑 Debug - Token: ${token.substring(0, 30)}...`)
        console.log(`🔑 Debug - Token length: ${token.length}`)
        console.log(`🔑 Debug - Token type: ${typeof token}`)

        // Check if it's a JWT token
        const isJWT = token.startsWith('eyJ')
        console.log(`🔑 Debug - Is JWT format: ${isJWT}`)

        if (isJWT) {
          try {
            const parts = token.split('.')
            console.log(`🔑 Debug - JWT parts count: ${parts.length}`)
            if (parts.length === 3) {
              const payload = JSON.parse(atob(parts[1]))
              console.log(`🔑 Debug - JWT payload:`, payload)
              console.log(`🔑 Debug - JWT provider: ${payload.provider || 'unknown'}`)
            }
          } catch (e) {
            console.log(`🔑 Debug - JWT decode error: ${e.message}`)
          }
        }
      }

      if (savedUser) {
        console.log(`👤 Debug - Saved user: ${JSON.stringify(savedUser, null, 2)}`)
        console.log(`👤 Debug - User provider: ${savedUser.provider || 'unknown'}`)
      }

      if (token && savedUser) {
        console.log('🔍 Auth Context: Found stored token and user, validating with enhanced backend...')
        console.log(`👤 Stored user: ${savedUser.username}`)
        console.log(`🔑 Token: ${token.substring(0, 20)}...`)

        // ✅ ENHANCED: Use enhanced token validation endpoint with Twitter-specific handling
        try {
          console.log('📡 Making token validation request...')

          // Check if this is a Twitter OAuth token
          const isTwitterToken = token.startsWith('eyJ') && savedUser?.provider === 'twitter'
          console.log(`🐦 Debug - Is Twitter token: ${isTwitterToken}`)

          const response = await authApi.validateToken(token)
          console.log('📡 Token validation response:', response)

          if (response.success && response.data) {
            console.log('✅ Auth Context: Enhanced token validation successful')
            console.log(`👤 Validated user: ${response.data.user.username}`)

            // ✅ ENHANCED: Use validated user data from backend
            const validatedUser = {
              id: response.data.user.id,
              username: response.data.user.username,
              email: response.data.user.email,
              displayName: response.data.user.displayName,
              profileImage: response.data.user.profileImage || savedUser.profileImage,
              role: response.data.user.role,
              isEmailVerified: savedUser.isEmailVerified || false,
              createdAt: savedUser.createdAt || new Date().toISOString(),
              lastUpdated: new Date().toISOString(),
              totalNfts: savedUser.totalNfts || 0
            };

            console.log('🔄 Setting user in context:', validatedUser)
            setUser(validatedUser as User)

            // ✅ ENHANCED: Update stored user data with validated data
            storage.set('user', validatedUser)

            console.log('✅ Auth Context: User session restored with enhanced data')
          } else {
            console.log(`❌ Auth Context: Enhanced token validation failed: ${response.error}`)
            console.log('❌ Full response:', response)

            // ✅ TWITTER FIX: For Twitter tokens, try alternative validation
            if (isTwitterToken) {
              console.log('🐦 Twitter token validation failed, trying alternative approach...')

              // If token validation fails but we have saved user data,
              // check if token is still structurally valid (not expired)
              try {
                const parts = token.split('.')
                if (parts.length === 3) {
                  const payload = JSON.parse(atob(parts[1]))
                  const now = Math.floor(Date.now() / 1000)

                  if (payload.exp && payload.exp > now) {
                    console.log('🐦 Twitter token is not expired, restoring session')
                    setUser(savedUser as User)
                    console.log('✅ Auth Context: Twitter session restored from saved data')
                    return
                  } else {
                    console.log('🐦 Twitter token is expired')
                  }
                }
              } catch (e) {
                console.log('🐦 Twitter token structure invalid')
              }
            }

            clearAuth()
          }
        } catch (validationError) {
          console.error('❌ Auth Context: Enhanced token validation error:', validationError)
          console.error('❌ Full error:', validationError)

          // ✅ TWITTER FIX: For Twitter tokens, try fallback
          const isTwitterToken = token.startsWith('eyJ') && savedUser?.provider === 'twitter'
          if (isTwitterToken) {
            console.log('🐦 Twitter token validation error, trying fallback...')
            try {
              const parts = token.split('.')
              if (parts.length === 3) {
                const payload = JSON.parse(atob(parts[1]))
                const now = Math.floor(Date.now() / 1000)

                if (payload.exp && payload.exp > now) {
                  console.log('🐦 Twitter token fallback: token not expired, restoring session')
                  setUser(savedUser as User)
                  console.log('✅ Auth Context: Twitter session restored via fallback')
                  return
                }
              }
            } catch (e) {
              console.log('🐦 Twitter token fallback failed')
            }
          }

          clearAuth()
        }
      } else {
        console.log('🔍 Auth Context: No stored authentication found')
        if (!token) console.log('❌ Missing token')
        if (!savedUser) console.log('❌ Missing saved user')
      }
    } catch (error) {
      console.error('❌ Auth Context: Enhanced auth initialization error:', error)
      clearAuth()
    } finally {
      setIsLoading(false)
      console.log('✅ Auth Context: Enhanced authentication initialization complete')
    }
  }

  const login = async (emailOrUsername: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.login({ emailOrUsername, password })

      if (response.success && response.data) {
        const { accessToken, user: userData } = response.data as any

        // Save to storage
        storage.set('auth_token', accessToken)
        storage.set('user', userData)

        // Update state
        setUser(userData as User)

        return { success: true }
      } else {
        return { success: false, error: response.error || 'Login failed' }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return { success: false, error: error.message || 'Login failed' }
    } finally {
      setIsLoading(false)
    }
  }

  const loginWithTwitter = async () => {
    try {
      setIsLoading(true)

      // Redirect to Twitter OAuth through API Gateway
      const twitterAuthUrl = `${process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:3010'}/api/auth/twitter`
      console.log('🐦 Frontend: Redirecting to Twitter OAuth:', twitterAuthUrl)
      window.location.href = twitterAuthUrl

      return { success: true }
    } catch (error: any) {
      console.error('Twitter login error:', error)
      return { success: false, error: error.message || 'Twitter login failed' }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true)
      const response = await authApi.register(data)

      if (response.success && response.data) {
        const { accessToken, user: userData } = response.data as any

        // Save to storage
        storage.set('auth_token', accessToken)
        storage.set('user', userData)

        // Update state
        setUser(userData as User)

        return { success: true }
      } else {
        return { success: false, error: response.error || 'Registration failed' }
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      return { success: false, error: error.message || 'Registration failed' }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Call logout API
      await authApi.logout()
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // Clear state and storage regardless of API response
      clearAuth()
    }
  }

  const updateUser = (data: Partial<User>) => {
    console.log('🔄 Auth Context: Updating user data', data)

    if (data && Object.keys(data).length > 0) {
      // If we have user data, update or create user
      const updatedUser = user ? { ...user, ...data } : data as User
      setUser(updatedUser)
      storage.set('user', updatedUser)
      console.log('✅ Auth Context: User updated successfully', updatedUser)
    } else if (user) {
      // If no data provided but user exists, just update existing
      const updatedUser = { ...user, ...data }
      setUser(updatedUser)
      storage.set('user', updatedUser)
    }
  }

  const refreshAuth = async () => {
    try {
      const response = await authApi.getProfile()
      if (response.success && response.data) {
        setUser(response.data as User)
        storage.set('user', response.data)
      }
    } catch (error) {
      console.error('Auth refresh error:', error)
    }
  }

  const clearAuth = () => {
    setUser(null)
    storage.remove('auth_token')
    storage.remove('user')
  }

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    loginWithTwitter,
    register,
    logout,
    updateUser,
    refreshAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// HOC for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth()

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      )
    }

    if (!isAuthenticated) {
      // Redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return null
    }

    return <Component {...props} />
  }
}

// Component for protected routes
export function ProtectedRoute({ children }: { children: ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    // Redirect to login
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
    return null
  }

  return <>{children}</>
}
