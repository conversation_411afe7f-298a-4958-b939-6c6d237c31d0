import { <PERSON>, <PERSON>, Get, Put, Body, Param, Query, <PERSON><PERSON>, <PERSON><PERSON>, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import axios from 'axios';

@ApiTags('Profile Analysis')
@Controller('profile-analysis')
export class ProfileAnalysisController {
  private readonly logger = new Logger(ProfileAnalysisController.name);
  private readonly profileAnalysisServiceUrl: string;

  constructor() {
    this.profileAnalysisServiceUrl = process.env.PROFILE_ANALYSIS_SERVICE_URL || 'http://localhost:3002';
  }

  @Post('analyze-twitter')
  @ApiOperation({ summary: 'Analyze Twitter profile and generate comprehensive analysis' })
  @ApiBody({
    description: 'Twitter profile analysis request',
    schema: {
      type: 'object',
      properties: {
        twitterHandle: {
          type: 'string',
          description: 'Twitter username (with or without @)',
          example: 'elonmusk'
        },
        userId: {
          type: 'string',
          description: 'User ID requesting the analysis (optional)',
          example: 'user_123'
        }
      },
      required: ['twitterHandle']
    }
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Analysis completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            userId: { type: 'string' },
            twitterHandle: { type: 'string' },
            status: { type: 'string' },
            score: { type: 'number' },
            analysisData: { type: 'object' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid input or analysis failed' })
  async analyzeTwitterProfile(@Body() analyzeDto: any, @Headers() headers: any) {
    try {
      this.logger.log(`Routing Twitter analysis request for: ${analyzeDto.twitterHandle}`);

      if (!analyzeDto.twitterHandle) {
        throw new BadRequestException('Twitter handle is required');
      }

      const response = await axios.post(
        `${this.profileAnalysisServiceUrl}/api/analysis/twitter-profile`,
        analyzeDto,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-correlation-id': headers['x-correlation-id'] || `gateway-${Date.now()}`,
          },
          timeout: 30000, // 30 second timeout
        }
      );

      this.logger.log(`Twitter analysis completed successfully for: ${analyzeDto.twitterHandle}`, {
        analysisId: response.data.data?.id,
        score: response.data.data?.score,
        rarity: response.data.data?.analysisData?.nftRecommendation?.rarity
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Twitter analysis failed for: ${analyzeDto.twitterHandle}`, {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      if (error.response) {
        throw new BadRequestException(error.response.data?.error?.message || 'Analysis service error');
      }
      throw new BadRequestException(`Analysis failed: ${error.message}`);
    }
  }

  @Get('results/:analysisId')
  @ApiOperation({ summary: 'Get analysis results by ID' })
  @ApiParam({ name: 'analysisId', description: 'Analysis ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Analysis results retrieved successfully'
  })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async getAnalysisResults(@Param('analysisId') analysisId: string, @Headers() headers: any) {
    try {
      this.logger.log(`Routing analysis results request for ID: ${analysisId}`);

      const response = await axios.get(
        `${this.profileAnalysisServiceUrl}/api/analysis/results/${analysisId}`,
        {
          headers: {
            'x-correlation-id': headers['x-correlation-id'] || `gateway-${Date.now()}`,
          },
          timeout: 10000,
        }
      );

      this.logger.log(`Analysis results retrieved successfully for ID: ${analysisId}`, {
        score: response.data.data?.score,
        status: response.data.data?.status
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Failed to get analysis results for ID: ${analysisId}`, {
        error: error.message,
        status: error.response?.status
      });

      if (error.response?.status === 404) {
        throw new BadRequestException(`Analysis with ID ${analysisId} not found`);
      }
      if (error.response) {
        throw new BadRequestException(error.response.data?.error?.message || 'Analysis service error');
      }
      throw new BadRequestException(`Failed to retrieve analysis results: ${error.message}`);
    }
  }

  @Get('history')
  @ApiOperation({ summary: 'Get user analysis history' })
  @ApiQuery({ name: 'userId', required: true, type: String, description: 'User ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results (default: 10)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Offset for pagination (default: 0)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Analysis history retrieved successfully'
  })
  @ApiResponse({ status: 400, description: 'Missing or invalid userId' })
  async getAnalysisHistory(
    @Query('userId') userId: string,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Headers() headers: any
  ) {
    try {
      if (!userId) {
        throw new BadRequestException('userId is required');
      }

      this.logger.log(`Routing analysis history request for user: ${userId}`, {
        limit,
        offset
      });

      const response = await axios.get(
        `${this.profileAnalysisServiceUrl}/api/analysis/history`,
        {
          params: { userId, limit, offset },
          headers: {
            'x-correlation-id': headers['x-correlation-id'] || `gateway-${Date.now()}`,
          },
          timeout: 10000,
        }
      );

      this.logger.log(`Analysis history retrieved successfully for user: ${userId}`, {
        count: response.data.data?.analyses?.length,
        total: response.data.data?.total
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Failed to get analysis history for user: ${userId}`, {
        error: error.message,
        status: error.response?.status
      });

      if (error.response) {
        throw new BadRequestException(error.response.data?.error?.message || 'Analysis service error');
      }
      throw new BadRequestException(`Failed to retrieve analysis history: ${error.message}`);
    }
  }

  @Put(':id/reanalyze')
  @ApiOperation({ summary: 'Re-analyze existing Twitter profile' })
  @ApiParam({ name: 'id', description: 'Analysis ID' })
  @ApiResponse({ status: 200, description: 'Re-analysis completed successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  async reanalyzeProfile(@Param('id') id: string, @Headers() headers: any) {
    try {
      this.logger.log(`Routing re-analysis request for ID: ${id}`);

      const response = await axios.put(
        `${this.profileAnalysisServiceUrl}/api/analysis/${id}/reanalyze`,
        {},
        {
          headers: {
            'x-correlation-id': headers['x-correlation-id'] || `gateway-${Date.now()}`,
          },
          timeout: 30000,
        }
      );

      this.logger.log(`Re-analysis completed successfully for ID: ${id}`, {
        previousScore: response.data.comparison?.previousScore,
        newScore: response.data.comparison?.newScore,
        scoreDifference: response.data.comparison?.scoreDifference
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Re-analysis failed for ID: ${id}`, {
        error: error.message,
        status: error.response?.status
      });

      if (error.response?.status === 404) {
        throw new BadRequestException(`Analysis with ID ${id} not found`);
      }
      if (error.response) {
        throw new BadRequestException(error.response.data?.error?.message || 'Analysis service error');
      }
      throw new BadRequestException(`Re-analysis failed: ${error.message}`);
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for profile analysis service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck(@Headers() headers: any) {
    try {
      const response = await axios.get(
        `${this.profileAnalysisServiceUrl}/api/analysis/health`,
        {
          headers: {
            'x-correlation-id': headers['x-correlation-id'] || `gateway-${Date.now()}`,
          },
          timeout: 5000,
        }
      );

      return {
        success: true,
        data: {
          ...response.data.data,
          gateway: 'api-gateway',
          timestamp: new Date().toISOString(),
        }
      };

    } catch (error) {
      this.logger.error('Profile analysis service health check failed', {
        error: error.message,
        status: error.response?.status
      });

      return {
        success: false,
        error: {
          message: 'Profile analysis service unavailable',
          timestamp: new Date().toISOString(),
        }
      };
    }
  }
}
