'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { authApi } from '@/lib/api'

export default function TwitterCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { updateUser } = useAuth()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [debugInfo, setDebugInfo] = useState<string[]>([])

  const addDebug = (msg: string) => {
    console.log(msg)
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${msg}`])
  }

  useEffect(() => {
    addDebug('🔄 Twitter Callback: useEffect triggered')

    const handleTwitterCallback = async () => {
      try {
        addDebug('🐦 Twitter Callback: Starting authentication process')

        // Get URL parameters directly from window.location
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token') // JWT token from backend
        const error = urlParams.get('error')

        addDebug(`🐦 URL search params: ${window.location.search}`)
        addDebug(`🔐 Token: ${token ? 'present' : 'missing'}, Error: ${error || 'none'}`)

        if (error) {
          addDebug(`❌ Authentication error: ${error}`)
          setStatus('error')
          setMessage(`Authentication failed: ${error}`)
          return
        }

        if (!token) {
          addDebug(`❌ Missing JWT token`)
          setStatus('error')
          setMessage('No authentication token received')
          return
        }

        // ✅ ENHANCEMENT: Handle JWT token from backend
        try {
          addDebug('🔐 Processing JWT token from backend')

          // Validate token format (basic check)
          if (!token.startsWith('eyJ')) {
            addDebug('❌ Invalid JWT token format')
            setStatus('error')
            setMessage('Invalid authentication token format')
            return
          }

          // ✅ ENHANCED: Validate JWT token with enhanced backend
          addDebug('🔍 Validating JWT token with enhanced backend')
          const validationResponse = await authApi.validateToken(token)

          if (!validationResponse.success) {
            addDebug(`❌ Enhanced token validation failed: ${validationResponse.error}`)
            setStatus('error')
            setMessage(`Enhanced token validation failed: ${validationResponse.error}`)
            return
          }

          addDebug('✅ Enhanced JWT token validation successful')
          const validatedUser = validationResponse.data.user
          addDebug(`👤 Enhanced authenticated user: ${validatedUser.username}`)
          addDebug(`✅ User details: ${validatedUser.displayName} (@${validatedUser.username})`)

          // ✅ ENHANCED: Create complete user object with enhanced data
          const userData = {
            id: validatedUser.id,
            username: validatedUser.username,
            email: validatedUser.email,
            displayName: validatedUser.displayName,
            profileImage: validatedUser.profileImage || 'https://via.placeholder.com/150',
            role: validatedUser.role,
            isEmailVerified: false, // Will be updated by backend if needed
            createdAt: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            totalNfts: 0 // Will be updated as user creates NFTs
          }

          addDebug(`💾 Saving enhanced user data: ${userData.username}`)

          // Save to storage
          localStorage.setItem('auth_token', token)
          localStorage.setItem('user', JSON.stringify(userData))

          addDebug('🔄 Updating auth context...')

          // Update auth context
          updateUser(userData)

          addDebug('✅ JWT authentication completed successfully')

          setStatus('success')
          setMessage('Successfully logged in with Twitter!')

          // Redirect to dashboard after a short delay
          addDebug('🔄 Redirecting to dashboard in 2 seconds...')
          setTimeout(() => {
            addDebug('🚀 Redirecting to dashboard now')
            router.push('/dashboard')
          }, 2000)

        } catch (tokenError) {
          console.error('❌ JWT token processing error:', tokenError)
          addDebug(`❌ Token processing failed: ${tokenError.message}`)
          setStatus('error')
          setMessage('Failed to process authentication token')
        }
      } catch (error: any) {
        console.error('Twitter callback error:', error)
        setStatus('error')
        setMessage('An unexpected error occurred during authentication')
      }
    }

    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      handleTwitterCallback()
    }, 100)

    return () => clearTimeout(timer)
  }, [router, updateUser])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <div className="mx-auto h-12 w-12 text-blue-600">
                <svg className="animate-spin h-12 w-12" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Completing Twitter Login
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Please wait while we complete your authentication...
              </p>
              {/* Debug Information */}
              {debugInfo.length > 0 && (
                <div className="mt-4 p-3 bg-gray-100 rounded text-xs text-left max-h-32 overflow-y-auto">
                  {debugInfo.map((info, index) => (
                    <div key={index} className="text-gray-700">{info}</div>
                  ))}
                </div>
              )}
            </>
          )}

          {status === 'success' && (
            <>
              <div className="mx-auto h-12 w-12 text-green-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Login Successful!
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                {message}
              </p>
              <p className="mt-2 text-center text-sm text-gray-500">
                Redirecting to dashboard...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="mx-auto h-12 w-12 text-red-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </div>
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Authentication Failed
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                {message}
              </p>
              <div className="mt-6">
                <button
                  onClick={() => router.push('/auth/login')}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Back to Login
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
