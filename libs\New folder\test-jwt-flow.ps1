# JWT Flow Testing Script
Write-Host "=== JWT FLOW TESTING ===" -ForegroundColor Cyan
Write-Host "Testing JWT token persistence and API calls" -ForegroundColor Green

$userServiceUrl = "http://localhost:3011"
$timestamp = Get-Date -Format "yyyyMMddHHmmss"

# Test 1: Register a new user
Write-Host "`n1. Testing User Registration..." -ForegroundColor Yellow
$testUser = @{
    username = "jwttest$timestamp"
    email = "jwttest$<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $regResponse = Invoke-RestMethod -Uri "$userServiceUrl/auth/register" -Method Post -Body $testUser -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ Registration successful!" -ForegroundColor Green
    Write-Host "User ID: $($regResponse.user.id)" -ForegroundColor Green
    Write-Host "Token: $($regResponse.accessToken.Substring(0, 20))..." -ForegroundColor Green
    $global:testToken = $regResponse.accessToken
    $global:testUserId = $regResponse.user.id
} catch {
    Write-Host "❌ Registration failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Test profile endpoint with token
Write-Host "`n2. Testing Profile Endpoint with Token..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $global:testToken"
        "Content-Type" = "application/json"
    }
    $profileResponse = Invoke-RestMethod -Uri "$userServiceUrl/auth/profile" -Method Get -Headers $headers -TimeoutSec 10
    Write-Host "✅ Profile fetch successful!" -ForegroundColor Green
    Write-Host "Username: $($profileResponse.username)" -ForegroundColor Green
} catch {
    Write-Host "❌ Profile fetch failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 JWT Flow Test Complete!" -ForegroundColor Green
