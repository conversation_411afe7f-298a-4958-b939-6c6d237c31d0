// Enterprise Prisma Schema - User Service
// Complete implementation with CQRS, audit trails, monitoring, compliance

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Command Model (Write Side - CQRS)
model UserCommand {
  // Primary identifier
  id                    String    @id @default(cuid())

  // Core user fields
  username              String    @unique @db.VarChar(50)
  email                 String    @unique @db.VarChar(100)
  password              String    @db.VarChar(255)

  // Social integration
  twitterUsername       String?   @map("twitter_username") @db.VarChar(50)
  twitterId             String?   @map("twitter_id") @db.VarChar(50)

  // Provider-based authentication
  provider              String?   @db.VarChar(20) // 'twitter', 'google', etc.
  providerId            String?   @map("provider_id") @db.VarChar(100) // External provider user ID
  providerData          Json?     @map("provider_data") // Additional provider data

  // User status and role
  role                  String    @default("user") @db.VarChar(20)
  isActive              Boolean   @default(true) @map("is_active")
  isEmailVerified       Boolean   @default(false) @map("is_email_verified")

  // Enhanced profile fields
  displayName           String?   @map("display_name") @db.VarChar(100)
  bio                   String?   @db.VarChar(500)
  profileImage          String?   @map("profile_image") @db.VarChar(255)
  location              String?   @db.VarChar(100)
  website               String?   @db.VarChar(255)
  interests             String[]  // Array of interest tags
  isProfileComplete     Boolean   @default(false) @map("is_profile_complete")

  // Enterprise audit fields
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  createdBy             String?   @map("created_by") @db.VarChar(50)
  updatedBy             String?   @map("updated_by") @db.VarChar(50)
  version               Int       @default(1)

  // Security and compliance
  lastLoginAt           DateTime? @map("last_login_at")
  passwordChangedAt     DateTime? @map("password_changed_at")
  failedLoginAttempts   Int       @default(0) @map("failed_login_attempts")
  lockedUntil           DateTime? @map("locked_until")

  // Data classification
  dataClassification    String    @default("internal") @map("data_classification")
  retentionPolicy       String    @default("7years") @map("retention_policy")

  // Real-time status
  isOnline              Boolean   @default(false) @map("is_online")
  lastSeenAt            DateTime? @map("last_seen_at")

  // Relationships
  auditLogs             AuditLog[]
  events                UserEvent[]
  campaignParticipations CampaignParticipation[]
  nfts                  NFT[]
  sellerListings        MarketplaceListing[] @relation("SellerListings")
  buyerOffers           MarketplaceOffer[] @relation("BuyerOffers")
  sellerTransactions    MarketplaceTransaction[] @relation("SellerTransactions")
  buyerTransactions     MarketplaceTransaction[] @relation("BuyerTransactions")
  notifications         Notification[]
  activityFeedItems     ActivityFeed[]
  sentMessages          ChatMessage[] @relation("MessageSender")

  @@map("user_commands")
  @@index([email])
  @@index([username])
  @@index([isActive])
  @@index([createdAt])
  @@index([lastLoginAt])
  @@index([provider, providerId])
  @@index([providerId])
}

// User Query Model (Read Side - CQRS)
model UserQuery {
  // Primary identifier (same as command)
  id                    String    @id

  // Denormalized user data for fast queries
  username              String    @unique @db.VarChar(50)
  email                 String    @unique @db.VarChar(100)
  displayName           String?   @map("display_name") @db.VarChar(100)

  // Social integration (denormalized)
  twitterUsername       String?   @map("twitter_username") @db.VarChar(50)
  twitterFollowers      Int?      @map("twitter_followers")
  twitterVerified       Boolean?  @map("twitter_verified")

  // User status and role
  role                  String    @default("user") @db.VarChar(20)
  isActive              Boolean   @default(true) @map("is_active")
  isEmailVerified       Boolean   @default(false) @map("is_email_verified")

  // Enhanced profile fields (denormalized)
  bio                   String?   @db.VarChar(500)
  profileImage          String?   @map("profile_image") @db.VarChar(255)
  location              String?   @db.VarChar(100)
  website               String?   @db.VarChar(255)
  isProfileComplete     Boolean   @default(false) @map("is_profile_complete")

  // Pre-computed aggregations
  totalNfts             Int       @default(0) @map("total_nfts")
  totalCampaigns        Int       @default(0) @map("total_campaigns")
  totalTransactions     Int       @default(0) @map("total_transactions")
  totalValue            Decimal   @default(0) @db.Decimal(18, 8) @map("total_value")

  // Performance metrics
  avgResponseTime       Float?    @map("avg_response_time")
  lastActivityAt        DateTime? @map("last_activity_at")

  // Timestamps
  createdAt             DateTime  @map("created_at")
  lastUpdated           DateTime  @map("last_updated")

  @@map("user_queries")
  @@index([email])
  @@index([username])
  @@index([isActive])
  @@index([totalNfts])
  @@index([lastActivityAt])
}

// Event Sourcing Schema
model UserEvent {
  // Event identification
  id                    String    @id @default(cuid())
  aggregateId           String    @map("aggregate_id") // User ID
  eventType             String    @map("event_type") @db.VarChar(50)
  eventVersion          Int       @map("event_version")

  // Event data
  eventData             Json      @map("event_data")
  metadata              Json?     // Additional context

  // Event tracking
  causationId           String?   @map("causation_id") // What caused this event
  correlationId         String?   @map("correlation_id") // Request correlation

  // Audit information
  createdAt             DateTime  @default(now()) @map("created_at")
  createdBy             String?   @map("created_by")

  // Relationships
  user                  UserCommand @relation(fields: [aggregateId], references: [id])

  @@map("user_events")
  @@index([aggregateId, eventVersion])
  @@index([eventType])
  @@index([createdAt])
  @@index([correlationId])
}

// Audit Log Schema
model AuditLog {
  // Audit identification
  id                    String    @id @default(cuid())
  entityType            String    @map("entity_type") @db.VarChar(50)
  entityId              String    @map("entity_id")
  action                String    @db.VarChar(50) // CREATE, UPDATE, DELETE

  // Change tracking
  oldValues             Json?     @map("old_values")
  newValues             Json?     @map("new_values")
  changedFields         String[]  @map("changed_fields")

  // Context information
  userId                String?   @map("user_id")
  sessionId             String?   @map("session_id")
  ipAddress             String?   @map("ip_address") @db.VarChar(45)
  userAgent             String?   @map("user_agent")

  // Compliance fields
  reason                String?   // Reason for change
  approvedBy            String?   @map("approved_by")
  complianceFlags       String[]  @map("compliance_flags")
  metadata              Json?     // Additional metadata

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  // Relationships
  user                  UserCommand? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
  @@index([entityType, entityId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
}

// Campaign Management Schema
model Campaign {
  // Primary identifier
  id                    String    @id @default(cuid())

  // Campaign basic info
  name                  String    @db.VarChar(100)
  description           String    @db.Text
  type                  String    @db.VarChar(50) // CampaignType enum
  status                String    @default("draft") @db.VarChar(20) // CampaignStatus enum

  // Campaign timing
  startDate             DateTime  @map("start_date")
  endDate               DateTime  @map("end_date")

  // Campaign configuration
  bannerImage           String?   @map("banner_image") @db.VarChar(255)
  maxParticipants       Int?      @map("max_participants")
  minAge                Int?      @map("min_age")
  geoRestrictions       String[]  @map("geo_restrictions")
  tags                  String[]
  priority              Int       @default(5)
  featured              Boolean   @default(false)
  metadata              Json      @default("{}")

  // Audit fields
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  createdBy             String    @map("created_by")
  updatedBy             String?   @map("updated_by")

  // Relationships
  requirements          CampaignRequirement[]
  rewards               CampaignReward[]
  participations        CampaignParticipation[]
  nfts                  NFT[]

  @@map("campaigns")
  @@index([status])
  @@index([type])
  @@index([featured])
  @@index([startDate, endDate])
  @@index([createdAt])
}

model CampaignRequirement {
  // Primary identifier
  id                    String    @id @default(cuid())

  // Campaign reference
  campaignId            String    @map("campaign_id")
  campaign              Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  // Requirement details
  type                  String    @db.VarChar(50) // ParticipationRequirement enum
  target                String?   @db.VarChar(255)
  minValue              Float?    @map("min_value")
  maxValue              Float?    @map("max_value")
  mandatory             Boolean   @default(true)
  points                Int       @default(0)

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  @@map("campaign_requirements")
  @@index([campaignId])
  @@index([type])
}

model CampaignReward {
  // Primary identifier
  id                    String    @id @default(cuid())

  // Campaign reference
  campaignId            String    @map("campaign_id")
  campaign              Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  // Reward details
  type                  String    @db.VarChar(50) // RewardType enum
  name                  String    @db.VarChar(100)
  description           String?   @db.Text
  value                 Float?
  rarity                String?   @db.VarChar(20)
  maxQuantity           Int?      @map("max_quantity")
  minPointsRequired     Int       @default(0) @map("min_points_required")
  metadata              Json      @default("{}")

  // Tracking
  currentQuantity       Int       @default(0) @map("current_quantity")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  // Relationships
  rewardsEarned         CampaignRewardEarned[]

  @@map("campaign_rewards")
  @@index([campaignId])
  @@index([type])
  @@index([rarity])
}

model CampaignParticipation {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  userId                String    @map("user_id")
  user                  UserCommand @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaignId            String    @map("campaign_id")
  campaign              Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  // Participation details
  status                String    @default("in_progress") @db.VarChar(20) // ParticipationStatus enum
  totalPoints           Int       @default(0) @map("total_points")
  referralCode          String?   @map("referral_code") @db.VarChar(50)
  acceptedTerms         Boolean   @default(true) @map("accepted_terms")

  // Timestamps
  joinedAt              DateTime  @default(now()) @map("joined_at")
  completedAt           DateTime? @map("completed_at")

  // Relationships
  submissions           RequirementSubmission[]
  rewardsEarned         CampaignRewardEarned[]

  @@unique([userId, campaignId])
  @@map("campaign_participations")
  @@index([userId])
  @@index([campaignId])
  @@index([status])
  @@index([joinedAt])
}

model RequirementSubmission {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  participationId       String    @map("participation_id")
  participation         CampaignParticipation @relation(fields: [participationId], references: [id], onDelete: Cascade)

  // Submission details
  requirementType       String    @map("requirement_type") @db.VarChar(50)
  submissionValue       String?   @map("submission_value") @db.Text
  proofUrls             String[]  @map("proof_urls")
  notes                 String?   @db.Text
  metadata              Json      @default("{}")

  // Review details
  status                String    @default("pending_review") @db.VarChar(20) // SubmissionStatus enum
  pointsAwarded         Int?      @map("points_awarded")
  reviewerComments      String?   @map("reviewer_comments") @db.Text
  feedback              String?   @db.Text
  reviewedBy            String?   @map("reviewed_by")

  // Timestamps
  submittedAt           DateTime  @default(now()) @map("submitted_at")
  reviewedAt            DateTime? @map("reviewed_at")

  @@map("requirement_submissions")
  @@index([participationId])
  @@index([requirementType])
  @@index([status])
  @@index([submittedAt])
}

model CampaignRewardEarned {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  participationId       String    @map("participation_id")
  participation         CampaignParticipation @relation(fields: [participationId], references: [id], onDelete: Cascade)
  rewardId              String    @map("reward_id")
  reward                CampaignReward @relation(fields: [rewardId], references: [id], onDelete: Cascade)

  // Reward status
  claimed               Boolean   @default(false)
  metadata              Json      @default("{}")

  // Timestamps
  earnedAt              DateTime  @default(now()) @map("earned_at")
  claimedAt             DateTime? @map("claimed_at")

  @@map("campaign_rewards_earned")
  @@index([participationId])
  @@index([rewardId])
  @@index([claimed])
  @@index([earnedAt])
}

// NFT Management Schema
model NFT {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  userId                String    @map("user_id")
  user                  UserCommand @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaignId            String    @map("campaign_id")
  campaign              Campaign  @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  // NFT core properties
  rarity                String    @db.VarChar(20) // NFTRarity enum
  status                String    @default("pending") @db.VarChar(20) // NFTStatus enum
  blockchain            String    @default("polygon") @db.VarChar(20) // BlockchainNetwork enum
  engagementScore       Float     @map("engagement_score")

  // NFT metadata
  metadata              Json      @default("{}")
  imageUrl              String?   @map("image_url") @db.VarChar(500)
  animationUrl          String?   @map("animation_url") @db.VarChar(500)
  externalUrl           String?   @map("external_url") @db.VarChar(500)

  // Blockchain data
  tokenId               String?   @map("token_id") @db.VarChar(100)
  contractAddress       String?   @map("contract_address") @db.VarChar(100)
  transactionHash       String?   @map("transaction_hash") @db.VarChar(100)
  ownerAddress          String?   @map("owner_address") @db.VarChar(100)

  // External service integration
  externalNftId         String?   @map("external_nft_id") @db.VarChar(100)
  generationParams      Json      @default("{}") @map("generation_params")
  additionalMetadata    Json      @default("{}") @map("additional_metadata")

  // Audit fields
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  createdBy             String    @map("created_by")
  updatedBy             String?   @map("updated_by")

  // Timestamps for lifecycle events
  mintedAt              DateTime? @map("minted_at")
  lastTransferredAt     DateTime? @map("last_transferred_at")

  // Relationships
  transfers             NFTTransfer[]
  marketplaceListings   MarketplaceListing[]
  marketplaceTransactions MarketplaceTransaction[]

  @@map("nfts")
  @@index([userId])
  @@index([campaignId])
  @@index([rarity])
  @@index([status])
  @@index([blockchain])
  @@index([tokenId])
  @@index([contractAddress])
  @@index([ownerAddress])
  @@index([createdAt])
}

model NFTTransfer {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  nftId                 String    @map("nft_id")
  nft                   NFT       @relation(fields: [nftId], references: [id], onDelete: Cascade)

  // Transfer details
  fromAddress           String    @map("from_address") @db.VarChar(100)
  toAddress             String    @map("to_address") @db.VarChar(100)
  transactionHash       String    @map("transaction_hash") @db.VarChar(100)
  blockNumber           Int?      @map("block_number")
  gasUsed               Int?      @map("gas_used")
  gasPrice              String?   @map("gas_price") @db.VarChar(50)

  // Transfer metadata
  transferReason        String?   @map("transfer_reason") @db.VarChar(255)
  transferType          String    @default("user_transfer") @map("transfer_type") @db.VarChar(50)
  metadata              Json      @default("{}")

  // Audit fields
  transferredAt         DateTime  @default(now()) @map("transferred_at")
  initiatedBy           String?   @map("initiated_by")

  @@map("nft_transfers")
  @@index([nftId])
  @@index([fromAddress])
  @@index([toAddress])
  @@index([transactionHash])
  @@index([transferredAt])
}

// Marketplace Management Schema
model MarketplaceListing {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  nftId                 String    @map("nft_id")
  nft                   NFT       @relation(fields: [nftId], references: [id], onDelete: Cascade)
  sellerId              String    @map("seller_id")
  seller                UserCommand @relation("SellerListings", fields: [sellerId], references: [id], onDelete: Cascade)

  // Listing details
  listingType           String    @map("listing_type") @db.VarChar(20) // ListingType enum
  status                String    @default("draft") @db.VarChar(20) // ListingStatus enum
  price                 String    @db.VarChar(100) // Price in wei or smallest unit
  currency              String    @db.VarChar(10) // PaymentMethod enum

  // Auction specific fields
  startingPrice         String?   @map("starting_price") @db.VarChar(100)
  reservePrice          String?   @map("reserve_price") @db.VarChar(100)
  buyNowPrice           String?   @map("buy_now_price") @db.VarChar(100)
  currentBid            String?   @map("current_bid") @db.VarChar(100)
  bidCount              Int       @default(0) @map("bid_count")

  // Listing content
  title                 String    @db.VarChar(100)
  description           String    @db.Text
  tags                  String[]

  // Offer settings
  acceptOffers          Boolean   @default(true) @map("accept_offers")
  minOfferAmount        String?   @map("min_offer_amount") @db.VarChar(100)

  // External integration
  externalListingId     String?   @map("external_listing_id") @db.VarChar(100)

  // Metadata and tracking
  metadata              Json      @default("{}")
  viewCount             Int       @default(0) @map("view_count")
  favoriteCount         Int       @default(0) @map("favorite_count")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  expiresAt             DateTime  @map("expires_at")
  soldAt                DateTime? @map("sold_at")

  // Audit fields
  createdBy             String    @map("created_by")
  updatedBy             String?   @map("updated_by")

  // Relationships
  offers                MarketplaceOffer[]
  transactions          MarketplaceTransaction[]

  @@map("marketplace_listings")
  @@index([nftId])
  @@index([sellerId])
  @@index([status])
  @@index([listingType])
  @@index([currency])
  @@index([price])
  @@index([createdAt])
  @@index([expiresAt])
}

model MarketplaceOffer {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  listingId             String    @map("listing_id")
  listing               MarketplaceListing @relation(fields: [listingId], references: [id], onDelete: Cascade)
  buyerId               String    @map("buyer_id")
  buyer                 UserCommand @relation("BuyerOffers", fields: [buyerId], references: [id], onDelete: Cascade)

  // Offer details
  amount                String    @db.VarChar(100) // Offer amount in wei or smallest unit
  currency              String    @db.VarChar(10) // PaymentMethod enum
  status                String    @default("pending") @db.VarChar(20) // TransactionStatus enum
  message               String?   @db.Text

  // External integration
  externalOfferId       String?   @map("external_offer_id") @db.VarChar(100)

  // Metadata
  metadata              Json      @default("{}")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")
  expiresAt             DateTime? @map("expires_at")
  acceptedAt            DateTime? @map("accepted_at")
  rejectedAt            DateTime? @map("rejected_at")

  @@map("marketplace_offers")
  @@index([listingId])
  @@index([buyerId])
  @@index([status])
  @@index([amount])
  @@index([createdAt])
  @@index([expiresAt])
}

model MarketplaceTransaction {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  nftId                 String    @map("nft_id")
  nft                   NFT       @relation(fields: [nftId], references: [id], onDelete: Cascade)
  listingId             String?   @map("listing_id")
  listing               MarketplaceListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)
  sellerId              String    @map("seller_id")
  seller                UserCommand @relation("SellerTransactions", fields: [sellerId], references: [id], onDelete: Cascade)
  buyerId               String    @map("buyer_id")
  buyer                 UserCommand @relation("BuyerTransactions", fields: [buyerId], references: [id], onDelete: Cascade)

  // Transaction details
  type                  String    @db.VarChar(20) // TransactionType enum
  status                String    @default("pending") @db.VarChar(20) // TransactionStatus enum
  amount                String    @db.VarChar(100) // Transaction amount in wei or smallest unit
  currency              String    @db.VarChar(10) // PaymentMethod enum

  // Blockchain data
  transactionHash       String?   @map("transaction_hash") @db.VarChar(100)
  blockNumber           Int?      @map("block_number")
  gasUsed               Int?      @map("gas_used")
  gasPrice              String?   @map("gas_price") @db.VarChar(50)

  // Fee breakdown
  platformFee           String?   @map("platform_fee") @db.VarChar(100)
  royaltyFee            String?   @map("royalty_fee") @db.VarChar(100)

  // External integration
  externalTransactionId String?   @map("external_transaction_id") @db.VarChar(100)

  // Metadata
  metadata              Json      @default("{}")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")
  confirmedAt           DateTime? @map("confirmed_at")
  failedAt              DateTime? @map("failed_at")

  @@map("marketplace_transactions")
  @@index([nftId])
  @@index([listingId])
  @@index([sellerId])
  @@index([buyerId])
  @@index([type])
  @@index([status])
  @@index([amount])
  @@index([transactionHash])
  @@index([createdAt])
}

// Real-time Features Schema
model Notification {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  userId                String    @map("user_id")
  user                  UserCommand @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Notification details
  type                  String    @db.VarChar(50) // NotificationType enum
  priority              String    @default("medium") @db.VarChar(20) // NotificationPriority enum
  title                 String    @db.VarChar(200)
  message               String    @db.Text
  icon                  String?   @db.VarChar(255)
  actionUrl             String?   @map("action_url") @db.VarChar(255)
  actionText            String?   @map("action_text") @db.VarChar(100)

  // Status
  isRead                Boolean   @default(false) @map("is_read")
  readAt                DateTime? @map("read_at")

  // Metadata
  data                  Json      @default("{}")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")
  expiresAt             DateTime? @map("expires_at")

  @@map("notifications")
  @@index([userId])
  @@index([type])
  @@index([priority])
  @@index([isRead])
  @@index([createdAt])
  @@index([expiresAt])
}

model ActivityFeed {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  userId                String    @map("user_id")
  user                  UserCommand @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Activity details
  type                  String    @db.VarChar(50) // ActivityType enum
  title                 String    @db.VarChar(200)
  description           String    @db.Text

  // Related entity
  entityType            String?   @map("entity_type") @db.VarChar(50)
  entityId              String?   @map("entity_id") @db.VarChar(100)

  // Metadata
  metadata              Json      @default("{}")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  @@map("activity_feed")
  @@index([userId])
  @@index([type])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
}

model ChatMessage {
  // Primary identifier
  id                    String    @id @default(cuid())

  // References
  roomId                String    @map("room_id") @db.VarChar(100)
  senderId              String    @map("sender_id")
  sender                UserCommand @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)

  // Message details
  content               String    @db.Text
  messageType           String    @default("text") @map("message_type") @db.VarChar(20)
  attachments           String[]

  // Reply functionality
  replyToId             String?   @map("reply_to_id")
  replyTo               ChatMessage? @relation("MessageReply", fields: [replyToId], references: [id], onDelete: SetNull)
  replies               ChatMessage[] @relation("MessageReply")

  // Status
  isEdited              Boolean   @default(false) @map("is_edited")
  editedAt              DateTime? @map("edited_at")

  // Metadata
  metadata              Json      @default("{}")

  // Timestamps
  createdAt             DateTime  @default(now()) @map("created_at")

  @@map("chat_messages")
  @@index([roomId])
  @@index([senderId])
  @@index([replyToId])
  @@index([createdAt])
}

model SearchAnalytics {
  // Primary identifier
  id                    String    @id @default(cuid())

  // Search details
  query                 String    @db.VarChar(500)
  type                  String    @db.VarChar(50) // SearchType enum
  resultCount           Int       @map("result_count")
  executionTime         Int       @map("execution_time") // in milliseconds

  // User context
  userId                String    @map("user_id")

  // Search criteria
  filters               Json      @default("[]")
  sortCriteria          Json      @default("{}") @map("sort_criteria")

  // Interaction tracking
  hasClick              Boolean   @default(false) @map("has_click")
  clickPosition         Int?      @map("click_position")
  sessionId             String?   @map("session_id") @db.VarChar(100)

  // Timestamps
  timestamp             DateTime  @default(now())
  createdAt             DateTime  @default(now()) @map("created_at")

  @@map("search_analytics")
  @@index([query])
  @@index([type])
  @@index([userId])
  @@index([timestamp])
  @@index([hasClick])
  @@index([sessionId])
}
