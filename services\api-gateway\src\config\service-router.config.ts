// Service Router Configuration for Environment-Based Routing
// Implements Approach 3: Independent Mock Services

export interface ServiceConfig {
  name: string;
  mockPort: number;
  realPort: number;
  basePath: string;
  description: string;
}

/**
 * Service Configuration Registry
 * Maps service names to their mock and real implementations
 */
export const SERVICE_CONFIGS: ServiceConfig[] = [
  {
    name: 'twitter',
    mockPort: 3020,
    realPort: 3002, // Profile Analysis Service (current Twitter integration)
    basePath: '/api/twitter',
    description: 'Twitter API integration service'
  },
  {
    name: 'blockchain',
    mockPort: 3021,
    realPort: 3005, // Real blockchain service
    basePath: '/api/blockchain',
    description: 'Blockchain integration service'
  },
  {
    name: 'nft-storage',
    mockPort: 3022,
    realPort: 3006, // Real NFT storage service (will use external APIs in production)
    basePath: '/api/nft-storage',
    description: 'NFT metadata and asset storage service'
  }
];

/**
 * Service Router Class
 * Handles environment-based routing to mock or real services
 */
export class ServiceRouter {
  private useMockServices: boolean;
  private serviceMap: Map<string, ServiceConfig>;

  constructor() {
    this.useMockServices = process.env.USE_MOCK_SERVICES === 'true';
    this.serviceMap = new Map(SERVICE_CONFIGS.map(config => [config.name, config]));
    
    console.log(`🔄 ServiceRouter: Initialized with USE_MOCK_SERVICES=${this.useMockServices}`);
    this.logServiceMappings();
  }

  /**
   * Get service URL based on environment configuration
   */
  getServiceUrl(serviceName: string): string {
    const config = this.serviceMap.get(serviceName);
    if (!config) {
      throw new Error(`Service '${serviceName}' not configured in SERVICE_CONFIGS`);
    }

    const port = this.useMockServices ? config.mockPort : config.realPort;
    const url = `http://localhost:${port}`;
    
    console.log(`🔄 ServiceRouter: ${serviceName} → ${url} (${this.useMockServices ? 'MOCK' : 'REAL'})`);
    return url;
  }

  /**
   * Get all configured services
   */
  getAllServices(): ServiceConfig[] {
    return SERVICE_CONFIGS;
  }

  /**
   * Check if using mock services
   */
  isUsingMockServices(): boolean {
    return this.useMockServices;
  }

  /**
   * Get service configuration
   */
  getServiceConfig(serviceName: string): ServiceConfig | undefined {
    return this.serviceMap.get(serviceName);
  }

  /**
   * Log current service mappings for debugging
   */
  private logServiceMappings(): void {
    console.log('🔄 ServiceRouter: Current service mappings:');
    SERVICE_CONFIGS.forEach(config => {
      const port = this.useMockServices ? config.mockPort : config.realPort;
      const type = this.useMockServices ? 'MOCK' : 'REAL';
      console.log(`  ${config.name}: http://localhost:${port} (${type})`);
    });
  }
}
