// Comprehensive Integration Test for Project Service
// This test validates all project and campaign endpoints for frontend integration

const axios = require('axios');

// Configuration
const PROJECT_SERVICE_URL = 'http://localhost:3005';
const API_GATEWAY_URL = 'http://localhost:3010/api';

class IntegrationTester {
  constructor() {
    this.testResults = [];
    this.createdProjects = [];
    this.createdCampaigns = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Integration Tests...\n');
    
    try {
      // Test 1: Service Health Check
      await this.testServiceHealth();
      
      // Test 2: Project CRUD Operations
      await this.testProjectCRUD();
      
      // Test 3: Campaign CRUD Operations
      await this.testCampaignCRUD();
      
      // Test 4: Project-Campaign Relationships
      await this.testProjectCampaignRelationships();
      
      // Test 5: Search and Filtering
      await this.testSearchAndFiltering();
      
      // Test 6: Analytics and Statistics
      await this.testAnalyticsAndStats();
      
      // Test 7: API Gateway Integration (if available)
      await this.testAPIGatewayIntegration();
      
      // Cleanup
      await this.cleanup();
      
      // Summary
      this.printSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      await this.cleanup();
    }
  }

  async testServiceHealth() {
    console.log('1️⃣ Testing Service Health...');
    
    try {
      const response = await axios.get(`${PROJECT_SERVICE_URL}/health`);
      this.logSuccess('Service Health', {
        status: response.data.status,
        service: response.data.service,
        port: response.data.port
      });
    } catch (error) {
      this.logError('Service Health', error.message);
      throw new Error('Service is not available');
    }
  }

  async testProjectCRUD() {
    console.log('\n2️⃣ Testing Project CRUD Operations...');
    
    // Create Project
    const projectData = {
      name: 'Integration Test Project',
      description: 'A comprehensive test project for integration testing',
      ownerId: 'test-owner-integration',
      category: 'Testing',
      images: ['https://example.com/test-image.jpg'],
      website: 'https://testproject.com',
      socialMediaLinks: {
        twitter: 'https://twitter.com/testproject'
      },
      duration: {
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        timezone: 'UTC'
      },
      analysisConfiguration: {
        fixedParameters: {
          hasBio: { weight: 10, enabled: true },
          hasAvatar: { weight: 5, enabled: true },
          followerCategories: [
            { name: 'Small', min: 0, max: 1000, weight: 5 }
          ],
          engagementRate: { weight: 20, enabled: true },
          accountAge: { weight: 10, enabled: true }
        },
        variableParameters: {
          activityLevel: { weight: 15, enabled: true },
          contentQuality: { weight: 10, enabled: true }
        },
        updateFrequencyHours: 24
      },
      nftConfiguration: {
        scoreThresholds: {
          common: 0,
          rare: 50,
          legendary: 80
        },
        design: {
          theme: 'modern',
          style: 'clean',
          mainColor: '#00ff88',
          fixedElements: ['background', 'border', 'logo']
        },
        evolutionTriggers: [
          { type: 'score_increase', condition: 'score >= 50', targetRarity: 'rare' }
        ],
        blockchainUpdatePolicy: 'immediate'
      },
      blockchainNetwork: 'ethereum',
      isPublic: true,
      allowParticipation: true,
      maxParticipants: 500
    };

    try {
      // Create
      const createResponse = await axios.post(`${PROJECT_SERVICE_URL}/enterprise/projects`, projectData);
      const project = createResponse.data.data;
      this.createdProjects.push(project.id);
      
      this.logSuccess('Project Creation', {
        id: project.id,
        name: project.name,
        status: project.status
      });

      // Read
      const readResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects/${project.id}?includeConfig=true`);
      this.logSuccess('Project Read', {
        id: readResponse.data.data.id,
        hasConfiguration: !!readResponse.data.data.analysisConfiguration
      });

      // Update
      const updateData = {
        description: 'Updated description for integration test',
        maxParticipants: 750
      };
      const updateResponse = await axios.put(`${PROJECT_SERVICE_URL}/enterprise/projects/${project.id}`, updateData);
      this.logSuccess('Project Update', {
        id: updateResponse.data.data.id,
        maxParticipants: updateResponse.data.data.maxParticipants
      });

      // List Projects
      const listResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects?page=1&limit=10`);
      this.logSuccess('Project List', {
        count: listResponse.data.data.projects.length,
        pagination: listResponse.data.data.pagination
      });

    } catch (error) {
      this.logError('Project CRUD', error.response?.data?.message || error.message);
    }
  }

  async testCampaignCRUD() {
    console.log('\n3️⃣ Testing Campaign CRUD Operations...');
    
    if (this.createdProjects.length === 0) {
      this.logError('Campaign CRUD', 'No projects available for campaign testing');
      return;
    }

    const campaignData = {
      projectId: this.createdProjects[0],
      name: 'Integration Test Campaign',
      description: 'A test campaign for integration testing',
      campaignType: 'user_acquisition',
      startDate: '2025-07-01T00:00:00Z',
      endDate: '2025-08-01T23:59:59Z',
      timezone: 'UTC',
      targetAudience: {
        minFollowers: 100,
        maxFollowers: 10000,
        minAccountAge: 30,
        minEngagementRate: 2.0
      },
      requirements: ['Follow on Twitter', 'Retweet announcement'],
      rewards: {
        nftRewards: {
          common: { probability: 60, bonusPoints: 10 },
          rare: { probability: 30, bonusPoints: 25 },
          legendary: { probability: 10, bonusPoints: 50 }
        }
      },
      socialPlatforms: [
        {
          platform: 'twitter',
          enabled: true,
          requiredHashtags: ['#TestDeFi', '#NFT'],
          minDailyInteractions: 3
        }
      ],
      maxParticipants: 1000,
      minParticipants: 10
    };

    try {
      // Create Campaign
      const createResponse = await axios.post(`${PROJECT_SERVICE_URL}/enterprise/campaigns`, campaignData);
      const campaign = createResponse.data.data;
      this.createdCampaigns.push(campaign.id);
      
      this.logSuccess('Campaign Creation', {
        id: campaign.id,
        name: campaign.name,
        projectId: campaign.projectId
      });

      // Read Campaign
      const readResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/campaigns/${campaign.id}`);
      this.logSuccess('Campaign Read', {
        id: readResponse.data.data.id,
        campaignType: readResponse.data.data.campaignType
      });

      // List Campaigns
      const listResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/campaigns?page=1&limit=10`);
      this.logSuccess('Campaign List', {
        count: listResponse.data.data.campaigns.length,
        pagination: listResponse.data.data.pagination
      });

    } catch (error) {
      this.logError('Campaign CRUD', error.response?.data?.message || error.message);
    }
  }

  async testProjectCampaignRelationships() {
    console.log('\n4️⃣ Testing Project-Campaign Relationships...');
    
    if (this.createdProjects.length === 0) {
      this.logError('Project-Campaign Relationships', 'No projects available');
      return;
    }

    try {
      const projectId = this.createdProjects[0];
      const response = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/campaigns/project/${projectId}`);
      
      this.logSuccess('Project Campaigns', {
        projectId: projectId,
        campaignCount: response.data.data.campaigns.length
      });
    } catch (error) {
      this.logError('Project-Campaign Relationships', error.response?.data?.message || error.message);
    }
  }

  async testSearchAndFiltering() {
    console.log('\n5️⃣ Testing Search and Filtering...');
    
    try {
      // Search Projects
      const projectSearchResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects/search?query=Integration&category=Testing`);
      this.logSuccess('Project Search', {
        resultsCount: projectSearchResponse.data.data.projects.length
      });

      // Search Campaigns
      const campaignSearchResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/campaigns/search?query=Integration&campaignType=user_acquisition`);
      this.logSuccess('Campaign Search', {
        resultsCount: campaignSearchResponse.data.data.campaigns.length
      });

    } catch (error) {
      this.logError('Search and Filtering', error.response?.data?.message || error.message);
    }
  }

  async testAnalyticsAndStats() {
    console.log('\n6️⃣ Testing Analytics and Statistics...');
    
    try {
      // Project Stats
      const projectStatsResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/projects/stats`);
      this.logSuccess('Project Statistics', {
        totalProjects: projectStatsResponse.data.data.totalProjects,
        activeProjects: projectStatsResponse.data.data.activeProjects
      });

      // Campaign Stats
      const campaignStatsResponse = await axios.get(`${PROJECT_SERVICE_URL}/enterprise/campaigns/stats`);
      this.logSuccess('Campaign Statistics', {
        totalCampaigns: campaignStatsResponse.data.data.totalCampaigns,
        activeCampaigns: campaignStatsResponse.data.data.activeCampaigns
      });

    } catch (error) {
      this.logError('Analytics and Statistics', error.response?.data?.message || error.message);
    }
  }

  async testAPIGatewayIntegration() {
    console.log('\n7️⃣ Testing API Gateway Integration...');
    
    try {
      // Test if API Gateway is available
      const healthResponse = await axios.get(`${API_GATEWAY_URL}/health`, { timeout: 5000 });
      
      // Test project endpoint through API Gateway
      const projectsResponse = await axios.get(`${API_GATEWAY_URL}/projects`, { timeout: 5000 });
      
      this.logSuccess('API Gateway Integration', {
        gatewayHealth: healthResponse.data.status,
        projectsAccessible: projectsResponse.status === 200
      });
      
    } catch (error) {
      this.logWarning('API Gateway Integration', 'API Gateway not available or not configured - using direct service access');
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      // Delete campaigns
      for (const campaignId of this.createdCampaigns) {
        await axios.delete(`${PROJECT_SERVICE_URL}/enterprise/campaigns/${campaignId}`);
      }
      
      // Delete projects
      for (const projectId of this.createdProjects) {
        await axios.delete(`${PROJECT_SERVICE_URL}/enterprise/projects/${projectId}`);
      }
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.log('⚠️ Cleanup had some issues:', error.message);
    }
  }

  logSuccess(testName, data) {
    console.log(`✅ ${testName}:`, data);
    this.testResults.push({ test: testName, status: 'PASS', data });
  }

  logError(testName, message) {
    console.log(`❌ ${testName}:`, message);
    this.testResults.push({ test: testName, status: 'FAIL', message });
  }

  logWarning(testName, message) {
    console.log(`⚠️ ${testName}:`, message);
    this.testResults.push({ test: testName, status: 'WARN', message });
  }

  printSummary() {
    console.log('\n📊 TEST SUMMARY:');
    console.log('================');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const warnings = this.testResults.filter(r => r.status === 'WARN').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`📈 Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 All critical tests passed! Project Service is ready for frontend integration.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }
  }
}

// Run the tests
const tester = new IntegrationTester();
tester.runAllTests().catch(console.error);
