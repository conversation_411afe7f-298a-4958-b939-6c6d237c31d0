import { Injectable, BadGatewayException, RequestTimeoutException } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { ServiceRouter } from '../../config/service-router.config';

@Injectable()
export class ProxyService {
  private readonly serviceRouter: ServiceRouter;

  // Legacy service URLs (for existing services not yet in ServiceRouter)
  private readonly legacyServiceUrls = {
    'user-service': 'http://localhost:3011',
    'profile-analysis-service': 'http://localhost:3002',
    'nft-generation-service': 'http://localhost:3003',
    'project-service': 'http://localhost:3005',
    'marketplace-service': 'http://localhost:3006',
    'analytics-service': 'http://localhost:3007',
  };

  constructor() {
    this.serviceRouter = new ServiceRouter();
  }

  /**
   * Get service URL using ServiceRouter for mock/real switching or legacy URLs
   */
  private getServiceUrl(serviceName: string): string | null {
    try {
      // Try to get URL from ServiceRouter (for mock/real services)
      return this.serviceRouter.getServiceUrl(serviceName);
    } catch (error) {
      // Fallback to legacy service URLs for existing services
      const legacyUrl = this.legacyServiceUrls[serviceName];
      if (legacyUrl) {
        console.log(`🔄 ProxyService: Using legacy URL for ${serviceName} → ${legacyUrl}`);
        return legacyUrl;
      }

      console.error(`🔄 ProxyService: Service ${serviceName} not found in ServiceRouter or legacy URLs`);
      return null;
    }
  }

  async forwardRequest(
    serviceName: string,
    path: string,
    method: string,
    data?: any,
    headers?: any,
    query?: any
  ): Promise<AxiosResponse> {
    const serviceUrl = this.getServiceUrl(serviceName);

    if (!serviceUrl) {
      throw new BadGatewayException(`Service ${serviceName} not found`);
    }

    try {
      const config = {
        method: method.toLowerCase(),
        url: `${serviceUrl}${path}`,
        data,
        headers: {
          ...headers,
          'x-forwarded-by': 'api-gateway',
        },
        params: query,
        timeout: 30000, // 30 seconds
        maxRedirects: 0, // Don't follow redirects - we want to capture them
        validateStatus: (status) => status < 400, // Accept redirects as valid responses
      };

      const response = await axios(config);

      // Log successful response for debugging
      console.log(`🔄 ProxyService: Successful response from ${serviceName}:`, {
        status: response.status,
        contentType: response.headers['content-type'],
        dataType: typeof response.data,
        hasData: !!response.data
      });

      // Debug: Log the actual response data for history endpoints
      if (path.includes('/history')) {
        console.log(`🔍 ProxyService: History response data:`, JSON.stringify(response.data).substring(0, 200) + '...');
      }

      return response;
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new BadGatewayException(`Service ${serviceName} is unavailable`);
      }

      if (error.code === 'ETIMEDOUT') {
        throw new RequestTimeoutException(`Service ${serviceName} request timeout`);
      }

      // Forward the error from the service
      if (error.response) {
        // Handle cases where response.data might be null or invalid JSON
        let errorData = null;
        let errorMessage = error.message;

        try {
          errorData = error.response.data;
          errorMessage = error.response.data?.message || error.message;
        } catch (parseError) {
          console.error('🔄 ProxyService: Error parsing response data:', parseError.message);
          errorMessage = `Response parsing error: ${parseError.message}`;
        }

        throw {
          status: error.response.status,
          message: errorMessage,
          data: errorData,
        };
      }

      throw new BadGatewayException(`Error communicating with ${serviceName}: ${error.message}`);
    }
  }

  async healthCheck(serviceName: string): Promise<boolean> {
    try {
      const serviceUrl = this.getServiceUrl(serviceName);
      if (!serviceUrl) return false;

      // Project service uses /health endpoint (no global prefix)
      const response = await axios.get(`${serviceUrl}/health`, { timeout: 5000 });
      return response.status === 200;
    } catch {
      return false;
    }
  }

  /**
   * Get environment information for debugging
   */
  getEnvironmentInfo() {
    return {
      useMockServices: this.serviceRouter.isUsingMockServices(),
      availableServices: this.serviceRouter.getAllServices(),
      legacyServices: Object.keys(this.legacyServiceUrls),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get all service statuses
   */
  async getAllServiceStatuses() {
    const allServices = [
      ...this.serviceRouter.getAllServices().map(s => s.name),
      ...Object.keys(this.legacyServiceUrls)
    ];

    const statuses = await Promise.all(
      allServices.map(async (serviceName) => ({
        name: serviceName,
        url: this.getServiceUrl(serviceName),
        healthy: await this.healthCheck(serviceName),
        type: this.serviceRouter.getAllServices().find(s => s.name === serviceName)
          ? (this.serviceRouter.isUsingMockServices() ? 'mock' : 'real')
          : 'legacy'
      }))
    );

    return {
      environment: this.serviceRouter.isUsingMockServices() ? 'mock' : 'real',
      services: statuses,
      timestamp: new Date().toISOString()
    };
  }
}
