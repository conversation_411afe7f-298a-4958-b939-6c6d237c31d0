// Step 5.1.2 - User Service Direct Test
const axios = require('axios');

async function testUserServiceDirect() {
  console.log('👤 Step 5.1.2: User Service Direct Test');
  
  try {
    // Test 1: Direct Service Health Check
    console.log('\n🏥 Test 1: Direct Service Health Check...');
    const healthResponse = await axios.get('http://localhost:3011/api/health/simple');
    console.log('✅ Direct service health:', healthResponse.data);
    
    // Test 2: Direct User Creation
    console.log('\n👤 Test 2: Direct User Creation...');
    const timestamp = Date.now();
    const createResponse = await axios.post('http://localhost:3011/api/users', {
      username: `direct_test_${timestamp}`,
      email: `direct_test_${timestamp}@example.com`,
      password: 'SecurePassword123',
      role: 'user'
    }, {
      headers: { 'X-Correlation-ID': 'direct-test-5.1.2' }
    });
    console.log('✅ Direct user creation:', createResponse.data.success);
    const userId = createResponse.data.data.id;
    
    // Test 3: Direct User Retrieval
    console.log('\n🔍 Test 3: Direct User Retrieval...');
    const getResponse = await axios.get(`http://localhost:3011/api/users/${userId}`, {
      headers: { 'X-Correlation-ID': 'direct-get-5.1.2' }
    });
    console.log('✅ Direct user retrieval:', getResponse.data.success);
    console.log('   CQRS Query Model working:', !!getResponse.data.data.displayName);
    
    console.log('\n🎉 Step 5.1.2 PASSED: User Service Direct Access Working');
    return { success: true, userId };
    
  } catch (error) {
    console.error('❌ Step 5.1.2 FAILED:', error.message);
    return { success: false };
  }
}

testUserServiceDirect();
