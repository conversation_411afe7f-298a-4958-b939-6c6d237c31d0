const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3003/api';

// Test data
const samplePreviewRequest = JSON.parse(
  fs.readFileSync(path.join(__dirname, 'test-data/sample-preview-request.json'), 'utf8')
);

const sampleTemplate = JSON.parse(
  fs.readFileSync(path.join(__dirname, 'test-data/sample-template.json'), 'utf8')
);

async function testPreviewSystem() {
  console.log('🧪 Testing NFT Preview System...\n');

  try {
    // Test 1: Create a sample template
    console.log('📋 Test 1: Creating sample template...');
    try {
      const templateResponse = await axios.post(`${BASE_URL}/templates`, sampleTemplate);
      console.log('✅ Template created successfully');
      console.log(`   Template ID: ${templateResponse.data.id}`);
      console.log(`   Template Name: ${templateResponse.data.name}\n`);
      
      // Update preview request with template ID
      samplePreviewRequest.templateId = templateResponse.data.id;
    } catch (error) {
      console.log('⚠️  Template creation failed (might already exist)');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 2: Generate NFT Preview
    console.log('🎨 Test 2: Generating NFT preview...');
    try {
      const previewResponse = await axios.post(`${BASE_URL}/preview/generate`, samplePreviewRequest);
      console.log('✅ NFT preview generated successfully');
      console.log(`   Preview ID: ${previewResponse.data.previewId}`);
      console.log(`   Estimated Rarity: ${previewResponse.data.estimatedRarity}`);
      console.log(`   Image URL: ${previewResponse.data.imageUrl}`);
      console.log(`   Template Used: ${previewResponse.data.template.name}\n`);
    } catch (error) {
      console.log('❌ NFT preview generation failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 3: Get NFT Gallery
    console.log('🖼️  Test 3: Getting NFT gallery...');
    try {
      const galleryResponse = await axios.get(`${BASE_URL}/preview/gallery?limit=5`);
      console.log('✅ NFT gallery retrieved successfully');
      console.log(`   Total NFTs: ${galleryResponse.data.total}`);
      console.log(`   NFTs in response: ${galleryResponse.data.nfts.length}`);
      console.log(`   Has more: ${galleryResponse.data.hasMore}\n`);
    } catch (error) {
      console.log('❌ NFT gallery retrieval failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 4: Get Gallery Statistics
    console.log('📊 Test 4: Getting gallery statistics...');
    try {
      const statsResponse = await axios.get(`${BASE_URL}/preview/gallery/stats`);
      console.log('✅ Gallery statistics retrieved successfully');
      console.log(`   Total NFTs: ${statsResponse.data.totalNFTs}`);
      console.log(`   Rarity Distribution:`, statsResponse.data.rarityDistribution);
      console.log(`   Minting Status:`, statsResponse.data.mintingStatus);
      console.log(`   Generated At: ${statsResponse.data.generatedAt}\n`);
    } catch (error) {
      console.log('❌ Gallery statistics retrieval failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 5: Get Template List
    console.log('📝 Test 5: Getting template list...');
    try {
      const templatesResponse = await axios.get(`${BASE_URL}/templates`);
      console.log('✅ Template list retrieved successfully');
      console.log(`   Total templates: ${templatesResponse.data.length}`);
      if (templatesResponse.data.length > 0) {
        console.log(`   First template: ${templatesResponse.data[0].name}`);
      }
      console.log('');
    } catch (error) {
      console.log('❌ Template list retrieval failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    // Test 6: Test Template Statistics
    console.log('📈 Test 6: Getting template statistics...');
    try {
      const templateStatsResponse = await axios.get(`${BASE_URL}/templates/stats/overview`);
      console.log('✅ Template statistics retrieved successfully');
      console.log(`   Total templates: ${templateStatsResponse.data.totalTemplates}`);
      console.log(`   Campaign templates: ${templateStatsResponse.data.campaignTemplates}`);
      console.log(`   Default templates: ${templateStatsResponse.data.defaultTemplates}`);
      console.log('');
    } catch (error) {
      console.log('❌ Template statistics retrieval failed');
      console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
    }

    console.log('🎉 Preview system testing completed!');

  } catch (error) {
    console.error('💥 Unexpected error during testing:', error.message);
  }
}

// Check if service is running
async function checkServiceHealth() {
  try {
    await axios.get(`${BASE_URL}/templates`);
    return true;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  console.log('🔍 Checking if NFT Generation Service is running...');
  
  const isServiceRunning = await checkServiceHealth();
  
  if (!isServiceRunning) {
    console.log('❌ NFT Generation Service is not running on http://localhost:3003');
    console.log('   Please start the service with: npm run start:dev');
    console.log('   Then run this test again.');
    return;
  }
  
  console.log('✅ NFT Generation Service is running\n');
  await testPreviewSystem();
}

main().catch(console.error);
