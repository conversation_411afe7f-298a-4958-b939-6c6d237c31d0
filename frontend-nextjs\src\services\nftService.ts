import axios from 'axios';
import { API_CONFIG, SERVICES } from '@/config/api';

export interface NFT {
  id: string;
  userId: string;
  campaignId: string;
  twitterHandle: string;
  tokenId?: string;
  name: string;
  description: string;
  imageUrl: string;
  metadata: {
    campaignConfiguration: any;
    special_effect?: string;
    [key: string]: any;
  };
  attributes: {
    trait_type: string;
    value: string | number;
  }[];
  rarity: string;
  currentScore: number;
  blockchain?: string;
  contractAddress?: string;
  isMinted: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GenerateNFTData {
  userId: string;
  campaignId: string;
  twitterHandle: string;
  currentScore: number;
  analysisData: {
    profile: {
      followerCount: number;
      engagementRate: number;
      [key: string]: any;
    };
    metrics: {
      contentQuality: number;
      activityLevel?: number;
      [key: string]: any;
    };
  };
  campaignConfiguration: {
    style: string;
    theme: string;
    scoreThresholds?: {
      common: number;
      rare: number;
      legendary: number;
    };
  };
}

export interface UpdateNFTData {
  nftId: string;
  newScore: number;
  updatedAnalysisData: {
    profile: {
      followerCount: number;
      engagementRate: number;
      [key: string]: any;
    };
    metrics: {
      contentQuality: number;
      activityLevel?: number;
      [key: string]: any;
    };
  };
}

export class NFTService {
  private baseURL = API_CONFIG.BASE_URL;  // Use API Gateway

  async getUserNFTs(userId: string): Promise<NFT[]> {
    try {
      // TEMPORARY: Return mock data for dashboard testing
      console.log('🔄 NFTService: Getting user NFTs for userId:', userId);

      // Mock NFT data for testing
      const mockNFTs: NFT[] = [
        {
          id: 'nft_1',
          userId: userId,
          campaignId: 'campaign_1',
          twitterHandle: 'MockUser',
          tokenId: '1',
          name: 'Social Influencer NFT #1',
          description: 'A dynamic NFT representing your social media influence',
          imageUrl: 'https://via.placeholder.com/300x300/1DA1F2/FFFFFF?text=NFT+1',
          metadata: {
            campaignConfiguration: { style: 'modern', theme: 'blue' },
            special_effect: 'glow'
          },
          attributes: [
            { trait_type: 'Influence Score', value: 75 },
            { trait_type: 'Engagement Rate', value: '8.5%' },
            { trait_type: 'Follower Count', value: 1250 }
          ],
          rarity: 'Rare',
          currentScore: 75,
          blockchain: 'Ethereum',
          contractAddress: '0x123...abc',
          isMinted: true,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'nft_2',
          userId: userId,
          campaignId: 'campaign_2',
          twitterHandle: 'MockUser',
          name: 'Community Builder NFT #2',
          description: 'Recognizing your community building efforts',
          imageUrl: 'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=NFT+2',
          metadata: {
            campaignConfiguration: { style: 'artistic', theme: 'red' }
          },
          attributes: [
            { trait_type: 'Community Score', value: 60 },
            { trait_type: 'Engagement Rate', value: '6.2%' },
            { trait_type: 'Follower Count', value: 980 }
          ],
          rarity: 'Common',
          currentScore: 60,
          isMinted: false,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      console.log('✅ NFTService: Returning mock NFTs:', mockNFTs.length);
      return mockNFTs;

      // TODO: Uncomment when NFT service is properly configured
      // const response = await axios.get(
      //   `${this.baseURL}${API_CONFIG.ENDPOINTS.NFTS.USER_NFTS}/${userId}`
      // );
      // return response.data;
    } catch (error) {
      console.error('Get user NFTs error:', error);
      throw error;
    }
  }

  async generateNFT(data: GenerateNFTData): Promise<NFT> {
    try {
      console.log('🎨 Sending NFT generation request:', data);

      const response = await axios.post(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.NFTS.GENERATE}`,
        data
      );

      console.log('✅ NFT generation response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Generate NFT error:', error);

      // Provide specific error messages based on response
      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || 'Invalid NFT generation data';
        throw new Error(`NFT Generation Error: ${Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage}`);
      } else if (error.response?.status === 500) {
        throw new Error('NFT generation service is temporarily unavailable. Please try again later.');
      } else {
        throw new Error(error.message || 'Failed to generate NFT. Please try again.');
      }
    }
  }

  async updateNFT(data: UpdateNFTData): Promise<NFT> {
    try {
      const response = await axios.patch(
        `${this.baseURL}${API_CONFIG.ENDPOINTS.NFTS.UPDATE}`,
        data
      );
      return response.data;
    } catch (error) {
      console.error('Update NFT error:', error);
      throw error;
    }
  }

  // Helper method to get NFTs by rarity
  async getUserNFTsByRarity(userId: string, rarity: string): Promise<NFT[]> {
    try {
      const nfts = await this.getUserNFTs(userId);
      return nfts.filter(nft => nft.rarity.toLowerCase() === rarity.toLowerCase());
    } catch (error) {
      console.error('Get user NFTs by rarity error:', error);
      throw error;
    }
  }

  // Helper method to get evolution-ready NFTs
  async getEvolutionReadyNFTs(userId: string): Promise<NFT[]> {
    try {
      const nfts = await this.getUserNFTs(userId);
      // NFTs that might be ready for evolution (score near thresholds)
      return nfts.filter(nft => {
        const score = nft.currentScore;
        return (
          (nft.rarity === 'Common' && score >= 45) ||
          (nft.rarity === 'Rare' && score >= 70)
        );
      });
    } catch (error) {
      console.error('Get evolution ready NFTs error:', error);
      throw error;
    }
  }
}

export const nftService = new NFTService();
