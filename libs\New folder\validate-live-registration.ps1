# Live Registration Validator
Write-Host "=== LIVE REGISTRATION VALIDATOR ===" -ForegroundColor Cyan
Write-Host "Checking if user registration was successful" -ForegroundColor Green

$backendUrl = "http://localhost:3011"
$testEmail = "<EMAIL>"

Write-Host "`nValidating registration for: $testEmail" -ForegroundColor Yellow

# Test: Try to register the same user again (should fail if first registration worked)
Write-Host "`nTesting if user was created (duplicate registration should fail)..." -ForegroundColor Yellow

$testUser = @{
    username = "livetest2024"
    email = "<EMAIL>"
    password = "SecurePass123!"
} | ConvertTo-<PERSON>son

try {
    $response = Invoke-RestMethod -Uri "$backendUrl/auth/register" -Method Post -Body $testUser -ContentType "application/json" -TimeoutSec 10
    Write-Host "❌ UNEXPECTED: Registration succeeded again (user might not have been created)" -ForegroundColor Red
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Yellow
} catch {
    if ($_.Exception.Message -like "*409*" -or $_.Exception.Message -like "*already exists*" -or $_.Exception.Message -like "*duplicate*") {
        Write-Host "✅ SUCCESS: User was created successfully (duplicate registration properly rejected)" -ForegroundColor Green
    } else {
        Write-Host "❓ UNKNOWN ERROR: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host "`n🎯 Registration validation complete!" -ForegroundColor Green
Write-Host "Now test login at: http://localhost:3000/auth/login" -ForegroundColor Cyan
