# ⚠️ DEPRECATED - API Endpoints Reference

> **🚨 THIS DOCUMENT IS DEPRECATED**
> **Please use:** [MASTER_REFERENCE.md](./MASTER_REFERENCE.md) - Single Source of Truth
> **This file will be removed in future updates.**

---

## 🔗 COMPLETE API ENDPOINTS DOCUMENTATION

> **⚠️ WARNING: This information may be outdated. Always refer to MASTER_REFERENCE.md**

### **API Gateway (Port 3010)**
**Base URL:** `http://localhost:3010/api/`
- `GET /health` - Gateway health check
- Routes to all backend services

### **User Service (Port 3001)**
**Base URL:** `http://localhost:3001/`
- `POST /users/register` - User registration
- `POST /users/login` - User authentication
- `GET /users/profile/:id` - Get user profile
- `PATCH /users/profile/:id` - Update user profile
- `GET /health` - Service health check

### **Profile Analysis Service (Port 3002)**
**Base URL:** `http://localhost:3002/`
- `POST /analysis/twitter-profile` - Analyze Twitter profile
- `GET /analysis/user/:userId` - Get user analysis
- `GET /analysis/campaign/:campaignId` - Get campaign analysis
- `GET /health` - Service health check

### **NFT Generation Service (Port 3003)**
**Base URL:** `http://localhost:3003/api/`
- `POST /nft-generation/generate` - Generate NFT
- `PATCH /nft-generation/update` - Update NFT
- `GET /nft-generation/user/:userId` - Get user NFTs
- `GET /nft-generation/campaign/:campaignId` - Get campaign NFTs
- `PATCH /nft-generation/mint` - Mint NFT
- `POST /marketplace/list` - Create marketplace listing
- `DELETE /marketplace/:nftId/listing/:listingId` - Remove marketplace listing
- `GET /marketplace/user/:userId/listings` - Get user marketplace listings
- `PATCH /marketplace/:nftId/listing/:listingId/price` - Update listing price
- `GET /health` - Service health check

### **Blockchain Service (Port 3004)**
**Base URL:** `http://localhost:3004/api/`
- `POST /blockchain/mint` - Mint single NFT
- `POST /blockchain/batch-mint` - Batch mint NFTs
- `GET /blockchain/mints/user/:userId` - Get user mints
- `PATCH /blockchain/update-status` - Update mint status
- `GET /blockchain-status` - Service status
- `GET /health` - Service health check

### **Project Service (Port 3005)**
**Base URL:** `http://localhost:3005/api/`
- `POST /projects` - Create project
- `GET /projects` - List projects
- `GET /projects/:id` - Get project details
- `PATCH /projects/:id` - Update project
- `DELETE /projects/:id` - Delete project
- `GET /projects/:id/campaigns` - Get project campaigns
- `POST /campaigns` - Create campaign
- `GET /campaigns` - List campaigns
- `GET /campaigns/:id` - Get campaign details
- `PATCH /campaigns/:id` - Update campaign
- `POST /campaigns/:id/join` - Join campaign
- `GET /health` - Service health check

### **Marketplace Service (Port 3006)**
**Base URL:** `http://localhost:3006/api/`
- `POST /marketplace/listings` - Create listing
- `GET /marketplace/listings` - Get all listings
- `GET /marketplace/listings/:id` - Get specific listing
- `PATCH /marketplace/listings/:id` - Update listing
- `DELETE /marketplace/listings/:id` - Delete listing
- `GET /marketplace/listings/seller/:sellerId` - Get seller listings
- `POST /marketplace/transactions/purchase` - Purchase NFT
- `PATCH /marketplace/transactions/:id/confirm` - Confirm transaction
- `PATCH /marketplace/transactions/:id/fail` - Mark transaction as failed
- `GET /marketplace/transactions/history` - Get transaction history
- `GET /marketplace/transactions/:id` - Get specific transaction
- `POST /marketplace/offers` - Create offer
- `GET /marketplace/offers/listing/:listingId` - Get listing offers
- `GET /marketplace/offers/user/my-offers` - Get user offers
- `PATCH /marketplace/offers/:id/accept` - Accept offer
- `PATCH /marketplace/offers/:id/reject` - Reject offer
- `DELETE /marketplace/offers/:id` - Delete offer
- `GET /marketplace/offers/:id` - Get specific offer
- `GET /health` - Service health check

### **Analytics Service (Port 3007)**
**Base URL:** `http://localhost:3007/api/`
- `POST /analytics/track` - Track analytics event
- `GET /analytics/metrics/:metricType` - Get metrics
- `GET /analytics/dashboard/:dashboardType` - Get dashboard data
- `GET /analytics-status` - Service status
- `GET /platform-overview` - Platform statistics
- `GET /health` - Service health check

### **Notification Service (Port 3008)**
**Base URL:** `http://localhost:3008/api/`
- `POST /notifications` - Create notification
- `POST /notifications/batch` - Create batch notifications
- `GET /notifications/user/:userId` - Get user notifications
- `PATCH /notifications/:notificationId/read/:userId` - Mark as read
- `PATCH /notifications/read-all/:userId` - Mark all as read
- `POST /notifications/preferences` - Update preferences
- `GET /notifications/preferences/:userId` - Get preferences
- `GET /notification-status` - Service status
- `GET /health` - Service health check

## 🧪 MOCK SERVICES (Development Environment)

### **Mock Twitter Service (Port 3020)**
**Base URL:** `http://localhost:3020/`
- `POST /auth/twitter` - Mock Twitter authentication
- `GET /profile/:username` - Get mock Twitter profile
- `GET /followers/:username` - Get mock followers
- `POST /tweet` - Post mock tweet
- `GET /health` - Service health check

### **Mock Blockchain Service (Port 3021)**
**Base URL:** `http://localhost:3021/`
- `POST /mint` - Mock NFT minting
- `POST /batch-mint` - Mock batch minting
- `GET /transaction/:txHash` - Get mock transaction
- `GET /wallet/:address` - Get mock wallet info
- `GET /health` - Service health check

### **Mock NFT Storage Service (Port 3022)**
**Base URL:** `http://localhost:3022/`
- `POST /upload` - Mock file upload
- `GET /metadata/:tokenId` - Get mock metadata
- `POST /metadata` - Store mock metadata
- `GET /image/:imageId` - Get mock image
- `GET /health` - Service health check

## 📊 API Documentation URLs

### Real Services Swagger Documentation:
- User Service: http://localhost:3011/api/docs
- Profile Analysis: http://localhost:3002/api/docs
- NFT Generation: http://localhost:3003/api/docs
- Blockchain Service: http://localhost:3004/api/docs
- Project Service: http://localhost:3005/api/docs
- Marketplace: http://localhost:3006/api/docs
- Analytics: http://localhost:3007/api/docs
- Notification: http://localhost:3008/api/docs
- API Gateway: http://localhost:3010/api/docs

### Mock Services Documentation:
- Mock Twitter: http://localhost:3020/docs
- Mock Blockchain: http://localhost:3021/docs
- Mock NFT Storage: http://localhost:3022/docs

## 🔐 Authentication

Most endpoints require JWT authentication via Authorization header:
```
Authorization: Bearer <jwt_token>
```

Admin endpoints require API key:
```
x-admin-key: <admin_api_key>
```

---

## 📈 Platform Summary

### **Real Services (Production Ready)**
- **Total Services**: 8 services
- **Total Endpoints**: 60+ endpoints
- **Port Range**: 3002-3008, 3011
- **Status**: ✅ All services running and healthy

### **Mock Services (Development)**
- **Total Services**: 3 services
- **Total Endpoints**: 15+ endpoints
- **Port Range**: 3020-3022
- **Status**: ✅ All services running and healthy

### **Core Infrastructure**
- **API Gateway**: Port 3010 ✅ (Single entry point)
- **Environment**: Development with mock services enabled
- **Database**: PostgreSQL (Port 5432)
- **Frontend**: Port 3000 (Next.js)

**🎉 TOTAL: 11 services, 75+ endpoints, fully operational platform!** ✅
