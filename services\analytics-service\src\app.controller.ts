import { <PERSON>, Get, Post, Param, Query, Body } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';

@ApiTags('analytics')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service health status' })
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('analytics-status')
  @ApiOperation({ summary: 'Check analytics service status and configuration' })
  @ApiResponse({ status: 200, description: 'Analytics service status and configuration' })
  getAnalyticsStatus() {
    return this.appService.getAnalyticsStatus();
  }

  @Get('platform-overview')
  @ApiOperation({ summary: 'Get platform overview statistics' })
  @ApiResponse({ status: 200, description: 'Platform overview statistics' })
  getPlatformOverview() {
    return this.appService.getPlatformOverview();
  }

  @Get('user/:userId/insights')
  @ApiOperation({ summary: 'Get user-specific analytics insights' })
  @ApiResponse({ status: 200, description: 'User insights retrieved successfully' })
  getUserInsights(@Param('userId') userId: string) {
    return this.appService.getUserInsights(userId);
  }

  @Get('nft-performance')
  @ApiOperation({ summary: 'Get NFT performance analytics' })
  @ApiResponse({ status: 200, description: 'NFT performance analytics retrieved successfully' })
  getNFTPerformance(@Query('period') period: string = '30d', @Query('rarity') rarity?: string) {
    return this.appService.getNFTPerformance(period, rarity);
  }

  @Get('engagement-metrics')
  @ApiOperation({ summary: 'Get user engagement metrics' })
  @ApiResponse({ status: 200, description: 'Engagement metrics retrieved successfully' })
  getEngagementMetrics(@Query('period') period: string = '30d') {
    return this.appService.getEngagementMetrics(period);
  }

  @Get('recent-transactions')
  @ApiOperation({ summary: 'Get recent transactions display' })
  @ApiResponse({ status: 200, description: 'Recent transactions retrieved successfully' })
  getRecentTransactions(@Query('limit') limit: string = '10') {
    return this.appService.getRecentTransactions(parseInt(limit));
  }

  @Get('top-gainers-losers')
  @ApiOperation({ summary: 'Get top gainers and losers' })
  @ApiResponse({ status: 200, description: 'Top gainers and losers retrieved successfully' })
  getTopGainersLosers(
    @Query('timeframe') timeframe: string = '7d',
    @Query('limit') limit: string = '10'
  ) {
    return this.appService.getTopGainersLosers(timeframe, parseInt(limit));
  }

  @Get('collection-market-value')
  @ApiOperation({ summary: 'Get collection market value data' })
  @ApiResponse({ status: 200, description: 'Collection market value retrieved successfully' })
  getCollectionMarketValue() {
    return this.appService.getCollectionMarketValue();
  }

  @Post('track-event')
  @ApiOperation({ summary: 'Track user analytics event' })
  @ApiResponse({ status: 200, description: 'Event tracked successfully' })
  trackEvent(@Body() eventData: any) {
    return this.appService.trackEvent(eventData);
  }
}
