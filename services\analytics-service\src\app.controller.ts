import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service health status' })
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('analytics-status')
  @ApiOperation({ summary: 'Check analytics service status and configuration' })
  @ApiResponse({ status: 200, description: 'Analytics service status and configuration' })
  getAnalyticsStatus() {
    return this.appService.getAnalyticsStatus();
  }

  @Get('platform-overview')
  @ApiOperation({ summary: 'Get platform overview statistics' })
  @ApiResponse({ status: 200, description: 'Platform overview statistics' })
  getPlatformOverview() {
    return this.appService.getPlatformOverview();
  }
}
