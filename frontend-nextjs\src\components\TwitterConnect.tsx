'use client'

import {
  <PERSON>,
  But<PERSON>,
  Input,
  <PERSON><PERSON><PERSON>ck,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Spinner,
  Badge,
  Heading
} from '@chakra-ui/react'
import { useState } from 'react'
import { profileAnalysisService, TwitterAnalysisData } from '@/services/profileAnalysisService'

interface TwitterConnectProps {
  onAnalysisComplete?: (analysis: TwitterAnalysisData) => void
}

export default function TwitterConnect({ onAnalysisComplete }: TwitterConnectProps) {
  const [twitterHandle, setTwitterHandle] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<TwitterAnalysisData | null>(null)
  const [error, setError] = useState('')

  const handleAnalyze = async () => {
    if (!twitterHandle.trim()) {
      setError('Please enter a Twitter username')
      return
    }

    // Clean the handle (remove @ if present)
    const cleanHandle = twitterHandle.replace('@', '').trim()

    try {
      setIsAnalyzing(true)
      setError('')

      console.log('🔍 Starting Twitter analysis for:', cleanHandle)

      const result = await profileAnalysisService.analyzeTwitterProfile({
        twitterHandle: cleanHandle
      })

      console.log('✅ Analysis completed:', result)
      setAnalysis(result)

      if (onAnalysisComplete) {
        onAnalysisComplete(result)
      }
    } catch (err: any) {
      console.error('❌ Analysis failed:', err)
      setError(err.message || 'Failed to analyze Twitter profile')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'legendary': return 'purple'
      case 'rare': return 'blue'
      case 'uncommon': return 'green'
      default: return 'gray'
    }
  }

  return (
    <Box bg="white" p={6} borderRadius="lg" boxShadow="md">
      <VStack gap={4} align="stretch">
        <Heading size="md">🐦 Twitter Profile Analysis</Heading>
        <VStack gap={4} align="stretch">
          {/* Input Section */}
          <VStack gap={3} align="stretch">
            <Text fontSize="sm" color="gray.600">
              Enter your Twitter username to analyze your profile and generate your NFT
            </Text>

            <HStack>
              <Input
                placeholder="Enter Twitter username (e.g., @username)"
                value={twitterHandle}
                onChange={(e) => setTwitterHandle(e.target.value)}
                disabled={isAnalyzing}
                onKeyDown={(e) => e.key === 'Enter' && handleAnalyze()}
              />
              <Button
                colorScheme="blue"
                onClick={handleAnalyze}
                loading={isAnalyzing}
                minW="120px"
              >
                Analyze
              </Button>
            </HStack>
          </VStack>

          {/* Error Display */}
          {error && (
            <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
              <Text color="red.600" fontWeight="medium">
                ❌ {error}
              </Text>
            </Box>
          )}

          {/* Loading State */}
          {isAnalyzing && (
            <Box p={4} bg="blue.50" borderRadius="md">
              <VStack gap={3}>
                <HStack>
                  <Spinner size="sm" color="blue.500" />
                  <Text color="blue.700" fontWeight="medium">
                    Analyzing Twitter profile...
                  </Text>
                </HStack>
                <Box w="100%" h="2" bg="blue.200" borderRadius="full">
                  <Box h="100%" bg="blue.500" borderRadius="full" animation="pulse 2s infinite" />
                </Box>
                <Text fontSize="xs" color="blue.600">
                  This may take a few moments while we analyze your social metrics
                </Text>
              </VStack>
            </Box>
          )}

          {/* Analysis Results */}
          {analysis && (
            <Box p={4} bg="green.50" borderRadius="md" borderLeft="4px solid" borderColor="green.500">
              <VStack gap={3} align="stretch">
                <HStack justify="space-between">
                  <Text fontWeight="bold" color="green.700">
                    ✅ Analysis Complete!
                  </Text>
                  <Badge
                    colorScheme={getRarityColor(profileAnalysisService.calculateRarity(analysis.score))}
                    variant="solid"
                  >
                    {profileAnalysisService.calculateRarity(analysis.score)}
                  </Badge>
                </HStack>

                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Social Score:</Text>
                  <Text fontWeight="bold" fontSize="lg" color={profileAnalysisService.getScoreColor(analysis.score)}>
                    {profileAnalysisService.formatScore(analysis.score)}/100
                  </Text>
                </HStack>

                <Box w="100%" h="4" bg="gray.200" borderRadius="md" overflow="hidden">
                  <Box
                    h="100%"
                    bg={`${getRarityColor(profileAnalysisService.calculateRarity(analysis.score))}.500`}
                    borderRadius="md"
                    width={`${analysis.score}%`}
                    transition="width 0.5s ease"
                  />
                </Box>

                {analysis.analysisData && (
                  <VStack gap={2} align="stretch" fontSize="sm">
                    <Text fontWeight="medium" color="gray.700">Profile Metrics:</Text>
                    <HStack justify="space-between">
                      <Text color="gray.600">Followers:</Text>
                      <Text fontWeight="medium">{analysis.analysisData.profile?.followerCount?.toLocaleString() || 'N/A'}</Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text color="gray.600">Engagement Rate:</Text>
                      <Text fontWeight="medium">{analysis.analysisData.profile?.engagementRate?.toFixed(1) || 'N/A'}%</Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text color="gray.600">Content Quality:</Text>
                      <Text fontWeight="medium">{analysis.analysisData.metrics?.contentQuality?.toFixed(0) || 'N/A'}/100</Text>
                    </HStack>
                  </VStack>
                )}

                <Text fontSize="xs" color="gray.500" textAlign="center">
                  Ready to join campaigns and mint NFTs based on this analysis!
                </Text>
              </VStack>
            </Box>
          )}
        </VStack>
      </VStack>
    </Box>
  )
}
