// import { apiClient } from './apiClient' // TODO: Uncomment when backend is ready

export interface Project {
  id: string
  name: string
  description: string
  status: 'active' | 'upcoming' | 'completed'
  imageUrl?: string
  campaignCount?: number
  createdAt: string
  updatedAt: string
}

export const projectService = {
  // Get all projects
  async getProjects(): Promise<Project[]> {
    try {
      console.log('🔄 ProjectService: Loading mock projects data');

      // Mock projects data for development
      const mockProjects: Project[] = [
        {
          id: 'project_1',
          name: 'Social Influencer NFTs',
          description: 'Create unique NFTs based on your social media influence and engagement metrics. Join campaigns to mint exclusive digital collectibles.',
          status: 'active',
          imageUrl: 'https://via.placeholder.com/400x300/1DA1F2/FFFFFF?text=Social+NFTs',
          campaignCount: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'project_2',
          name: 'Community Builder Collection',
          description: 'Recognize and reward community builders with special NFTs that evolve based on community engagement and growth.',
          status: 'active',
          imageUrl: 'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Community+NFTs',
          campaignCount: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'project_3',
          name: 'Creator Economy NFTs',
          description: 'Empower content creators with NFTs that represent their creative journey and audience engagement.',
          status: 'upcoming',
          imageUrl: 'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Creator+NFTs',
          campaignCount: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      console.log('✅ ProjectService: Returning mock projects:', mockProjects.length);
      return mockProjects;

      // TODO: Uncomment when project service is properly configured
      // const response = await apiClient.get('/projects')
      // return response.data
    } catch (error) {
      console.error('Error fetching projects:', error)
      throw error
    }
  },

  // Get project by ID
  async getProject(id: string): Promise<Project> {
    try {
      console.log('🔄 ProjectService: Loading project by ID:', id);

      // Get all projects and find the specific one
      const projects = await this.getProjects();
      const project = projects.find(p => p.id === id);

      if (!project) {
        throw new Error(`Project with ID ${id} not found`);
      }

      console.log('✅ ProjectService: Found project:', project.name);
      return project;

      // TODO: Uncomment when project service is properly configured
      // const response = await apiClient.get(`/projects/${id}`)
      // return response.data
    } catch (error) {
      console.error('Error fetching project:', error)
      throw error
    }
  },

  // Get project campaigns
  async getProjectCampaigns(projectId: string) {
    try {
      console.log('🔄 ProjectService: Loading campaigns for project:', projectId);

      // Mock campaigns data for development
      const mockCampaigns = [
        {
          id: `campaign_${projectId}_1`,
          projectId: projectId,
          name: 'Twitter Influencer Campaign',
          description: 'Mint NFTs based on your Twitter influence metrics',
          status: 'active',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          participantCount: 150,
          maxParticipants: 500
        },
        {
          id: `campaign_${projectId}_2`,
          projectId: projectId,
          name: 'Community Growth Campaign',
          description: 'Reward community builders and active participants',
          status: 'active',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days from now
          participantCount: 89,
          maxParticipants: 300
        }
      ];

      console.log('✅ ProjectService: Returning mock campaigns:', mockCampaigns.length);
      return mockCampaigns;

      // TODO: Uncomment when project service is properly configured
      // const response = await apiClient.get(`/projects/${projectId}/campaigns`)
      // return response.data
    } catch (error) {
      console.error('Error fetching project campaigns:', error)
      throw error
    }
  }
}
