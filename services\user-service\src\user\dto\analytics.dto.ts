import { IsString, IsOptional, <PERSON>E<PERSON>, IsN<PERSON>ber, IsBoolean, IsArray, IsDateString, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum AnalyticsTimeframe {
  HOUR = '1h',
  DAY = '24h',
  WEEK = '7d',
  MONTH = '30d',
  QUARTER = '90d',
  YEAR = '365d',
  ALL_TIME = 'all',
}

export enum AnalyticsMetric {
  USERS = 'users',
  CAMPAIGNS = 'campaigns',
  NFTS = 'nfts',
  MARKETPLACE = 'marketplace',
  ENGAGEMENT = 'engagement',
  REVENUE = 'revenue',
}

export enum ReportType {
  DASHBOARD = 'dashboard',
  USER_ACTIVITY = 'user_activity',
  CAMPAIGN_PERFORMANCE = 'campaign_performance',
  NFT_ANALYTICS = 'nft_analytics',
  MARKETPLACE_ANALYTICS = 'marketplace_analytics',
  FINANCIAL = 'financial',
  ENGAGEMENT = 'engagement',
}

export enum ReportFormat {
  JSON = 'json',
  CSV = 'csv',
  PDF = 'pdf',
  EXCEL = 'excel',
}

export class AnalyticsQueryDto {
  @ApiProperty({
    description: 'Analytics timeframe',
    enum: AnalyticsTimeframe,
    example: AnalyticsTimeframe.WEEK,
  })
  @IsEnum(AnalyticsTimeframe)
  timeframe: AnalyticsTimeframe;

  @ApiPropertyOptional({
    description: 'Start date for custom timeframe',
    example: '2025-06-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for custom timeframe',
    example: '2025-06-30T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Specific metrics to include',
    enum: AnalyticsMetric,
    isArray: true,
    example: [AnalyticsMetric.USERS, AnalyticsMetric.CAMPAIGNS],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(AnalyticsMetric, { each: true })
  metrics?: AnalyticsMetric[];

  @ApiPropertyOptional({
    description: 'Filter by user ID',
    example: 'user_123',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Filter by campaign ID',
    example: 'campaign_456',
  })
  @IsOptional()
  @IsString()
  campaignId?: string;

  @ApiPropertyOptional({
    description: 'Group results by time interval',
    example: 'day',
    enum: ['hour', 'day', 'week', 'month'],
  })
  @IsOptional()
  @IsString()
  groupBy?: string;

  @ApiPropertyOptional({
    description: 'Include comparison with previous period',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  includeComparison?: boolean = false;
}

export class GenerateReportDto {
  @ApiProperty({
    description: 'Report type',
    enum: ReportType,
    example: ReportType.DASHBOARD,
  })
  @IsEnum(ReportType)
  reportType: ReportType;

  @ApiProperty({
    description: 'Report format',
    enum: ReportFormat,
    example: ReportFormat.JSON,
  })
  @IsEnum(ReportFormat)
  format: ReportFormat;

  @ApiProperty({
    description: 'Analytics timeframe',
    enum: AnalyticsTimeframe,
    example: AnalyticsTimeframe.MONTH,
  })
  @IsEnum(AnalyticsTimeframe)
  timeframe: AnalyticsTimeframe;

  @ApiPropertyOptional({
    description: 'Report title',
    example: 'Monthly Platform Analytics Report',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Report description',
    example: 'Comprehensive analytics report for platform performance',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Include detailed breakdowns',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  includeDetails?: boolean = true;

  @ApiPropertyOptional({
    description: 'Include charts and visualizations',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  includeCharts?: boolean = true;

  @ApiPropertyOptional({
    description: 'Email recipients for the report',
    example: ['<EMAIL>', '<EMAIL>'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  emailRecipients?: string[];

  @ApiPropertyOptional({
    description: 'Additional report filters',
    example: {
      campaignType: 'social_engagement',
      nftRarity: 'legendary',
    },
  })
  @IsOptional()
  filters?: Record<string, any>;
}

export class UserAnalyticsDto {
  @ApiProperty({
    description: 'Total registered users',
    example: 15420,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'New users in timeframe',
    example: 342,
  })
  newUsers: number;

  @ApiProperty({
    description: 'Active users in timeframe',
    example: 8750,
  })
  activeUsers: number;

  @ApiProperty({
    description: 'User retention rate',
    example: 68.5,
  })
  retentionRate: number;

  @ApiProperty({
    description: 'Average session duration in minutes',
    example: 24.7,
  })
  avgSessionDuration: number;

  @ApiProperty({
    description: 'User growth rate percentage',
    example: 12.3,
  })
  growthRate: number;

  @ApiProperty({
    description: 'User activity breakdown',
  })
  activityBreakdown: {
    campaignParticipants: number;
    nftOwners: number;
    marketplaceUsers: number;
    socialEngagers: number;
  };

  @ApiProperty({
    description: 'User demographics',
  })
  demographics: {
    topCountries: Array<{ country: string; count: number; percentage: number }>;
    ageGroups: Array<{ ageGroup: string; count: number; percentage: number }>;
    deviceTypes: Array<{ device: string; count: number; percentage: number }>;
  };
}

export class CampaignAnalyticsOverviewDto {
  @ApiProperty({
    description: 'Total campaigns',
    example: 45,
  })
  totalCampaigns: number;

  @ApiProperty({
    description: 'Active campaigns',
    example: 12,
  })
  activeCampaigns: number;

  @ApiProperty({
    description: 'Completed campaigns',
    example: 28,
  })
  completedCampaigns: number;

  @ApiProperty({
    description: 'Total participants across all campaigns',
    example: 23450,
  })
  totalParticipants: number;

  @ApiProperty({
    description: 'Average completion rate',
    example: 42.8,
  })
  avgCompletionRate: number;

  @ApiProperty({
    description: 'Total rewards distributed',
    example: 8920,
  })
  totalRewardsDistributed: number;

  @ApiProperty({
    description: 'Campaign performance by type',
  })
  performanceByType: Array<{
    type: string;
    count: number;
    avgParticipants: number;
    avgCompletionRate: number;
    totalRewards: number;
  }>;

  @ApiProperty({
    description: 'Top performing campaigns',
  })
  topCampaigns: Array<{
    id: string;
    name: string;
    participants: number;
    completionRate: number;
    rewardsDistributed: number;
  }>;
}

export class NFTAnalyticsOverviewDto {
  @ApiProperty({
    description: 'Total NFTs generated',
    example: 12750,
  })
  totalNFTs: number;

  @ApiProperty({
    description: 'NFTs minted',
    example: 9840,
  })
  mintedNFTs: number;

  @ApiProperty({
    description: 'NFTs listed on marketplace',
    example: 3420,
  })
  listedNFTs: number;

  @ApiProperty({
    description: 'NFTs sold',
    example: 1890,
  })
  soldNFTs: number;

  @ApiProperty({
    description: 'Average NFT value in ETH',
    example: '0.245',
  })
  avgNFTValue: string;

  @ApiProperty({
    description: 'Total NFT volume in ETH',
    example: '463.05',
  })
  totalVolume: string;

  @ApiProperty({
    description: 'NFT rarity distribution',
  })
  rarityDistribution: {
    common: { count: number; percentage: number };
    rare: { count: number; percentage: number };
    epic: { count: number; percentage: number };
    legendary: { count: number; percentage: number };
    mythic: { count: number; percentage: number };
  };

  @ApiProperty({
    description: 'Top NFT collections by volume',
  })
  topCollections: Array<{
    campaignId: string;
    campaignName: string;
    nftCount: number;
    totalVolume: string;
    floorPrice: string;
  }>;
}

export class MarketplaceAnalyticsOverviewDto {
  @ApiProperty({
    description: 'Total marketplace volume in ETH',
    example: '1247.85',
  })
  totalVolume: string;

  @ApiProperty({
    description: 'Volume in timeframe',
    example: '156.32',
  })
  periodVolume: string;

  @ApiProperty({
    description: 'Total transactions',
    example: 5670,
  })
  totalTransactions: number;

  @ApiProperty({
    description: 'Transactions in timeframe',
    example: 234,
  })
  periodTransactions: number;

  @ApiProperty({
    description: 'Average transaction value',
    example: '0.534',
  })
  avgTransactionValue: string;

  @ApiProperty({
    description: 'Active listings',
    example: 892,
  })
  activeListings: number;

  @ApiProperty({
    description: 'Unique traders',
    example: 3420,
  })
  uniqueTraders: number;

  @ApiProperty({
    description: 'Platform fees collected',
    example: '31.20',
  })
  platformFees: string;

  @ApiProperty({
    description: 'Trading activity by currency',
  })
  currencyBreakdown: Array<{
    currency: string;
    volume: string;
    transactions: number;
    percentage: number;
  }>;
}

export class EngagementAnalyticsDto {
  @ApiProperty({
    description: 'Overall engagement score',
    example: 78.5,
  })
  overallScore: number;

  @ApiProperty({
    description: 'Social media engagement',
    example: 82.3,
  })
  socialEngagement: number;

  @ApiProperty({
    description: 'Platform engagement',
    example: 74.7,
  })
  platformEngagement: number;

  @ApiProperty({
    description: 'Campaign engagement',
    example: 68.9,
  })
  campaignEngagement: number;

  @ApiProperty({
    description: 'NFT engagement',
    example: 85.2,
  })
  nftEngagement: number;

  @ApiProperty({
    description: 'Marketplace engagement',
    example: 71.4,
  })
  marketplaceEngagement: number;

  @ApiProperty({
    description: 'Engagement trends over time',
  })
  trends: Array<{
    date: string;
    score: number;
    socialEngagement: number;
    platformEngagement: number;
  }>;

  @ApiProperty({
    description: 'Top engagement activities',
  })
  topActivities: Array<{
    activity: string;
    count: number;
    avgScore: number;
    trend: number;
  }>;
}

export class RevenueAnalyticsDto {
  @ApiProperty({
    description: 'Total revenue in USD',
    example: '45670.25',
  })
  totalRevenue: string;

  @ApiProperty({
    description: 'Revenue in timeframe',
    example: '8920.50',
  })
  periodRevenue: string;

  @ApiProperty({
    description: 'Revenue growth rate',
    example: 23.7,
  })
  growthRate: number;

  @ApiProperty({
    description: 'Average revenue per user',
    example: '12.45',
  })
  avgRevenuePerUser: string;

  @ApiProperty({
    description: 'Revenue by source',
  })
  revenueBySource: {
    marketplaceFees: { amount: string; percentage: number };
    premiumFeatures: { amount: string; percentage: number };
    partnerships: { amount: string; percentage: number };
    other: { amount: string; percentage: number };
  };

  @ApiProperty({
    description: 'Revenue trends over time',
  })
  trends: Array<{
    date: string;
    revenue: string;
    transactions: number;
    avgValue: string;
  }>;

  @ApiProperty({
    description: 'Top revenue generating campaigns',
  })
  topCampaigns: Array<{
    campaignId: string;
    campaignName: string;
    revenue: string;
    participants: number;
    roi: number;
  }>;
}

export class PlatformAnalyticsDto {
  @ApiProperty({
    description: 'Analytics timeframe',
    enum: AnalyticsTimeframe,
    example: AnalyticsTimeframe.WEEK,
  })
  timeframe: AnalyticsTimeframe;

  @ApiProperty({
    description: 'Analytics generation timestamp',
    example: '2025-06-03T23:00:00Z',
  })
  generatedAt: string;

  @ApiProperty({
    description: 'User analytics',
  })
  users?: UserAnalyticsDto;

  @ApiProperty({
    description: 'Campaign analytics',
  })
  campaigns?: CampaignAnalyticsOverviewDto;

  @ApiProperty({
    description: 'NFT analytics',
  })
  nfts?: NFTAnalyticsOverviewDto;

  @ApiProperty({
    description: 'Marketplace analytics',
  })
  marketplace?: MarketplaceAnalyticsOverviewDto;

  @ApiProperty({
    description: 'Engagement analytics',
  })
  engagement?: EngagementAnalyticsDto;

  @ApiProperty({
    description: 'Revenue analytics',
  })
  revenue?: RevenueAnalyticsDto;
}
